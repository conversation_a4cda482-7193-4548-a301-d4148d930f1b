// package/@stackframe/react/node_modules/@stackframe/stack-shared/dist/esm/utils/globals.js
var globalVar = typeof globalThis !== "undefined" ? globalThis : typeof global !== "undefined" ? global : typeof window !== "undefined" ? window : typeof self !== "undefined" ? self : {};
if (typeof globalThis === "undefined") {
  globalVar.globalThis = globalVar;
}
var stackGlobalsSymbol = Symbol.for("__stack-globals");
globalVar[stackGlobalsSymbol] ?? (globalVar[stackGlobalsSymbol] = {});
function createGlobal(key, init) {
  if (!globalVar[stackGlobalsSymbol][key]) {
    globalVar[stackGlobalsSymbol][key] = init();
  }
  return globalVar[stackGlobalsSymbol][key];
}

export {
  globalVar,
  createGlobal
};
//# sourceMappingURL=chunk-4BLY47KI.js.map
