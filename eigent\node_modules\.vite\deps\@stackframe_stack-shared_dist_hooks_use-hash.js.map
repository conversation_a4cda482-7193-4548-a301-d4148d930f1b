{"version": 3, "sources": ["../../../package/@stackframe/react/node_modules/@stackframe/stack-shared/src/hooks/use-hash.tsx"], "sourcesContent": ["import { useSyncExternalStore } from \"react\";\nimport { suspendIfSsr } from \"../utils/react\";\n\nexport const useHash = () => {\n  suspendIfSsr(\"useHash\");\n  return useSyncExternalStore(\n    (onChange) => {\n      const interval = setInterval(() => onChange(), 10);\n      return () => clearInterval(interval);\n    },\n    () => window.location.hash.substring(1)\n  );\n};\n\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAAA,mBAAqC;AAG9B,IAAM,UAAU,MAAM;AAC3B,eAAa,SAAS;AACtB,aAAO;IACL,CAAC,aAAa;AACZ,YAAM,WAAW,YAAY,MAAM,SAAS,GAAG,EAAE;AACjD,aAAO,MAAM,cAAc,QAAQ;IACrC;IACA,MAAM,OAAO,SAAS,KAAK,UAAU,CAAC;EACxC;AACF;", "names": []}