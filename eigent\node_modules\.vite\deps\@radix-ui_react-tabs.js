"use client";
import {
  Content,
  List,
  Root2,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  createTabsScope
} from "./chunk-XKHELC34.js";
import "./chunk-5ZCRRBOX.js";
import "./chunk-Y6LLMME5.js";
import "./chunk-INJJPTDC.js";
import "./chunk-C2D4OKTQ.js";
import "./chunk-IWV5NBJL.js";
import "./chunk-54BE7P2O.js";
import "./chunk-753TZ7EK.js";
import "./chunk-4SYFA6CU.js";
import "./chunk-6ONK65C5.js";
import "./chunk-I3M3GCX4.js";
import "./chunk-MBTB7A2C.js";
import "./chunk-6OBIWUOU.js";
import "./chunk-7UVSMXVG.js";
export {
  Content,
  List,
  Root2 as Root,
  Ta<PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  createTabs<PERSON>cope
};
//# sourceMappingURL=@radix-ui_react-tabs.js.map
