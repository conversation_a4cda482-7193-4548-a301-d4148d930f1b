{"version": 3, "sources": ["../../monaco-editor/esm/vs/basic-languages/ini/ini.js"], "sourcesContent": ["/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/ini/ini.ts\nvar conf = {\n  comments: {\n    lineComment: \"#\"\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" }\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" }\n  ]\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: \".ini\",\n  // we include these common regular expressions\n  escapes: /\\\\(?:[abfnrtv\\\\\"']|x[0-9A-Fa-f]{1,4}|u[0-9A-Fa-f]{4}|U[0-9A-Fa-f]{8})/,\n  // The main tokenizer for our languages\n  tokenizer: {\n    root: [\n      // sections\n      [/^\\[[^\\]]*\\]/, \"metatag\"],\n      // keys\n      [/(^\\w+)(\\s*)(\\=)/, [\"key\", \"\", \"delimiter\"]],\n      // whitespace\n      { include: \"@whitespace\" },\n      // numbers\n      [/\\d+/, \"number\"],\n      // strings: recover on non-terminated strings\n      [/\"([^\"\\\\]|\\\\.)*$/, \"string.invalid\"],\n      // non-teminated string\n      [/'([^'\\\\]|\\\\.)*$/, \"string.invalid\"],\n      // non-teminated string\n      [/\"/, \"string\", '@string.\"'],\n      [/'/, \"string\", \"@string.'\"]\n    ],\n    whitespace: [\n      [/[ \\t\\r\\n]+/, \"\"],\n      [/^\\s*[#;].*$/, \"comment\"]\n    ],\n    string: [\n      [/[^\\\\\"']+/, \"string\"],\n      [/@escapes/, \"string.escape\"],\n      [/\\\\./, \"string.escape.invalid\"],\n      [\n        /[\"']/,\n        {\n          cases: {\n            \"$#==$S2\": { token: \"string\", next: \"@pop\" },\n            \"@default\": \"string\"\n          }\n        }\n      ]\n    ]\n  }\n};\nexport {\n  conf,\n  language\n};\n"], "mappings": ";;;AASA,IAAI,OAAO;AAAA,EACT,UAAU;AAAA,IACR,aAAa;AAAA,EACf;AAAA,EACA,UAAU;AAAA,IACR,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,EACX;AAAA,EACA,kBAAkB;AAAA,IAChB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,EAC1B;AAAA,EACA,kBAAkB;AAAA,IAChB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,EAC1B;AACF;AACA,IAAI,WAAW;AAAA,EACb,cAAc;AAAA,EACd,cAAc;AAAA;AAAA,EAEd,SAAS;AAAA;AAAA,EAET,WAAW;AAAA,IACT,MAAM;AAAA;AAAA,MAEJ,CAAC,eAAe,SAAS;AAAA;AAAA,MAEzB,CAAC,mBAAmB,CAAC,OAAO,IAAI,WAAW,CAAC;AAAA;AAAA,MAE5C,EAAE,SAAS,cAAc;AAAA;AAAA,MAEzB,CAAC,OAAO,QAAQ;AAAA;AAAA,MAEhB,CAAC,mBAAmB,gBAAgB;AAAA;AAAA,MAEpC,CAAC,mBAAmB,gBAAgB;AAAA;AAAA,MAEpC,CAAC,KAAK,UAAU,WAAW;AAAA,MAC3B,CAAC,KAAK,UAAU,WAAW;AAAA,IAC7B;AAAA,IACA,YAAY;AAAA,MACV,CAAC,cAAc,EAAE;AAAA,MACjB,CAAC,eAAe,SAAS;AAAA,IAC3B;AAAA,IACA,QAAQ;AAAA,MACN,CAAC,YAAY,QAAQ;AAAA,MACrB,CAAC,YAAY,eAAe;AAAA,MAC5B,CAAC,OAAO,uBAAuB;AAAA,MAC/B;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,WAAW,EAAE,OAAO,UAAU,MAAM,OAAO;AAAA,YAC3C,YAAY;AAAA,UACd;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;", "names": []}