import {
  deindent,
  deindentTemplate,
  escapeTemplateLiteral,
  extractScopes,
  getWhitespacePrefix,
  getWhitespaceSuffix,
  mergeScopeStrings,
  nicify,
  replaceAll,
  stringCompare,
  templateIdentity,
  trimEmptyLinesEnd,
  trimEmptyLinesStart,
  trimLines,
  typedCapitalize,
  typedToLowercase,
  typedToUppercase
} from "./chunk-Z26222H5.js";
import "./chunk-4BLY47KI.js";
import "./chunk-GMJRCDEU.js";
import "./chunk-7UVSMXVG.js";
export {
  deindent,
  deindentTemplate,
  escapeTemplateLiteral,
  extractScopes,
  getWhitespacePrefix,
  getWhitespaceSuffix,
  mergeScopeStrings,
  nicify,
  replaceAll,
  stringCompare,
  templateIdentity,
  trimEmptyLinesEnd,
  trimEmptyLinesStart,
  trimLines,
  typedCapitalize,
  typedToLowercase,
  typedToUppercase
};
//# sourceMappingURL=@stackframe_stack-shared_dist_utils_strings.js.map
