import {
  generateUuid
} from "./chunk-QNRW4OZD.js";
import {
  KnownError,
  KnownErrors
} from "./chunk-REXVUZ33.js";
import {
  urlString
} from "./chunk-UFCUR7KH.js";
import {
  generateSecureRandomString
} from "./chunk-JBDVY4WB.js";
import {
  decodeBase64,
  isBase64
} from "./chunk-LWWYEWPG.js";
import {
  StackAssertionError,
  captureError,
  concatStacktraces,
  deindent,
  filterUndefined,
  filterUndefinedOrNull,
  nicify,
  throwErr
} from "./chunk-Z26222H5.js";
import {
  globalVar
} from "./chunk-4BLY47KI.js";
import {
  __export
} from "./chunk-7UVSMXVG.js";

// node_modules/jose/dist/browser/runtime/webcrypto.js
var webcrypto_default = crypto;
var isCryptoKey = (key) => key instanceof CryptoKey;

// node_modules/jose/dist/browser/lib/buffer_utils.js
var encoder = new TextEncoder();
var decoder = new TextDecoder();
var MAX_INT32 = 2 ** 32;

// node_modules/jose/dist/browser/runtime/base64url.js
var encodeBase64 = (input) => {
  let unencoded = input;
  if (typeof unencoded === "string") {
    unencoded = encoder.encode(unencoded);
  }
  const CHUNK_SIZE2 = 32768;
  const arr = [];
  for (let i = 0; i < unencoded.length; i += CHUNK_SIZE2) {
    arr.push(String.fromCharCode.apply(null, unencoded.subarray(i, i + CHUNK_SIZE2)));
  }
  return btoa(arr.join(""));
};
var encode = (input) => {
  return encodeBase64(input).replace(/=/g, "").replace(/\+/g, "-").replace(/\//g, "_");
};
var decodeBase642 = (encoded) => {
  const binary = atob(encoded);
  const bytes = new Uint8Array(binary.length);
  for (let i = 0; i < binary.length; i++) {
    bytes[i] = binary.charCodeAt(i);
  }
  return bytes;
};
var decode = (input) => {
  let encoded = input;
  if (encoded instanceof Uint8Array) {
    encoded = decoder.decode(encoded);
  }
  encoded = encoded.replace(/-/g, "+").replace(/_/g, "/").replace(/\s/g, "");
  try {
    return decodeBase642(encoded);
  } catch {
    throw new TypeError("The input to be decoded is not correctly encoded.");
  }
};

// node_modules/jose/dist/browser/util/errors.js
var errors_exports = {};
__export(errors_exports, {
  JOSEAlgNotAllowed: () => JOSEAlgNotAllowed,
  JOSEError: () => JOSEError,
  JOSENotSupported: () => JOSENotSupported,
  JWEDecryptionFailed: () => JWEDecryptionFailed,
  JWEInvalid: () => JWEInvalid,
  JWKInvalid: () => JWKInvalid,
  JWKSInvalid: () => JWKSInvalid,
  JWKSMultipleMatchingKeys: () => JWKSMultipleMatchingKeys,
  JWKSNoMatchingKey: () => JWKSNoMatchingKey,
  JWKSTimeout: () => JWKSTimeout,
  JWSInvalid: () => JWSInvalid,
  JWSSignatureVerificationFailed: () => JWSSignatureVerificationFailed,
  JWTClaimValidationFailed: () => JWTClaimValidationFailed,
  JWTExpired: () => JWTExpired,
  JWTInvalid: () => JWTInvalid
});
var JOSEError = class extends Error {
  constructor(message2, options) {
    var _a4;
    super(message2, options);
    this.code = "ERR_JOSE_GENERIC";
    this.name = this.constructor.name;
    (_a4 = Error.captureStackTrace) == null ? void 0 : _a4.call(Error, this, this.constructor);
  }
};
JOSEError.code = "ERR_JOSE_GENERIC";
var JWTClaimValidationFailed = class extends JOSEError {
  constructor(message2, payload, claim = "unspecified", reason = "unspecified") {
    super(message2, { cause: { claim, reason, payload } });
    this.code = "ERR_JWT_CLAIM_VALIDATION_FAILED";
    this.claim = claim;
    this.reason = reason;
    this.payload = payload;
  }
};
JWTClaimValidationFailed.code = "ERR_JWT_CLAIM_VALIDATION_FAILED";
var JWTExpired = class extends JOSEError {
  constructor(message2, payload, claim = "unspecified", reason = "unspecified") {
    super(message2, { cause: { claim, reason, payload } });
    this.code = "ERR_JWT_EXPIRED";
    this.claim = claim;
    this.reason = reason;
    this.payload = payload;
  }
};
JWTExpired.code = "ERR_JWT_EXPIRED";
var JOSEAlgNotAllowed = class extends JOSEError {
  constructor() {
    super(...arguments);
    this.code = "ERR_JOSE_ALG_NOT_ALLOWED";
  }
};
JOSEAlgNotAllowed.code = "ERR_JOSE_ALG_NOT_ALLOWED";
var JOSENotSupported = class extends JOSEError {
  constructor() {
    super(...arguments);
    this.code = "ERR_JOSE_NOT_SUPPORTED";
  }
};
JOSENotSupported.code = "ERR_JOSE_NOT_SUPPORTED";
var JWEDecryptionFailed = class extends JOSEError {
  constructor(message2 = "decryption operation failed", options) {
    super(message2, options);
    this.code = "ERR_JWE_DECRYPTION_FAILED";
  }
};
JWEDecryptionFailed.code = "ERR_JWE_DECRYPTION_FAILED";
var JWEInvalid = class extends JOSEError {
  constructor() {
    super(...arguments);
    this.code = "ERR_JWE_INVALID";
  }
};
JWEInvalid.code = "ERR_JWE_INVALID";
var JWSInvalid = class extends JOSEError {
  constructor() {
    super(...arguments);
    this.code = "ERR_JWS_INVALID";
  }
};
JWSInvalid.code = "ERR_JWS_INVALID";
var JWTInvalid = class extends JOSEError {
  constructor() {
    super(...arguments);
    this.code = "ERR_JWT_INVALID";
  }
};
JWTInvalid.code = "ERR_JWT_INVALID";
var JWKInvalid = class extends JOSEError {
  constructor() {
    super(...arguments);
    this.code = "ERR_JWK_INVALID";
  }
};
JWKInvalid.code = "ERR_JWK_INVALID";
var JWKSInvalid = class extends JOSEError {
  constructor() {
    super(...arguments);
    this.code = "ERR_JWKS_INVALID";
  }
};
JWKSInvalid.code = "ERR_JWKS_INVALID";
var JWKSNoMatchingKey = class extends JOSEError {
  constructor(message2 = "no applicable key found in the JSON Web Key Set", options) {
    super(message2, options);
    this.code = "ERR_JWKS_NO_MATCHING_KEY";
  }
};
JWKSNoMatchingKey.code = "ERR_JWKS_NO_MATCHING_KEY";
var JWKSMultipleMatchingKeys = class extends JOSEError {
  constructor(message2 = "multiple matching keys found in the JSON Web Key Set", options) {
    super(message2, options);
    this.code = "ERR_JWKS_MULTIPLE_MATCHING_KEYS";
  }
};
JWKSMultipleMatchingKeys.code = "ERR_JWKS_MULTIPLE_MATCHING_KEYS";
var JWKSTimeout = class extends JOSEError {
  constructor(message2 = "request timed out", options) {
    super(message2, options);
    this.code = "ERR_JWKS_TIMEOUT";
  }
};
JWKSTimeout.code = "ERR_JWKS_TIMEOUT";
var JWSSignatureVerificationFailed = class extends JOSEError {
  constructor(message2 = "signature verification failed", options) {
    super(message2, options);
    this.code = "ERR_JWS_SIGNATURE_VERIFICATION_FAILED";
  }
};
JWSSignatureVerificationFailed.code = "ERR_JWS_SIGNATURE_VERIFICATION_FAILED";

// node_modules/jose/dist/browser/runtime/random.js
var random_default = webcrypto_default.getRandomValues.bind(webcrypto_default);

// node_modules/jose/dist/browser/lib/invalid_key_input.js
function message(msg, actual, ...types2) {
  var _a4;
  types2 = types2.filter(Boolean);
  if (types2.length > 2) {
    const last = types2.pop();
    msg += `one of type ${types2.join(", ")}, or ${last}.`;
  } else if (types2.length === 2) {
    msg += `one of type ${types2[0]} or ${types2[1]}.`;
  } else {
    msg += `of type ${types2[0]}.`;
  }
  if (actual == null) {
    msg += ` Received ${actual}`;
  } else if (typeof actual === "function" && actual.name) {
    msg += ` Received function ${actual.name}`;
  } else if (typeof actual === "object" && actual != null) {
    if ((_a4 = actual.constructor) == null ? void 0 : _a4.name) {
      msg += ` Received an instance of ${actual.constructor.name}`;
    }
  }
  return msg;
}
function withAlg(alg, actual, ...types2) {
  return message(`Key for the ${alg} algorithm must be `, actual, ...types2);
}

// node_modules/jose/dist/browser/runtime/is_key_like.js
var is_key_like_default = (key) => {
  if (isCryptoKey(key)) {
    return true;
  }
  return (key == null ? void 0 : key[Symbol.toStringTag]) === "KeyObject";
};
var types = ["CryptoKey"];

// node_modules/jose/dist/browser/lib/is_object.js
function isObjectLike(value) {
  return typeof value === "object" && value !== null;
}
function isObject(input) {
  if (!isObjectLike(input) || Object.prototype.toString.call(input) !== "[object Object]") {
    return false;
  }
  if (Object.getPrototypeOf(input) === null) {
    return true;
  }
  let proto = input;
  while (Object.getPrototypeOf(proto) !== null) {
    proto = Object.getPrototypeOf(proto);
  }
  return Object.getPrototypeOf(input) === proto;
}

// node_modules/jose/dist/browser/lib/is_jwk.js
function isJWK(key) {
  return isObject(key) && typeof key.kty === "string";
}
function isPrivateJWK(key) {
  return key.kty !== "oct" && typeof key.d === "string";
}
function isPublicJWK(key) {
  return key.kty !== "oct" && typeof key.d === "undefined";
}
function isSecretJWK(key) {
  return isJWK(key) && key.kty === "oct" && typeof key.k === "string";
}

// node_modules/jose/dist/browser/lib/check_key_type.js
var tag = (key) => key == null ? void 0 : key[Symbol.toStringTag];
var jwkMatchesOp = (alg, key, usage) => {
  var _a4, _b4;
  if (key.use !== void 0 && key.use !== "sig") {
    throw new TypeError("Invalid key for this operation, when present its use must be sig");
  }
  if (key.key_ops !== void 0 && ((_b4 = (_a4 = key.key_ops).includes) == null ? void 0 : _b4.call(_a4, usage)) !== true) {
    throw new TypeError(`Invalid key for this operation, when present its key_ops must include ${usage}`);
  }
  if (key.alg !== void 0 && key.alg !== alg) {
    throw new TypeError(`Invalid key for this operation, when present its alg must be ${alg}`);
  }
  return true;
};
var symmetricTypeCheck = (alg, key, usage, allowJwk) => {
  if (key instanceof Uint8Array)
    return;
  if (allowJwk && isJWK(key)) {
    if (isSecretJWK(key) && jwkMatchesOp(alg, key, usage))
      return;
    throw new TypeError(`JSON Web Key for symmetric algorithms must have JWK "kty" (Key Type) equal to "oct" and the JWK "k" (Key Value) present`);
  }
  if (!is_key_like_default(key)) {
    throw new TypeError(withAlg(alg, key, ...types, "Uint8Array", allowJwk ? "JSON Web Key" : null));
  }
  if (key.type !== "secret") {
    throw new TypeError(`${tag(key)} instances for symmetric algorithms must be of type "secret"`);
  }
};
var asymmetricTypeCheck = (alg, key, usage, allowJwk) => {
  if (allowJwk && isJWK(key)) {
    switch (usage) {
      case "sign":
        if (isPrivateJWK(key) && jwkMatchesOp(alg, key, usage))
          return;
        throw new TypeError(`JSON Web Key for this operation be a private JWK`);
      case "verify":
        if (isPublicJWK(key) && jwkMatchesOp(alg, key, usage))
          return;
        throw new TypeError(`JSON Web Key for this operation be a public JWK`);
    }
  }
  if (!is_key_like_default(key)) {
    throw new TypeError(withAlg(alg, key, ...types, allowJwk ? "JSON Web Key" : null));
  }
  if (key.type === "secret") {
    throw new TypeError(`${tag(key)} instances for asymmetric algorithms must not be of type "secret"`);
  }
  if (usage === "sign" && key.type === "public") {
    throw new TypeError(`${tag(key)} instances for asymmetric algorithm signing must be of type "private"`);
  }
  if (usage === "decrypt" && key.type === "public") {
    throw new TypeError(`${tag(key)} instances for asymmetric algorithm decryption must be of type "private"`);
  }
  if (key.algorithm && usage === "verify" && key.type === "private") {
    throw new TypeError(`${tag(key)} instances for asymmetric algorithm verifying must be of type "public"`);
  }
  if (key.algorithm && usage === "encrypt" && key.type === "private") {
    throw new TypeError(`${tag(key)} instances for asymmetric algorithm encryption must be of type "public"`);
  }
};
function checkKeyType(allowJwk, alg, key, usage) {
  const symmetric = alg.startsWith("HS") || alg === "dir" || alg.startsWith("PBES2") || /^A\d{3}(?:GCM)?KW$/.test(alg);
  if (symmetric) {
    symmetricTypeCheck(alg, key, usage, allowJwk);
  } else {
    asymmetricTypeCheck(alg, key, usage, allowJwk);
  }
}
var check_key_type_default = checkKeyType.bind(void 0, false);
var checkKeyTypeWithJwk = checkKeyType.bind(void 0, true);

// node_modules/jose/dist/browser/lib/private_symbols.js
var unprotected = Symbol();

// node_modules/jose/dist/browser/lib/secs.js
var minute = 60;
var hour = minute * 60;
var day = hour * 24;
var week = day * 7;
var year = day * 365.25;

// node_modules/jose/dist/browser/jwks/remote.js
var USER_AGENT;
var _a, _b;
if (typeof navigator === "undefined" || !((_b = (_a = navigator.userAgent) == null ? void 0 : _a.startsWith) == null ? void 0 : _b.call(_a, "Mozilla/5.0 "))) {
  const NAME = "jose";
  const VERSION = "v5.10.0";
  USER_AGENT = `${NAME}/${VERSION}`;
}
var jwksCache = Symbol();

// node_modules/jose/dist/browser/util/base64url.js
var base64url_exports2 = {};
__export(base64url_exports2, {
  decode: () => decode2,
  encode: () => encode2
});
var encode2 = encode;
var decode2 = decode;

// node_modules/jose/dist/browser/util/decode_jwt.js
function decodeJwt(jwt2) {
  if (typeof jwt2 !== "string")
    throw new JWTInvalid("JWTs must use Compact JWS serialization, JWT must be a string");
  const { 1: payload, length } = jwt2.split(".");
  if (length === 5)
    throw new JWTInvalid("Only JWTs using Compact JWS serialization can be decoded");
  if (length !== 3)
    throw new JWTInvalid("Invalid JWT");
  if (!payload)
    throw new JWTInvalid("JWTs must contain a payload");
  let decoded;
  try {
    decoded = decode2(payload);
  } catch {
    throw new JWTInvalid("Failed to base64url decode the payload");
  }
  let result;
  try {
    result = JSON.parse(decoder.decode(decoded));
  } catch {
    throw new JWTInvalid("Failed to parse the decoded payload as JSON");
  }
  if (!isObject(result))
    throw new JWTInvalid("Invalid JWT Claims Set");
  return result;
}

// node_modules/async-mutex/index.mjs
var E_TIMEOUT = new Error("timeout while waiting for mutex to become available");
var E_ALREADY_LOCKED = new Error("mutex already locked");
var E_CANCELED = new Error("request for lock canceled");
var __awaiter$2 = function(thisArg, _arguments, P, generator) {
  function adopt(value) {
    return value instanceof P ? value : new P(function(resolve) {
      resolve(value);
    });
  }
  return new (P || (P = Promise))(function(resolve, reject) {
    function fulfilled(value) {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    }
    function rejected2(value) {
      try {
        step(generator["throw"](value));
      } catch (e) {
        reject(e);
      }
    }
    function step(result) {
      result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected2);
    }
    step((generator = generator.apply(thisArg, _arguments || [])).next());
  });
};
var Semaphore = class {
  constructor(_value, _cancelError = E_CANCELED) {
    this._value = _value;
    this._cancelError = _cancelError;
    this._queue = [];
    this._weightedWaiters = [];
  }
  acquire(weight = 1, priority = 0) {
    if (weight <= 0)
      throw new Error(`invalid weight ${weight}: must be positive`);
    return new Promise((resolve, reject) => {
      const task = { resolve, reject, weight, priority };
      const i = findIndexFromEnd(this._queue, (other) => priority <= other.priority);
      if (i === -1 && weight <= this._value) {
        this._dispatchItem(task);
      } else {
        this._queue.splice(i + 1, 0, task);
      }
    });
  }
  runExclusive(callback_1) {
    return __awaiter$2(this, arguments, void 0, function* (callback, weight = 1, priority = 0) {
      const [value, release] = yield this.acquire(weight, priority);
      try {
        return yield callback(value);
      } finally {
        release();
      }
    });
  }
  waitForUnlock(weight = 1, priority = 0) {
    if (weight <= 0)
      throw new Error(`invalid weight ${weight}: must be positive`);
    if (this._couldLockImmediately(weight, priority)) {
      return Promise.resolve();
    } else {
      return new Promise((resolve) => {
        if (!this._weightedWaiters[weight - 1])
          this._weightedWaiters[weight - 1] = [];
        insertSorted(this._weightedWaiters[weight - 1], { resolve, priority });
      });
    }
  }
  isLocked() {
    return this._value <= 0;
  }
  getValue() {
    return this._value;
  }
  setValue(value) {
    this._value = value;
    this._dispatchQueue();
  }
  release(weight = 1) {
    if (weight <= 0)
      throw new Error(`invalid weight ${weight}: must be positive`);
    this._value += weight;
    this._dispatchQueue();
  }
  cancel() {
    this._queue.forEach((entry) => entry.reject(this._cancelError));
    this._queue = [];
  }
  _dispatchQueue() {
    this._drainUnlockWaiters();
    while (this._queue.length > 0 && this._queue[0].weight <= this._value) {
      this._dispatchItem(this._queue.shift());
      this._drainUnlockWaiters();
    }
  }
  _dispatchItem(item) {
    const previousValue = this._value;
    this._value -= item.weight;
    item.resolve([previousValue, this._newReleaser(item.weight)]);
  }
  _newReleaser(weight) {
    let called = false;
    return () => {
      if (called)
        return;
      called = true;
      this.release(weight);
    };
  }
  _drainUnlockWaiters() {
    if (this._queue.length === 0) {
      for (let weight = this._value; weight > 0; weight--) {
        const waiters = this._weightedWaiters[weight - 1];
        if (!waiters)
          continue;
        waiters.forEach((waiter) => waiter.resolve());
        this._weightedWaiters[weight - 1] = [];
      }
    } else {
      const queuedPriority = this._queue[0].priority;
      for (let weight = this._value; weight > 0; weight--) {
        const waiters = this._weightedWaiters[weight - 1];
        if (!waiters)
          continue;
        const i = waiters.findIndex((waiter) => waiter.priority <= queuedPriority);
        (i === -1 ? waiters : waiters.splice(0, i)).forEach((waiter) => waiter.resolve());
      }
    }
  }
  _couldLockImmediately(weight, priority) {
    return (this._queue.length === 0 || this._queue[0].priority < priority) && weight <= this._value;
  }
};
function insertSorted(a, v) {
  const i = findIndexFromEnd(a, (other) => v.priority <= other.priority);
  a.splice(i + 1, 0, v);
}
function findIndexFromEnd(a, predicate) {
  for (let i = a.length - 1; i >= 0; i--) {
    if (predicate(a[i])) {
      return i;
    }
  }
  return -1;
}

// package/@stackframe/react/node_modules/@stackframe/stack-shared/dist/esm/utils/locks.js
var ReadWriteLock = class {
  constructor() {
    this.semaphore = new Semaphore(1);
    this.readers = 0;
    this.readersMutex = new Semaphore(1);
  }
  async withReadLock(callback) {
    await this._acquireReadLock();
    try {
      return await callback();
    } finally {
      await this._releaseReadLock();
    }
  }
  async withWriteLock(callback) {
    await this._acquireWriteLock();
    try {
      return await callback();
    } finally {
      await this._releaseWriteLock();
    }
  }
  async _acquireReadLock() {
    await this.readersMutex.acquire();
    try {
      this.readers += 1;
      if (this.readers === 1) {
        await this.semaphore.acquire();
      }
    } finally {
      this.readersMutex.release();
    }
  }
  async _releaseReadLock() {
    await this.readersMutex.acquire();
    try {
      this.readers -= 1;
      if (this.readers === 0) {
        this.semaphore.release();
      }
    } finally {
      this.readersMutex.release();
    }
  }
  async _acquireWriteLock() {
    await this.semaphore.acquire();
  }
  async _releaseWriteLock() {
    this.semaphore.release();
  }
};

// package/@stackframe/react/node_modules/@stackframe/stack-shared/dist/esm/utils/stores.js
var Store = class {
  constructor(_value) {
    this._value = _value;
    this._callbacks = /* @__PURE__ */ new Map();
  }
  get() {
    return this._value;
  }
  set(value) {
    const oldValue = this._value;
    this._value = value;
    this._callbacks.forEach((callback) => callback(value, oldValue));
  }
  update(updater) {
    const value = updater(this._value);
    this.set(value);
    return value;
  }
  onChange(callback) {
    const uuid = generateUuid();
    this._callbacks.set(uuid, callback);
    return {
      unsubscribe: () => {
        this._callbacks.delete(uuid);
      }
    };
  }
  onceChange(callback) {
    const { unsubscribe } = this.onChange((...args) => {
      unsubscribe();
      callback(...args);
    });
    return { unsubscribe };
  }
};
var storeLock = new ReadWriteLock();
var AsyncStore = class _AsyncStore {
  constructor(...args) {
    this._mostRecentOkValue = void 0;
    this._isRejected = false;
    this._waitingRejectFunctions = /* @__PURE__ */ new Map();
    this._callbacks = /* @__PURE__ */ new Map();
    this._updateCounter = 0;
    this._lastSuccessfulUpdate = -1;
    if (args.length === 0) {
      this._isAvailable = false;
    } else {
      this._isAvailable = true;
      this._mostRecentOkValue = args[0];
    }
  }
  isAvailable() {
    return this._isAvailable;
  }
  isRejected() {
    return this._isRejected;
  }
  get() {
    if (this.isRejected()) {
      return AsyncResult.error(this._rejectionError);
    } else if (this.isAvailable()) {
      return AsyncResult.ok(this._mostRecentOkValue);
    } else {
      return AsyncResult.pending();
    }
  }
  getOrWait() {
    const uuid = generateUuid();
    if (this.isRejected()) {
      return rejected(this._rejectionError);
    } else if (this.isAvailable()) {
      return resolved(this._mostRecentOkValue);
    }
    const promise = new Promise((resolve, reject) => {
      this.onceChange((value) => {
        resolve(value);
      });
      this._waitingRejectFunctions.set(uuid, reject);
    });
    const withFinally = promise.finally(() => {
      this._waitingRejectFunctions.delete(uuid);
    });
    return pending(withFinally);
  }
  _setIfLatest(result, curCounter) {
    const oldState = this.get();
    const oldValue = this._mostRecentOkValue;
    if (curCounter > this._lastSuccessfulUpdate) {
      switch (result.status) {
        case "ok": {
          if (!this._isAvailable || this._isRejected || this._mostRecentOkValue !== result.data) {
            this._lastSuccessfulUpdate = curCounter;
            this._isAvailable = true;
            this._isRejected = false;
            this._mostRecentOkValue = result.data;
            this._rejectionError = void 0;
            this._callbacks.forEach((callback) => callback({
              state: this.get(),
              oldState,
              lastOkValue: oldValue
            }));
            return true;
          }
          return false;
        }
        case "error": {
          this._lastSuccessfulUpdate = curCounter;
          this._isAvailable = false;
          this._isRejected = true;
          this._rejectionError = result.error;
          this._waitingRejectFunctions.forEach((reject) => reject(result.error));
          this._callbacks.forEach((callback) => callback({
            state: this.get(),
            oldState,
            lastOkValue: oldValue
          }));
          return true;
        }
      }
    }
    return false;
  }
  set(value) {
    this._setIfLatest(Result.ok(value), ++this._updateCounter);
  }
  update(updater) {
    const value = updater(this._mostRecentOkValue);
    this.set(value);
    return value;
  }
  async setAsync(promise) {
    return await storeLock.withReadLock(async () => {
      const curCounter = ++this._updateCounter;
      const result = await Result.fromPromise(promise);
      return this._setIfLatest(result, curCounter);
    });
  }
  setUnavailable() {
    this._lastSuccessfulUpdate = ++this._updateCounter;
    this._isAvailable = false;
    this._isRejected = false;
    this._rejectionError = void 0;
  }
  setRejected(error) {
    this._setIfLatest(Result.error(error), ++this._updateCounter);
  }
  map(mapper) {
    const store = new _AsyncStore();
    this.onChange((value) => {
      store.set(mapper(value));
    });
    return store;
  }
  onChange(callback) {
    return this.onStateChange(({ state, lastOkValue }) => {
      if (state.status === "ok") {
        callback(state.data, lastOkValue);
      }
    });
  }
  onStateChange(callback) {
    const uuid = generateUuid();
    this._callbacks.set(uuid, callback);
    return {
      unsubscribe: () => {
        this._callbacks.delete(uuid);
      }
    };
  }
  onceChange(callback) {
    const { unsubscribe } = this.onChange((...args) => {
      unsubscribe();
      callback(...args);
    });
    return { unsubscribe };
  }
  onceStateChange(callback) {
    const { unsubscribe } = this.onStateChange((...args) => {
      unsubscribe();
      callback(...args);
    });
    return { unsubscribe };
  }
};

// package/@stackframe/react/node_modules/@stackframe/stack-shared/dist/esm/sessions.js
var AccessToken = class {
  constructor(token) {
    this.token = token;
    if (token === "undefined") {
      throw new StackAssertionError("Access token is the string 'undefined'; it's unlikely this is the correct value. They're supposed to be unguessable!");
    }
  }
  get decoded() {
    return decodeJwt(this.token);
  }
  get expiresAt() {
    const { exp } = this.decoded;
    if (exp === void 0) return /* @__PURE__ */ new Date(864e13);
    return new Date(exp * 1e3);
  }
  /**
   * @returns The number of milliseconds until the access token expires, or 0 if it has already expired.
   */
  get expiresInMillis() {
    return Math.max(0, this.expiresAt.getTime() - Date.now());
  }
  isExpired() {
    return this.expiresInMillis <= 0;
  }
};
var RefreshToken = class {
  constructor(token) {
    this.token = token;
    if (token === "undefined") {
      throw new StackAssertionError("Refresh token is the string 'undefined'; it's unlikely this is the correct value. They're supposed to be unguessable!");
    }
  }
};
var InternalSession = class _InternalSession {
  constructor(_options) {
    this._options = _options;
    this._knownToBeInvalid = new Store(false);
    this._refreshPromise = null;
    this._accessToken = new Store(_options.accessToken ? new AccessToken(_options.accessToken) : null);
    this._refreshToken = _options.refreshToken ? new RefreshToken(_options.refreshToken) : null;
    if (_options.accessToken === null && _options.refreshToken === null) {
      this._knownToBeInvalid.set(true);
    }
    this.sessionKey = _InternalSession.calculateSessionKey({ accessToken: _options.accessToken ?? null, refreshToken: _options.refreshToken });
  }
  static calculateSessionKey(ofTokens) {
    if (ofTokens.refreshToken) {
      return `refresh-${ofTokens.refreshToken}`;
    } else if (ofTokens.accessToken) {
      return `access-${ofTokens.accessToken}`;
    } else {
      return "not-logged-in";
    }
  }
  isKnownToBeInvalid() {
    return this._knownToBeInvalid.get();
  }
  /**
   * Marks the session object as invalid, meaning that the refresh and access tokens can no longer be used.
   */
  markInvalid() {
    this._accessToken.set(null);
    this._knownToBeInvalid.set(true);
  }
  onInvalidate(callback) {
    return this._knownToBeInvalid.onChange(() => callback());
  }
  /**
   * Returns the access token if it is found in the cache, fetching it otherwise.
   *
   * This is usually the function you want to call to get an access token. Either set `minMillisUntilExpiration` to a reasonable value, or catch errors that occur if it expires, and call `markAccessTokenExpired` to mark the token as expired if so (after which a call to this function will always refetch the token).
   *
   * @returns null if the session is known to be invalid, cached tokens if they exist in the cache (which may or may not be valid still), or new tokens otherwise.
   */
  async getOrFetchLikelyValidTokens(minMillisUntilExpiration) {
    if (minMillisUntilExpiration >= 6e4) {
      throw new Error(`Required access token expiry ${minMillisUntilExpiration}ms is too long; access tokens are too short to be used for more than 60s`);
    }
    const accessToken = this._getPotentiallyInvalidAccessTokenIfAvailable();
    if (!accessToken || accessToken.expiresInMillis < minMillisUntilExpiration) {
      const newTokens = await this.fetchNewTokens();
      const expiresInMillis = newTokens == null ? void 0 : newTokens.accessToken.expiresInMillis;
      if (expiresInMillis && expiresInMillis < minMillisUntilExpiration) {
        throw new StackAssertionError(`Required access token expiry ${minMillisUntilExpiration}ms is too long; access tokens are too short when they're generated (${expiresInMillis}ms)`);
      }
      return newTokens;
    }
    return { accessToken, refreshToken: this._refreshToken };
  }
  /**
   * Fetches new tokens that are, at the time of fetching, guaranteed to be valid.
   *
   * The newly generated tokens are short-lived, so it's good practice not to rely on their validity (if possible). However, this function is useful in some cases where you only want to pass access tokens to a service, and you want to make sure said access token has the longest possible lifetime.
   *
   * In most cases, you should prefer `getOrFetchLikelyValidTokens`.
   *
   * @returns null if the session is known to be invalid, or new tokens otherwise (which, at the time of fetching, are guaranteed to be valid).
   */
  async fetchNewTokens() {
    const accessToken = await this._getNewlyFetchedAccessToken();
    return accessToken ? { accessToken, refreshToken: this._refreshToken } : null;
  }
  markAccessTokenExpired(accessToken) {
    if (this._accessToken.get() === accessToken) {
      this._accessToken.set(null);
    }
  }
  /**
   * Note that a callback invocation with `null` does not mean the session has been invalidated; the access token may just have expired. Use `onInvalidate` to detect invalidation.
   */
  onAccessTokenChange(callback) {
    return this._accessToken.onChange(callback);
  }
  /**
   * @returns An access token, which may be expired or expire soon, or null if it is known to be invalid.
   */
  _getPotentiallyInvalidAccessTokenIfAvailable() {
    if (!this._refreshToken) return null;
    if (this.isKnownToBeInvalid()) return null;
    const accessToken = this._accessToken.get();
    if (accessToken && !accessToken.isExpired()) return accessToken;
    return null;
  }
  /**
   * You should prefer `_getOrFetchPotentiallyInvalidAccessToken` in almost all cases.
   *
   * @returns A newly fetched access token (never read from cache), or null if the session either does not represent a user or the session is invalid.
   */
  async _getNewlyFetchedAccessToken() {
    if (!this._refreshToken) return null;
    if (this._knownToBeInvalid.get()) return null;
    if (!this._refreshPromise) {
      this._refreshAndSetRefreshPromise(this._refreshToken);
    }
    return await this._refreshPromise;
  }
  _refreshAndSetRefreshPromise(refreshToken) {
    let refreshPromise = this._options.refreshAccessTokenCallback(refreshToken).then((accessToken) => {
      if (refreshPromise === this._refreshPromise) {
        this._refreshPromise = null;
        this._accessToken.set(accessToken);
        if (!accessToken) {
          this.markInvalid();
        }
      }
      return accessToken;
    });
    this._refreshPromise = refreshPromise;
  }
};

// package/@stackframe/react/node_modules/@stackframe/stack-shared/dist/esm/utils/http.js
var HTTP_METHODS = {
  "GET": {
    safe: true,
    idempotent: true
  },
  "POST": {
    safe: false,
    idempotent: false
  },
  "PUT": {
    safe: false,
    idempotent: true
  },
  "DELETE": {
    safe: false,
    idempotent: true
  },
  "PATCH": {
    safe: false,
    idempotent: false
  },
  "OPTIONS": {
    safe: true,
    idempotent: true
  },
  "HEAD": {
    safe: true,
    idempotent: true
  },
  "TRACE": {
    safe: true,
    idempotent: true
  },
  "CONNECT": {
    safe: false,
    idempotent: false
  }
};
function decodeBasicAuthorizationHeader(value) {
  const [type, encoded, ...rest] = value.split(" ");
  if (rest.length > 0) return null;
  if (!encoded) return null;
  if (type !== "Basic") return null;
  if (!isBase64(encoded)) return null;
  const decoded = new TextDecoder().decode(decodeBase64(encoded));
  const split = decoded.split(":");
  return [split[0], split.slice(1).join(":")];
}

// package/@stackframe/react/node_modules/@stackframe/stack-shared/dist/esm/interface/clientInterface.js
var USER_AGENT2;
var _a2, _b2;
if (typeof navigator === "undefined" || !((_b2 = (_a2 = navigator.userAgent) == null ? void 0 : _a2.startsWith) == null ? void 0 : _b2.call(_a2, "Mozilla/5.0 "))) {
  const NAME = "oauth4webapi";
  const VERSION = "v2.10.4";
  USER_AGENT2 = `${NAME}/${VERSION}`;
}
function looseInstanceOf(input, expected) {
  if (input == null) {
    return false;
  }
  try {
    return input instanceof expected || Object.getPrototypeOf(input)[Symbol.toStringTag] === expected.prototype[Symbol.toStringTag];
  } catch {
    return false;
  }
}
var clockSkew = Symbol();
var clockTolerance = Symbol();
var customFetch = Symbol();
var useMtlsAlias = Symbol();
var encoder2 = new TextEncoder();
var decoder2 = new TextDecoder();
function buf(input) {
  if (typeof input === "string") {
    return encoder2.encode(input);
  }
  return decoder2.decode(input);
}
var CHUNK_SIZE = 32768;
function encodeBase64Url(input) {
  if (input instanceof ArrayBuffer) {
    input = new Uint8Array(input);
  }
  const arr = [];
  for (let i = 0; i < input.byteLength; i += CHUNK_SIZE) {
    arr.push(String.fromCharCode.apply(null, input.subarray(i, i + CHUNK_SIZE)));
  }
  return btoa(arr.join("")).replace(/=/g, "").replace(/\+/g, "-").replace(/\//g, "_");
}
function decodeBase64Url(input) {
  try {
    const binary = atob(input.replace(/-/g, "+").replace(/_/g, "/").replace(/\s/g, ""));
    const bytes = new Uint8Array(binary.length);
    for (let i = 0; i < binary.length; i++) {
      bytes[i] = binary.charCodeAt(i);
    }
    return bytes;
  } catch (cause) {
    throw new OPE("The input to be decoded is not correctly encoded.", { cause });
  }
}
function b64u(input) {
  if (typeof input === "string") {
    return decodeBase64Url(input);
  }
  return encodeBase64Url(input);
}
var LRU = class {
  constructor(maxSize) {
    this.cache = /* @__PURE__ */ new Map();
    this._cache = /* @__PURE__ */ new Map();
    this.maxSize = maxSize;
  }
  get(key) {
    let v = this.cache.get(key);
    if (v) {
      return v;
    }
    if (v = this._cache.get(key)) {
      this.update(key, v);
      return v;
    }
    return void 0;
  }
  has(key) {
    return this.cache.has(key) || this._cache.has(key);
  }
  set(key, value) {
    if (this.cache.has(key)) {
      this.cache.set(key, value);
    } else {
      this.update(key, value);
    }
    return this;
  }
  delete(key) {
    if (this.cache.has(key)) {
      return this.cache.delete(key);
    }
    if (this._cache.has(key)) {
      return this._cache.delete(key);
    }
    return false;
  }
  update(key, value) {
    this.cache.set(key, value);
    if (this.cache.size >= this.maxSize) {
      this._cache = this.cache;
      this.cache = /* @__PURE__ */ new Map();
    }
  }
};
var UnsupportedOperationError = class extends Error {
  constructor(message2) {
    var _a4;
    super(message2 ?? "operation not supported");
    this.name = this.constructor.name;
    (_a4 = Error.captureStackTrace) == null ? void 0 : _a4.call(Error, this, this.constructor);
  }
};
var OperationProcessingError = class extends Error {
  constructor(message2, options) {
    var _a4;
    super(message2, options);
    this.name = this.constructor.name;
    (_a4 = Error.captureStackTrace) == null ? void 0 : _a4.call(Error, this, this.constructor);
  }
};
var OPE = OperationProcessingError;
var dpopNonces = new LRU(100);
function isCryptoKey2(key) {
  return key instanceof CryptoKey;
}
function isPrivateKey(key) {
  return isCryptoKey2(key) && key.type === "private";
}
function isPublicKey(key) {
  return isCryptoKey2(key) && key.type === "public";
}
function processDpopNonce(response) {
  try {
    const nonce = response.headers.get("dpop-nonce");
    if (nonce) {
      dpopNonces.set(new URL(response.url).origin, nonce);
    }
  } catch {
  }
  return response;
}
function isJsonObject(input) {
  if (input === null || typeof input !== "object" || Array.isArray(input)) {
    return false;
  }
  return true;
}
function prepareHeaders(input) {
  if (looseInstanceOf(input, Headers)) {
    input = Object.fromEntries(input.entries());
  }
  const headers = new Headers(input);
  if (USER_AGENT2 && !headers.has("user-agent")) {
    headers.set("user-agent", USER_AGENT2);
  }
  if (headers.has("authorization")) {
    throw new TypeError('"options.headers" must not include the "authorization" header name');
  }
  if (headers.has("dpop")) {
    throw new TypeError('"options.headers" must not include the "dpop" header name');
  }
  return headers;
}
function signal(value) {
  if (typeof value === "function") {
    value = value();
  }
  if (!(value instanceof AbortSignal)) {
    throw new TypeError('"options.signal" must return or be an instance of AbortSignal');
  }
  return value;
}
function validateString(input) {
  return typeof input === "string" && input.length !== 0;
}
function randomBytes() {
  return b64u(crypto.getRandomValues(new Uint8Array(32)));
}
function getKeyAndKid(input) {
  if (input instanceof CryptoKey) {
    return { key: input };
  }
  if (!((input == null ? void 0 : input.key) instanceof CryptoKey)) {
    return {};
  }
  if (input.kid !== void 0 && !validateString(input.kid)) {
    throw new TypeError('"kid" must be a non-empty string');
  }
  return { key: input.key, kid: input.kid };
}
function formUrlEncode(token) {
  return encodeURIComponent(token).replace(/%20/g, "+");
}
function clientSecretBasic(clientId, clientSecret) {
  const username = formUrlEncode(clientId);
  const password = formUrlEncode(clientSecret);
  const credentials = btoa(`${username}:${password}`);
  return `Basic ${credentials}`;
}
function psAlg(key) {
  switch (key.algorithm.hash.name) {
    case "SHA-256":
      return "PS256";
    case "SHA-384":
      return "PS384";
    case "SHA-512":
      return "PS512";
    default:
      throw new UnsupportedOperationError("unsupported RsaHashedKeyAlgorithm hash name");
  }
}
function rsAlg(key) {
  switch (key.algorithm.hash.name) {
    case "SHA-256":
      return "RS256";
    case "SHA-384":
      return "RS384";
    case "SHA-512":
      return "RS512";
    default:
      throw new UnsupportedOperationError("unsupported RsaHashedKeyAlgorithm hash name");
  }
}
function esAlg(key) {
  switch (key.algorithm.namedCurve) {
    case "P-256":
      return "ES256";
    case "P-384":
      return "ES384";
    case "P-521":
      return "ES512";
    default:
      throw new UnsupportedOperationError("unsupported EcKeyAlgorithm namedCurve");
  }
}
function keyToJws(key) {
  switch (key.algorithm.name) {
    case "RSA-PSS":
      return psAlg(key);
    case "RSASSA-PKCS1-v1_5":
      return rsAlg(key);
    case "ECDSA":
      return esAlg(key);
    case "Ed25519":
    case "Ed448":
      return "EdDSA";
    default:
      throw new UnsupportedOperationError("unsupported CryptoKey algorithm name");
  }
}
function getClockSkew(client) {
  const skew = client == null ? void 0 : client[clockSkew];
  return typeof skew === "number" && Number.isFinite(skew) ? skew : 0;
}
function getClockTolerance(client) {
  const tolerance = client == null ? void 0 : client[clockTolerance];
  return typeof tolerance === "number" && Number.isFinite(tolerance) && Math.sign(tolerance) !== -1 ? tolerance : 30;
}
function epochTime() {
  return Math.floor(Date.now() / 1e3);
}
function clientAssertion(as, client) {
  const now = epochTime() + getClockSkew(client);
  return {
    jti: randomBytes(),
    aud: [as.issuer, as.token_endpoint],
    exp: now + 60,
    iat: now,
    nbf: now,
    iss: client.client_id,
    sub: client.client_id
  };
}
async function privateKeyJwt(as, client, key, kid) {
  return jwt({
    alg: keyToJws(key),
    kid
  }, clientAssertion(as, client), key);
}
function assertAs(as) {
  if (typeof as !== "object" || as === null) {
    throw new TypeError('"as" must be an object');
  }
  if (!validateString(as.issuer)) {
    throw new TypeError('"as.issuer" property must be a non-empty string');
  }
  return true;
}
function assertClient(client) {
  if (typeof client !== "object" || client === null) {
    throw new TypeError('"client" must be an object');
  }
  if (!validateString(client.client_id)) {
    throw new TypeError('"client.client_id" property must be a non-empty string');
  }
  return true;
}
function assertClientSecret(clientSecret) {
  if (!validateString(clientSecret)) {
    throw new TypeError('"client.client_secret" property must be a non-empty string');
  }
  return clientSecret;
}
function assertNoClientPrivateKey(clientAuthMethod, clientPrivateKey) {
  if (clientPrivateKey !== void 0) {
    throw new TypeError(`"options.clientPrivateKey" property must not be provided when ${clientAuthMethod} client authentication method is used.`);
  }
}
function assertNoClientSecret(clientAuthMethod, clientSecret) {
  if (clientSecret !== void 0) {
    throw new TypeError(`"client.client_secret" property must not be provided when ${clientAuthMethod} client authentication method is used.`);
  }
}
async function clientAuthentication(as, client, body, headers, clientPrivateKey) {
  body.delete("client_secret");
  body.delete("client_assertion_type");
  body.delete("client_assertion");
  switch (client.token_endpoint_auth_method) {
    case void 0:
    case "client_secret_basic": {
      assertNoClientPrivateKey("client_secret_basic", clientPrivateKey);
      headers.set("authorization", clientSecretBasic(client.client_id, assertClientSecret(client.client_secret)));
      break;
    }
    case "client_secret_post": {
      assertNoClientPrivateKey("client_secret_post", clientPrivateKey);
      body.set("client_id", client.client_id);
      body.set("client_secret", assertClientSecret(client.client_secret));
      break;
    }
    case "private_key_jwt": {
      assertNoClientSecret("private_key_jwt", client.client_secret);
      if (clientPrivateKey === void 0) {
        throw new TypeError('"options.clientPrivateKey" must be provided when "client.token_endpoint_auth_method" is "private_key_jwt"');
      }
      const { key, kid } = getKeyAndKid(clientPrivateKey);
      if (!isPrivateKey(key)) {
        throw new TypeError('"options.clientPrivateKey.key" must be a private CryptoKey');
      }
      body.set("client_id", client.client_id);
      body.set("client_assertion_type", "urn:ietf:params:oauth:client-assertion-type:jwt-bearer");
      body.set("client_assertion", await privateKeyJwt(as, client, key, kid));
      break;
    }
    case "tls_client_auth":
    case "self_signed_tls_client_auth":
    case "none": {
      assertNoClientSecret(client.token_endpoint_auth_method, client.client_secret);
      assertNoClientPrivateKey(client.token_endpoint_auth_method, clientPrivateKey);
      body.set("client_id", client.client_id);
      break;
    }
    default:
      throw new UnsupportedOperationError("unsupported client token_endpoint_auth_method");
  }
}
async function jwt(header, claimsSet, key) {
  if (!key.usages.includes("sign")) {
    throw new TypeError('CryptoKey instances used for signing assertions must include "sign" in their "usages"');
  }
  const input = `${b64u(buf(JSON.stringify(header)))}.${b64u(buf(JSON.stringify(claimsSet)))}`;
  const signature = b64u(await crypto.subtle.sign(keyToSubtle(key), key, buf(input)));
  return `${input}.${signature}`;
}
async function dpopProofJwt(headers, options, url, htm, clockSkew2, accessToken) {
  const { privateKey, publicKey, nonce = dpopNonces.get(url.origin) } = options;
  if (!isPrivateKey(privateKey)) {
    throw new TypeError('"DPoP.privateKey" must be a private CryptoKey');
  }
  if (!isPublicKey(publicKey)) {
    throw new TypeError('"DPoP.publicKey" must be a public CryptoKey');
  }
  if (nonce !== void 0 && !validateString(nonce)) {
    throw new TypeError('"DPoP.nonce" must be a non-empty string or undefined');
  }
  if (!publicKey.extractable) {
    throw new TypeError('"DPoP.publicKey.extractable" must be true');
  }
  const now = epochTime() + clockSkew2;
  const proof = await jwt({
    alg: keyToJws(privateKey),
    typ: "dpop+jwt",
    jwk: await publicJwk(publicKey)
  }, {
    iat: now,
    jti: randomBytes(),
    htm,
    nonce,
    htu: `${url.origin}${url.pathname}`,
    ath: accessToken ? b64u(await crypto.subtle.digest("SHA-256", buf(accessToken))) : void 0
  }, privateKey);
  headers.set("dpop", proof);
}
var jwkCache;
async function getSetPublicJwkCache(key) {
  const { kty, e, n, x, y, crv } = await crypto.subtle.exportKey("jwk", key);
  const jwk = { kty, e, n, x, y, crv };
  jwkCache.set(key, jwk);
  return jwk;
}
async function publicJwk(key) {
  jwkCache || (jwkCache = /* @__PURE__ */ new WeakMap());
  return jwkCache.get(key) || getSetPublicJwkCache(key);
}
function validateEndpoint(value, endpoint, options) {
  if (typeof value !== "string") {
    if (options == null ? void 0 : options[useMtlsAlias]) {
      throw new TypeError(`"as.mtls_endpoint_aliases.${endpoint}" must be a string`);
    }
    throw new TypeError(`"as.${endpoint}" must be a string`);
  }
  return new URL(value);
}
function resolveEndpoint(as, endpoint, options) {
  if ((options == null ? void 0 : options[useMtlsAlias]) && as.mtls_endpoint_aliases && endpoint in as.mtls_endpoint_aliases) {
    return validateEndpoint(as.mtls_endpoint_aliases[endpoint], endpoint, options);
  }
  return validateEndpoint(as[endpoint], endpoint);
}
function isOAuth2Error(input) {
  const value = input;
  if (typeof value !== "object" || Array.isArray(value) || value === null) {
    return false;
  }
  return value.error !== void 0;
}
var skipSubjectCheck = Symbol();
async function authenticatedRequest(as, client, method, url, body, headers, options) {
  await clientAuthentication(as, client, body, headers, options == null ? void 0 : options.clientPrivateKey);
  headers.set("content-type", "application/x-www-form-urlencoded;charset=UTF-8");
  return ((options == null ? void 0 : options[customFetch]) || fetch)(url.href, {
    body,
    headers: Object.fromEntries(headers.entries()),
    method,
    redirect: "manual",
    signal: (options == null ? void 0 : options.signal) ? signal(options.signal) : null
  }).then(processDpopNonce);
}
async function tokenEndpointRequest(as, client, grantType, parameters, options) {
  const url = resolveEndpoint(as, "token_endpoint", options);
  parameters.set("grant_type", grantType);
  const headers = prepareHeaders(options == null ? void 0 : options.headers);
  headers.set("accept", "application/json");
  if ((options == null ? void 0 : options.DPoP) !== void 0) {
    await dpopProofJwt(headers, options.DPoP, url, "POST", getClockSkew(client));
  }
  return authenticatedRequest(as, client, "POST", url, parameters, headers, options);
}
async function refreshTokenGrantRequest(as, client, refreshToken, options) {
  assertAs(as);
  assertClient(client);
  if (!validateString(refreshToken)) {
    throw new TypeError('"refreshToken" must be a non-empty string');
  }
  const parameters = new URLSearchParams(options == null ? void 0 : options.additionalParameters);
  parameters.set("refresh_token", refreshToken);
  return tokenEndpointRequest(as, client, "refresh_token", parameters, options);
}
var idTokenClaims = /* @__PURE__ */ new WeakMap();
async function processGenericAccessTokenResponse(as, client, response, ignoreIdToken = false, ignoreRefreshToken = false) {
  assertAs(as);
  assertClient(client);
  if (!looseInstanceOf(response, Response)) {
    throw new TypeError('"response" must be an instance of Response');
  }
  if (response.status !== 200) {
    let err;
    if (err = await handleOAuthBodyError(response)) {
      return err;
    }
    throw new OPE('"response" is not a conform Token Endpoint response');
  }
  assertReadableResponse(response);
  let json;
  try {
    json = await response.json();
  } catch (cause) {
    throw new OPE('failed to parse "response" body as JSON', { cause });
  }
  if (!isJsonObject(json)) {
    throw new OPE('"response" body must be a top level object');
  }
  if (!validateString(json.access_token)) {
    throw new OPE('"response" body "access_token" property must be a non-empty string');
  }
  if (!validateString(json.token_type)) {
    throw new OPE('"response" body "token_type" property must be a non-empty string');
  }
  json.token_type = json.token_type.toLowerCase();
  if (json.token_type !== "dpop" && json.token_type !== "bearer") {
    throw new UnsupportedOperationError("unsupported `token_type` value");
  }
  if (json.expires_in !== void 0 && (typeof json.expires_in !== "number" || json.expires_in <= 0)) {
    throw new OPE('"response" body "expires_in" property must be a positive number');
  }
  if (!ignoreRefreshToken && json.refresh_token !== void 0 && !validateString(json.refresh_token)) {
    throw new OPE('"response" body "refresh_token" property must be a non-empty string');
  }
  if (json.scope !== void 0 && typeof json.scope !== "string") {
    throw new OPE('"response" body "scope" property must be a string');
  }
  if (!ignoreIdToken) {
    if (json.id_token !== void 0 && !validateString(json.id_token)) {
      throw new OPE('"response" body "id_token" property must be a non-empty string');
    }
    if (json.id_token) {
      const { claims } = await validateJwt(json.id_token, checkSigningAlgorithm.bind(void 0, client.id_token_signed_response_alg, as.id_token_signing_alg_values_supported), noSignatureCheck, getClockSkew(client), getClockTolerance(client)).then(validatePresence.bind(void 0, ["aud", "exp", "iat", "iss", "sub"])).then(validateIssuer.bind(void 0, as.issuer)).then(validateAudience.bind(void 0, client.client_id));
      if (Array.isArray(claims.aud) && claims.aud.length !== 1 && claims.azp !== client.client_id) {
        throw new OPE('unexpected ID Token "azp" (authorized party) claim value');
      }
      if (client.require_auth_time && typeof claims.auth_time !== "number") {
        throw new OPE('unexpected ID Token "auth_time" (authentication time) claim value');
      }
      idTokenClaims.set(json, claims);
    }
  }
  return json;
}
async function processRefreshTokenResponse(as, client, response) {
  return processGenericAccessTokenResponse(as, client, response);
}
function validateAudience(expected, result) {
  if (Array.isArray(result.claims.aud)) {
    if (!result.claims.aud.includes(expected)) {
      throw new OPE('unexpected JWT "aud" (audience) claim value');
    }
  } else if (result.claims.aud !== expected) {
    throw new OPE('unexpected JWT "aud" (audience) claim value');
  }
  return result;
}
function validateIssuer(expected, result) {
  if (result.claims.iss !== expected) {
    throw new OPE('unexpected JWT "iss" (issuer) claim value');
  }
  return result;
}
var branded = /* @__PURE__ */ new WeakSet();
function brand(searchParams) {
  branded.add(searchParams);
  return searchParams;
}
async function authorizationCodeGrantRequest(as, client, callbackParameters, redirectUri, codeVerifier, options) {
  assertAs(as);
  assertClient(client);
  if (!branded.has(callbackParameters)) {
    throw new TypeError('"callbackParameters" must be an instance of URLSearchParams obtained from "validateAuthResponse()", or "validateJwtAuthResponse()');
  }
  if (!validateString(redirectUri)) {
    throw new TypeError('"redirectUri" must be a non-empty string');
  }
  if (!validateString(codeVerifier)) {
    throw new TypeError('"codeVerifier" must be a non-empty string');
  }
  const code = getURLSearchParameter(callbackParameters, "code");
  if (!code) {
    throw new OPE('no authorization code in "callbackParameters"');
  }
  const parameters = new URLSearchParams(options == null ? void 0 : options.additionalParameters);
  parameters.set("redirect_uri", redirectUri);
  parameters.set("code_verifier", codeVerifier);
  parameters.set("code", code);
  return tokenEndpointRequest(as, client, "authorization_code", parameters, options);
}
var jwtClaimNames = {
  aud: "audience",
  c_hash: "code hash",
  client_id: "client id",
  exp: "expiration time",
  iat: "issued at",
  iss: "issuer",
  jti: "jwt id",
  nonce: "nonce",
  s_hash: "state hash",
  sub: "subject",
  ath: "access token hash",
  htm: "http method",
  htu: "http uri",
  cnf: "confirmation"
};
function validatePresence(required, result) {
  for (const claim of required) {
    if (result.claims[claim] === void 0) {
      throw new OPE(`JWT "${claim}" (${jwtClaimNames[claim]}) claim missing`);
    }
  }
  return result;
}
var expectNoNonce = Symbol();
var skipAuthTimeCheck = Symbol();
async function processAuthorizationCodeOAuth2Response(as, client, response) {
  const result = await processGenericAccessTokenResponse(as, client, response, true);
  if (isOAuth2Error(result)) {
    return result;
  }
  if (result.id_token !== void 0) {
    if (typeof result.id_token === "string" && result.id_token.length) {
      throw new OPE("Unexpected ID Token returned, use processAuthorizationCodeOpenIDResponse() for OpenID Connect callback processing");
    }
    delete result.id_token;
  }
  return result;
}
function assertReadableResponse(response) {
  if (response.bodyUsed) {
    throw new TypeError('"response" body has been used already');
  }
}
async function handleOAuthBodyError(response) {
  if (response.status > 399 && response.status < 500) {
    assertReadableResponse(response);
    try {
      const json = await response.json();
      if (isJsonObject(json) && typeof json.error === "string" && json.error.length) {
        if (json.error_description !== void 0 && typeof json.error_description !== "string") {
          delete json.error_description;
        }
        if (json.error_uri !== void 0 && typeof json.error_uri !== "string") {
          delete json.error_uri;
        }
        if (json.algs !== void 0 && typeof json.algs !== "string") {
          delete json.algs;
        }
        if (json.scope !== void 0 && typeof json.scope !== "string") {
          delete json.scope;
        }
        return json;
      }
    } catch {
    }
  }
  return void 0;
}
function checkRsaKeyAlgorithm(algorithm) {
  if (typeof algorithm.modulusLength !== "number" || algorithm.modulusLength < 2048) {
    throw new OPE(`${algorithm.name} modulusLength must be at least 2048 bits`);
  }
}
function ecdsaHashName(namedCurve) {
  switch (namedCurve) {
    case "P-256":
      return "SHA-256";
    case "P-384":
      return "SHA-384";
    case "P-521":
      return "SHA-512";
    default:
      throw new UnsupportedOperationError();
  }
}
function keyToSubtle(key) {
  switch (key.algorithm.name) {
    case "ECDSA":
      return {
        name: key.algorithm.name,
        hash: ecdsaHashName(key.algorithm.namedCurve)
      };
    case "RSA-PSS": {
      checkRsaKeyAlgorithm(key.algorithm);
      switch (key.algorithm.hash.name) {
        case "SHA-256":
        case "SHA-384":
        case "SHA-512":
          return {
            name: key.algorithm.name,
            saltLength: parseInt(key.algorithm.hash.name.slice(-3), 10) >> 3
          };
        default:
          throw new UnsupportedOperationError();
      }
    }
    case "RSASSA-PKCS1-v1_5":
      checkRsaKeyAlgorithm(key.algorithm);
      return key.algorithm.name;
    case "Ed448":
    case "Ed25519":
      return key.algorithm.name;
  }
  throw new UnsupportedOperationError();
}
var noSignatureCheck = Symbol();
async function validateJwt(jws, checkAlg, getKey, clockSkew2, clockTolerance2) {
  const { 0: protectedHeader, 1: payload, 2: encodedSignature, length } = jws.split(".");
  if (length === 5) {
    throw new UnsupportedOperationError("JWE structure JWTs are not supported");
  }
  if (length !== 3) {
    throw new OPE("Invalid JWT");
  }
  let header;
  try {
    header = JSON.parse(buf(b64u(protectedHeader)));
  } catch (cause) {
    throw new OPE("failed to parse JWT Header body as base64url encoded JSON", { cause });
  }
  if (!isJsonObject(header)) {
    throw new OPE("JWT Header must be a top level object");
  }
  checkAlg(header);
  if (header.crit !== void 0) {
    throw new OPE('unexpected JWT "crit" header parameter');
  }
  const signature = b64u(encodedSignature);
  let key;
  if (getKey !== noSignatureCheck) {
    key = await getKey(header);
    const input = `${protectedHeader}.${payload}`;
    const verified = await crypto.subtle.verify(keyToSubtle(key), key, signature, buf(input));
    if (!verified) {
      throw new OPE("JWT signature verification failed");
    }
  }
  let claims;
  try {
    claims = JSON.parse(buf(b64u(payload)));
  } catch (cause) {
    throw new OPE("failed to parse JWT Payload body as base64url encoded JSON", { cause });
  }
  if (!isJsonObject(claims)) {
    throw new OPE("JWT Payload must be a top level object");
  }
  const now = epochTime() + clockSkew2;
  if (claims.exp !== void 0) {
    if (typeof claims.exp !== "number") {
      throw new OPE('unexpected JWT "exp" (expiration time) claim type');
    }
    if (claims.exp <= now - clockTolerance2) {
      throw new OPE('unexpected JWT "exp" (expiration time) claim value, timestamp is <= now()');
    }
  }
  if (claims.iat !== void 0) {
    if (typeof claims.iat !== "number") {
      throw new OPE('unexpected JWT "iat" (issued at) claim type');
    }
  }
  if (claims.iss !== void 0) {
    if (typeof claims.iss !== "string") {
      throw new OPE('unexpected JWT "iss" (issuer) claim type');
    }
  }
  if (claims.nbf !== void 0) {
    if (typeof claims.nbf !== "number") {
      throw new OPE('unexpected JWT "nbf" (not before) claim type');
    }
    if (claims.nbf > now + clockTolerance2) {
      throw new OPE('unexpected JWT "nbf" (not before) claim value, timestamp is > now()');
    }
  }
  if (claims.aud !== void 0) {
    if (typeof claims.aud !== "string" && !Array.isArray(claims.aud)) {
      throw new OPE('unexpected JWT "aud" (audience) claim type');
    }
  }
  return { header, claims, signature, key };
}
function checkSigningAlgorithm(client, issuer, header) {
  if (client !== void 0) {
    if (header.alg !== client) {
      throw new OPE('unexpected JWT "alg" header parameter');
    }
    return;
  }
  if (Array.isArray(issuer)) {
    if (!issuer.includes(header.alg)) {
      throw new OPE('unexpected JWT "alg" header parameter');
    }
    return;
  }
  if (header.alg !== "RS256") {
    throw new OPE('unexpected JWT "alg" header parameter');
  }
}
function getURLSearchParameter(parameters, name) {
  const { 0: value, length } = parameters.getAll(name);
  if (length > 1) {
    throw new OPE(`"${name}" parameter must be provided only once`);
  }
  return value;
}
var skipStateCheck = Symbol();
var expectNoState = Symbol();
function validateAuthResponse(as, client, parameters, expectedState) {
  assertAs(as);
  assertClient(client);
  if (parameters instanceof URL) {
    parameters = parameters.searchParams;
  }
  if (!(parameters instanceof URLSearchParams)) {
    throw new TypeError('"parameters" must be an instance of URLSearchParams, or URL');
  }
  if (getURLSearchParameter(parameters, "response")) {
    throw new OPE('"parameters" contains a JARM response, use validateJwtAuthResponse() instead of validateAuthResponse()');
  }
  const iss = getURLSearchParameter(parameters, "iss");
  const state = getURLSearchParameter(parameters, "state");
  if (!iss && as.authorization_response_iss_parameter_supported) {
    throw new OPE('response parameter "iss" (issuer) missing');
  }
  if (iss && iss !== as.issuer) {
    throw new OPE('unexpected "iss" (issuer) response parameter value');
  }
  switch (expectedState) {
    case void 0:
    case expectNoState:
      if (state !== void 0) {
        throw new OPE('unexpected "state" response parameter encountered');
      }
      break;
    case skipStateCheck:
      break;
    default:
      if (!validateString(expectedState)) {
        throw new OPE('"expectedState" must be a non-empty string');
      }
      if (state === void 0) {
        throw new OPE('response parameter "state" missing');
      }
      if (state !== expectedState) {
        throw new OPE('unexpected "state" response parameter value');
      }
  }
  const error = getURLSearchParameter(parameters, "error");
  if (error) {
    return {
      error,
      error_description: getURLSearchParameter(parameters, "error_description"),
      error_uri: getURLSearchParameter(parameters, "error_uri")
    };
  }
  const id_token = getURLSearchParameter(parameters, "id_token");
  const token = getURLSearchParameter(parameters, "token");
  if (id_token !== void 0 || token !== void 0) {
    throw new UnsupportedOperationError("implicit and hybrid flows are not supported");
  }
  return brand(new URLSearchParams(parameters));
}
var StackClientInterface = class {
  constructor(options) {
    this.options = options;
  }
  get projectId() {
    return this.options.projectId;
  }
  getApiUrl() {
    return this.options.getBaseUrl() + "/api/v1";
  }
  async runNetworkDiagnostics(session, requestType) {
    var _a4;
    const tryRequest = async (cb) => {
      try {
        await cb();
        return "OK";
      } catch (e) {
        return `${e}`;
      }
    };
    const cfTrace = await tryRequest(async () => {
      const res = await fetch("https://*******/cdn-cgi/trace");
      if (!res.ok) {
        throw new Error(`${res.status} ${res.statusText}: ${await res.text()}`);
      }
    });
    const apiRoot = session !== void 0 && requestType !== void 0 ? await tryRequest(async () => {
      const res = await this.sendClientRequestInner("/", {}, session, requestType);
      if (res.status === "error") {
        throw res.error;
      }
    }) : "Not tested";
    const baseUrlBackend = await tryRequest(async () => {
      const res = await fetch(new URL("/health", this.getApiUrl()));
      if (!res.ok) {
        throw new Error(`${res.status} ${res.statusText}: ${await res.text()}`);
      }
    });
    const prodDashboard = await tryRequest(async () => {
      const res = await fetch("https://app.stack-auth.com/health");
      if (!res.ok) {
        throw new Error(`${res.status} ${res.statusText}: ${await res.text()}`);
      }
    });
    const prodBackend = await tryRequest(async () => {
      const res = await fetch("https://api.stack-auth.com/health");
      if (!res.ok) {
        throw new Error(`${res.status} ${res.statusText}: ${await res.text()}`);
      }
    });
    return {
      "navigator?.onLine": (_a4 = globalVar.navigator) == null ? void 0 : _a4.onLine,
      cfTrace,
      apiRoot,
      baseUrlBackend,
      prodDashboard,
      prodBackend
    };
  }
  async _createNetworkError(cause, session, requestType) {
    return new Error(deindent`
      Stack Auth is unable to connect to the server. Please check your internet connection and try again.
      
      If the problem persists, please contact support and provide a screenshot of your entire browser console.

      ${cause}
      
      ${JSON.stringify(await this.runNetworkDiagnostics(session, requestType), null, 2)}
    `, { cause });
  }
  async _networkRetry(cb, session, requestType) {
    const retriedResult = await Result.retry(
      cb,
      5,
      { exponentialDelayBase: 1e3 }
    );
    if (retriedResult.status === "error") {
      if (globalVar.navigator && !globalVar.navigator.onLine) {
        throw new Error("Failed to send Stack network request. It seems like you are offline, please check your internet connection and try again. This is not an error with Stack Auth. (window.navigator.onLine is falsy)", { cause: retriedResult.error });
      }
      throw await this._createNetworkError(retriedResult.error, session, requestType);
    }
    return retriedResult.data;
  }
  async _networkRetryException(cb, session, requestType) {
    return await this._networkRetry(async () => await Result.fromThrowingAsync(cb), session, requestType);
  }
  async fetchNewAccessToken(refreshToken) {
    if (!("publishableClientKey" in this.options)) {
      throw new Error("Admin session token is currently not supported for fetching new access token. Did you try to log in on a StackApp initiated with the admin session?");
    }
    const as = {
      issuer: this.options.getBaseUrl(),
      algorithm: "oauth2",
      token_endpoint: this.getApiUrl() + "/auth/oauth/token"
    };
    const client = {
      client_id: this.projectId,
      client_secret: this.options.publishableClientKey,
      token_endpoint_auth_method: "client_secret_post"
    };
    const rawResponse = await this._networkRetryException(
      async () => await refreshTokenGrantRequest(
        as,
        client,
        refreshToken.token
      )
    );
    const response = await this._processResponse(rawResponse);
    if (response.status === "error") {
      const error = response.error;
      if (KnownErrors.RefreshTokenError.isInstance(error)) {
        return null;
      }
      throw error;
    }
    if (!response.data.ok) {
      const body = await response.data.text();
      throw new Error(`Failed to send refresh token request: ${response.status} ${body}`);
    }
    const result = await processRefreshTokenResponse(as, client, response.data);
    if (isOAuth2Error(result)) {
      throw new StackAssertionError("OAuth error", { result });
    }
    if (!result.access_token) {
      throw new StackAssertionError("Access token not found in token endpoint response, this is weird!");
    }
    return new AccessToken(result.access_token);
  }
  async sendClientRequest(path, requestOptions, session, requestType = "client") {
    session ?? (session = this.createSession({
      refreshToken: null
    }));
    return await this._networkRetry(
      () => this.sendClientRequestInner(path, requestOptions, session, requestType),
      session,
      requestType
    );
  }
  createSession(options) {
    const session = new InternalSession({
      refreshAccessTokenCallback: async (refreshToken) => await this.fetchNewAccessToken(refreshToken),
      ...options
    });
    return session;
  }
  async sendClientRequestAndCatchKnownError(path, requestOptions, tokenStoreOrNull, errorsToCatch) {
    try {
      return Result.ok(await this.sendClientRequest(path, requestOptions, tokenStoreOrNull));
    } catch (e) {
      for (const errorType of errorsToCatch) {
        if (errorType.isInstance(e)) {
          return Result.error(e);
        }
      }
      throw e;
    }
  }
  async sendClientRequestInner(path, options, session, requestType) {
    var _a4, _b4;
    let tokenObj = await session.getOrFetchLikelyValidTokens(2e4);
    let adminSession = "projectOwnerSession" in this.options ? this.options.projectOwnerSession : null;
    let adminTokenObj = adminSession ? await adminSession.getOrFetchLikelyValidTokens(2e4) : null;
    await ((_b4 = (_a4 = this.options).prepareRequest) == null ? void 0 : _b4.call(_a4));
    let url = this.getApiUrl() + path;
    if (url.endsWith("/")) {
      url = url.slice(0, -1);
    }
    const params = {
      /**
       * This fetch may be cross-origin, in which case we don't want to send cookies of the
       * original origin (this is the default behavior of `credentials`).
       *
       * To help debugging, also omit cookies on same-origin, so we don't accidentally
       * implement reliance on cookies anywhere.
       *
       * However, Cloudflare Workers don't actually support `credentials`, so we only set it
       * if Cloudflare-exclusive globals are not detected. https://github.com/cloudflare/workers-sdk/issues/2514
       */
      ..."WebSocketPair" in globalVar ? {} : {
        credentials: "omit"
      },
      ...options,
      headers: {
        "X-Stack-Override-Error-Status": "true",
        "X-Stack-Project-Id": this.projectId,
        "X-Stack-Access-Type": requestType,
        "X-Stack-Client-Version": this.options.clientVersion,
        ...tokenObj ? {
          "X-Stack-Access-Token": tokenObj.accessToken.token
        } : {},
        ...(tokenObj == null ? void 0 : tokenObj.refreshToken) ? {
          "X-Stack-Refresh-Token": tokenObj.refreshToken.token
        } : {},
        ..."publishableClientKey" in this.options ? {
          "X-Stack-Publishable-Client-Key": this.options.publishableClientKey
        } : {},
        ...adminTokenObj ? {
          "X-Stack-Admin-Access-Token": adminTokenObj.accessToken.token
        } : {},
        /**
         * Next.js until v15 would cache fetch requests by default, and forcefully disabling it was nearly impossible.
         *
         * This header is used to change the cache key and hence always disable it, because we do our own caching.
         *
         * When we drop support for Next.js <15, we may be able to remove this header, but please make sure that this is
         * the case (I haven't actually tested.)
         */
        "X-Stack-Random-Nonce": generateSecureRandomString(),
        // don't show a warning when proxying the API through ngrok (only relevant if the API url is an ngrok site)
        "ngrok-skip-browser-warning": "true",
        ...this.options.extraRequestHeaders,
        ...options.headers
      },
      /**
       * Cloudflare Workers does not support cache, so don't pass it there
       */
      ..."WebSocketPair" in globalVar ? {} : {
        cache: "no-store"
      }
    };
    let rawRes;
    try {
      rawRes = await fetch(url, params);
    } catch (e) {
      if (e instanceof TypeError) {
        if (HTTP_METHODS[params.method ?? "GET"].idempotent) {
          return Result.error(e);
        } else {
          throw await this._createNetworkError(e, session, requestType);
        }
      }
      throw e;
    }
    const processedRes = await this._processResponse(rawRes);
    if (processedRes.status === "error") {
      if (KnownErrors.InvalidAccessToken.isInstance(processedRes.error)) {
        if (!tokenObj) {
          throw new StackAssertionError("Received invalid access token, but session is not logged in", { tokenObj, processedRes });
        }
        session.markAccessTokenExpired(tokenObj.accessToken);
        return Result.error(processedRes.error);
      }
      if (adminSession && (KnownErrors.InvalidAdminAccessToken.isInstance(processedRes.error) || KnownErrors.ApiKeyNotFound.isInstance(processedRes.error))) {
        if (!adminTokenObj) {
          throw new StackAssertionError("Received invalid admin access token, but admin session is not logged in", { adminTokenObj, processedRes });
        }
        adminSession.markAccessTokenExpired(adminTokenObj.accessToken);
        return Result.error(processedRes.error);
      }
      throw processedRes.error;
    }
    const res = Object.assign(processedRes.data, {
      usedTokens: tokenObj
    });
    if (res.ok) {
      return Result.ok(res);
    } else if (res.status === 429) {
      const retryAfter = res.headers.get("Retry-After");
      if (retryAfter !== null) {
        console.log(`Rate limited while sending request to ${url}. Will retry after ${retryAfter} seconds...`);
        await wait(Number(retryAfter) * 1e3);
        return Result.error(new Error(`Rate limited, retrying after ${retryAfter} seconds`));
      }
      console.log(`Rate limited while sending request to ${url}, no retry-after header received. Retrying...`);
      return Result.error(new Error("Rate limited, no retry-after header received"));
    } else {
      const error = await res.text();
      const errorObj = new StackAssertionError(`Failed to send request to ${url}: ${res.status} ${error}`, { request: params, res, path });
      if (res.status === 508 && error.includes("INFINITE_LOOP_DETECTED")) {
        return Result.error(errorObj);
      }
      throw errorObj;
    }
  }
  async _processResponse(rawRes) {
    let res = rawRes;
    if (rawRes.headers.has("x-stack-actual-status")) {
      const actualStatus = Number(rawRes.headers.get("x-stack-actual-status"));
      res = new Response(rawRes.body, {
        status: actualStatus,
        statusText: rawRes.statusText,
        headers: rawRes.headers
      });
    }
    if (res.headers.has("x-stack-known-error")) {
      const errorJson = await res.json();
      if (res.headers.get("x-stack-known-error") !== errorJson.code) {
        throw new StackAssertionError("Mismatch between x-stack-known-error header and error code in body; the server's response is invalid");
      }
      const error = KnownError.fromJson(errorJson);
      return Result.error(error);
    }
    return Result.ok(res);
  }
  async checkFeatureSupport(options) {
    const res = await this.sendClientRequest("/check-feature-support", {
      method: "POST",
      headers: {
        "Content-Type": "application/json"
      },
      body: JSON.stringify(options)
    }, null);
    throw new StackAssertionError(await res.text());
  }
  async sendForgotPasswordEmail(email, callbackUrl) {
    const res = await this.sendClientRequestAndCatchKnownError(
      "/auth/password/send-reset-code",
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify({
          email,
          callback_url: callbackUrl
        })
      },
      null,
      [KnownErrors.UserNotFound]
    );
    if (res.status === "error") {
      return Result.error(res.error);
    } else {
      return Result.ok(void 0);
    }
  }
  async sendVerificationEmail(email, callbackUrl, session) {
    const res = await this.sendClientRequestAndCatchKnownError(
      "/contact-channels/send-verification-code",
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify({
          email,
          callback_url: callbackUrl
        })
      },
      session,
      [KnownErrors.EmailAlreadyVerified]
    );
    if (res.status === "error") {
      return res.error;
    }
  }
  async sendMagicLinkEmail(email, callbackUrl) {
    const res = await this.sendClientRequestAndCatchKnownError(
      "/auth/otp/send-sign-in-code",
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify({
          email,
          callback_url: callbackUrl
        })
      },
      null,
      [KnownErrors.RedirectUrlNotWhitelisted]
    );
    if (res.status === "error") {
      return Result.error(res.error);
    } else {
      return Result.ok(await res.data.json());
    }
  }
  async resetPassword(options) {
    const res = await this.sendClientRequestAndCatchKnownError(
      "onlyVerifyCode" in options ? "/auth/password/reset/check-code" : "/auth/password/reset",
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify({
          code: options.code,
          ..."password" in options ? { password: options.password } : {}
        })
      },
      null,
      [KnownErrors.VerificationCodeError]
    );
    if (res.status === "error") {
      return Result.error(res.error);
    } else {
      return Result.ok(void 0);
    }
  }
  async updatePassword(options, session) {
    const res = await this.sendClientRequestAndCatchKnownError(
      "/auth/password/update",
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify({
          old_password: options.oldPassword,
          new_password: options.newPassword
        })
      },
      session,
      [KnownErrors.PasswordConfirmationMismatch, KnownErrors.PasswordRequirementsNotMet]
    );
    if (res.status === "error") {
      return res.error;
    }
  }
  async setPassword(options, session) {
    const res = await this.sendClientRequestAndCatchKnownError(
      "/auth/password/set",
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify(options)
      },
      session,
      [KnownErrors.PasswordRequirementsNotMet]
    );
    if (res.status === "error") {
      return res.error;
    }
  }
  async verifyPasswordResetCode(code) {
    const res = await this.resetPassword({ code, onlyVerifyCode: true });
    if (res.status === "error") {
      return Result.error(res.error);
    } else {
      return Result.ok(void 0);
    }
  }
  async verifyEmail(code) {
    const res = await this.sendClientRequestAndCatchKnownError(
      "/contact-channels/verify",
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify({
          code
        })
      },
      null,
      [KnownErrors.VerificationCodeError]
    );
    if (res.status === "error") {
      return Result.error(res.error);
    } else {
      return Result.ok(void 0);
    }
  }
  async initiatePasskeyRegistration(options, session) {
    const res = await this.sendClientRequestAndCatchKnownError(
      "/auth/passkey/initiate-passkey-registration",
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify(options)
      },
      session,
      []
    );
    if (res.status === "error") {
      return Result.error(res.error);
    }
    return Result.ok(await res.data.json());
  }
  async registerPasskey(options, session) {
    const res = await this.sendClientRequestAndCatchKnownError(
      "/auth/passkey/register",
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify(options)
      },
      session,
      [KnownErrors.PasskeyRegistrationFailed]
    );
    if (res.status === "error") {
      return Result.error(res.error);
    }
    return Result.ok(void 0);
  }
  async initiatePasskeyAuthentication(options, session) {
    const res = await this.sendClientRequestAndCatchKnownError(
      "/auth/passkey/initiate-passkey-authentication",
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify(options)
      },
      session,
      []
    );
    if (res.status === "error") {
      return Result.error(res.error);
    }
    return Result.ok(await res.data.json());
  }
  async sendTeamInvitation(options) {
    await this.sendClientRequest(
      "/team-invitations/send-code",
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify({
          email: options.email,
          team_id: options.teamId,
          callback_url: options.callbackUrl
        })
      },
      options.session
    );
  }
  async acceptTeamInvitation(options) {
    const res = await this.sendClientRequestAndCatchKnownError(
      options.type === "check" ? "/team-invitations/accept/check-code" : options.type === "details" ? "/team-invitations/accept/details" : "/team-invitations/accept",
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify({
          code: options.code
        })
      },
      options.session,
      [KnownErrors.VerificationCodeError]
    );
    if (res.status === "error") {
      return Result.error(res.error);
    } else {
      return Result.ok(await res.data.json());
    }
  }
  async totpMfa(attemptCode, totp, session) {
    const res = await this.sendClientRequest("/auth/mfa/sign-in", {
      method: "POST",
      headers: {
        "Content-Type": "application/json"
      },
      body: JSON.stringify({
        code: attemptCode,
        type: "totp",
        totp
      })
    }, session);
    const result = await res.json();
    return {
      accessToken: result.access_token,
      refreshToken: result.refresh_token,
      newUser: result.is_new_user
    };
  }
  async signInWithCredential(email, password, session) {
    const res = await this.sendClientRequestAndCatchKnownError(
      "/auth/password/sign-in",
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify({
          email,
          password
        })
      },
      session,
      [KnownErrors.EmailPasswordMismatch]
    );
    if (res.status === "error") {
      return Result.error(res.error);
    }
    const result = await res.data.json();
    return Result.ok({
      accessToken: result.access_token,
      refreshToken: result.refresh_token
    });
  }
  async signUpWithCredential(email, password, emailVerificationRedirectUrl, session) {
    const res = await this.sendClientRequestAndCatchKnownError(
      "/auth/password/sign-up",
      {
        headers: {
          "Content-Type": "application/json"
        },
        method: "POST",
        body: JSON.stringify({
          email,
          password,
          verification_callback_url: emailVerificationRedirectUrl
        })
      },
      session,
      [KnownErrors.UserWithEmailAlreadyExists, KnownErrors.PasswordRequirementsNotMet]
    );
    if (res.status === "error") {
      return Result.error(res.error);
    }
    const result = await res.data.json();
    return Result.ok({
      accessToken: result.access_token,
      refreshToken: result.refresh_token
    });
  }
  async signUpAnonymously(session) {
    const res = await this.sendClientRequestAndCatchKnownError(
      "/auth/anonymous/sign-up",
      {
        method: "POST"
      },
      session,
      []
    );
    if (res.status === "error") {
      return Result.error(res.error);
    }
    const result = await res.data.json();
    return Result.ok({
      accessToken: result.access_token,
      refreshToken: result.refresh_token
    });
  }
  async signInWithMagicLink(code) {
    const res = await this.sendClientRequestAndCatchKnownError(
      "/auth/otp/sign-in",
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify({
          code
        })
      },
      null,
      [KnownErrors.VerificationCodeError]
    );
    if (res.status === "error") {
      return Result.error(res.error);
    }
    const result = await res.data.json();
    return Result.ok({
      accessToken: result.access_token,
      refreshToken: result.refresh_token,
      newUser: result.is_new_user
    });
  }
  async signInWithPasskey(body) {
    const res = await this.sendClientRequestAndCatchKnownError(
      "/auth/passkey/sign-in",
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify(body)
      },
      null,
      [KnownErrors.PasskeyAuthenticationFailed]
    );
    if (res.status === "error") {
      return Result.error(res.error);
    }
    const result = await res.data.json();
    return Result.ok({
      accessToken: result.access_token,
      refreshToken: result.refresh_token
    });
  }
  async getOAuthUrl(options) {
    const updatedRedirectUrl = new URL(options.redirectUrl);
    for (const key of ["code", "state"]) {
      if (updatedRedirectUrl.searchParams.has(key)) {
        console.warn("Redirect URL already contains " + key + " parameter, removing it as it will be overwritten by the OAuth callback");
      }
      updatedRedirectUrl.searchParams.delete(key);
    }
    if (!("publishableClientKey" in this.options)) {
      throw new Error("Admin session token is currently not supported for OAuth");
    }
    const url = new URL(this.getApiUrl() + "/auth/oauth/authorize/" + options.provider.toLowerCase());
    url.searchParams.set("client_id", this.projectId);
    url.searchParams.set("client_secret", this.options.publishableClientKey);
    url.searchParams.set("redirect_uri", updatedRedirectUrl.toString());
    url.searchParams.set("scope", "legacy");
    url.searchParams.set("state", options.state);
    url.searchParams.set("grant_type", "authorization_code");
    url.searchParams.set("code_challenge", options.codeChallenge);
    url.searchParams.set("code_challenge_method", "S256");
    url.searchParams.set("response_type", "code");
    url.searchParams.set("type", options.type);
    url.searchParams.set("error_redirect_url", options.errorRedirectUrl);
    if (options.afterCallbackRedirectUrl) {
      url.searchParams.set("after_callback_redirect_url", options.afterCallbackRedirectUrl);
    }
    if (options.type === "link") {
      const tokens = await options.session.getOrFetchLikelyValidTokens(2e4);
      url.searchParams.set("token", (tokens == null ? void 0 : tokens.accessToken.token) || "");
      if (options.providerScope) {
        url.searchParams.set("provider_scope", options.providerScope);
      }
    }
    return url.toString();
  }
  async callOAuthCallback(options) {
    if (!("publishableClientKey" in this.options)) {
      throw new Error("Admin session token is currently not supported for OAuth");
    }
    const as = {
      issuer: this.options.getBaseUrl(),
      algorithm: "oauth2",
      token_endpoint: this.getApiUrl() + "/auth/oauth/token"
    };
    const client = {
      client_id: this.projectId,
      client_secret: this.options.publishableClientKey,
      token_endpoint_auth_method: "client_secret_post"
    };
    const params = await this._networkRetryException(
      async () => validateAuthResponse(as, client, options.oauthParams, options.state)
    );
    if (isOAuth2Error(params)) {
      throw new StackAssertionError("Error validating outer OAuth response", { params });
    }
    const response = await authorizationCodeGrantRequest(
      as,
      client,
      params,
      options.redirectUri,
      options.codeVerifier
    );
    const result = await processAuthorizationCodeOAuth2Response(as, client, response);
    if (isOAuth2Error(result)) {
      if ("code" in result && result.code === "MULTI_FACTOR_AUTHENTICATION_REQUIRED") {
        throw new KnownErrors.MultiFactorAuthenticationRequired(result.details.attempt_code);
      }
      throw new StackAssertionError("Outer OAuth error during authorization code response", { result });
    }
    return {
      newUser: result.is_new_user,
      afterCallbackRedirectUrl: result.after_callback_redirect_url,
      accessToken: result.access_token,
      refreshToken: result.refresh_token ?? throwErr("Refresh token not found in outer OAuth response")
    };
  }
  async signOut(session) {
    const tokenObj = await session.getOrFetchLikelyValidTokens(2e4);
    if (tokenObj) {
      const resOrError = await this.sendClientRequestAndCatchKnownError(
        "/auth/sessions/current",
        {
          method: "DELETE",
          headers: {
            "Content-Type": "application/json"
          },
          body: JSON.stringify({})
        },
        session,
        [KnownErrors.RefreshTokenError]
      );
      if (resOrError.status === "error") {
        if (KnownErrors.RefreshTokenError.isInstance(resOrError.error)) {
        } else {
          throw new StackAssertionError("Unexpected error", { error: resOrError.error });
        }
      } else {
      }
    }
    session.markInvalid();
  }
  async getClientUserByToken(session) {
    const responseOrError = await this.sendClientRequestAndCatchKnownError(
      "/users/me",
      {},
      session,
      [KnownErrors.CannotGetOwnUserWithoutUser]
    );
    if (responseOrError.status === "error") {
      if (KnownErrors.CannotGetOwnUserWithoutUser.isInstance(responseOrError.error)) {
        return null;
      } else {
        throw new StackAssertionError("Unexpected uncaught error", { cause: responseOrError.error });
      }
    }
    const response = responseOrError.data;
    const user = await response.json();
    if (!user) throw new StackAssertionError("User endpoint returned null; this should never happen");
    return user;
  }
  async listTeamInvitations(options, session) {
    const response = await this.sendClientRequest(
      "/team-invitations?" + new URLSearchParams({ team_id: options.teamId }),
      {},
      session
    );
    const result = await response.json();
    return result.items;
  }
  async revokeTeamInvitation(invitationId, teamId, session) {
    await this.sendClientRequest(
      `/team-invitations/${invitationId}?team_id=${teamId}`,
      { method: "DELETE" },
      session
    );
  }
  async listTeamMemberProfiles(options, session) {
    const response = await this.sendClientRequest(
      "/team-member-profiles?" + new URLSearchParams(filterUndefined({
        team_id: options.teamId,
        user_id: options.userId
      })),
      {},
      session
    );
    const result = await response.json();
    return result.items;
  }
  async getTeamMemberProfile(options, session) {
    const response = await this.sendClientRequest(
      `/team-member-profiles/${options.teamId}/${options.userId}`,
      {},
      session
    );
    return await response.json();
  }
  async leaveTeam(teamId, session) {
    await this.sendClientRequest(
      `/team-memberships/${teamId}/me`,
      {
        method: "DELETE",
        headers: {
          "content-type": "application/json"
        },
        body: JSON.stringify({})
      },
      session
    );
  }
  async updateTeamMemberProfile(options, session) {
    await this.sendClientRequest(
      `/team-member-profiles/${options.teamId}/${options.userId}`,
      {
        method: "PATCH",
        headers: {
          "content-type": "application/json"
        },
        body: JSON.stringify(options.profile)
      },
      session
    );
  }
  async updateTeam(options, session) {
    await this.sendClientRequest(
      `/teams/${options.teamId}`,
      {
        method: "PATCH",
        headers: {
          "content-type": "application/json"
        },
        body: JSON.stringify(options.data)
      },
      session
    );
  }
  async listCurrentUserTeamPermissions(options, session) {
    const response = await this.sendClientRequest(
      `/team-permissions?team_id=${options.teamId}&user_id=me&recursive=${options.recursive}`,
      {},
      session
    );
    const result = await response.json();
    return result.items;
  }
  async listCurrentUserProjectPermissions(options, session) {
    const response = await this.sendClientRequest(
      `/project-permissions?user_id=me&recursive=${options.recursive}`,
      {},
      session
    );
    const result = await response.json();
    return result.items;
  }
  async listCurrentUserTeams(session) {
    const response = await this.sendClientRequest(
      "/teams?user_id=me",
      {},
      session
    );
    const result = await response.json();
    return result.items;
  }
  async getClientProject() {
    const responseOrError = await this.sendClientRequestAndCatchKnownError("/projects/current", {}, null, [KnownErrors.ProjectNotFound]);
    if (responseOrError.status === "error") {
      return Result.error(responseOrError.error);
    }
    const response = responseOrError.data;
    const project = await response.json();
    return Result.ok(project);
  }
  async updateClientUser(update, session) {
    await this.sendClientRequest(
      "/users/me",
      {
        method: "PATCH",
        headers: {
          "content-type": "application/json"
        },
        body: JSON.stringify(update)
      },
      session
    );
  }
  async listProjects(session) {
    const response = await this.sendClientRequest("/internal/projects", {}, session);
    if (!response.ok) {
      throw new Error("Failed to list projects: " + response.status + " " + await response.text());
    }
    const json = await response.json();
    return json.items;
  }
  async createProject(project, session) {
    const fetchResponse = await this.sendClientRequest(
      "/internal/projects",
      {
        method: "POST",
        headers: {
          "content-type": "application/json"
        },
        body: JSON.stringify(project)
      },
      session
    );
    if (!fetchResponse.ok) {
      throw new Error("Failed to create project: " + fetchResponse.status + " " + await fetchResponse.text());
    }
    const json = await fetchResponse.json();
    return json;
  }
  async createProviderAccessToken(provider, scope, session) {
    const response = await this.sendClientRequest(
      `/connected-accounts/me/${provider}/access-token`,
      {
        method: "POST",
        headers: {
          "content-type": "application/json"
        },
        body: JSON.stringify({ scope })
      },
      session
    );
    return await response.json();
  }
  async createClientTeam(data, session) {
    const response = await this.sendClientRequest(
      "/teams",
      {
        method: "POST",
        headers: {
          "content-type": "application/json"
        },
        body: JSON.stringify(data)
      },
      session
    );
    return await response.json();
  }
  async deleteTeam(teamId, session) {
    await this.sendClientRequest(
      `/teams/${teamId}`,
      {
        method: "DELETE"
      },
      session
    );
  }
  async deleteCurrentUser(session) {
    await this.sendClientRequest(
      "/users/me",
      {
        method: "DELETE"
      },
      session
    );
  }
  async createClientContactChannel(data, session) {
    const response = await this.sendClientRequest(
      "/contact-channels",
      {
        method: "POST",
        headers: {
          "content-type": "application/json"
        },
        body: JSON.stringify(data)
      },
      session
    );
    return await response.json();
  }
  async updateClientContactChannel(id, data, session) {
    const response = await this.sendClientRequest(
      `/contact-channels/me/${id}`,
      {
        method: "PATCH",
        headers: {
          "content-type": "application/json"
        },
        body: JSON.stringify(data)
      },
      session
    );
    return await response.json();
  }
  async deleteClientContactChannel(id, session) {
    await this.sendClientRequest(
      `/contact-channels/me/${id}`,
      {
        method: "DELETE"
      },
      session
    );
  }
  async deleteSession(sessionId, session) {
    await this.sendClientRequest(
      `/auth/sessions/${sessionId}?user_id=me`,
      {
        method: "DELETE"
      },
      session
    );
  }
  async listSessions(session) {
    const response = await this.sendClientRequest(
      "/auth/sessions?user_id=me",
      {
        method: "GET"
      },
      session
    );
    return await response.json();
  }
  async listClientContactChannels(session) {
    const response = await this.sendClientRequest(
      "/contact-channels?user_id=me",
      {
        method: "GET"
      },
      session
    );
    const json = await response.json();
    return json.items;
  }
  async sendCurrentUserContactChannelVerificationEmail(contactChannelId, callbackUrl, session) {
    const responseOrError = await this.sendClientRequestAndCatchKnownError(
      `/contact-channels/me/${contactChannelId}/send-verification-code`,
      {
        method: "POST",
        headers: {
          "content-type": "application/json"
        },
        body: JSON.stringify({ callback_url: callbackUrl })
      },
      session,
      [KnownErrors.EmailAlreadyVerified]
    );
    if (responseOrError.status === "error") {
      return Result.error(responseOrError.error);
    }
    return Result.ok(void 0);
  }
  async cliLogin(loginCode, refreshToken, session) {
    const responseOrError = await this.sendClientRequestAndCatchKnownError(
      "/auth/cli/complete",
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify({
          login_code: loginCode,
          refresh_token: refreshToken
        })
      },
      session,
      [KnownErrors.SchemaError]
    );
    if (responseOrError.status === "error") {
      return Result.error(responseOrError.error);
    }
    return Result.ok(void 0);
  }
  async _getApiKeyRequestInfo(options) {
    if ("user_id" in options && "team_id" in options) {
      throw new StackAssertionError("Cannot specify both user_id and team_id in _getApiKeyRequestInfo");
    }
    return {
      endpoint: "team_id" in options ? "/team-api-keys" : "/user-api-keys",
      queryParams: new URLSearchParams(filterUndefinedOrNull(options))
    };
  }
  async listProjectApiKeys(options, session, requestType) {
    const sendRequest = (requestType === "client" ? this.sendClientRequest : this.sendServerRequest).bind(this);
    const { endpoint, queryParams } = await this._getApiKeyRequestInfo(options);
    const response = await sendRequest(
      `${endpoint}?${queryParams.toString()}`,
      {
        method: "GET"
      },
      session,
      requestType
    );
    const json = await response.json();
    return json.items;
  }
  async createProjectApiKey(data, session, requestType) {
    const sendRequest = (requestType === "client" ? this.sendClientRequest : this.sendServerRequest).bind(this);
    const { endpoint } = await this._getApiKeyRequestInfo(data);
    const response = await sendRequest(
      `${endpoint}`,
      {
        method: "POST",
        headers: {
          "content-type": "application/json"
        },
        body: JSON.stringify(data)
      },
      session,
      requestType
    );
    return await response.json();
  }
  async getProjectApiKey(options, keyId, session, requestType) {
    const sendRequest = (requestType === "client" ? this.sendClientRequest : this.sendServerRequest).bind(this);
    const { endpoint, queryParams } = await this._getApiKeyRequestInfo(options);
    const response = await sendRequest(
      `${endpoint}/${keyId}?${queryParams.toString()}`,
      {
        method: "GET"
      },
      session,
      requestType
    );
    return await response.json();
  }
  async updateProjectApiKey(options, keyId, data, session, requestType) {
    const sendRequest = (requestType === "client" ? this.sendClientRequest : this.sendServerRequest).bind(this);
    const { endpoint, queryParams } = await this._getApiKeyRequestInfo(options);
    const response = await sendRequest(
      `${endpoint}/${keyId}?${queryParams.toString()}`,
      {
        method: "PATCH",
        headers: {
          "content-type": "application/json"
        },
        body: JSON.stringify(data)
      },
      session,
      requestType
    );
    return await response.json();
  }
  async checkProjectApiKey(type, apiKey, session, requestType) {
    const sendRequest = (requestType === "client" ? this.sendClientRequestAndCatchKnownError : this.sendServerRequestAndCatchKnownError).bind(this);
    const result = await sendRequest(
      `/${type}-api-keys/check`,
      {
        method: "POST",
        headers: {
          "content-type": "application/json"
        },
        body: JSON.stringify({ api_key: apiKey })
      },
      session,
      [KnownErrors.ApiKeyNotValid]
    );
    if (result.status === "error") {
      return null;
    }
    return await result.data.json();
  }
};

// package/@stackframe/react/node_modules/@stackframe/stack-shared/dist/esm/interface/serverInterface.js
var StackServerInterface = class extends StackClientInterface {
  constructor(options) {
    super(options);
    this.options = options;
  }
  async sendServerRequest(path, options, session, requestType = "server") {
    return await this.sendClientRequest(
      path,
      {
        ...options,
        headers: {
          "x-stack-secret-server-key": "secretServerKey" in this.options ? this.options.secretServerKey : "",
          ...options.headers
        }
      },
      session,
      requestType
    );
  }
  async sendServerRequestAndCatchKnownError(path, requestOptions, tokenStoreOrNull, errorsToCatch) {
    try {
      return Result.ok(await this.sendServerRequest(path, requestOptions, tokenStoreOrNull));
    } catch (e) {
      for (const errorType of errorsToCatch) {
        if (errorType.isInstance(e)) {
          return Result.error(e);
        }
      }
      throw e;
    }
  }
  async createServerUser(data) {
    const response = await this.sendServerRequest(
      "/users",
      {
        method: "POST",
        headers: {
          "content-type": "application/json"
        },
        body: JSON.stringify(data)
      },
      null
    );
    return await response.json();
  }
  async getServerUserByToken(session) {
    const responseOrError = await this.sendServerRequestAndCatchKnownError(
      "/users/me",
      {},
      session,
      [KnownErrors.CannotGetOwnUserWithoutUser]
    );
    if (responseOrError.status === "error") {
      if (KnownErrors.CannotGetOwnUserWithoutUser.isInstance(responseOrError.error)) {
        return null;
      } else {
        throw new StackAssertionError("Unexpected uncaught error", { cause: responseOrError.error });
      }
    }
    const response = responseOrError.data;
    const user = await response.json();
    if (!user) throw new StackAssertionError("User endpoint returned null; this should never happen");
    return user;
  }
  async getServerUserById(userId) {
    const responseOrError = await this.sendServerRequestAndCatchKnownError(
      urlString`/users/${userId}`,
      {},
      null,
      [KnownErrors.UserNotFound]
    );
    if (responseOrError.status === "error") {
      return Result.error(responseOrError.error);
    }
    const user = await responseOrError.data.json();
    return Result.ok(user);
  }
  async listServerTeamInvitations(options) {
    const response = await this.sendServerRequest(
      urlString`/team-invitations?team_id=${options.teamId}`,
      {},
      null
    );
    const result = await response.json();
    return result.items;
  }
  async revokeServerTeamInvitation(invitationId, teamId) {
    await this.sendServerRequest(
      urlString`/team-invitations/${invitationId}?team_id=${teamId}`,
      { method: "DELETE" },
      null
    );
  }
  async listServerTeamMemberProfiles(options) {
    const response = await this.sendServerRequest(
      urlString`/team-member-profiles?team_id=${options.teamId}`,
      {},
      null
    );
    const result = await response.json();
    return result.items;
  }
  async getServerTeamMemberProfile(options) {
    const response = await this.sendServerRequest(
      urlString`/team-member-profiles/${options.teamId}/${options.userId}`,
      {},
      null
    );
    return await response.json();
  }
  async listServerTeamPermissions(options, session) {
    const response = await this.sendServerRequest(
      `/team-permissions?${new URLSearchParams(filterUndefined({
        user_id: options.userId,
        team_id: options.teamId,
        recursive: options.recursive.toString()
      }))}`,
      {},
      session
    );
    const result = await response.json();
    return result.items;
  }
  async listServerProjectPermissions(options, session) {
    const response = await this.sendServerRequest(
      `/project-permissions?${new URLSearchParams(filterUndefined({
        user_id: options.userId,
        recursive: options.recursive.toString()
      }))}`,
      {},
      session
    );
    const result = await response.json();
    return result.items;
  }
  async listServerUsers(options) {
    var _a4, _b4;
    const searchParams = new URLSearchParams(filterUndefined({
      cursor: options.cursor,
      limit: (_a4 = options.limit) == null ? void 0 : _a4.toString(),
      desc: (_b4 = options.desc) == null ? void 0 : _b4.toString(),
      ...options.orderBy ? {
        order_by: {
          signedUpAt: "signed_up_at"
        }[options.orderBy]
      } : {},
      ...options.query ? {
        query: options.query
      } : {}
    }));
    const response = await this.sendServerRequest("/users?" + searchParams.toString(), {}, null);
    return await response.json();
  }
  async listServerTeams(options) {
    const response = await this.sendServerRequest(
      `/teams?${new URLSearchParams(filterUndefined({
        user_id: options == null ? void 0 : options.userId
      }))}`,
      {},
      null
    );
    const result = await response.json();
    return result.items;
  }
  async getServerTeam(teamId) {
    const response = await this.sendServerRequest(
      `/teams/${teamId}`,
      {},
      null
    );
    return await response.json();
  }
  async listServerTeamUsers(teamId) {
    const response = await this.sendServerRequest(`/users?team_id=${teamId}`, {}, null);
    const result = await response.json();
    return result.items;
  }
  /* when passing a session, the user will be added to the team */
  async createServerTeam(data) {
    const response = await this.sendServerRequest(
      "/teams",
      {
        method: "POST",
        headers: {
          "content-type": "application/json"
        },
        body: JSON.stringify(data)
      },
      null
    );
    return await response.json();
  }
  async updateServerTeam(teamId, data) {
    const response = await this.sendServerRequest(
      urlString`/teams/${teamId}`,
      {
        method: "PATCH",
        headers: {
          "content-type": "application/json"
        },
        body: JSON.stringify(data)
      },
      null
    );
    return await response.json();
  }
  async deleteServerTeam(teamId) {
    await this.sendServerRequest(
      urlString`/teams/${teamId}`,
      { method: "DELETE" },
      null
    );
  }
  async addServerUserToTeam(options) {
    const response = await this.sendServerRequest(
      urlString`/team-memberships/${options.teamId}/${options.userId}`,
      {
        method: "POST",
        headers: {
          "content-type": "application/json"
        },
        body: JSON.stringify({})
      },
      null
    );
    return await response.json();
  }
  async removeServerUserFromTeam(options) {
    await this.sendServerRequest(
      urlString`/team-memberships/${options.teamId}/${options.userId}`,
      {
        method: "DELETE",
        headers: {
          "content-type": "application/json"
        },
        body: JSON.stringify({})
      },
      null
    );
  }
  async updateServerUser(userId, update) {
    const response = await this.sendServerRequest(
      urlString`/users/${userId}`,
      {
        method: "PATCH",
        headers: {
          "content-type": "application/json"
        },
        body: JSON.stringify(update)
      },
      null
    );
    return await response.json();
  }
  async createServerProviderAccessToken(userId, provider, scope) {
    const response = await this.sendServerRequest(
      urlString`/connected-accounts/${userId}/${provider}/access-token`,
      {
        method: "POST",
        headers: {
          "content-type": "application/json"
        },
        body: JSON.stringify({ scope })
      },
      null
    );
    return await response.json();
  }
  async createServerUserSession(userId, expiresInMillis, isImpersonation) {
    const response = await this.sendServerRequest(
      "/auth/sessions",
      {
        method: "POST",
        headers: {
          "content-type": "application/json"
        },
        body: JSON.stringify({
          user_id: userId,
          expires_in_millis: expiresInMillis,
          is_impersonation: isImpersonation
        })
      },
      null
    );
    const result = await response.json();
    return {
      accessToken: result.access_token,
      refreshToken: result.refresh_token
    };
  }
  async leaveServerTeam(options) {
    await this.sendClientRequest(
      urlString`/team-memberships/${options.teamId}/${options.userId}`,
      {
        method: "DELETE",
        headers: {
          "content-type": "application/json"
        },
        body: JSON.stringify({})
      },
      null
    );
  }
  async updateServerTeamMemberProfile(options) {
    await this.sendServerRequest(
      urlString`/team-member-profiles/${options.teamId}/${options.userId}`,
      {
        method: "PATCH",
        headers: {
          "content-type": "application/json"
        },
        body: JSON.stringify(options.profile)
      },
      null
    );
  }
  async grantServerTeamUserPermission(teamId, userId, permissionId) {
    await this.sendServerRequest(
      urlString`/team-permissions/${teamId}/${userId}/${permissionId}`,
      {
        method: "POST",
        headers: {
          "content-type": "application/json"
        },
        body: JSON.stringify({})
      },
      null
    );
  }
  async grantServerProjectPermission(userId, permissionId) {
    await this.sendServerRequest(
      urlString`/project-permissions/${userId}/${permissionId}`,
      {
        method: "POST",
        headers: {
          "content-type": "application/json"
        },
        body: JSON.stringify({})
      },
      null
    );
  }
  async revokeServerTeamUserPermission(teamId, userId, permissionId) {
    await this.sendServerRequest(
      urlString`/team-permissions/${teamId}/${userId}/${permissionId}`,
      {
        method: "DELETE",
        headers: {
          "content-type": "application/json"
        },
        body: JSON.stringify({})
      },
      null
    );
  }
  async revokeServerProjectPermission(userId, permissionId) {
    await this.sendServerRequest(
      urlString`/project-permissions/${userId}/${permissionId}`,
      {
        method: "DELETE",
        headers: {
          "content-type": "application/json"
        },
        body: JSON.stringify({})
      },
      null
    );
  }
  async deleteServerUser(userId) {
    await this.sendServerRequest(
      urlString`/users/${userId}`,
      {
        method: "DELETE",
        headers: {
          "content-type": "application/json"
        },
        body: JSON.stringify({})
      },
      null
    );
  }
  async createServerContactChannel(data) {
    const response = await this.sendServerRequest(
      "/contact-channels",
      {
        method: "POST",
        headers: {
          "content-type": "application/json"
        },
        body: JSON.stringify(data)
      },
      null
    );
    return await response.json();
  }
  async updateServerContactChannel(userId, contactChannelId, data) {
    const response = await this.sendServerRequest(
      urlString`/contact-channels/${userId}/${contactChannelId}`,
      {
        method: "PATCH",
        headers: {
          "content-type": "application/json"
        },
        body: JSON.stringify(data)
      },
      null
    );
    return await response.json();
  }
  async deleteServerContactChannel(userId, contactChannelId) {
    await this.sendServerRequest(
      urlString`/contact-channels/${userId}/${contactChannelId}`,
      {
        method: "DELETE"
      },
      null
    );
  }
  async listServerContactChannels(userId) {
    const response = await this.sendServerRequest(
      urlString`/contact-channels?user_id=${userId}`,
      {
        method: "GET"
      },
      null
    );
    const json = await response.json();
    return json.items;
  }
  async sendServerContactChannelVerificationEmail(userId, contactChannelId, callbackUrl) {
    await this.sendServerRequest(
      urlString`/contact-channels/${userId}/${contactChannelId}/send-verification-code`,
      {
        method: "POST",
        headers: {
          "content-type": "application/json"
        },
        body: JSON.stringify({ callback_url: callbackUrl })
      },
      null
    );
  }
  async listServerSessions(userId) {
    const response = await this.sendServerRequest(
      urlString`/auth/sessions?user_id=${userId}`,
      {
        method: "GET"
      },
      null
    );
    return await response.json();
  }
  async deleteServerSession(sessionId) {
    await this.sendServerRequest(
      urlString`/auth/sessions/${sessionId}`,
      {
        method: "DELETE"
      },
      null
    );
  }
  async sendServerTeamInvitation(options) {
    await this.sendServerRequest(
      "/team-invitations/send-code",
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify({
          email: options.email,
          team_id: options.teamId,
          callback_url: options.callbackUrl
        })
      },
      null
    );
  }
  async updatePassword(options) {
    const res = await this.sendServerRequestAndCatchKnownError(
      "/auth/password/update",
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify({
          old_password: options.oldPassword,
          new_password: options.newPassword
        })
      },
      null,
      [KnownErrors.PasswordConfirmationMismatch, KnownErrors.PasswordRequirementsNotMet]
    );
    if (res.status === "error") {
      return res.error;
    }
  }
};

// package/@stackframe/react/node_modules/@stackframe/stack-shared/dist/esm/interface/adminInterface.js
var StackAdminInterface = class extends StackServerInterface {
  constructor(options) {
    super(options);
    this.options = options;
  }
  async sendAdminRequest(path, options, session, requestType = "admin") {
    return await this.sendServerRequest(
      path,
      {
        ...options,
        headers: {
          "x-stack-super-secret-admin-key": "superSecretAdminKey" in this.options ? this.options.superSecretAdminKey : "",
          ...options.headers
        }
      },
      session,
      requestType
    );
  }
  async getProject() {
    const response = await this.sendAdminRequest(
      "/internal/projects/current",
      {
        method: "GET"
      },
      null
    );
    return await response.json();
  }
  async updateProject(update) {
    const response = await this.sendAdminRequest(
      "/internal/projects/current",
      {
        method: "PATCH",
        headers: {
          "content-type": "application/json"
        },
        body: JSON.stringify(update)
      },
      null
    );
    return await response.json();
  }
  async createInternalApiKey(options) {
    const response = await this.sendAdminRequest(
      "/internal/api-keys",
      {
        method: "POST",
        headers: {
          "content-type": "application/json"
        },
        body: JSON.stringify(options)
      },
      null
    );
    return await response.json();
  }
  async listInternalApiKeys() {
    const response = await this.sendAdminRequest("/internal/api-keys", {}, null);
    const result = await response.json();
    return result.items;
  }
  async revokeInternalApiKeyById(id) {
    await this.sendAdminRequest(
      `/internal/api-keys/${id}`,
      {
        method: "PATCH",
        headers: {
          "content-type": "application/json"
        },
        body: JSON.stringify({
          revoked: true
        })
      },
      null
    );
  }
  async getInternalApiKey(id, session) {
    const response = await this.sendAdminRequest(`/internal/api-keys/${id}`, {}, session);
    return await response.json();
  }
  async listEmailTemplates() {
    const response = await this.sendAdminRequest(`/email-templates`, {}, null);
    const result = await response.json();
    return result.items;
  }
  async updateEmailTemplate(type, data) {
    const result = await this.sendAdminRequest(
      `/email-templates/${type}`,
      {
        method: "PATCH",
        headers: {
          "content-type": "application/json"
        },
        body: JSON.stringify(data)
      },
      null
    );
    return await result.json();
  }
  async resetEmailTemplate(type) {
    await this.sendAdminRequest(
      `/email-templates/${type}`,
      { method: "DELETE" },
      null
    );
  }
  // Team permission definitions methods
  async listTeamPermissionDefinitions() {
    const response = await this.sendAdminRequest(`/team-permission-definitions`, {}, null);
    const result = await response.json();
    return result.items;
  }
  async createTeamPermissionDefinition(data) {
    const response = await this.sendAdminRequest(
      "/team-permission-definitions",
      {
        method: "POST",
        headers: {
          "content-type": "application/json"
        },
        body: JSON.stringify(data)
      },
      null
    );
    return await response.json();
  }
  async updateTeamPermissionDefinition(permissionId, data) {
    const response = await this.sendAdminRequest(
      `/team-permission-definitions/${permissionId}`,
      {
        method: "PATCH",
        headers: {
          "content-type": "application/json"
        },
        body: JSON.stringify(data)
      },
      null
    );
    return await response.json();
  }
  async deleteTeamPermissionDefinition(permissionId) {
    await this.sendAdminRequest(
      `/team-permission-definitions/${permissionId}`,
      { method: "DELETE" },
      null
    );
  }
  async listProjectPermissionDefinitions() {
    const response = await this.sendAdminRequest(`/project-permission-definitions`, {}, null);
    const result = await response.json();
    return result.items;
  }
  async createProjectPermissionDefinition(data) {
    const response = await this.sendAdminRequest(
      "/project-permission-definitions",
      {
        method: "POST",
        headers: {
          "content-type": "application/json"
        },
        body: JSON.stringify(data)
      },
      null
    );
    return await response.json();
  }
  async updateProjectPermissionDefinition(permissionId, data) {
    const response = await this.sendAdminRequest(
      `/project-permission-definitions/${permissionId}`,
      {
        method: "PATCH",
        headers: {
          "content-type": "application/json"
        },
        body: JSON.stringify(data)
      },
      null
    );
    return await response.json();
  }
  async deleteProjectPermissionDefinition(permissionId) {
    await this.sendAdminRequest(
      `/project-permission-definitions/${permissionId}`,
      { method: "DELETE" },
      null
    );
  }
  async getSvixToken() {
    const response = await this.sendAdminRequest(
      "/webhooks/svix-token",
      {
        method: "POST",
        headers: {
          "content-type": "application/json"
        },
        body: JSON.stringify({})
      },
      null
    );
    return await response.json();
  }
  async deleteProject() {
    await this.sendAdminRequest(
      "/internal/projects/current",
      {
        method: "DELETE"
      },
      null
    );
  }
  async getMetrics() {
    const response = await this.sendAdminRequest(
      "/internal/metrics",
      {
        method: "GET"
      },
      null
    );
    return await response.json();
  }
  async sendTestEmail(data) {
    const response = await this.sendAdminRequest(`/internal/send-test-email`, {
      method: "POST",
      headers: {
        "content-type": "application/json"
      },
      body: JSON.stringify(data)
    }, null);
    return await response.json();
  }
  async listSentEmails() {
    const response = await this.sendAdminRequest("/internal/emails", {
      method: "GET"
    }, null);
    return await response.json();
  }
};

// package/@stackframe/react/node_modules/@stackframe/stack-shared/dist/esm/utils/promises.js
function createPromise(callback) {
  let status = "pending";
  let valueOrReason = void 0;
  let resolve = null;
  let reject = null;
  const promise = new Promise((res, rej) => {
    resolve = (value) => {
      if (status !== "pending") return;
      status = "fulfilled";
      valueOrReason = value;
      res(value);
    };
    reject = (reason) => {
      if (status !== "pending") return;
      status = "rejected";
      valueOrReason = reason;
      rej(reason);
    };
  });
  callback(resolve, reject);
  return Object.assign(promise, {
    status,
    ...status === "fulfilled" ? { value: valueOrReason } : {},
    ...status === "rejected" ? { reason: valueOrReason } : {}
  });
}
var resolvedCache = null;
function resolved(value) {
  resolvedCache ?? (resolvedCache = new DependenciesMap());
  if (resolvedCache.has([value])) {
    return resolvedCache.get([value]);
  }
  const res = Object.assign(Promise.resolve(value), {
    status: "fulfilled",
    value
  });
  resolvedCache.set([value], res);
  return res;
}
var rejectedCache = null;
function rejected(reason) {
  rejectedCache ?? (rejectedCache = new DependenciesMap());
  if (rejectedCache.has([reason])) {
    return rejectedCache.get([reason]);
  }
  const promise = Promise.reject(reason);
  ignoreUnhandledRejection(promise);
  const res = Object.assign(promise, {
    status: "rejected",
    reason
  });
  rejectedCache.set([reason], res);
  return res;
}
var neverResolvePromise = pending(new Promise(() => {
}));
function neverResolve() {
  return neverResolvePromise;
}
function pending(promise, options = {}) {
  const res = promise.then(
    (value) => {
      res.status = "fulfilled";
      res.value = value;
      return value;
    },
    (actualReason) => {
      res.status = "rejected";
      res.reason = actualReason;
      throw actualReason;
    }
  );
  res.status = "pending";
  return res;
}
function ignoreUnhandledRejection(promise) {
  promise.catch(() => {
  });
}
async function wait(ms) {
  if (!Number.isFinite(ms) || ms < 0) {
    throw new StackAssertionError(`wait() requires a non-negative integer number of milliseconds to wait. (found: ${ms}ms)`);
  }
  if (ms >= 2 ** 31) {
    throw new StackAssertionError("The maximum timeout for wait() is 2147483647ms (2**31 - 1). (found: ${ms}ms)");
  }
  return await new Promise((resolve) => setTimeout(resolve, ms));
}
async function waitUntil(date) {
  return await wait(date.getTime() - Date.now());
}
function runAsynchronouslyWithAlert(...args) {
  return runAsynchronously(
    args[0],
    {
      ...args[1],
      onError: (error) => {
        var _a4, _b4;
        if (KnownError.isKnownError(error) && typeof process !== "undefined" && ("development" == null ? void 0 : "development".includes("production"))) {
          alert(error.message);
        } else {
          alert(`An unhandled error occurred. Please ${true ? `check the browser console for the full error.` : "report this to the developer."}

${error}`);
        }
        (_b4 = (_a4 = args[1]) == null ? void 0 : _a4.onError) == null ? void 0 : _b4.call(_a4, error);
      }
    },
    ...args.slice(2)
  );
}
function runAsynchronously(promiseOrFunc, options = {}) {
  if (typeof promiseOrFunc === "function") {
    promiseOrFunc = promiseOrFunc();
  }
  const duringError = new Error();
  promiseOrFunc == null ? void 0 : promiseOrFunc.catch((error) => {
    var _a4;
    (_a4 = options.onError) == null ? void 0 : _a4.call(options, error);
    const newError = new StackAssertionError(
      "Uncaught error in asynchronous function: " + error.toString(),
      { cause: error }
    );
    concatStacktraces(newError, duringError);
    if (!options.noErrorLogging) {
      captureError("runAsynchronously", newError);
    }
  });
}
var TimeoutError = class extends Error {
  constructor(ms) {
    super(`Timeout after ${ms}ms`);
    this.ms = ms;
    this.name = "TimeoutError";
  }
};
async function timeout(promise, ms) {
  return await Promise.race([
    promise.then((value) => Result.ok(value)),
    wait(ms).then(() => Result.error(new TimeoutError(ms)))
  ]);
}
async function timeoutThrow(promise, ms) {
  return Result.orThrow(await timeout(promise, ms));
}
function rateLimited(func, options) {
  let waitUntil2 = performance.now();
  let queue = [];
  let addedToQueueCallbacks = /* @__PURE__ */ new Map();
  const next = async () => {
    while (true) {
      if (waitUntil2 > performance.now()) {
        await wait(Math.max(1, waitUntil2 - performance.now() + 1));
      } else if (queue.length === 0) {
        const uuid = generateUuid();
        await new Promise((resolve) => {
          addedToQueueCallbacks.set(uuid, resolve);
        });
        addedToQueueCallbacks.delete(uuid);
      } else {
        break;
      }
    }
    const nextFuncs = options.batchCalls ? queue.splice(0, queue.length) : [queue.shift()];
    const start = performance.now();
    const value = await Result.fromPromise(func());
    const end = performance.now();
    waitUntil2 = Math.max(
      waitUntil2,
      start + (options.throttleMs ?? 0),
      end + (options.gapMs ?? 0)
    );
    for (const nextFunc of nextFuncs) {
      if (value.status === "ok") {
        nextFunc[0](value.data);
      } else {
        nextFunc[1](value.error);
      }
    }
  };
  runAsynchronously(async () => {
    while (true) {
      await next();
    }
  });
  return () => {
    return new Promise((resolve, reject) => {
      waitUntil2 = Math.max(
        waitUntil2,
        performance.now() + (options.debounceMs ?? 0)
      );
      queue.push([resolve, reject]);
      addedToQueueCallbacks.forEach((cb) => cb());
    });
  };
}
function throttled(func, delayMs) {
  let timeout2 = null;
  let nextAvailable = null;
  return async (...args) => {
    while (nextAvailable !== null) {
      await nextAvailable;
    }
    nextAvailable = new Promise((resolve) => {
      timeout2 = setTimeout(() => {
        nextAvailable = null;
        resolve(func(...args));
      }, delayMs);
    });
    return await nextAvailable;
  };
}

// package/@stackframe/react/node_modules/@stackframe/stack-shared/dist/esm/utils/results.js
var Result = {
  fromThrowing,
  fromThrowingAsync,
  fromPromise: promiseToResult,
  ok(data) {
    return {
      status: "ok",
      data
    };
  },
  error(error) {
    return {
      status: "error",
      error
    };
  },
  map: mapResult,
  or: (result, fallback) => {
    return result.status === "ok" ? result.data : fallback;
  },
  orThrow: (result) => {
    if (result.status === "error") {
      throw result.error;
    }
    return result.data;
  },
  orThrowAsync: async (result) => {
    return Result.orThrow(await result);
  },
  retry
};
var AsyncResult = {
  fromThrowing,
  fromPromise: promiseToResult,
  ok: Result.ok,
  error: Result.error,
  pending: pending2,
  map: mapResult,
  or: (result, fallback) => {
    if (result.status === "pending") {
      return fallback;
    }
    return Result.or(result, fallback);
  },
  orThrow: (result) => {
    if (result.status === "pending") {
      throw new Error("Result still pending");
    }
    return Result.orThrow(result);
  },
  retry
};
function pending2(progress) {
  return {
    status: "pending",
    progress
  };
}
async function promiseToResult(promise) {
  try {
    const value = await promise;
    return Result.ok(value);
  } catch (error) {
    return Result.error(error);
  }
}
function fromThrowing(fn) {
  try {
    return Result.ok(fn());
  } catch (error) {
    return Result.error(error);
  }
}
async function fromThrowingAsync(fn) {
  try {
    return Result.ok(await fn());
  } catch (error) {
    return Result.error(error);
  }
}
function mapResult(result, fn) {
  if (result.status === "error") return {
    status: "error",
    error: result.error
  };
  if (result.status === "pending") return {
    status: "pending",
    ..."progress" in result ? { progress: result.progress } : {}
  };
  return Result.ok(fn(result.data));
}
var RetryError = class extends AggregateError {
  constructor(errors) {
    const strings = errors.map((e) => nicify(e));
    const isAllSame = strings.length > 1 && strings.every((s) => s === strings[0]);
    super(
      errors,
      deindent`
      Error after ${errors.length} attempts.
      
      ${isAllSame ? deindent`
        Attempts 1-${errors.length}:
          ${strings[0]}
      ` : strings.map((s, i) => deindent`
          Attempt ${i + 1}:
            ${s}
        `).join("\n\n")}
      `,
      { cause: errors[errors.length - 1] }
    );
    this.errors = errors;
    this.name = "RetryError";
  }
  get attempts() {
    return this.errors.length;
  }
};
RetryError.prototype.name = "RetryError";
async function retry(fn, totalAttempts, { exponentialDelayBase = 1e3 } = {}) {
  const errors = [];
  for (let i = 0; i < totalAttempts; i++) {
    const res = await fn(i);
    if (res.status === "ok") {
      return Object.assign(Result.ok(res.data), { attempts: i + 1 });
    } else {
      errors.push(res.error);
      if (i < totalAttempts - 1) {
        await wait((Math.random() + 0.5) * exponentialDelayBase * 2 ** i);
      }
    }
  }
  return Object.assign(Result.error(new RetryError(errors)), { attempts: totalAttempts });
}

// package/@stackframe/react/node_modules/@stackframe/stack-shared/dist/esm/utils/maps.js
var WeakRefIfAvailable = class {
  constructor(value) {
    if (typeof WeakRef === "undefined") {
      this._ref = { deref: () => value };
    } else {
      this._ref = new WeakRef(value);
    }
  }
  deref() {
    return this._ref.deref();
  }
};
var _a3;
var _b3;
var IterableWeakMap = class {
  constructor(entries) {
    this[_a3] = "IterableWeakMap";
    const mappedEntries = entries == null ? void 0 : entries.map((e) => [e[0], { value: e[1], keyRef: new WeakRefIfAvailable(e[0]) }]);
    this._weakMap = new WeakMap(mappedEntries ?? []);
    this._keyRefs = new Set((mappedEntries == null ? void 0 : mappedEntries.map((e) => e[1].keyRef)) ?? []);
  }
  get(key) {
    var _a4;
    return (_a4 = this._weakMap.get(key)) == null ? void 0 : _a4.value;
  }
  set(key, value) {
    const existing = this._weakMap.get(key);
    const updated = { value, keyRef: (existing == null ? void 0 : existing.keyRef) ?? new WeakRefIfAvailable(key) };
    this._weakMap.set(key, updated);
    this._keyRefs.add(updated.keyRef);
    return this;
  }
  delete(key) {
    const res = this._weakMap.get(key);
    if (res) {
      this._weakMap.delete(key);
      this._keyRefs.delete(res.keyRef);
      return true;
    }
    return false;
  }
  has(key) {
    return this._weakMap.has(key) && this._keyRefs.has(this._weakMap.get(key).keyRef);
  }
  *[(_b3 = Symbol.iterator, _a3 = Symbol.toStringTag, _b3)]() {
    for (const keyRef of this._keyRefs) {
      const key = keyRef.deref();
      const existing = key ? this._weakMap.get(key) : void 0;
      if (!key) {
        this._keyRefs.delete(keyRef);
      } else if (existing) {
        yield [key, existing.value];
      }
    }
  }
};
var _a22;
var _b22;
var MaybeWeakMap = class {
  constructor(entries) {
    this[_a22] = "MaybeWeakMap";
    const entriesArray = [...entries ?? []];
    this._primitiveMap = new Map(entriesArray.filter((e) => !this._isAllowedInWeakMap(e[0])));
    this._weakMap = new IterableWeakMap(entriesArray.filter((e) => this._isAllowedInWeakMap(e[0])));
  }
  _isAllowedInWeakMap(key) {
    return typeof key === "object" && key !== null || typeof key === "symbol" && Symbol.keyFor(key) === void 0;
  }
  get(key) {
    if (this._isAllowedInWeakMap(key)) {
      return this._weakMap.get(key);
    } else {
      return this._primitiveMap.get(key);
    }
  }
  set(key, value) {
    if (this._isAllowedInWeakMap(key)) {
      this._weakMap.set(key, value);
    } else {
      this._primitiveMap.set(key, value);
    }
    return this;
  }
  delete(key) {
    if (this._isAllowedInWeakMap(key)) {
      return this._weakMap.delete(key);
    } else {
      return this._primitiveMap.delete(key);
    }
  }
  has(key) {
    if (this._isAllowedInWeakMap(key)) {
      return this._weakMap.has(key);
    } else {
      return this._primitiveMap.has(key);
    }
  }
  *[(_b22 = Symbol.iterator, _a22 = Symbol.toStringTag, _b22)]() {
    yield* this._primitiveMap;
    yield* this._weakMap;
  }
};
var _a32;
var _b32;
var DependenciesMap = class {
  constructor() {
    this._inner = { map: new MaybeWeakMap(), hasValue: false, value: void 0 };
    this[_a32] = "DependenciesMap";
  }
  _valueToResult(inner) {
    if (inner.hasValue) {
      return Result.ok(inner.value);
    } else {
      return Result.error(void 0);
    }
  }
  _unwrapFromInner(dependencies, inner) {
    if (dependencies.length === 0) {
      return this._valueToResult(inner);
    } else {
      const [key, ...rest] = dependencies;
      const newInner = inner.map.get(key);
      if (!newInner) {
        return Result.error(void 0);
      }
      return this._unwrapFromInner(rest, newInner);
    }
  }
  _setInInner(dependencies, value, inner) {
    if (dependencies.length === 0) {
      const res = this._valueToResult(inner);
      if (value.status === "ok") {
        inner.hasValue = true;
        inner.value = value.data;
      } else {
        inner.hasValue = false;
        inner.value = void 0;
      }
      return res;
    } else {
      const [key, ...rest] = dependencies;
      let newInner = inner.map.get(key);
      if (!newInner) {
        inner.map.set(key, newInner = { map: new MaybeWeakMap(), hasValue: false, value: void 0 });
      }
      return this._setInInner(rest, value, newInner);
    }
  }
  *_iterateInner(dependencies, inner) {
    if (inner.hasValue) {
      yield [dependencies, inner.value];
    }
    for (const [key, value] of inner.map) {
      yield* this._iterateInner([...dependencies, key], value);
    }
  }
  get(dependencies) {
    return Result.or(this._unwrapFromInner(dependencies, this._inner), void 0);
  }
  set(dependencies, value) {
    this._setInInner(dependencies, Result.ok(value), this._inner);
    return this;
  }
  delete(dependencies) {
    return this._setInInner(dependencies, Result.error(void 0), this._inner).status === "ok";
  }
  has(dependencies) {
    return this._unwrapFromInner(dependencies, this._inner).status === "ok";
  }
  clear() {
    this._inner = { map: new MaybeWeakMap(), hasValue: false, value: void 0 };
  }
  *[(_b32 = Symbol.iterator, _a32 = Symbol.toStringTag, _b32)]() {
    yield* this._iterateInner([], this._inner);
  }
};

export {
  WeakRefIfAvailable,
  IterableWeakMap,
  MaybeWeakMap,
  DependenciesMap,
  createPromise,
  resolved,
  rejected,
  neverResolve,
  pending,
  ignoreUnhandledRejection,
  wait,
  waitUntil,
  runAsynchronouslyWithAlert,
  runAsynchronously,
  timeout,
  timeoutThrow,
  rateLimited,
  throttled,
  Result,
  AsyncResult,
  Store,
  storeLock,
  AsyncStore,
  AccessToken,
  RefreshToken,
  InternalSession,
  decodeBasicAuthorizationHeader,
  StackClientInterface,
  StackServerInterface,
  StackAdminInterface
};
//# sourceMappingURL=chunk-GVADET3V.js.map
