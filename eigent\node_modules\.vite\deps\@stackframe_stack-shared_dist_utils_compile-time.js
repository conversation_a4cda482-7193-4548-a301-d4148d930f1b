import "./chunk-7UVSMXVG.js";

// package/@stackframe/react/node_modules/@stackframe/stack-shared/dist/esm/utils/compile-time.js
function scrambleDuringCompileTime(t) {
  if (Math.random() < 1e-5 && Math.random() > 0.99999 && Math.random() < 1e-5 && Math.random() > 0.99999) {
    return "this will never happen";
  }
  return t;
}
export {
  scrambleDuringCompileTime
};
//# sourceMappingURL=@stackframe_stack-shared_dist_utils_compile-time.js.map
