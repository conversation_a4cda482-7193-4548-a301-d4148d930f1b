{"version": 3, "sources": ["../../browser-image-compression/lib/copyExifWithoutOrientation.js", "../../browser-image-compression/node_modules/uzip/UZIP.js", "../../browser-image-compression/lib/UPNG.js", "../../browser-image-compression/lib/canvastobmp.js", "../../browser-image-compression/lib/config/browser-name.js", "../../browser-image-compression/lib/config/max-canvas-size.js", "../../browser-image-compression/lib/utils.js", "../../browser-image-compression/lib/image-compression.js", "../../browser-image-compression/lib/web-worker.js", "../../browser-image-compression/lib/index.js"], "sourcesContent": ["// https://gist.github.com/tonytonyjan/ffb7cd0e82cb293b843ece7e79364233\n// Copyright (c) 2022 <PERSON><PERSON> <<EMAIL>>\n\nexport default async function copyExifWithoutOrientation(srcBlob, destBlob) {\n  const exif = await getApp1Segment(srcBlob);\n  return new Blob([destBlob.slice(0, 2), exif, destBlob.slice(2)], {\n    type: 'image/jpeg',\n  });\n}\n\nconst SOI = 0xffd8;\nconst SOS = 0xffda;\nconst APP1 = 0xffe1;\nconst EXIF = 0x45786966;\nconst LITTLE_ENDIAN = 0x4949;\nconst BIG_ENDIAN = 0x4d4d;\nconst TAG_ID_ORIENTATION = 0x0112;\nconst TAG_TYPE_SHORT = 3;\nconst getApp1Segment = (blob) => new Promise((resolve, reject) => {\n  const reader = new FileReader();\n  reader.addEventListener('load', ({ target: { result: buffer } }) => {\n    const view = new DataView(buffer);\n    let offset = 0;\n    if (view.getUint16(offset) !== SOI) return reject('not a valid JPEG');\n    offset += 2;\n\n    while (true) {\n      const marker = view.getUint16(offset);\n      if (marker === SOS) break;\n\n      const size = view.getUint16(offset + 2);\n      if (marker === APP1 && view.getUint32(offset + 4) === EXIF) {\n        const tiffOffset = offset + 10;\n        let littleEndian;\n        switch (view.getUint16(tiffOffset)) {\n          case LITTLE_ENDIAN:\n            littleEndian = true;\n            break;\n          case BIG_ENDIAN:\n            littleEndian = false;\n            break;\n          default:\n            return reject('TIFF header contains invalid endian');\n        }\n        if (view.getUint16(tiffOffset + 2, littleEndian) !== 0x2a) { return reject('TIFF header contains invalid version'); }\n\n        const ifd0Offset = view.getUint32(tiffOffset + 4, littleEndian);\n        const endOfTagsOffset = tiffOffset\n              + ifd0Offset\n              + 2\n              + view.getUint16(tiffOffset + ifd0Offset, littleEndian) * 12;\n        for (\n          let i = tiffOffset + ifd0Offset + 2;\n          i < endOfTagsOffset;\n          i += 12\n        ) {\n          const tagId = view.getUint16(i, littleEndian);\n          if (tagId == TAG_ID_ORIENTATION) {\n            if (view.getUint16(i + 2, littleEndian) !== TAG_TYPE_SHORT) { return reject('Orientation data type is invalid'); }\n\n            if (view.getUint32(i + 4, littleEndian) !== 1) { return reject('Orientation data count is invalid'); }\n\n            view.setUint16(i + 8, 1, littleEndian);\n            break;\n          }\n        }\n        return resolve(buffer.slice(offset, offset + 2 + size));\n      }\n      offset += 2 + size;\n    }\n    return resolve(new Blob());\n  });\n  reader.readAsArrayBuffer(blob);\n});\n", "\r\n\r\nvar UZIP = {};\r\nif(typeof module == \"object\") module.exports = UZIP;\r\n\r\n\r\nUZIP[\"parse\"] = function(buf, onlyNames)\t// ArrayBuffer\r\n{\r\n\tvar rUs = UZIP.bin.readUshort, rUi = UZIP.bin.readUint, o = 0, out = {};\r\n\tvar data = new Uint8Array(buf);\r\n\tvar eocd = data.length-4;\r\n\t\r\n\twhile(rUi(data, eocd)!=0x06054b50) eocd--;\r\n\t\r\n\tvar o = eocd;\r\n\to+=4;\t// sign  = 0x06054b50\r\n\to+=4;  // disks = 0;\r\n\tvar cnu = rUs(data, o);  o+=2;\r\n\tvar cnt = rUs(data, o);  o+=2;\r\n\t\t\t\r\n\tvar csize = rUi(data, o);  o+=4;\r\n\tvar coffs = rUi(data, o);  o+=4;\r\n\t\r\n\to = coffs;\r\n\tfor(var i=0; i<cnu; i++)\r\n\t{\r\n\t\tvar sign = rUi(data, o);  o+=4;\r\n\t\to += 4;  // versions;\r\n\t\to += 4;  // flag + compr\r\n\t\to += 4;  // time\r\n\t\t\r\n\t\tvar crc32 = rUi(data, o);  o+=4;\r\n\t\tvar csize = rUi(data, o);  o+=4;\r\n\t\tvar usize = rUi(data, o);  o+=4;\r\n\t\t\r\n\t\tvar nl = rUs(data, o), el = rUs(data, o+2), cl = rUs(data, o+4);  o += 6;  // name, extra, comment\r\n\t\to += 8;  // disk, attribs\r\n\t\t\r\n\t\tvar roff = rUi(data, o);  o+=4;\r\n\t\to += nl + el + cl;\r\n\t\t\r\n\t\tUZIP._readLocal(data, roff, out, csize, usize, onlyNames);\r\n\t}\r\n\t//console.log(out);\r\n\treturn out;\r\n}\r\n\r\nUZIP._readLocal = function(data, o, out, csize, usize, onlyNames)\r\n{\r\n\tvar rUs = UZIP.bin.readUshort, rUi = UZIP.bin.readUint;\r\n\tvar sign  = rUi(data, o);  o+=4;\r\n\tvar ver   = rUs(data, o);  o+=2;\r\n\tvar gpflg = rUs(data, o);  o+=2;\r\n\t//if((gpflg&8)!=0) throw \"unknown sizes\";\r\n\tvar cmpr  = rUs(data, o);  o+=2;\r\n\t\r\n\tvar time  = rUi(data, o);  o+=4;\r\n\t\r\n\tvar crc32 = rUi(data, o);  o+=4;\r\n\t//var csize = rUi(data, o);  o+=4;\r\n\t//var usize = rUi(data, o);  o+=4;\r\n\to+=8;\r\n\t\t\r\n\tvar nlen  = rUs(data, o);  o+=2;\r\n\tvar elen  = rUs(data, o);  o+=2;\r\n\t\t\r\n\tvar name =  UZIP.bin.readUTF8(data, o, nlen);  o+=nlen;  //console.log(name);\r\n\to += elen;\r\n\t\t\t\r\n\t//console.log(sign.toString(16), ver, gpflg, cmpr, crc32.toString(16), \"csize, usize\", csize, usize, nlen, elen, name, o);\r\n\tif(onlyNames) {  out[name]={size:usize, csize:csize};  return;  }   \r\n\tvar file = new Uint8Array(data.buffer, o);\r\n\tif(false) {}\r\n\telse if(cmpr==0) out[name] = new Uint8Array(file.buffer.slice(o, o+csize));\r\n\telse if(cmpr==8) {\r\n\t\tvar buf = new Uint8Array(usize);  UZIP.inflateRaw(file, buf);\r\n\t\t/*var nbuf = pako[\"inflateRaw\"](file);\r\n\t\tif(usize>8514000) {\r\n\t\t\t//console.log(PUtils.readASCII(buf , 8514500, 500));\r\n\t\t\t//console.log(PUtils.readASCII(nbuf, 8514500, 500));\r\n\t\t}\r\n\t\tfor(var i=0; i<buf.length; i++) if(buf[i]!=nbuf[i]) {  console.log(buf.length, nbuf.length, usize, i);  throw \"e\";  }\r\n\t\t*/\r\n\t\tout[name] = buf;\r\n\t}\r\n\telse throw \"unknown compression method: \"+cmpr;\r\n}\r\n\r\nUZIP.inflateRaw = function(file, buf) {  return UZIP.F.inflate(file, buf);  }\r\nUZIP.inflate    = function(file, buf) { \r\n\tvar CMF = file[0], FLG = file[1];\r\n\tvar CM = (CMF&15), CINFO = (CMF>>>4);\r\n\t//console.log(CM, CINFO,CMF,FLG);\r\n\treturn UZIP.inflateRaw(new Uint8Array(file.buffer, file.byteOffset+2, file.length-6), buf);  \r\n}\r\nUZIP.deflate    = function(data, opts/*, buf, off*/) {\r\n\tif(opts==null) opts={level:6};\r\n\tvar off=0, buf=new Uint8Array(50+Math.floor(data.length*1.1));\r\n\tbuf[off]=120;  buf[off+1]=156;  off+=2;\r\n\toff = UZIP.F.deflateRaw(data, buf, off, opts.level);\r\n\tvar crc = UZIP.adler(data, 0, data.length);\r\n\tbuf[off+0]=((crc>>>24)&255); \r\n\tbuf[off+1]=((crc>>>16)&255); \r\n\tbuf[off+2]=((crc>>> 8)&255); \r\n\tbuf[off+3]=((crc>>> 0)&255); \t\r\n\treturn new Uint8Array(buf.buffer, 0, off+4);\r\n}\r\nUZIP.deflateRaw = function(data, opts) {\r\n\tif(opts==null) opts={level:6};\r\n\tvar buf=new Uint8Array(50+Math.floor(data.length*1.1));\r\n\tvar off = UZIP.F.deflateRaw(data, buf, off, opts.level);\r\n\treturn new Uint8Array(buf.buffer, 0, off);\r\n}\r\n\r\n\r\nUZIP.encode = function(obj, noCmpr) {\r\n\tif(noCmpr==null) noCmpr=false;\r\n\tvar tot = 0, wUi = UZIP.bin.writeUint, wUs = UZIP.bin.writeUshort;\r\n\tvar zpd = {};\r\n\tfor(var p in obj) {  var cpr = !UZIP._noNeed(p) && !noCmpr, buf = obj[p], crc = UZIP.crc.crc(buf,0,buf.length); \r\n\t\tzpd[p] = {  cpr:cpr, usize:buf.length, crc:crc, file: (cpr ? UZIP.deflateRaw(buf) : buf)  };  }\r\n\t\r\n\tfor(var p in zpd) tot += zpd[p].file.length + 30 + 46 + 2*UZIP.bin.sizeUTF8(p);\r\n\ttot +=  22;\r\n\t\r\n\tvar data = new Uint8Array(tot), o = 0;\r\n\tvar fof = []\r\n\t\r\n\tfor(var p in zpd) {\r\n\t\tvar file = zpd[p];  fof.push(o);\r\n\t\to = UZIP._writeHeader(data, o, p, file, 0);\r\n\t}\r\n\tvar i=0, ioff = o;\r\n\tfor(var p in zpd) {\r\n\t\tvar file = zpd[p];  fof.push(o);\r\n\t\to = UZIP._writeHeader(data, o, p, file, 1, fof[i++]);\t\t\r\n\t}\r\n\tvar csize = o-ioff;\r\n\t\r\n\twUi(data, o, 0x06054b50);  o+=4;\r\n\to += 4;  // disks\r\n\twUs(data, o, i);  o += 2;\r\n\twUs(data, o, i);  o += 2;\t// number of c d records\r\n\twUi(data, o, csize);  o += 4;\r\n\twUi(data, o, ioff );  o += 4;\r\n\to += 2;\r\n\treturn data.buffer;\r\n}\r\n// no need to compress .PNG, .ZIP, .JPEG ....\r\nUZIP._noNeed = function(fn) {  var ext = fn.split(\".\").pop().toLowerCase();  return \"png,jpg,jpeg,zip\".indexOf(ext)!=-1;  }\r\n\r\nUZIP._writeHeader = function(data, o, p, obj, t, roff)\r\n{\r\n\tvar wUi = UZIP.bin.writeUint, wUs = UZIP.bin.writeUshort;\r\n\tvar file = obj.file;\r\n\t\r\n\twUi(data, o, t==0 ? 0x04034b50 : 0x02014b50);  o+=4; // sign\r\n\tif(t==1) o+=2;  // ver made by\r\n\twUs(data, o, 20);  o+=2;\t// ver\r\n\twUs(data, o,  0);  o+=2;    // gflip\r\n\twUs(data, o,  obj.cpr?8:0);  o+=2;\t// cmpr\r\n\t\t\r\n\twUi(data, o,  0);  o+=4;\t// time\t\t\r\n\twUi(data, o, obj.crc);  o+=4;\t// crc32\r\n\twUi(data, o, file.length);  o+=4;\t// csize\r\n\twUi(data, o, obj.usize);  o+=4;\t// usize\r\n\t\t\r\n\twUs(data, o, UZIP.bin.sizeUTF8(p));  o+=2;\t// nlen\r\n\twUs(data, o, 0);  o+=2;\t// elen\r\n\t\r\n\tif(t==1) {\r\n\t\to += 2;  // comment length\r\n\t\to += 2;  // disk number\r\n\t\to += 6;  // attributes\r\n\t\twUi(data, o, roff);  o+=4;\t// usize\r\n\t}\r\n\tvar nlen = UZIP.bin.writeUTF8(data, o, p);  o+= nlen;\t\r\n\tif(t==0) {  data.set(file, o);  o += file.length;  }\r\n\treturn o;\r\n}\r\n\r\n\r\n\r\n\r\n\r\nUZIP.crc = {\r\n\ttable : ( function() {\r\n\t   var tab = new Uint32Array(256);\r\n\t   for (var n=0; n<256; n++) {\r\n\t\t\tvar c = n;\r\n\t\t\tfor (var k=0; k<8; k++) {\r\n\t\t\t\tif (c & 1)  c = 0xedb88320 ^ (c >>> 1);\r\n\t\t\t\telse        c = c >>> 1;\r\n\t\t\t}\r\n\t\t\ttab[n] = c;  }    \r\n\t\treturn tab;  })(),\r\n\tupdate : function(c, buf, off, len) {\r\n\t\tfor (var i=0; i<len; i++)  c = UZIP.crc.table[(c ^ buf[off+i]) & 0xff] ^ (c >>> 8);\r\n\t\treturn c;\r\n\t},\r\n\tcrc : function(b,o,l)  {  return UZIP.crc.update(0xffffffff,b,o,l) ^ 0xffffffff;  }\r\n}\r\nUZIP.adler = function(data,o,len) {\r\n\tvar a = 1, b = 0;\r\n\tvar off = o, end=o+len;\r\n\twhile(off<end) {\r\n\t\tvar eend = Math.min(off+5552, end);\r\n\t\twhile(off<eend) {\r\n\t\t\ta += data[off++];\r\n\t\t\tb += a;\r\n\t\t}\r\n\t\ta=a%65521;\r\n\t\tb=b%65521;\r\n\t}\r\n    return (b << 16) | a;\r\n}\r\n\r\nUZIP.bin = {\r\n\treadUshort : function(buff,p)  {  return (buff[p]) | (buff[p+1]<<8);  },\r\n\twriteUshort: function(buff,p,n){  buff[p] = (n)&255;  buff[p+1] = (n>>8)&255;  },\r\n\treadUint   : function(buff,p)  {  return (buff[p+3]*(256*256*256)) + ((buff[p+2]<<16) | (buff[p+1]<< 8) | buff[p]);  },\r\n\twriteUint  : function(buff,p,n){  buff[p]=n&255;  buff[p+1]=(n>>8)&255;  buff[p+2]=(n>>16)&255;  buff[p+3]=(n>>24)&255;  },\r\n\treadASCII  : function(buff,p,l){  var s = \"\";  for(var i=0; i<l; i++) s += String.fromCharCode(buff[p+i]);  return s;    },\r\n\twriteASCII : function(data,p,s){  for(var i=0; i<s.length; i++) data[p+i] = s.charCodeAt(i);  },\r\n\tpad : function(n) { return n.length < 2 ? \"0\" + n : n; },\r\n\treadUTF8 : function(buff, p, l) {\r\n\t\tvar s = \"\", ns;\r\n\t\tfor(var i=0; i<l; i++) s += \"%\" + UZIP.bin.pad(buff[p+i].toString(16));\r\n\t\ttry {  ns = decodeURIComponent(s); }\r\n\t\tcatch(e) {  return UZIP.bin.readASCII(buff, p, l);  }\r\n\t\treturn  ns;\r\n\t},\r\n\twriteUTF8 : function(buff, p, str) {\r\n\t\tvar strl = str.length, i=0;\r\n\t\tfor(var ci=0; ci<strl; ci++)\r\n\t\t{\r\n\t\t\tvar code = str.charCodeAt(ci);\r\n\t\t\tif     ((code&(0xffffffff-(1<< 7)+1))==0) {  buff[p+i] = (     code     );  i++;  }\r\n\t\t\telse if((code&(0xffffffff-(1<<11)+1))==0) {  buff[p+i] = (192|(code>> 6));  buff[p+i+1] = (128|((code>> 0)&63));  i+=2;  }\r\n\t\t\telse if((code&(0xffffffff-(1<<16)+1))==0) {  buff[p+i] = (224|(code>>12));  buff[p+i+1] = (128|((code>> 6)&63));  buff[p+i+2] = (128|((code>>0)&63));  i+=3;  }\r\n\t\t\telse if((code&(0xffffffff-(1<<21)+1))==0) {  buff[p+i] = (240|(code>>18));  buff[p+i+1] = (128|((code>>12)&63));  buff[p+i+2] = (128|((code>>6)&63));  buff[p+i+3] = (128|((code>>0)&63)); i+=4;  }\r\n\t\t\telse throw \"e\";\r\n\t\t}\r\n\t\treturn i;\r\n\t},\r\n\tsizeUTF8 : function(str) {\r\n\t\tvar strl = str.length, i=0;\r\n\t\tfor(var ci=0; ci<strl; ci++)\r\n\t\t{\r\n\t\t\tvar code = str.charCodeAt(ci);\r\n\t\t\tif     ((code&(0xffffffff-(1<< 7)+1))==0) {  i++ ;  }\r\n\t\t\telse if((code&(0xffffffff-(1<<11)+1))==0) {  i+=2;  }\r\n\t\t\telse if((code&(0xffffffff-(1<<16)+1))==0) {  i+=3;  }\r\n\t\t\telse if((code&(0xffffffff-(1<<21)+1))==0) {  i+=4;  }\r\n\t\t\telse throw \"e\";\r\n\t\t}\r\n\t\treturn i;\r\n\t}\r\n}\r\n\r\n\r\n\r\n\r\n\r\nUZIP.F = {};\r\n\r\nUZIP.F.deflateRaw = function(data, out, opos, lvl) {\t\r\n\tvar opts = [\r\n\t/*\r\n\t\t ush good_length; /* reduce lazy search above this match length \r\n\t\t ush max_lazy;    /* do not perform lazy search above this match length \r\n         ush nice_length; /* quit search above this match length \r\n\t*/\r\n\t/*      good lazy nice chain */\r\n\t/* 0 */ [ 0,   0,   0,    0,0],  /* store only */\r\n\t/* 1 */ [ 4,   4,   8,    4,0], /* max speed, no lazy matches */\r\n\t/* 2 */ [ 4,   5,  16,    8,0],\r\n\t/* 3 */ [ 4,   6,  16,   16,0],\r\n\r\n\t/* 4 */ [ 4,  10,  16,   32,0],  /* lazy matches */\r\n\t/* 5 */ [ 8,  16,  32,   32,0],\r\n\t/* 6 */ [ 8,  16, 128,  128,0],\r\n\t/* 7 */ [ 8,  32, 128,  256,0],\r\n\t/* 8 */ [32, 128, 258, 1024,1],\r\n\t/* 9 */ [32, 258, 258, 4096,1]]; /* max compression */\r\n\t\r\n\tvar opt = opts[lvl];\r\n\t\r\n\t\r\n\tvar U = UZIP.F.U, goodIndex = UZIP.F._goodIndex, hash = UZIP.F._hash, putsE = UZIP.F._putsE;\r\n\tvar i = 0, pos = opos<<3, cvrd = 0, dlen = data.length;\r\n\t\r\n\tif(lvl==0) {\r\n\t\twhile(i<dlen) {   var len = Math.min(0xffff, dlen-i);\r\n\t\t\tputsE(out, pos, (i+len==dlen ? 1 : 0));  pos = UZIP.F._copyExact(data, i, len, out, pos+8);  i += len;  }\r\n\t\treturn pos>>>3;\r\n\t}\r\n\r\n\tvar lits = U.lits, strt=U.strt, prev=U.prev, li=0, lc=0, bs=0, ebits=0, c=0, nc=0;  // last_item, literal_count, block_start\r\n\tif(dlen>2) {  nc=UZIP.F._hash(data,0);  strt[nc]=0;  }\r\n\tvar nmch=0,nmci=0;\r\n\t\r\n\tfor(i=0; i<dlen; i++)  {\r\n\t\tc = nc;\r\n\t\t//*\r\n\t\tif(i+1<dlen-2) {\r\n\t\t\tnc = UZIP.F._hash(data, i+1);\r\n\t\t\tvar ii = ((i+1)&0x7fff);\r\n\t\t\tprev[ii]=strt[nc];\r\n\t\t\tstrt[nc]=ii;\r\n\t\t} //*/\r\n\t\tif(cvrd<=i) {\r\n\t\t\tif((li>14000 || lc>26697) && (dlen-i)>100) {\r\n\t\t\t\tif(cvrd<i) {  lits[li]=i-cvrd;  li+=2;  cvrd=i;  }\r\n\t\t\t\tpos = UZIP.F._writeBlock(((i==dlen-1) || (cvrd==dlen))?1:0, lits, li, ebits, data,bs,i-bs, out, pos);  li=lc=ebits=0;  bs=i;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\tvar mch = 0;\r\n\t\t\t//if(nmci==i) mch= nmch;  else \r\n\t\t\tif(i<dlen-2) mch = UZIP.F._bestMatch(data, i, prev, c, Math.min(opt[2],dlen-i), opt[3]);\r\n\t\t\t/*\r\n\t\t\tif(mch!=0 && opt[4]==1 && (mch>>>16)<opt[1] && i+1<dlen-2) {\r\n\t\t\t\tnmch = UZIP.F._bestMatch(data, i+1, prev, nc, opt[2], opt[3]);  nmci=i+1;\r\n\t\t\t\t//var mch2 = UZIP.F._bestMatch(data, i+2, prev, nnc);  //nmci=i+1;\r\n\t\t\t\tif((nmch>>>16)>(mch>>>16)) mch=0;\r\n\t\t\t}//*/\r\n\t\t\tvar len = mch>>>16, dst = mch&0xffff;  //if(i-dst<0) throw \"e\";\r\n\t\t\tif(mch!=0) { \r\n\t\t\t\tvar len = mch>>>16, dst = mch&0xffff;  //if(i-dst<0) throw \"e\";\r\n\t\t\t\tvar lgi = goodIndex(len, U.of0);  U.lhst[257+lgi]++; \r\n\t\t\t\tvar dgi = goodIndex(dst, U.df0);  U.dhst[    dgi]++;  ebits += U.exb[lgi] + U.dxb[dgi]; \r\n\t\t\t\tlits[li] = (len<<23)|(i-cvrd);  lits[li+1] = (dst<<16)|(lgi<<8)|dgi;  li+=2;\r\n\t\t\t\tcvrd = i + len;  \r\n\t\t\t}\r\n\t\t\telse {\tU.lhst[data[i]]++;  }\r\n\t\t\tlc++;\r\n\t\t}\r\n\t}\r\n\tif(bs!=i || data.length==0) {\r\n\t\tif(cvrd<i) {  lits[li]=i-cvrd;  li+=2;  cvrd=i;  }\r\n\t\tpos = UZIP.F._writeBlock(1, lits, li, ebits, data,bs,i-bs, out, pos);  li=0;  lc=0;  li=lc=ebits=0;  bs=i;\r\n\t}\r\n\twhile((pos&7)!=0) pos++;\r\n\treturn pos>>>3;\r\n}\r\nUZIP.F._bestMatch = function(data, i, prev, c, nice, chain) {\r\n\tvar ci = (i&0x7fff), pi=prev[ci];  \r\n\t//console.log(\"----\", i);\r\n\tvar dif = ((ci-pi + (1<<15)) & 0x7fff);  if(pi==ci || c!=UZIP.F._hash(data,i-dif)) return 0;\r\n\tvar tl=0, td=0;  // top length, top distance\r\n\tvar dlim = Math.min(0x7fff, i);\r\n\twhile(dif<=dlim && --chain!=0 && pi!=ci /*&& c==UZIP.F._hash(data,i-dif)*/) {\r\n\t\tif(tl==0 || (data[i+tl]==data[i+tl-dif])) {\r\n\t\t\tvar cl = UZIP.F._howLong(data, i, dif);\r\n\t\t\tif(cl>tl) {  \r\n\t\t\t\ttl=cl;  td=dif;  if(tl>=nice) break;    //* \r\n\t\t\t\tif(dif+2<cl) cl = dif+2;\r\n\t\t\t\tvar maxd = 0; // pi does not point to the start of the word\r\n\t\t\t\tfor(var j=0; j<cl-2; j++) {\r\n\t\t\t\t\tvar ei =  (i-dif+j+ (1<<15)) & 0x7fff;\r\n\t\t\t\t\tvar li = prev[ei];\r\n\t\t\t\t\tvar curd = (ei-li + (1<<15)) & 0x7fff;\r\n\t\t\t\t\tif(curd>maxd) {  maxd=curd;  pi = ei; }\r\n\t\t\t\t}  //*/\r\n\t\t\t}\r\n\t\t}\r\n\t\t\r\n\t\tci=pi;  pi = prev[ci];\r\n\t\tdif += ((ci-pi + (1<<15)) & 0x7fff);\r\n\t}\r\n\treturn (tl<<16)|td;\r\n}\r\nUZIP.F._howLong = function(data, i, dif) {\r\n\tif(data[i]!=data[i-dif] || data[i+1]!=data[i+1-dif] || data[i+2]!=data[i+2-dif]) return 0;\r\n\tvar oi=i, l = Math.min(data.length, i+258);  i+=3;\r\n\t//while(i+4<l && data[i]==data[i-dif] && data[i+1]==data[i+1-dif] && data[i+2]==data[i+2-dif] && data[i+3]==data[i+3-dif]) i+=4;\r\n\twhile(i<l && data[i]==data[i-dif]) i++;\r\n\treturn i-oi;\r\n}\r\nUZIP.F._hash = function(data, i) {\r\n\treturn (((data[i]<<8) | data[i+1])+(data[i+2]<<4))&0xffff;\r\n\t//var hash_shift = 0, hash_mask = 255;\r\n\t//var h = data[i+1] % 251;\r\n\t//h = (((h << 8) + data[i+2]) % 251);\r\n\t//h = (((h << 8) + data[i+2]) % 251);\r\n\t//h = ((h<<hash_shift) ^ (c) ) & hash_mask;\r\n\t//return h | (data[i]<<8);\r\n\t//return (data[i] | (data[i+1]<<8));\r\n}\r\n//UZIP.___toth = 0;\r\nUZIP.saved = 0;\r\nUZIP.F._writeBlock = function(BFINAL, lits, li, ebits, data,o0,l0, out, pos) {\r\n\tvar U = UZIP.F.U, putsF = UZIP.F._putsF, putsE = UZIP.F._putsE;\r\n\t\r\n\t//*\r\n\tvar T, ML, MD, MH, numl, numd, numh, lset, dset;  U.lhst[256]++;\r\n\tT = UZIP.F.getTrees(); ML=T[0]; MD=T[1]; MH=T[2]; numl=T[3]; numd=T[4]; numh=T[5]; lset=T[6]; dset=T[7];\r\n\t\r\n\tvar cstSize = (((pos+3)&7)==0 ? 0 : 8-((pos+3)&7)) + 32 + (l0<<3);\r\n\tvar fxdSize = ebits + UZIP.F.contSize(U.fltree, U.lhst) + UZIP.F.contSize(U.fdtree, U.dhst);\r\n\tvar dynSize = ebits + UZIP.F.contSize(U.ltree , U.lhst) + UZIP.F.contSize(U.dtree , U.dhst);\r\n\tdynSize    += 14 + 3*numh + UZIP.F.contSize(U.itree, U.ihst) + (U.ihst[16]*2 + U.ihst[17]*3 + U.ihst[18]*7);\r\n\t\r\n\tfor(var j=0; j<286; j++) U.lhst[j]=0;   for(var j=0; j<30; j++) U.dhst[j]=0;   for(var j=0; j<19; j++) U.ihst[j]=0;\r\n\t//*/\r\n\tvar BTYPE = (cstSize<fxdSize && cstSize<dynSize) ? 0 : ( fxdSize<dynSize ? 1 : 2 );\r\n\tputsF(out, pos, BFINAL);  putsF(out, pos+1, BTYPE);  pos+=3;\r\n\t\r\n\tvar opos = pos;\r\n\tif(BTYPE==0) {\r\n\t\twhile((pos&7)!=0) pos++;\r\n\t\tpos = UZIP.F._copyExact(data, o0, l0, out, pos);\r\n\t}\r\n\telse {\r\n\t\tvar ltree, dtree;\r\n\t\tif(BTYPE==1) {  ltree=U.fltree;  dtree=U.fdtree;  }\r\n\t\tif(BTYPE==2) {\t\r\n\t\t\tUZIP.F.makeCodes(U.ltree, ML);  UZIP.F.revCodes(U.ltree, ML);\r\n\t\t\tUZIP.F.makeCodes(U.dtree, MD);  UZIP.F.revCodes(U.dtree, MD);\r\n\t\t\tUZIP.F.makeCodes(U.itree, MH);  UZIP.F.revCodes(U.itree, MH);\r\n\t\t\t\r\n\t\t\tltree = U.ltree;  dtree = U.dtree;\r\n\t\t\t\r\n\t\t\tputsE(out, pos,numl-257);  pos+=5;  // 286\r\n\t\t\tputsE(out, pos,numd-  1);  pos+=5;  // 30\r\n\t\t\tputsE(out, pos,numh-  4);  pos+=4;  // 19\r\n\t\t\t\r\n\t\t\tfor(var i=0; i<numh; i++) putsE(out, pos+i*3, U.itree[(U.ordr[i]<<1)+1]);   pos+=3* numh;\r\n\t\t\tpos = UZIP.F._codeTiny(lset, U.itree, out, pos);\r\n\t\t\tpos = UZIP.F._codeTiny(dset, U.itree, out, pos);\r\n\t\t}\r\n\t\t\r\n\t\tvar off=o0;\r\n\t\tfor(var si=0; si<li; si+=2) {\r\n\t\t\tvar qb=lits[si], len=(qb>>>23), end = off+(qb&((1<<23)-1));\r\n\t\t\twhile(off<end) pos = UZIP.F._writeLit(data[off++], ltree, out, pos);\r\n\t\t\t\r\n\t\t\tif(len!=0) {\r\n\t\t\t\tvar qc = lits[si+1], dst=(qc>>16), lgi=(qc>>8)&255, dgi=(qc&255);\r\n\t\t\t\tpos = UZIP.F._writeLit(257+lgi, ltree, out, pos);\r\n\t\t\t\tputsE(out, pos, len-U.of0[lgi]);  pos+=U.exb[lgi];\r\n\t\t\t\t\r\n\t\t\t\tpos = UZIP.F._writeLit(dgi, dtree, out, pos);\r\n\t\t\t\tputsF(out, pos, dst-U.df0[dgi]);  pos+=U.dxb[dgi];  off+=len;\r\n\t\t\t}\r\n\t\t}\r\n\t\tpos = UZIP.F._writeLit(256, ltree, out, pos);\r\n\t}\r\n\t//console.log(pos-opos, fxdSize, dynSize, cstSize);\r\n\treturn pos;\r\n}\r\nUZIP.F._copyExact = function(data,off,len,out,pos) {\r\n\tvar p8 = (pos>>>3);\r\n\tout[p8]=(len);  out[p8+1]=(len>>>8);  out[p8+2]=255-out[p8];  out[p8+3]=255-out[p8+1];  p8+=4;\r\n\tout.set(new Uint8Array(data.buffer, off, len), p8);\r\n\t//for(var i=0; i<len; i++) out[p8+i]=data[off+i];\r\n\treturn pos + ((len+4)<<3);\r\n}\r\n/*\r\n\tInteresting facts:\r\n\t- decompressed block can have bytes, which do not occur in a Huffman tree (copied from the previous block by reference)\r\n*/\r\n\r\nUZIP.F.getTrees = function() {\r\n\tvar U = UZIP.F.U;\r\n\tvar ML = UZIP.F._hufTree(U.lhst, U.ltree, 15);\r\n\tvar MD = UZIP.F._hufTree(U.dhst, U.dtree, 15);\r\n\tvar lset = [], numl = UZIP.F._lenCodes(U.ltree, lset);\r\n\tvar dset = [], numd = UZIP.F._lenCodes(U.dtree, dset);\r\n\tfor(var i=0; i<lset.length; i+=2) U.ihst[lset[i]]++;\r\n\tfor(var i=0; i<dset.length; i+=2) U.ihst[dset[i]]++;\r\n\tvar MH = UZIP.F._hufTree(U.ihst, U.itree,  7);\r\n\tvar numh = 19;  while(numh>4 && U.itree[(U.ordr[numh-1]<<1)+1]==0) numh--;\r\n\treturn [ML, MD, MH, numl, numd, numh, lset, dset];\r\n}\r\nUZIP.F.getSecond= function(a) {  var b=[];  for(var i=0; i<a.length; i+=2) b.push  (a[i+1]);  return b;  }\r\nUZIP.F.nonZero  = function(a) {  var b= \"\";  for(var i=0; i<a.length; i+=2) if(a[i+1]!=0)b+=(i>>1)+\",\";  return b;  }\r\nUZIP.F.contSize = function(tree, hst) {  var s=0;  for(var i=0; i<hst.length; i++) s+= hst[i]*tree[(i<<1)+1];  return s;  }\r\nUZIP.F._codeTiny = function(set, tree, out, pos) {\r\n\tfor(var i=0; i<set.length; i+=2) {\r\n\t\tvar l = set[i], rst = set[i+1];  //console.log(l, pos, tree[(l<<1)+1]);\r\n\t\tpos = UZIP.F._writeLit(l, tree, out, pos);\r\n\t\tvar rsl = l==16 ? 2 : (l==17 ? 3 : 7);\r\n\t\tif(l>15) {  UZIP.F._putsE(out, pos, rst, rsl);  pos+=rsl;  }\r\n\t}\r\n\treturn pos;\r\n}\r\nUZIP.F._lenCodes = function(tree, set) {\r\n\tvar len=tree.length;  while(len!=2 && tree[len-1]==0) len-=2;  // when no distances, keep one code with length 0\r\n\tfor(var i=0; i<len; i+=2) {\r\n\t\tvar l = tree[i+1], nxt = (i+3<len ? tree[i+3]:-1),  nnxt = (i+5<len ? tree[i+5]:-1),  prv = (i==0 ? -1 : tree[i-1]);\r\n\t\tif(l==0 && nxt==l && nnxt==l) {\r\n\t\t\tvar lz = i+5;\r\n\t\t\twhile(lz+2<len && tree[lz+2]==l) lz+=2;\r\n\t\t\tvar zc = Math.min((lz+1-i)>>>1, 138);\r\n\t\t\tif(zc<11) set.push(17, zc-3);\r\n\t\t\telse set.push(18, zc-11);\r\n\t\t\ti += zc*2-2;\r\n\t\t}\r\n\t\telse if(l==prv && nxt==l && nnxt==l) {\r\n\t\t\tvar lz = i+5;\r\n\t\t\twhile(lz+2<len && tree[lz+2]==l) lz+=2;\r\n\t\t\tvar zc = Math.min((lz+1-i)>>>1, 6);\r\n\t\t\tset.push(16, zc-3);\r\n\t\t\ti += zc*2-2;\r\n\t\t}\r\n\t\telse set.push(l, 0);\r\n\t}\r\n\treturn len>>>1;\r\n}\r\nUZIP.F._hufTree   = function(hst, tree, MAXL) {\r\n\tvar list=[], hl = hst.length, tl=tree.length, i=0;\r\n\tfor(i=0; i<tl; i+=2) {  tree[i]=0;  tree[i+1]=0;  }\t\r\n\tfor(i=0; i<hl; i++) if(hst[i]!=0) list.push({lit:i, f:hst[i]});\r\n\tvar end = list.length, l2=list.slice(0);\r\n\tif(end==0) return 0;  // empty histogram (usually for dist)\r\n\tif(end==1) {  var lit=list[0].lit, l2=lit==0?1:0;  tree[(lit<<1)+1]=1;  tree[(l2<<1)+1]=1;  return 1;  }\r\n\tlist.sort(function(a,b){return a.f-b.f;});\r\n\tvar a=list[0], b=list[1], i0=0, i1=1, i2=2;  list[0]={lit:-1,f:a.f+b.f,l:a,r:b,d:0};\r\n\twhile(i1!=end-1) {\r\n\t\tif(i0!=i1 && (i2==end || list[i0].f<list[i2].f)) {  a=list[i0++];  }  else {  a=list[i2++];  }\r\n\t\tif(i0!=i1 && (i2==end || list[i0].f<list[i2].f)) {  b=list[i0++];  }  else {  b=list[i2++];  }\r\n\t\tlist[i1++]={lit:-1,f:a.f+b.f, l:a,r:b};\r\n\t}\r\n\tvar maxl = UZIP.F.setDepth(list[i1-1], 0);\r\n\tif(maxl>MAXL) {  UZIP.F.restrictDepth(l2, MAXL, maxl);  maxl = MAXL;  }\r\n\tfor(i=0; i<end; i++) tree[(l2[i].lit<<1)+1]=l2[i].d;\r\n\treturn maxl;\r\n}\r\n\r\nUZIP.F.setDepth  = function(t, d) {\r\n\tif(t.lit!=-1) {  t.d=d;  return d;  }\r\n\treturn Math.max( UZIP.F.setDepth(t.l, d+1),  UZIP.F.setDepth(t.r, d+1) );\r\n}\r\n\r\nUZIP.F.restrictDepth = function(dps, MD, maxl) {\r\n\tvar i=0, bCost=1<<(maxl-MD), dbt=0;\r\n\tdps.sort(function(a,b){return b.d==a.d ? a.f-b.f : b.d-a.d;});\r\n\t\r\n\tfor(i=0; i<dps.length; i++) if(dps[i].d>MD) {  var od=dps[i].d;  dps[i].d=MD;  dbt+=bCost-(1<<(maxl-od));  }  else break;\r\n\tdbt = dbt>>>(maxl-MD);\r\n\twhile(dbt>0) {  var od=dps[i].d;  if(od<MD) {  dps[i].d++;  dbt-=(1<<(MD-od-1));  }  else  i++;  }\r\n\tfor(; i>=0; i--) if(dps[i].d==MD && dbt<0) {  dps[i].d--;  dbt++;  }  if(dbt!=0) console.log(\"debt left\");\r\n}\r\n\r\nUZIP.F._goodIndex = function(v, arr) {\r\n\tvar i=0;  if(arr[i|16]<=v) i|=16;  if(arr[i|8]<=v) i|=8;  if(arr[i|4]<=v) i|=4;  if(arr[i|2]<=v) i|=2;  if(arr[i|1]<=v) i|=1;  return i;\r\n}\r\nUZIP.F._writeLit = function(ch, ltree, out, pos) {\r\n\tUZIP.F._putsF(out, pos, ltree[ch<<1]);\r\n\treturn pos+ltree[(ch<<1)+1];\r\n}\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\nUZIP.F.inflate = function(data, buf) {\r\n\tvar u8=Uint8Array;\r\n\tif(data[0]==3 && data[1]==0) return (buf ? buf : new u8(0));\r\n\tvar F=UZIP.F, bitsF = F._bitsF, bitsE = F._bitsE, decodeTiny = F._decodeTiny, makeCodes = F.makeCodes, codes2map=F.codes2map, get17 = F._get17;\r\n\tvar U = F.U;\r\n\t\r\n\tvar noBuf = (buf==null);\r\n\tif(noBuf) buf = new u8((data.length>>>2)<<3);\r\n\t\r\n\tvar BFINAL=0, BTYPE=0, HLIT=0, HDIST=0, HCLEN=0, ML=0, MD=0; \t\r\n\tvar off = 0, pos = 0;\r\n\tvar lmap, dmap;\r\n\t\r\n\twhile(BFINAL==0) {\t\t\r\n\t\tBFINAL = bitsF(data, pos  , 1);\r\n\t\tBTYPE  = bitsF(data, pos+1, 2);  pos+=3;\r\n\t\t//console.log(BFINAL, BTYPE);\r\n\t\t\r\n\t\tif(BTYPE==0) {\r\n\t\t\tif((pos&7)!=0) pos+=8-(pos&7);\r\n\t\t\tvar p8 = (pos>>>3)+4, len = data[p8-4]|(data[p8-3]<<8);  //console.log(len);//bitsF(data, pos, 16), \r\n\t\t\tif(noBuf) buf=UZIP.F._check(buf, off+len);\r\n\t\t\tbuf.set(new u8(data.buffer, data.byteOffset+p8, len), off);\r\n\t\t\t//for(var i=0; i<len; i++) buf[off+i] = data[p8+i];\r\n\t\t\t//for(var i=0; i<len; i++) if(buf[off+i] != data[p8+i]) throw \"e\";\r\n\t\t\tpos = ((p8+len)<<3);  off+=len;  continue;\r\n\t\t}\r\n\t\tif(noBuf) buf=UZIP.F._check(buf, off+(1<<17));  // really not enough in many cases (but PNG and ZIP provide buffer in advance)\r\n\t\tif(BTYPE==1) {  lmap = U.flmap;  dmap = U.fdmap;  ML = (1<<9)-1;  MD = (1<<5)-1;   }\r\n\t\tif(BTYPE==2) {\r\n\t\t\tHLIT  = bitsE(data, pos   , 5)+257;  \r\n\t\t\tHDIST = bitsE(data, pos+ 5, 5)+  1;  \r\n\t\t\tHCLEN = bitsE(data, pos+10, 4)+  4;  pos+=14;\r\n\t\t\t\r\n\t\t\tvar ppos = pos;\r\n\t\t\tfor(var i=0; i<38; i+=2) {  U.itree[i]=0;  U.itree[i+1]=0;  }\r\n\t\t\tvar tl = 1;\r\n\t\t\tfor(var i=0; i<HCLEN; i++) {  var l=bitsE(data, pos+i*3, 3);  U.itree[(U.ordr[i]<<1)+1] = l;  if(l>tl)tl=l;  }     pos+=3*HCLEN;  //console.log(itree);\r\n\t\t\tmakeCodes(U.itree, tl);\r\n\t\t\tcodes2map(U.itree, tl, U.imap);\r\n\t\t\t\r\n\t\t\tlmap = U.lmap;  dmap = U.dmap;\r\n\t\t\t\r\n\t\t\tpos = decodeTiny(U.imap, (1<<tl)-1, HLIT+HDIST, data, pos, U.ttree);\r\n\t\t\tvar mx0 = F._copyOut(U.ttree,    0, HLIT , U.ltree);  ML = (1<<mx0)-1;\r\n\t\t\tvar mx1 = F._copyOut(U.ttree, HLIT, HDIST, U.dtree);  MD = (1<<mx1)-1;\r\n\t\t\t\r\n\t\t\t//var ml = decodeTiny(U.imap, (1<<tl)-1, HLIT , data, pos, U.ltree); ML = (1<<(ml>>>24))-1;  pos+=(ml&0xffffff);\r\n\t\t\tmakeCodes(U.ltree, mx0);\r\n\t\t\tcodes2map(U.ltree, mx0, lmap);\r\n\t\t\t\r\n\t\t\t//var md = decodeTiny(U.imap, (1<<tl)-1, HDIST, data, pos, U.dtree); MD = (1<<(md>>>24))-1;  pos+=(md&0xffffff);\r\n\t\t\tmakeCodes(U.dtree, mx1);\r\n\t\t\tcodes2map(U.dtree, mx1, dmap);\r\n\t\t}\r\n\t\t//var ooff=off, opos=pos;\r\n\t\twhile(true) {\r\n\t\t\tvar code = lmap[get17(data, pos) & ML];  pos += code&15;\r\n\t\t\tvar lit = code>>>4;  //U.lhst[lit]++;  \r\n\t\t\tif((lit>>>8)==0) {  buf[off++] = lit;  }\r\n\t\t\telse if(lit==256) {  break;  }\r\n\t\t\telse {\r\n\t\t\t\tvar end = off+lit-254;\r\n\t\t\t\tif(lit>264) { var ebs = U.ldef[lit-257];  end = off + (ebs>>>3) + bitsE(data, pos, ebs&7);  pos += ebs&7;  }\r\n\t\t\t\t//UZIP.F.dst[end-off]++;\r\n\t\t\t\t\r\n\t\t\t\tvar dcode = dmap[get17(data, pos) & MD];  pos += dcode&15;\r\n\t\t\t\tvar dlit = dcode>>>4;\r\n\t\t\t\tvar dbs = U.ddef[dlit], dst = (dbs>>>4) + bitsF(data, pos, dbs&15);  pos += dbs&15;\r\n\t\t\t\t\r\n\t\t\t\t//var o0 = off-dst, stp = Math.min(end-off, dst);\r\n\t\t\t\t//if(stp>20) while(off<end) {  buf.copyWithin(off, o0, o0+stp);  off+=stp;  }  else\r\n\t\t\t\t//if(end-dst<=off) buf.copyWithin(off, off-dst, end-dst);  else\r\n\t\t\t\t//if(dst==1) buf.fill(buf[off-1], off, end);  else\r\n\t\t\t\tif(noBuf) buf=UZIP.F._check(buf, off+(1<<17));\r\n\t\t\t\twhile(off<end) {  buf[off]=buf[off++-dst];    buf[off]=buf[off++-dst];  buf[off]=buf[off++-dst];  buf[off]=buf[off++-dst];  }   \r\n\t\t\t\toff=end;\r\n\t\t\t\t//while(off!=end) {  buf[off]=buf[off++-dst];  }\r\n\t\t\t}\r\n\t\t}\r\n\t\t//console.log(off-ooff, (pos-opos)>>>3);\r\n\t}\r\n\t//console.log(UZIP.F.dst);\r\n\t//console.log(tlen, dlen, off-tlen+tcnt);\r\n\treturn buf.length==off ? buf : buf.slice(0,off);\r\n}\r\nUZIP.F._check=function(buf, len) {\r\n\tvar bl=buf.length;  if(len<=bl) return buf;\r\n\tvar nbuf = new Uint8Array(Math.max(bl<<1,len));  nbuf.set(buf,0);\r\n\t//for(var i=0; i<bl; i+=4) {  nbuf[i]=buf[i];  nbuf[i+1]=buf[i+1];  nbuf[i+2]=buf[i+2];  nbuf[i+3]=buf[i+3];  }\r\n\treturn nbuf;\r\n}\r\n\r\nUZIP.F._decodeTiny = function(lmap, LL, len, data, pos, tree) {\r\n\tvar bitsE = UZIP.F._bitsE, get17 = UZIP.F._get17;\r\n\tvar i = 0;\r\n\twhile(i<len) {\r\n\t\tvar code = lmap[get17(data, pos)&LL];  pos+=code&15;\r\n\t\tvar lit = code>>>4; \r\n\t\tif(lit<=15) {  tree[i]=lit;  i++;  }\r\n\t\telse {\r\n\t\t\tvar ll = 0, n = 0;\r\n\t\t\tif(lit==16) {\r\n\t\t\t\tn = (3  + bitsE(data, pos, 2));  pos += 2;  ll = tree[i-1];\r\n\t\t\t}\r\n\t\t\telse if(lit==17) {\r\n\t\t\t\tn = (3  + bitsE(data, pos, 3));  pos += 3;\r\n\t\t\t}\r\n\t\t\telse if(lit==18) {\r\n\t\t\t\tn = (11 + bitsE(data, pos, 7));  pos += 7;\r\n\t\t\t}\r\n\t\t\tvar ni = i+n;\r\n\t\t\twhile(i<ni) {  tree[i]=ll;  i++; }\r\n\t\t}\r\n\t}\r\n\treturn pos;\r\n}\r\nUZIP.F._copyOut = function(src, off, len, tree) {\r\n\tvar mx=0, i=0, tl=tree.length>>>1;\r\n\twhile(i<len) {  var v=src[i+off];  tree[(i<<1)]=0;  tree[(i<<1)+1]=v;  if(v>mx)mx=v;  i++;  }\r\n\twhile(i<tl ) {  tree[(i<<1)]=0;  tree[(i<<1)+1]=0;  i++;  }\r\n\treturn mx;\r\n}\r\n\r\nUZIP.F.makeCodes = function(tree, MAX_BITS) {  // code, length\r\n\tvar U = UZIP.F.U;\r\n\tvar max_code = tree.length;\r\n\tvar code, bits, n, i, len;\r\n\t\r\n\tvar bl_count = U.bl_count;  for(var i=0; i<=MAX_BITS; i++) bl_count[i]=0;\r\n\tfor(i=1; i<max_code; i+=2) bl_count[tree[i]]++;\r\n\t\r\n\tvar next_code = U.next_code;\t// smallest code for each length\r\n\t\r\n\tcode = 0;\r\n\tbl_count[0] = 0;\r\n\tfor (bits = 1; bits <= MAX_BITS; bits++) {\r\n\t\tcode = (code + bl_count[bits-1]) << 1;\r\n\t\tnext_code[bits] = code;\r\n\t}\r\n\t\r\n\tfor (n = 0; n < max_code; n+=2) {\r\n\t\tlen = tree[n+1];\r\n\t\tif (len != 0) {\r\n\t\t\ttree[n] = next_code[len];\r\n\t\t\tnext_code[len]++;\r\n\t\t}\r\n\t}\r\n}\r\nUZIP.F.codes2map = function(tree, MAX_BITS, map) {\r\n\tvar max_code = tree.length;\r\n\tvar U=UZIP.F.U, r15 = U.rev15;\r\n\tfor(var i=0; i<max_code; i+=2) if(tree[i+1]!=0)  {\r\n\t\tvar lit = i>>1;\r\n\t\tvar cl = tree[i+1], val = (lit<<4)|cl; // :  (0x8000 | (U.of0[lit-257]<<7) | (U.exb[lit-257]<<4) | cl);\r\n\t\tvar rest = (MAX_BITS-cl), i0 = tree[i]<<rest, i1 = i0 + (1<<rest);\r\n\t\t//tree[i]=r15[i0]>>>(15-MAX_BITS);\r\n\t\twhile(i0!=i1) {\r\n\t\t\tvar p0 = r15[i0]>>>(15-MAX_BITS);\r\n\t\t\tmap[p0]=val;  i0++;\r\n\t\t}\r\n\t}\r\n}\r\nUZIP.F.revCodes = function(tree, MAX_BITS) {\r\n\tvar r15 = UZIP.F.U.rev15, imb = 15-MAX_BITS;\r\n\tfor(var i=0; i<tree.length; i+=2) {  var i0 = (tree[i]<<(MAX_BITS-tree[i+1]));  tree[i] = r15[i0]>>>imb;  }\r\n}\r\n\r\n// used only in deflate\r\nUZIP.F._putsE= function(dt, pos, val   ) {  val = val<<(pos&7);  var o=(pos>>>3);  dt[o]|=val;  dt[o+1]|=(val>>>8);                        }\r\nUZIP.F._putsF= function(dt, pos, val   ) {  val = val<<(pos&7);  var o=(pos>>>3);  dt[o]|=val;  dt[o+1]|=(val>>>8);  dt[o+2]|=(val>>>16);  }\r\n\r\nUZIP.F._bitsE= function(dt, pos, length) {  return ((dt[pos>>>3] | (dt[(pos>>>3)+1]<<8)                        )>>>(pos&7))&((1<<length)-1);  }\r\nUZIP.F._bitsF= function(dt, pos, length) {  return ((dt[pos>>>3] | (dt[(pos>>>3)+1]<<8) | (dt[(pos>>>3)+2]<<16))>>>(pos&7))&((1<<length)-1);  }\r\n/*\r\nUZIP.F._get9 = function(dt, pos) {\r\n\treturn ((dt[pos>>>3] | (dt[(pos>>>3)+1]<<8))>>>(pos&7))&511;\r\n} */\r\nUZIP.F._get17= function(dt, pos) {\t// return at least 17 meaningful bytes\r\n\treturn (dt[pos>>>3] | (dt[(pos>>>3)+1]<<8) | (dt[(pos>>>3)+2]<<16) )>>>(pos&7);\r\n}\r\nUZIP.F._get25= function(dt, pos) {\t// return at least 17 meaningful bytes\r\n\treturn (dt[pos>>>3] | (dt[(pos>>>3)+1]<<8) | (dt[(pos>>>3)+2]<<16) | (dt[(pos>>>3)+3]<<24) )>>>(pos&7);\r\n}\r\nUZIP.F.U = function(){\r\n\tvar u16=Uint16Array, u32=Uint32Array;\r\n\treturn {\r\n\t\tnext_code : new u16(16),\r\n\t\tbl_count  : new u16(16),\r\n\t\tordr : [ 16, 17, 18, 0, 8, 7, 9, 6, 10, 5, 11, 4, 12, 3, 13, 2, 14, 1, 15 ],\r\n\t\tof0  : [3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258,999,999,999],\r\n\t\texb  : [0,0,0,0,0,0,0, 0, 1, 1, 1, 1, 2, 2, 2, 2, 3, 3, 3, 3, 4, 4, 4,  4,  5,  5,  5,  5,  0,  0,  0,  0],\r\n\t\tldef : new u16(32),\r\n\t\tdf0  : [1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577, 65535, 65535],\r\n\t\tdxb  : [0,0,0,0,1,1,2, 2, 3, 3, 4, 4, 5, 5,  6,  6,  7,  7,  8,  8,   9,   9,  10,  10,  11,  11,  12,   12,   13,   13,     0,     0],\r\n\t\tddef : new u32(32),\r\n\t\tflmap: new u16(  512),  fltree: [],\r\n\t\tfdmap: new u16(   32),  fdtree: [],\r\n\t\tlmap : new u16(32768),  ltree : [],  ttree:[],\r\n\t\tdmap : new u16(32768),  dtree : [],\r\n\t\timap : new u16(  512),  itree : [],\r\n\t\t//rev9 : new u16(  512)\r\n\t\trev15: new u16(1<<15),\r\n\t\tlhst : new u32(286), dhst : new u32( 30), ihst : new u32(19),\r\n\t\tlits : new u32(15000),\r\n\t\tstrt : new u16(1<<16),\r\n\t\tprev : new u16(1<<15)\r\n\t};  \r\n} ();\r\n\r\n(function(){\t\r\n\tvar U = UZIP.F.U;\r\n\tvar len = 1<<15;\r\n\tfor(var i=0; i<len; i++) {\r\n\t\tvar x = i;\r\n\t\tx = (((x & 0xaaaaaaaa) >>> 1) | ((x & 0x55555555) << 1));\r\n\t\tx = (((x & 0xcccccccc) >>> 2) | ((x & 0x33333333) << 2));\r\n\t\tx = (((x & 0xf0f0f0f0) >>> 4) | ((x & 0x0f0f0f0f) << 4));\r\n\t\tx = (((x & 0xff00ff00) >>> 8) | ((x & 0x00ff00ff) << 8));\r\n\t\tU.rev15[i] = (((x >>> 16) | (x << 16)))>>>17;\r\n\t}\r\n\t\r\n\tfunction pushV(tgt, n, sv) {  while(n--!=0) tgt.push(0,sv);  }\r\n\t\r\n\tfor(var i=0; i<32; i++) {  U.ldef[i]=(U.of0[i]<<3)|U.exb[i];  U.ddef[i]=(U.df0[i]<<4)|U.dxb[i];  }\r\n\t\r\n\tpushV(U.fltree, 144, 8);  pushV(U.fltree, 255-143, 9);  pushV(U.fltree, 279-255, 7);  pushV(U.fltree,287-279,8);\r\n\t/*\r\n\tvar i = 0;\r\n\tfor(; i<=143; i++) U.fltree.push(0,8);\r\n\tfor(; i<=255; i++) U.fltree.push(0,9);\r\n\tfor(; i<=279; i++) U.fltree.push(0,7);\r\n\tfor(; i<=287; i++) U.fltree.push(0,8);\r\n\t*/\r\n\tUZIP.F.makeCodes(U.fltree, 9);\r\n\tUZIP.F.codes2map(U.fltree, 9, U.flmap);\r\n\tUZIP.F.revCodes (U.fltree, 9)\r\n\t\r\n\tpushV(U.fdtree,32,5);\r\n\t//for(i=0;i<32; i++) U.fdtree.push(0,5);\r\n\tUZIP.F.makeCodes(U.fdtree, 5);\r\n\tUZIP.F.codes2map(U.fdtree, 5, U.fdmap);\r\n\tUZIP.F.revCodes (U.fdtree, 5)\r\n\t\r\n\tpushV(U.itree,19,0);  pushV(U.ltree,286,0);  pushV(U.dtree,30,0);  pushV(U.ttree,320,0);\r\n\t/*\r\n\tfor(var i=0; i< 19; i++) U.itree.push(0,0);\r\n\tfor(var i=0; i<286; i++) U.ltree.push(0,0);\r\n\tfor(var i=0; i< 30; i++) U.dtree.push(0,0);\r\n\tfor(var i=0; i<320; i++) U.ttree.push(0,0);\r\n\t*/\r\n})()\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n", "// https://github.com/photopea/UPNG.js/blob/f6e5f93da01094b1ffb3cef364abce4d9e758cbf/UPNG.js\n\n// import * as pako from 'pako'\nimport * as UZIP from 'uzip';\n\nconst UPNG = (function () {\n  var _bin = {\n    nextZero(data, p) { while (data[p] != 0) p++; return p; },\n    readUshort(buff, p) { return (buff[p] << 8) | buff[p + 1]; },\n    writeUshort(buff, p, n) { buff[p] = (n >> 8) & 255; buff[p + 1] = n & 255; },\n    readUint(buff, p) { return (buff[p] * (256 * 256 * 256)) + ((buff[p + 1] << 16) | (buff[p + 2] << 8) | buff[p + 3]); },\n    writeUint(buff, p, n) { buff[p] = (n >> 24) & 255; buff[p + 1] = (n >> 16) & 255; buff[p + 2] = (n >> 8) & 255; buff[p + 3] = n & 255; },\n    readASCII(buff, p, l) { let s = ''; for (let i = 0; i < l; i++) s += String.fromCharCode(buff[p + i]); return s; },\n    writeASCII(data, p, s) { for (let i = 0; i < s.length; i++) data[p + i] = s.charCodeAt(i); },\n    readBytes(buff, p, l) { const arr = []; for (let i = 0; i < l; i++) arr.push(buff[p + i]); return arr; },\n    pad(n) { return n.length < 2 ? `0${n}` : n; },\n    readUTF8(buff, p, l) {\n      let s = '';\n      let ns;\n      for (let i = 0; i < l; i++) s += `%${_bin.pad(buff[p + i].toString(16))}`;\n      try { ns = decodeURIComponent(s); } catch (e) { return _bin.readASCII(buff, p, l); }\n      return ns;\n    },\n  };\n\n  function toRGBA8(out) {\n    const w = out.width; const\n      h = out.height;\n    if (out.tabs.acTL == null) return [decodeImage(out.data, w, h, out).buffer];\n\n    const frms = [];\n    if (out.frames[0].data == null) out.frames[0].data = out.data;\n\n    const len = w * h * 4; const img = new Uint8Array(len); const empty = new Uint8Array(len); const\n      prev = new Uint8Array(len);\n    for (let i = 0; i < out.frames.length; i++) {\n      const frm = out.frames[i];\n      const fx = frm.rect.x; const fy = frm.rect.y; const fw = frm.rect.width; const\n        fh = frm.rect.height;\n      const fdata = decodeImage(frm.data, fw, fh, out);\n\n      if (i != 0) for (var j = 0; j < len; j++) prev[j] = img[j];\n\n      if (frm.blend == 0) _copyTile(fdata, fw, fh, img, w, h, fx, fy, 0);\n      else if (frm.blend == 1) _copyTile(fdata, fw, fh, img, w, h, fx, fy, 1);\n\n      frms.push(img.buffer.slice(0));\n\n      if (frm.dispose == 0) {} else if (frm.dispose == 1) _copyTile(empty, fw, fh, img, w, h, fx, fy, 0);\n      else if (frm.dispose == 2) for (var j = 0; j < len; j++) img[j] = prev[j];\n    }\n    return frms;\n  }\n  function decodeImage(data, w, h, out) {\n    const area = w * h; const\n      bpp = _getBPP(out);\n    const bpl = Math.ceil(w * bpp / 8);\t// bytes per line\n\n    const bf = new Uint8Array(area * 4); const\n      bf32 = new Uint32Array(bf.buffer);\n    const { ctype } = out;\n    const { depth } = out;\n    const rs = _bin.readUshort;\n\n    // console.log(ctype, depth);\n    const time = Date.now();\n\n    if (ctype == 6) { // RGB + alpha\n      const qarea = area << 2;\n      if (depth == 8) for (var i = 0; i < qarea; i += 4) { bf[i] = data[i]; bf[i + 1] = data[i + 1]; bf[i + 2] = data[i + 2]; bf[i + 3] = data[i + 3]; }\n      if (depth == 16) for (var i = 0; i < qarea; i++) { bf[i] = data[i << 1]; }\n    } else if (ctype == 2) {\t// RGB\n      const ts = out.tabs.tRNS;\n      if (ts == null) {\n        if (depth == 8) for (var i = 0; i < area; i++) { var ti = i * 3; bf32[i] = (255 << 24) | (data[ti + 2] << 16) | (data[ti + 1] << 8) | data[ti]; }\n        if (depth == 16) for (var i = 0; i < area; i++) { var ti = i * 6; bf32[i] = (255 << 24) | (data[ti + 4] << 16) | (data[ti + 2] << 8) | data[ti]; }\n      } else {\n        var tr = ts[0]; const tg = ts[1]; const\n          tb = ts[2];\n        if (depth == 8) {\n          for (var i = 0; i < area; i++) {\n            var qi = i << 2; var\n              ti = i * 3; bf32[i] = (255 << 24) | (data[ti + 2] << 16) | (data[ti + 1] << 8) | data[ti];\n            if (data[ti] == tr && data[ti + 1] == tg && data[ti + 2] == tb) bf[qi + 3] = 0;\n          }\n        }\n        if (depth == 16) {\n          for (var i = 0; i < area; i++) {\n            var qi = i << 2; var\n              ti = i * 6; bf32[i] = (255 << 24) | (data[ti + 4] << 16) | (data[ti + 2] << 8) | data[ti];\n            if (rs(data, ti) == tr && rs(data, ti + 2) == tg && rs(data, ti + 4) == tb) bf[qi + 3] = 0;\n          }\n        }\n      }\n    } else if (ctype == 3) {\t// palette\n      const p = out.tabs.PLTE;\n      const ap = out.tabs.tRNS;\n      const tl = ap ? ap.length : 0;\n      // console.log(p, ap);\n      if (depth == 1) {\n        for (var y = 0; y < h; y++) {\n          var s0 = y * bpl; var\n            t0 = y * w;\n          for (var i = 0; i < w; i++) {\n            var qi = (t0 + i) << 2; var j = ((data[s0 + (i >> 3)] >> (7 - ((i & 7) << 0))) & 1); var\n              cj = 3 * j; bf[qi] = p[cj]; bf[qi + 1] = p[cj + 1]; bf[qi + 2] = p[cj + 2]; bf[qi + 3] = (j < tl) ? ap[j] : 255;\n          }\n        }\n      }\n      if (depth == 2) {\n        for (var y = 0; y < h; y++) {\n          var s0 = y * bpl; var\n            t0 = y * w;\n          for (var i = 0; i < w; i++) {\n            var qi = (t0 + i) << 2; var j = ((data[s0 + (i >> 2)] >> (6 - ((i & 3) << 1))) & 3); var\n              cj = 3 * j; bf[qi] = p[cj]; bf[qi + 1] = p[cj + 1]; bf[qi + 2] = p[cj + 2]; bf[qi + 3] = (j < tl) ? ap[j] : 255;\n          }\n        }\n      }\n      if (depth == 4) {\n        for (var y = 0; y < h; y++) {\n          var s0 = y * bpl; var\n            t0 = y * w;\n          for (var i = 0; i < w; i++) {\n            var qi = (t0 + i) << 2; var j = ((data[s0 + (i >> 1)] >> (4 - ((i & 1) << 2))) & 15); var\n              cj = 3 * j; bf[qi] = p[cj]; bf[qi + 1] = p[cj + 1]; bf[qi + 2] = p[cj + 2]; bf[qi + 3] = (j < tl) ? ap[j] : 255;\n          }\n        }\n      }\n      if (depth == 8) {\n        for (var i = 0; i < area; i++) {\n          var qi = i << 2; var j = data[i]; var\n            cj = 3 * j; bf[qi] = p[cj]; bf[qi + 1] = p[cj + 1]; bf[qi + 2] = p[cj + 2]; bf[qi + 3] = (j < tl) ? ap[j] : 255;\n        }\n      }\n    } else if (ctype == 4) {\t// gray + alpha\n      if (depth == 8) {\n        for (var i = 0; i < area; i++) {\n          var qi = i << 2; var di = i << 1; var\n            gr = data[di]; bf[qi] = gr; bf[qi + 1] = gr; bf[qi + 2] = gr; bf[qi + 3] = data[di + 1];\n        }\n      }\n      if (depth == 16) {\n        for (var i = 0; i < area; i++) {\n          var qi = i << 2; var di = i << 2; var\n            gr = data[di]; bf[qi] = gr; bf[qi + 1] = gr; bf[qi + 2] = gr; bf[qi + 3] = data[di + 2];\n        }\n      }\n    } else if (ctype == 0) {\t// gray\n      var tr = out.tabs.tRNS ? out.tabs.tRNS : -1;\n      for (var y = 0; y < h; y++) {\n        const off = y * bpl; const\n          to = y * w;\n        if (depth == 1) {\n          for (var x = 0; x < w; x++) {\n            var gr = 255 * ((data[off + (x >>> 3)] >>> (7 - ((x & 7)))) & 1); var\n              al = (gr == tr * 255) ? 0 : 255; bf32[to + x] = (al << 24) | (gr << 16) | (gr << 8) | gr;\n          }\n        } else if (depth == 2) {\n          for (var x = 0; x < w; x++) {\n            var gr = 85 * ((data[off + (x >>> 2)] >>> (6 - ((x & 3) << 1))) & 3); var\n              al = (gr == tr * 85) ? 0 : 255; bf32[to + x] = (al << 24) | (gr << 16) | (gr << 8) | gr;\n          }\n        } else if (depth == 4) {\n          for (var x = 0; x < w; x++) {\n            var gr = 17 * ((data[off + (x >>> 1)] >>> (4 - ((x & 1) << 2))) & 15); var\n              al = (gr == tr * 17) ? 0 : 255; bf32[to + x] = (al << 24) | (gr << 16) | (gr << 8) | gr;\n          }\n        } else if (depth == 8) {\n          for (var x = 0; x < w; x++) {\n            var gr = data[off + x]; var\n              al = (gr == tr) ? 0 : 255; bf32[to + x] = (al << 24) | (gr << 16) | (gr << 8) | gr;\n          }\n        } else if (depth == 16) {\n          for (var x = 0; x < w; x++) {\n            var gr = data[off + (x << 1)]; var\n              al = (rs(data, off + (x << 1)) == tr) ? 0 : 255; bf32[to + x] = (al << 24) | (gr << 16) | (gr << 8) | gr;\n          }\n        }\n      }\n    }\n    // console.log(Date.now()-time);\n    return bf;\n  }\n\n  function decode(buff) {\n    const data = new Uint8Array(buff); let offset = 8; const bin = _bin; const rUs = bin.readUshort; const\n      rUi = bin.readUint;\n    const out = { tabs: {}, frames: [] };\n    const dd = new Uint8Array(data.length); let\n      doff = 0;\t // put all IDAT data into it\n    let fd; let\n      foff = 0;\t// frames\n\n    const mgck = [0x89, 0x50, 0x4e, 0x47, 0x0d, 0x0a, 0x1a, 0x0a];\n    for (var i = 0; i < 8; i++) if (data[i] != mgck[i]) throw 'The input is not a PNG file!';\n\n    while (offset < data.length) {\n      const len = bin.readUint(data, offset); offset += 4;\n      const type = bin.readASCII(data, offset, 4); offset += 4;\n      // console.log(type,len);\n\n      if (type == 'IHDR') { _IHDR(data, offset, out); } else if (type == 'iCCP') {\n        var off = offset; while (data[off] != 0) off++;\n        const nam = bin.readASCII(data, offset, off - offset);\n        const cpr = data[off + 1];\n        const fil = data.slice(off + 2, offset + len);\n        let res = null;\n        try { res = _inflate(fil); } catch (e) { res = inflateRaw(fil); }\n        out.tabs[type] = res;\n      } else if (type == 'CgBI') { out.tabs[type] = data.slice(offset, offset + 4); } else if (type == 'IDAT') {\n        for (var i = 0; i < len; i++) dd[doff + i] = data[offset + i];\n        doff += len;\n      } else if (type == 'acTL') {\n        out.tabs[type] = { num_frames: rUi(data, offset), num_plays: rUi(data, offset + 4) };\n        fd = new Uint8Array(data.length);\n      } else if (type == 'fcTL') {\n        if (foff != 0) {\n          var fr = out.frames[out.frames.length - 1];\n          fr.data = _decompress(out, fd.slice(0, foff), fr.rect.width, fr.rect.height); foff = 0;\n        }\n        const rct = {\n          x: rUi(data, offset + 12), y: rUi(data, offset + 16), width: rUi(data, offset + 4), height: rUi(data, offset + 8),\n        };\n        let del = rUs(data, offset + 22); del = rUs(data, offset + 20) / (del == 0 ? 100 : del);\n        const frm = {\n          rect: rct, delay: Math.round(del * 1000), dispose: data[offset + 24], blend: data[offset + 25],\n        };\n        // console.log(frm);\n        out.frames.push(frm);\n      } else if (type == 'fdAT') {\n        for (var i = 0; i < len - 4; i++) fd[foff + i] = data[offset + i + 4];\n        foff += len - 4;\n      } else if (type == 'pHYs') {\n        out.tabs[type] = [bin.readUint(data, offset), bin.readUint(data, offset + 4), data[offset + 8]];\n      } else if (type == 'cHRM') {\n        out.tabs[type] = [];\n        for (var i = 0; i < 8; i++) out.tabs[type].push(bin.readUint(data, offset + i * 4));\n      } else if (type == 'tEXt' || type == 'zTXt') {\n        if (out.tabs[type] == null) out.tabs[type] = {};\n        var nz = bin.nextZero(data, offset);\n        var keyw = bin.readASCII(data, offset, nz - offset);\n        var text; var\n          tl = offset + len - nz - 1;\n        if (type == 'tEXt') text = bin.readASCII(data, nz + 1, tl);\n        else {\n          var bfr = _inflate(data.slice(nz + 2, nz + 2 + tl));\n          text = bin.readUTF8(bfr, 0, bfr.length);\n        }\n        out.tabs[type][keyw] = text;\n      } else if (type == 'iTXt') {\n        if (out.tabs[type] == null) out.tabs[type] = {};\n        var nz = 0; var\n          off = offset;\n        nz = bin.nextZero(data, off);\n        var keyw = bin.readASCII(data, off, nz - off); off = nz + 1;\n        const cflag = data[off]; const\n          cmeth = data[off + 1]; off += 2;\n        nz = bin.nextZero(data, off);\n        const ltag = bin.readASCII(data, off, nz - off); off = nz + 1;\n        nz = bin.nextZero(data, off);\n        const tkeyw = bin.readUTF8(data, off, nz - off); off = nz + 1;\n        var text; var\n          tl = len - (off - offset);\n        if (cflag == 0) text = bin.readUTF8(data, off, tl);\n        else {\n          var bfr = _inflate(data.slice(off, off + tl));\n          text = bin.readUTF8(bfr, 0, bfr.length);\n        }\n        out.tabs[type][keyw] = text;\n      } else if (type == 'PLTE') {\n        out.tabs[type] = bin.readBytes(data, offset, len);\n      } else if (type == 'hIST') {\n        const pl = out.tabs.PLTE.length / 3;\n        out.tabs[type] = []; for (var i = 0; i < pl; i++) out.tabs[type].push(rUs(data, offset + i * 2));\n      } else if (type == 'tRNS') {\n        if (out.ctype == 3) out.tabs[type] = bin.readBytes(data, offset, len);\n        else if (out.ctype == 0) out.tabs[type] = rUs(data, offset);\n        else if (out.ctype == 2) out.tabs[type] = [rUs(data, offset), rUs(data, offset + 2), rUs(data, offset + 4)];\n        // else console.log(\"tRNS for unsupported color type\",out.ctype, len);\n      } else if (type == 'gAMA') out.tabs[type] = bin.readUint(data, offset) / 100000;\n      else if (type == 'sRGB') out.tabs[type] = data[offset];\n      else if (type == 'bKGD') {\n        if (out.ctype == 0 || out.ctype == 4) out.tabs[type] = [rUs(data, offset)];\n        else if (out.ctype == 2 || out.ctype == 6) out.tabs[type] = [rUs(data, offset), rUs(data, offset + 2), rUs(data, offset + 4)];\n        else if (out.ctype == 3) out.tabs[type] = data[offset];\n      } else if (type == 'IEND') {\n        break;\n      }\n      // else {  console.log(\"unknown chunk type\", type, len);  out.tabs[type]=data.slice(offset,offset+len);  }\n      offset += len;\n      const crc = bin.readUint(data, offset); offset += 4;\n    }\n    if (foff != 0) {\n      var fr = out.frames[out.frames.length - 1];\n      fr.data = _decompress(out, fd.slice(0, foff), fr.rect.width, fr.rect.height);\n    }\n    out.data = _decompress(out, dd, out.width, out.height);\n\n    delete out.compress; delete out.interlace; delete out.filter;\n    return out;\n  }\n\n  function _decompress(out, dd, w, h) {\n    var time = Date.now();\n    const bpp = _getBPP(out); const bpl = Math.ceil(w * bpp / 8); const\n      buff = new Uint8Array((bpl + 1 + out.interlace) * h);\n    if (out.tabs.CgBI) dd = inflateRaw(dd, buff);\n    else dd = _inflate(dd, buff);\n    // console.log(dd.length, buff.length);\n    // console.log(Date.now()-time);\n\n    var time = Date.now();\n    if (out.interlace == 0) dd = _filterZero(dd, out, 0, w, h);\n    else if (out.interlace == 1) dd = _readInterlace(dd, out);\n    // console.log(Date.now()-time);\n    return dd;\n  }\n\n  function _inflate(data, buff) { const out = inflateRaw(new Uint8Array(data.buffer, 2, data.length - 6), buff); return out; }\n\n  var inflateRaw = (function () {\n    const H = {}; H.H = {}; H.H.N = function (N, W) {\n      const R = Uint8Array; let i = 0; let m = 0; let J = 0; let h = 0; let Q = 0; let X = 0; let u = 0; let w = 0; let d = 0; let v; let C;\n      if (N[0] == 3 && N[1] == 0) return W || new R(0); const V = H.H; const n = V.b; const A = V.e; const l = V.R; const M = V.n; const I = V.A; const e = V.Z; const b = V.m; const Z = W == null;\n      if (Z)W = new R(N.length >>> 2 << 5); while (i == 0) {\n        i = n(N, d, 1); m = n(N, d + 1, 2); d += 3; if (m == 0) {\n          if ((d & 7) != 0)d += 8 - (d & 7);\n          const D = (d >>> 3) + 4; const q = N[D - 4] | N[D - 3] << 8; if (Z)W = H.H.W(W, w + q); W.set(new R(N.buffer, N.byteOffset + D, q), w); d = D + q << 3;\n          w += q; continue;\n        } if (Z)W = H.H.W(W, w + (1 << 17)); if (m == 1) { v = b.J; C = b.h; X = (1 << 9) - 1; u = (1 << 5) - 1; } if (m == 2) {\n          J = A(N, d, 5) + 257;\n          h = A(N, d + 5, 5) + 1; Q = A(N, d + 10, 4) + 4; d += 14; const E = d; let j = 1; for (var c = 0; c < 38; c += 2) { b.Q[c] = 0; b.Q[c + 1] = 0; } for (var c = 0;\n            c < Q; c++) { const K = A(N, d + c * 3, 3); b.Q[(b.X[c] << 1) + 1] = K; if (K > j)j = K; }d += 3 * Q; M(b.Q, j); I(b.Q, j, b.u); v = b.w; C = b.d;\n          d = l(b.u, (1 << j) - 1, J + h, N, d, b.v); const r = V.V(b.v, 0, J, b.C); X = (1 << r) - 1; const S = V.V(b.v, J, h, b.D); u = (1 << S) - 1; M(b.C, r);\n          I(b.C, r, v); M(b.D, S); I(b.D, S, C);\n        } while (!0) {\n          const T = v[e(N, d) & X]; d += T & 15; const p = T >>> 4; if (p >>> 8 == 0) { W[w++] = p; } else if (p == 256) { break; } else {\n            let z = w + p - 254;\n            if (p > 264) { const _ = b.q[p - 257]; z = w + (_ >>> 3) + A(N, d, _ & 7); d += _ & 7; } const $ = C[e(N, d) & u]; d += $ & 15; const s = $ >>> 4; const Y = b.c[s]; const a = (Y >>> 4) + n(N, d, Y & 15);\n            d += Y & 15; while (w < z) { W[w] = W[w++ - a]; W[w] = W[w++ - a]; W[w] = W[w++ - a]; W[w] = W[w++ - a]; }w = z;\n          }\n        }\n      } return W.length == w ? W : W.slice(0, w);\n    };\n    H.H.W = function (N, W) { const R = N.length; if (W <= R) return N; const V = new Uint8Array(R << 1); V.set(N, 0); return V; };\n    H.H.R = function (N, W, R, V, n, A) {\n      const l = H.H.e; const M = H.H.Z; let I = 0; while (I < R) {\n        const e = N[M(V, n) & W]; n += e & 15; const b = e >>> 4;\n        if (b <= 15) { A[I] = b; I++; } else {\n          let Z = 0; let m = 0; if (b == 16) { m = 3 + l(V, n, 2); n += 2; Z = A[I - 1]; } else if (b == 17) {\n            m = 3 + l(V, n, 3);\n            n += 3;\n          } else if (b == 18) { m = 11 + l(V, n, 7); n += 7; } const J = I + m; while (I < J) { A[I] = Z; I++; }\n        }\n      } return n;\n    }; H.H.V = function (N, W, R, V) {\n      let n = 0; let A = 0; const l = V.length >>> 1;\n      while (A < R) { const M = N[A + W]; V[A << 1] = 0; V[(A << 1) + 1] = M; if (M > n)n = M; A++; } while (A < l) { V[A << 1] = 0; V[(A << 1) + 1] = 0; A++; } return n;\n    };\n    H.H.n = function (N, W) {\n      const R = H.H.m; const V = N.length; let n; let A; let l; var M; let I; const e = R.j; for (var M = 0; M <= W; M++)e[M] = 0; for (M = 1; M < V; M += 2)e[N[M]]++;\n      const b = R.K; n = 0; e[0] = 0; for (A = 1; A <= W; A++) { n = n + e[A - 1] << 1; b[A] = n; } for (l = 0; l < V; l += 2) {\n        I = N[l + 1]; if (I != 0) {\n          N[l] = b[I];\n          b[I]++;\n        }\n      }\n    }; H.H.A = function (N, W, R) {\n      const V = N.length; const n = H.H.m; const A = n.r; for (let l = 0; l < V; l += 2) {\n        if (N[l + 1] != 0) {\n          const M = l >> 1; const I = N[l + 1]; const e = M << 4 | I; const b = W - I; let Z = N[l] << b; const m = Z + (1 << b);\n          while (Z != m) { const J = A[Z] >>> 15 - W; R[J] = e; Z++; }\n        }\n      }\n    }; H.H.l = function (N, W) {\n      const R = H.H.m.r; const V = 15 - W; for (let n = 0; n < N.length;\n        n += 2) { const A = N[n] << W - N[n + 1]; N[n] = R[A] >>> V; }\n    }; H.H.M = function (N, W, R) { R <<= (W & 7); const V = W >>> 3; N[V] |= R; N[V + 1] |= R >>> 8; };\n    H.H.I = function (N, W, R) { R <<= (W & 7); const V = W >>> 3; N[V] |= R; N[V + 1] |= R >>> 8; N[V + 2] |= R >>> 16; }; H.H.e = function (N, W, R) { return (N[W >>> 3] | N[(W >>> 3) + 1] << 8) >>> (W & 7) & (1 << R) - 1; };\n    H.H.b = function (N, W, R) { return (N[W >>> 3] | N[(W >>> 3) + 1] << 8 | N[(W >>> 3) + 2] << 16) >>> (W & 7) & (1 << R) - 1; }; H.H.Z = function (N, W) { return (N[W >>> 3] | N[(W >>> 3) + 1] << 8 | N[(W >>> 3) + 2] << 16) >>> (W & 7); };\n    H.H.i = function (N, W) { return (N[W >>> 3] | N[(W >>> 3) + 1] << 8 | N[(W >>> 3) + 2] << 16 | N[(W >>> 3) + 3] << 24) >>> (W & 7); }; H.H.m = (function () {\n      const N = Uint16Array; const W = Uint32Array;\n      return {\n        K: new N(16), j: new N(16), X: [16, 17, 18, 0, 8, 7, 9, 6, 10, 5, 11, 4, 12, 3, 13, 2, 14, 1, 15], S: [3, 4, 5, 6, 7, 8, 9, 10, 11, 13, 15, 17, 19, 23, 27, 31, 35, 43, 51, 59, 67, 83, 99, 115, 131, 163, 195, 227, 258, 999, 999, 999], T: [0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 2, 2, 2, 2, 3, 3, 3, 3, 4, 4, 4, 4, 5, 5, 5, 5, 0, 0, 0, 0], q: new N(32), p: [1, 2, 3, 4, 5, 7, 9, 13, 17, 25, 33, 49, 65, 97, 129, 193, 257, 385, 513, 769, 1025, 1537, 2049, 3073, 4097, 6145, 8193, 12289, 16385, 24577, 65535, 65535], z: [0, 0, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 10, 10, 11, 11, 12, 12, 13, 13, 0, 0], c: new W(32), J: new N(512), _: [], h: new N(32), $: [], w: new N(32768), C: [], v: [], d: new N(32768), D: [], u: new N(512), Q: [], r: new N(1 << 15), s: new W(286), Y: new W(30), a: new W(19), t: new W(15e3), k: new N(1 << 16), g: new N(1 << 15),\n      };\n    }());\n    (function () {\n      const N = H.H.m; const W = 1 << 15; for (var R = 0; R < W; R++) {\n        let V = R; V = (V & 2863311530) >>> 1 | (V & 1431655765) << 1;\n        V = (V & 3435973836) >>> 2 | (V & 858993459) << 2; V = (V & 4042322160) >>> 4 | (V & 252645135) << 4; V = (V & 4278255360) >>> 8 | (V & 16711935) << 8;\n        N.r[R] = (V >>> 16 | V << 16) >>> 17;\n      } function n(A, l, M) { while (l-- != 0)A.push(0, M); } for (var R = 0; R < 32; R++) {\n        N.q[R] = N.S[R] << 3 | N.T[R];\n        N.c[R] = N.p[R] << 4 | N.z[R];\n      }n(N._, 144, 8); n(N._, 255 - 143, 9); n(N._, 279 - 255, 7); n(N._, 287 - 279, 8); H.H.n(N._, 9);\n      H.H.A(N._, 9, N.J); H.H.l(N._, 9); n(N.$, 32, 5); H.H.n(N.$, 5); H.H.A(N.$, 5, N.h); H.H.l(N.$, 5); n(N.Q, 19, 0); n(N.C, 286, 0);\n      n(N.D, 30, 0); n(N.v, 320, 0);\n    }()); return H.H.N;\n  }());\n\n  function _readInterlace(data, out) {\n    const w = out.width; const\n      h = out.height;\n    const bpp = _getBPP(out); const cbpp = bpp >> 3; const\n      bpl = Math.ceil(w * bpp / 8);\n    const img = new Uint8Array(h * bpl);\n    let di = 0;\n\n    const starting_row = [0, 0, 4, 0, 2, 0, 1];\n    const starting_col = [0, 4, 0, 2, 0, 1, 0];\n    const row_increment = [8, 8, 8, 4, 4, 2, 2];\n    const col_increment = [8, 8, 4, 4, 2, 2, 1];\n\n    let pass = 0;\n    while (pass < 7) {\n      const ri = row_increment[pass]; const\n        ci = col_increment[pass];\n      let sw = 0; let\n        sh = 0;\n      let cr = starting_row[pass]; while (cr < h) { cr += ri; sh++; }\n      let cc = starting_col[pass]; while (cc < w) { cc += ci; sw++; }\n      const bpll = Math.ceil(sw * bpp / 8);\n      _filterZero(data, out, di, sw, sh);\n\n      let y = 0; let\n        row = starting_row[pass];\n      while (row < h) {\n        let col = starting_col[pass];\n        let cdi = (di + y * bpll) << 3;\n\n        while (col < w) {\n          if (bpp == 1) {\n            var val = data[cdi >> 3]; val = (val >> (7 - (cdi & 7))) & 1;\n            img[row * bpl + (col >> 3)] |= (val << (7 - ((col & 7) << 0)));\n          }\n          if (bpp == 2) {\n            var val = data[cdi >> 3]; val = (val >> (6 - (cdi & 7))) & 3;\n            img[row * bpl + (col >> 2)] |= (val << (6 - ((col & 3) << 1)));\n          }\n          if (bpp == 4) {\n            var val = data[cdi >> 3]; val = (val >> (4 - (cdi & 7))) & 15;\n            img[row * bpl + (col >> 1)] |= (val << (4 - ((col & 1) << 2)));\n          }\n          if (bpp >= 8) {\n            const ii = row * bpl + col * cbpp;\n            for (let j = 0; j < cbpp; j++) img[ii + j] = data[(cdi >> 3) + j];\n          }\n          cdi += bpp; col += ci;\n        }\n        y++; row += ri;\n      }\n      if (sw * sh != 0) di += sh * (1 + bpll);\n      pass += 1;\n    }\n    return img;\n  }\n\n  function _getBPP(out) {\n    const noc = [1, null, 3, 1, 2, null, 4][out.ctype];\n    return noc * out.depth;\n  }\n\n  function _filterZero(data, out, off, w, h) {\n    let bpp = _getBPP(out); const\n      bpl = Math.ceil(w * bpp / 8);\n    bpp = Math.ceil(bpp / 8);\n\n    let i; let di; let type = data[off]; let\n      x = 0;\n\n    if (type > 1) data[off] = [0, 0, 1][type - 2];\n    if (type == 3) for (x = bpp; x < bpl; x++) data[x + 1] = (data[x + 1] + (data[x + 1 - bpp] >>> 1)) & 255;\n\n    for (let y = 0; y < h; y++) {\n      i = off + y * bpl; di = i + y + 1;\n      type = data[di - 1]; x = 0;\n\n      if (type == 0) for (; x < bpl; x++) data[i + x] = data[di + x];\n      else if (type == 1) {\n        for (; x < bpp; x++) data[i + x] = data[di + x];\n\t\t\t\t\t\t\t   for (; x < bpl; x++) data[i + x] = (data[di + x] + data[i + x - bpp]);\n      } else if (type == 2) { for (; x < bpl; x++) data[i + x] = (data[di + x] + data[i + x - bpl]); } else if (type == 3) {\n        for (; x < bpp; x++) data[i + x] = (data[di + x] + (data[i + x - bpl] >>> 1));\n\t\t\t\t\t\t\t   for (; x < bpl; x++) data[i + x] = (data[di + x] + ((data[i + x - bpl] + data[i + x - bpp]) >>> 1));\n      } else {\n        for (; x < bpp; x++) data[i + x] = (data[di + x] + _paeth(0, data[i + x - bpl], 0));\n\t\t\t\t\t\t\t   for (; x < bpl; x++) data[i + x] = (data[di + x] + _paeth(data[i + x - bpp], data[i + x - bpl], data[i + x - bpp - bpl]));\n      }\n    }\n    return data;\n  }\n\n  function _paeth(a, b, c) {\n    const p = a + b - c; const pa = (p - a); const pb = (p - b); const\n      pc = (p - c);\n    if (pa * pa <= pb * pb && pa * pa <= pc * pc) return a;\n    if (pb * pb <= pc * pc) return b;\n    return c;\n  }\n\n  function _IHDR(data, offset, out) {\n    out.width = _bin.readUint(data, offset); offset += 4;\n    out.height = _bin.readUint(data, offset); offset += 4;\n    out.depth = data[offset]; offset++;\n    out.ctype = data[offset]; offset++;\n    out.compress = data[offset]; offset++;\n    out.filter = data[offset]; offset++;\n    out.interlace = data[offset]; offset++;\n  }\n\n  function _copyTile(sb, sw, sh, tb, tw, th, xoff, yoff, mode) {\n    const w = Math.min(sw, tw); const\n      h = Math.min(sh, th);\n    let si = 0; let\n      ti = 0;\n    for (let y = 0; y < h; y++) {\n      for (let x = 0; x < w; x++) {\n        if (xoff >= 0 && yoff >= 0) { si = (y * sw + x) << 2; ti = ((yoff + y) * tw + xoff + x) << 2; } else { si = ((-yoff + y) * sw - xoff + x) << 2; ti = (y * tw + x) << 2; }\n\n        if (mode == 0) { tb[ti] = sb[si]; tb[ti + 1] = sb[si + 1]; tb[ti + 2] = sb[si + 2]; tb[ti + 3] = sb[si + 3]; } else if (mode == 1) {\n          var fa = sb[si + 3] * (1 / 255); var fr = sb[si] * fa; var fg = sb[si + 1] * fa; var\n            fb = sb[si + 2] * fa;\n          var ba = tb[ti + 3] * (1 / 255); var br = tb[ti] * ba; var bg = tb[ti + 1] * ba; var\n            bb = tb[ti + 2] * ba;\n\n          const ifa = 1 - fa; const oa = fa + ba * ifa; const\n            ioa = (oa == 0 ? 0 : 1 / oa);\n          tb[ti + 3] = 255 * oa;\n          tb[ti + 0] = (fr + br * ifa) * ioa;\n          tb[ti + 1] = (fg + bg * ifa) * ioa;\n          tb[ti + 2] = (fb + bb * ifa) * ioa;\n        } else if (mode == 2) {\t// copy only differences, otherwise zero\n          var fa = sb[si + 3]; var fr = sb[si]; var fg = sb[si + 1]; var\n            fb = sb[si + 2];\n          var ba = tb[ti + 3]; var br = tb[ti]; var bg = tb[ti + 1]; var\n            bb = tb[ti + 2];\n          if (fa == ba && fr == br && fg == bg && fb == bb) { tb[ti] = 0; tb[ti + 1] = 0; tb[ti + 2] = 0; tb[ti + 3] = 0; } else { tb[ti] = fr; tb[ti + 1] = fg; tb[ti + 2] = fb; tb[ti + 3] = fa; }\n        } else if (mode == 3) {\t// check if can be blended\n          var fa = sb[si + 3]; var fr = sb[si]; var fg = sb[si + 1]; var\n            fb = sb[si + 2];\n          var ba = tb[ti + 3]; var br = tb[ti]; var bg = tb[ti + 1]; var\n            bb = tb[ti + 2];\n          if (fa == ba && fr == br && fg == bg && fb == bb) continue;\n          // if(fa!=255 && ba!=0) return false;\n          if (fa < 220 && ba > 20) return false;\n        }\n      }\n    }\n    return true;\n  }\n\n  return {\n    decode,\n    toRGBA8,\n    _paeth,\n    _copyTile,\n    _bin,\n  };\n}());\n\n(function () {\n  const { _copyTile } = UPNG;\n  const { _bin } = UPNG;\n  const paeth = UPNG._paeth;\n  var crcLib = {\n    table: (function () {\n\t\t   const tab = new Uint32Array(256);\n\t\t   for (let n = 0; n < 256; n++) {\n        let c = n;\n        for (let k = 0; k < 8; k++) {\n          if (c & 1) c = 0xedb88320 ^ (c >>> 1);\n          else c >>>= 1;\n        }\n        tab[n] = c;\n      }\n      return tab;\n    }()),\n    update(c, buf, off, len) {\n      for (let i = 0; i < len; i++) c = crcLib.table[(c ^ buf[off + i]) & 0xff] ^ (c >>> 8);\n      return c;\n    },\n    crc(b, o, l) { return crcLib.update(0xffffffff, b, o, l) ^ 0xffffffff; },\n  };\n\n  function addErr(er, tg, ti, f) {\n    tg[ti] += (er[0] * f) >> 4; tg[ti + 1] += (er[1] * f) >> 4; tg[ti + 2] += (er[2] * f) >> 4; tg[ti + 3] += (er[3] * f) >> 4;\n  }\n  function N(x) { return Math.max(0, Math.min(255, x)); }\n  function D(a, b) {\n    const dr = a[0] - b[0]; const dg = a[1] - b[1]; const db = a[2] - b[2]; const\n      da = a[3] - b[3]; return (dr * dr + dg * dg + db * db + da * da);\n  }\n\n  // MTD: 0: None, 1: floyd-steinberg, 2: Bayer\n  function dither(sb, w, h, plte, tb, oind, MTD) {\n    if (MTD == null) MTD = 1;\n\n    const pc = plte.length; const nplt = []; const\n      rads = [];\n    for (var i = 0; i < pc; i++) {\n      const c = plte[i];\n      nplt.push([((c >>> 0) & 255), ((c >>> 8) & 255), ((c >>> 16) & 255), ((c >>> 24) & 255)]);\n    }\n    for (var i = 0; i < pc; i++) {\n      let ne = 0xffffffff; var\n        ni = 0;\n      for (var j = 0; j < pc; j++) { var ce = D(nplt[i], nplt[j]); if (j != i && ce < ne) { ne = ce; ni = j; } }\n      const hd = Math.sqrt(ne) / 2;\n      rads[i] = ~~(hd * hd);\n    }\n\n    const tb32 = new Uint32Array(tb.buffer);\n    const err = new Int16Array(w * h * 4);\n\n    /*\n\t\tvar S=2, M = [\n\t\t\t0,2,\n\t\t    3,1];  // */\n    //*\n    const S = 4; const\n      M = [\n\t\t\t 0, 8, 2, 10,\n\t\t    12, 4, 14, 6,\n\t\t\t 3, 11, 1, 9,\n        15, 7, 13, 5]; //* /\n    for (var i = 0; i < M.length; i++) M[i] = 255 * (-0.5 + (M[i] + 0.5) / (S * S));\n\n    for (let y = 0; y < h; y++) {\n      for (let x = 0; x < w; x++) {\n        var i = (y * w + x) * 4;\n\n        var cc;\n        if (MTD != 2) cc = [N(sb[i] + err[i]), N(sb[i + 1] + err[i + 1]), N(sb[i + 2] + err[i + 2]), N(sb[i + 3] + err[i + 3])];\n        else {\n          var ce = M[(y & (S - 1)) * S + (x & (S - 1))];\n          cc = [N(sb[i] + ce), N(sb[i + 1] + ce), N(sb[i + 2] + ce), N(sb[i + 3] + ce)];\n        }\n\n        var ni = 0; let\n          nd = 0xffffff;\n        for (var j = 0; j < pc; j++) {\n          const cd = D(cc, nplt[j]);\n          if (cd < nd) { nd = cd; ni = j; }\n        }\n\n        const nc = nplt[ni];\n        const er = [cc[0] - nc[0], cc[1] - nc[1], cc[2] - nc[2], cc[3] - nc[3]];\n\n        if (MTD == 1) {\n          // addErr(er, err, i+4, 16);\n          if (x != w - 1) addErr(er, err, i + 4, 7);\n          if (y != h - 1) {\n            if (x != 0) addErr(er, err, i + 4 * w - 4, 3);\n\t\t\t\t\t\t\t\t   addErr(er, err, i + 4 * w, 5);\n            if (x != w - 1) addErr(er, err, i + 4 * w + 4, 1);\n          }//* /\n        }\n        oind[i >> 2] = ni; tb32[i >> 2] = plte[ni];\n      }\n    }\n  }\n\n  function encode(bufs, w, h, ps, dels, tabs, forbidPlte) {\n    if (ps == null) ps = 0;\n    if (forbidPlte == null) forbidPlte = false;\n\n    const nimg = compress(bufs, w, h, ps, [false, false, false, 0, forbidPlte, false]);\n    compressPNG(nimg, -1);\n\n    return _main(nimg, w, h, dels, tabs);\n  }\n\n  function encodeLL(bufs, w, h, cc, ac, depth, dels, tabs) {\n    const nimg = { ctype: 0 + (cc == 1 ? 0 : 2) + (ac == 0 ? 0 : 4), depth, frames: [] };\n\n    const time = Date.now();\n    const bipp = (cc + ac) * depth; const\n      bipl = bipp * w;\n    for (let i = 0; i < bufs.length; i++) {\n      nimg.frames.push({\n        rect: {\n          x: 0, y: 0, width: w, height: h,\n        },\n        img: new Uint8Array(bufs[i]),\n        blend: 0,\n        dispose: 1,\n        bpp: Math.ceil(bipp / 8),\n        bpl: Math.ceil(bipl / 8),\n      });\n    }\n\n    compressPNG(nimg, 0, true);\n\n    const out = _main(nimg, w, h, dels, tabs);\n    return out;\n  }\n\n  function _main(nimg, w, h, dels, tabs) {\n    if (tabs == null) tabs = {};\n    const { crc } = crcLib;\n    const wUi = _bin.writeUint;\n    const wUs = _bin.writeUshort;\n    const wAs = _bin.writeASCII;\n    let offset = 8; const anim = nimg.frames.length > 1; let\n      pltAlpha = false;\n\n    let cicc;\n\n    let leng = 8 + (16 + 5 + 4) /* + (9+4) */ + (anim ? 20 : 0);\n    if (tabs.sRGB != null) leng += 8 + 1 + 4;\n    if (tabs.pHYs != null) leng += 8 + 9 + 4;\n    if (tabs.iCCP != null) { cicc = pako.deflate(tabs.iCCP); leng += 8 + 11 + 2 + cicc.length + 4; }\n    if (nimg.ctype == 3) {\n      var dl = nimg.plte.length;\n      for (var i = 0; i < dl; i++) if ((nimg.plte[i] >>> 24) != 255) pltAlpha = true;\n      leng += (8 + dl * 3 + 4) + (pltAlpha ? (8 + dl * 1 + 4) : 0);\n    }\n    for (var j = 0; j < nimg.frames.length; j++) {\n      var fr = nimg.frames[j];\n      if (anim) leng += 38;\n      leng += fr.cimg.length + 12;\n      if (j != 0) leng += 4;\n    }\n    leng += 12;\n\n    const data = new Uint8Array(leng);\n    const wr = [0x89, 0x50, 0x4e, 0x47, 0x0d, 0x0a, 0x1a, 0x0a];\n    for (var i = 0; i < 8; i++) data[i] = wr[i];\n\n    wUi(data, offset, 13); offset += 4;\n    wAs(data, offset, 'IHDR'); offset += 4;\n    wUi(data, offset, w); offset += 4;\n    wUi(data, offset, h); offset += 4;\n    data[offset] = nimg.depth; offset++; // depth\n    data[offset] = nimg.ctype; offset++; // ctype\n    data[offset] = 0; offset++; // compress\n    data[offset] = 0; offset++; // filter\n    data[offset] = 0; offset++; // interlace\n    wUi(data, offset, crc(data, offset - 17, 17)); offset += 4; // crc\n\n    // 13 bytes to say, that it is sRGB\n    if (tabs.sRGB != null) {\n      wUi(data, offset, 1); offset += 4;\n      wAs(data, offset, 'sRGB'); offset += 4;\n      data[offset] = tabs.sRGB; offset++;\n      wUi(data, offset, crc(data, offset - 5, 5)); offset += 4; // crc\n    }\n    if (tabs.iCCP != null) {\n      const sl = 11 + 2 + cicc.length;\n      wUi(data, offset, sl); offset += 4;\n      wAs(data, offset, 'iCCP'); offset += 4;\n      wAs(data, offset, 'ICC profile'); offset += 11; offset += 2;\n      data.set(cicc, offset); offset += cicc.length;\n      wUi(data, offset, crc(data, offset - (sl + 4), sl + 4)); offset += 4; // crc\n    }\n    if (tabs.pHYs != null) {\n      wUi(data, offset, 9); offset += 4;\n      wAs(data, offset, 'pHYs'); offset += 4;\n      wUi(data, offset, tabs.pHYs[0]); offset += 4;\n      wUi(data, offset, tabs.pHYs[1]); offset += 4;\n      data[offset] = tabs.pHYs[2];\t\t\toffset++;\n      wUi(data, offset, crc(data, offset - 13, 13)); offset += 4; // crc\n    }\n\n    if (anim) {\n      wUi(data, offset, 8); offset += 4;\n      wAs(data, offset, 'acTL'); offset += 4;\n      wUi(data, offset, nimg.frames.length); offset += 4;\n      wUi(data, offset, tabs.loop != null ? tabs.loop : 0); offset += 4;\n      wUi(data, offset, crc(data, offset - 12, 12)); offset += 4; // crc\n    }\n\n    if (nimg.ctype == 3) {\n      var dl = nimg.plte.length;\n      wUi(data, offset, dl * 3); offset += 4;\n      wAs(data, offset, 'PLTE'); offset += 4;\n      for (var i = 0; i < dl; i++) {\n        const ti = i * 3; const c = nimg.plte[i]; const r = (c) & 255; const g = (c >>> 8) & 255; const\n          b = (c >>> 16) & 255;\n        data[offset + ti + 0] = r; data[offset + ti + 1] = g; data[offset + ti + 2] = b;\n      }\n      offset += dl * 3;\n      wUi(data, offset, crc(data, offset - dl * 3 - 4, dl * 3 + 4)); offset += 4; // crc\n\n      if (pltAlpha) {\n        wUi(data, offset, dl); offset += 4;\n        wAs(data, offset, 'tRNS'); offset += 4;\n        for (var i = 0; i < dl; i++) data[offset + i] = (nimg.plte[i] >>> 24) & 255;\n        offset += dl;\n        wUi(data, offset, crc(data, offset - dl - 4, dl + 4)); offset += 4; // crc\n      }\n    }\n\n    let fi = 0;\n    for (var j = 0; j < nimg.frames.length; j++) {\n      var fr = nimg.frames[j];\n      if (anim) {\n        wUi(data, offset, 26); offset += 4;\n        wAs(data, offset, 'fcTL'); offset += 4;\n        wUi(data, offset, fi++); offset += 4;\n        wUi(data, offset, fr.rect.width); offset += 4;\n        wUi(data, offset, fr.rect.height); offset += 4;\n        wUi(data, offset, fr.rect.x); offset += 4;\n        wUi(data, offset, fr.rect.y); offset += 4;\n        wUs(data, offset, dels[j]); offset += 2;\n        wUs(data, offset, 1000); offset += 2;\n        data[offset] = fr.dispose; offset++;\t// dispose\n        data[offset] = fr.blend; offset++;\t// blend\n        wUi(data, offset, crc(data, offset - 30, 30)); offset += 4; // crc\n      }\n\n      const imgd = fr.cimg; var\n        dl = imgd.length;\n      wUi(data, offset, dl + (j == 0 ? 0 : 4)); offset += 4;\n      const ioff = offset;\n      wAs(data, offset, (j == 0) ? 'IDAT' : 'fdAT'); offset += 4;\n      if (j != 0) { wUi(data, offset, fi++); offset += 4; }\n      data.set(imgd, offset);\n      offset += dl;\n      wUi(data, offset, crc(data, ioff, offset - ioff)); offset += 4; // crc\n    }\n\n    wUi(data, offset, 0); offset += 4;\n    wAs(data, offset, 'IEND'); offset += 4;\n    wUi(data, offset, crc(data, offset - 4, 4)); offset += 4; // crc\n\n    return data.buffer;\n  }\n\n  function compressPNG(out, filter, levelZero) {\n    for (let i = 0; i < out.frames.length; i++) {\n      const frm = out.frames[i]; const nw = frm.rect.width; const\n        nh = frm.rect.height;\n      const fdata = new Uint8Array(nh * frm.bpl + nh);\n      frm.cimg = _filterZero(frm.img, nh, frm.bpp, frm.bpl, fdata, filter, levelZero);\n    }\n  }\n\n  function compress(bufs, w, h, ps, prms) // prms:  onlyBlend, minBits, forbidPlte\n  {\n    // var time = Date.now();\n    const onlyBlend = prms[0]; const evenCrd = prms[1]; const forbidPrev = prms[2]; const minBits = prms[3]; const forbidPlte = prms[4]; const\n      dith = prms[5];\n\n    let ctype = 6; let depth = 8; let\n      alphaAnd = 255;\n\n    for (var j = 0; j < bufs.length; j++) { // when not quantized, other frames can contain colors, that are not in an initial frame\n      const img = new Uint8Array(bufs[j]); var\n        ilen = img.length;\n      for (var i = 0; i < ilen; i += 4) alphaAnd &= img[i + 3];\n    }\n    const gotAlpha = (alphaAnd != 255);\n\n    // console.log(\"alpha check\", Date.now()-time);  time = Date.now();\n\n    // var brute = gotAlpha && forGIF;\t\t// brute : frames can only be copied, not \"blended\"\n    const frms = framize(bufs, w, h, onlyBlend, evenCrd, forbidPrev);\n    // console.log(\"framize\", Date.now()-time);  time = Date.now();\n\n    const cmap = {}; const plte = []; const\n      inds = [];\n\n    if (ps != 0) {\n      const nbufs = []; for (var i = 0; i < frms.length; i++) nbufs.push(frms[i].img.buffer);\n\n      const abuf = concatRGBA(nbufs); const\n        qres = quantize(abuf, ps);\n\n      for (var i = 0; i < qres.plte.length; i++) plte.push(qres.plte[i].est.rgba);\n\n      let cof = 0;\n      for (var i = 0; i < frms.length; i++) {\n        var frm = frms[i]; const bln = frm.img.length; var\n          ind = new Uint8Array(qres.inds.buffer, cof >> 2, bln >> 2); inds.push(ind);\n        const bb = new Uint8Array(qres.abuf, cof, bln);\n\n        // console.log(frm.img, frm.width, frm.height);\n        // var time = Date.now();\n        if (dith) dither(frm.img, frm.rect.width, frm.rect.height, plte, bb, ind);\n        // console.log(Date.now()-time);\n        frm.img.set(bb); cof += bln;\n      }\n\n      // console.log(\"quantize\", Date.now()-time);  time = Date.now();\n    } else {\n      // what if ps==0, but there are <=256 colors?  we still need to detect, if the palette could be used\n      for (var j = 0; j < frms.length; j++) { // when not quantized, other frames can contain colors, that are not in an initial frame\n        var frm = frms[j]; const img32 = new Uint32Array(frm.img.buffer); var nw = frm.rect.width; var\n          ilen = img32.length;\n        var ind = new Uint8Array(ilen); inds.push(ind);\n        for (var i = 0; i < ilen; i++) {\n          const c = img32[i];\n          if (i != 0 && c == img32[i - 1]) ind[i] = ind[i - 1];\n          else if (i > nw && c == img32[i - nw]) ind[i] = ind[i - nw];\n          else {\n            let cmc = cmap[c];\n            if (cmc == null) { cmap[c] = cmc = plte.length; plte.push(c); if (plte.length >= 300) break; }\n            ind[i] = cmc;\n          }\n        }\n      }\n      // console.log(\"make palette\", Date.now()-time);  time = Date.now();\n    }\n\n    const cc = plte.length; // console.log(\"colors:\",cc);\n    if (cc <= 256 && forbidPlte == false) {\n      if (cc <= 2) depth = 1; else if (cc <= 4) depth = 2; else if (cc <= 16) depth = 4; else depth = 8;\n      depth = Math.max(depth, minBits);\n    }\n\n    for (var j = 0; j < frms.length; j++) {\n      var frm = frms[j]; const nx = frm.rect.x; const ny = frm.rect.y; var nw = frm.rect.width; const\n        nh = frm.rect.height;\n      let cimg = frm.img; const\n        cimg32 = new Uint32Array(cimg.buffer);\n      let bpl = 4 * nw; let\n        bpp = 4;\n      if (cc <= 256 && forbidPlte == false) {\n        bpl = Math.ceil(depth * nw / 8);\n        var nimg = new Uint8Array(bpl * nh);\n        const inj = inds[j];\n        for (let y = 0; y < nh; y++) {\n          var i = y * bpl; const\n            ii = y * nw;\n          if (depth == 8) for (var x = 0; x < nw; x++) nimg[i + (x)] = (inj[ii + x]);\n          else if (depth == 4) for (var x = 0; x < nw; x++) nimg[i + (x >> 1)] |= (inj[ii + x] << (4 - (x & 1) * 4));\n          else if (depth == 2) for (var x = 0; x < nw; x++) nimg[i + (x >> 2)] |= (inj[ii + x] << (6 - (x & 3) * 2));\n          else if (depth == 1) for (var x = 0; x < nw; x++) nimg[i + (x >> 3)] |= (inj[ii + x] << (7 - (x & 7) * 1));\n        }\n        cimg = nimg; ctype = 3; bpp = 1;\n      } else if (gotAlpha == false && frms.length == 1) {\t// some next \"reduced\" frames may contain alpha for blending\n        var nimg = new Uint8Array(nw * nh * 3); const\n          area = nw * nh;\n        for (var i = 0; i < area; i++) {\n          const ti = i * 3; const\n            qi = i * 4; nimg[ti] = cimg[qi]; nimg[ti + 1] = cimg[qi + 1]; nimg[ti + 2] = cimg[qi + 2];\n        }\n        cimg = nimg; ctype = 2; bpp = 3; bpl = 3 * nw;\n      }\n      frm.img = cimg; frm.bpl = bpl; frm.bpp = bpp;\n    }\n    // console.log(\"colors => palette indices\", Date.now()-time);  time = Date.now();\n\n    return {\n      ctype, depth, plte, frames: frms,\n    };\n  }\n  function framize(bufs, w, h, alwaysBlend, evenCrd, forbidPrev) {\n    /*  DISPOSE\n\t\t\t- 0 : no change\n\t\t\t- 1 : clear to transparent\n\t\t\t- 2 : retstore to content before rendering (previous frame disposed)\n\t\t\tBLEND\n\t\t\t- 0 : replace\n\t\t\t- 1 : blend\n\t\t*/\n    const frms = [];\n    for (var j = 0; j < bufs.length; j++) {\n      const cimg = new Uint8Array(bufs[j]); const\n        cimg32 = new Uint32Array(cimg.buffer);\n      var nimg;\n\n      let nx = 0; let ny = 0; let nw = w; let nh = h; let\n        blend = alwaysBlend ? 1 : 0;\n      if (j != 0) {\n        const tlim = (forbidPrev || alwaysBlend || j == 1 || frms[j - 2].dispose != 0) ? 1 : 2; let tstp = 0; let\n          tarea = 1e9;\n        for (let it = 0; it < tlim; it++) {\n          var pimg = new Uint8Array(bufs[j - 1 - it]); const\n            p32 = new Uint32Array(bufs[j - 1 - it]);\n          let mix = w; let miy = h; let max = -1; let may = -1;\n          for (let y = 0; y < h; y++) {\n            for (let x = 0; x < w; x++) {\n              var i = y * w + x;\n              if (cimg32[i] != p32[i]) {\n                if (x < mix) mix = x; if (x > max) max = x;\n                if (y < miy) miy = y; if (y > may) may = y;\n              }\n            }\n          }\n          if (max == -1) mix = miy = max = may = 0;\n          if (evenCrd) { if ((mix & 1) == 1)mix--; if ((miy & 1) == 1)miy--; }\n          const sarea = (max - mix + 1) * (may - miy + 1);\n          if (sarea < tarea) {\n            tarea = sarea; tstp = it;\n            nx = mix; ny = miy; nw = max - mix + 1; nh = may - miy + 1;\n          }\n        }\n\n        // alwaysBlend: pokud zjistím, že blendit nelze, nastavím předchozímu snímku dispose=1. Zajistím, aby obsahoval můj obdélník.\n        var pimg = new Uint8Array(bufs[j - 1 - tstp]);\n        if (tstp == 1) frms[j - 1].dispose = 2;\n\n        nimg = new Uint8Array(nw * nh * 4);\n        _copyTile(pimg, w, h, nimg, nw, nh, -nx, -ny, 0);\n\n        blend = _copyTile(cimg, w, h, nimg, nw, nh, -nx, -ny, 3) ? 1 : 0;\n        if (blend == 1) {\n          _prepareDiff(cimg, w, h, nimg, {\n            x: nx, y: ny, width: nw, height: nh,\n          });\n        } else _copyTile(cimg, w, h, nimg, nw, nh, -nx, -ny, 0);\n      } else nimg = cimg.slice(0);\t// img may be rewritten further ... don't rewrite input\n\n      frms.push({\n        rect: {\n          x: nx, y: ny, width: nw, height: nh,\n        },\n        img: nimg,\n        blend,\n        dispose: 0,\n      });\n    }\n\n    if (alwaysBlend) {\n      for (var j = 0; j < frms.length; j++) {\n        var frm = frms[j]; if (frm.blend == 1) continue;\n        const r0 = frm.rect; const\n          r1 = frms[j - 1].rect;\n        const miX = Math.min(r0.x, r1.x); const\n          miY = Math.min(r0.y, r1.y);\n        const maX = Math.max(r0.x + r0.width, r1.x + r1.width); const\n          maY = Math.max(r0.y + r0.height, r1.y + r1.height);\n        const r = {\n          x: miX, y: miY, width: maX - miX, height: maY - miY,\n        };\n\n        frms[j - 1].dispose = 1;\n        if (j - 1 != 0) _updateFrame(bufs, w, h, frms, j - 1, r, evenCrd);\n        _updateFrame(bufs, w, h, frms, j, r, evenCrd);\n      }\n    }\n    let area = 0;\n    if (bufs.length != 1) {\n      for (var i = 0; i < frms.length; i++) {\n        var frm = frms[i];\n        area += frm.rect.width * frm.rect.height;\n      // if(i==0 || frm.blend!=1) continue;\n      // var ob = new Uint8Array(\n      // console.log(frm.blend, frm.dispose, frm.rect);\n      }\n    }\n    // if(area!=0) console.log(area);\n    return frms;\n  }\n  function _updateFrame(bufs, w, h, frms, i, r, evenCrd) {\n    const U8 = Uint8Array; const\n      U32 = Uint32Array;\n    const pimg = new U8(bufs[i - 1]); const pimg32 = new U32(bufs[i - 1]); const\n      nimg = i + 1 < bufs.length ? new U8(bufs[i + 1]) : null;\n    const cimg = new U8(bufs[i]); const\n      cimg32 = new U32(cimg.buffer);\n\n    let mix = w; let miy = h; let max = -1; let may = -1;\n    for (let y = 0; y < r.height; y++) {\n      for (let x = 0; x < r.width; x++) {\n        const cx = r.x + x; const\n          cy = r.y + y;\n        const j = cy * w + cx; const\n          cc = cimg32[j];\n        // no need to draw transparency, or to dispose it. Or, if writing the same color and the next one does not need transparency.\n        if (cc == 0 || (frms[i - 1].dispose == 0 && pimg32[j] == cc && (nimg == null || nimg[j * 4 + 3] != 0))/**/) {} else {\n          if (cx < mix) mix = cx; if (cx > max) max = cx;\n          if (cy < miy) miy = cy; if (cy > may) may = cy;\n        }\n      }\n    }\n    if (max == -1) mix = miy = max = may = 0;\n    if (evenCrd) { if ((mix & 1) == 1)mix--; if ((miy & 1) == 1)miy--; }\n    r = {\n      x: mix, y: miy, width: max - mix + 1, height: may - miy + 1,\n    };\n\n    const fr = frms[i]; fr.rect = r; fr.blend = 1; fr.img = new Uint8Array(r.width * r.height * 4);\n    if (frms[i - 1].dispose == 0) {\n      _copyTile(pimg, w, h, fr.img, r.width, r.height, -r.x, -r.y, 0);\n      _prepareDiff(cimg, w, h, fr.img, r);\n    } else _copyTile(cimg, w, h, fr.img, r.width, r.height, -r.x, -r.y, 0);\n  }\n  function _prepareDiff(cimg, w, h, nimg, rec) {\n    _copyTile(cimg, w, h, nimg, rec.width, rec.height, -rec.x, -rec.y, 2);\n  }\n\n  function _filterZero(img, h, bpp, bpl, data, filter, levelZero) {\n    const fls = []; let\n      ftry = [0, 1, 2, 3, 4];\n    if (filter != -1) ftry = [filter];\n    else if (h * bpl > 500000 || bpp == 1) ftry = [0];\n    let opts; if (levelZero) opts = { level: 0 };\n\n    const CMPR = UZIP;\n\n    const time = Date.now();\n    for (var i = 0; i < ftry.length; i++) {\n      for (let y = 0; y < h; y++) _filterLine(data, img, y, bpl, bpp, ftry[i]);\n      // var nimg = new Uint8Array(data.length);\n      // var sz = UZIP.F.deflate(data, nimg);  fls.push(nimg.slice(0,sz));\n      // var dfl = pako[\"deflate\"](data), dl=dfl.length-4;\n      // var crc = (dfl[dl+3]<<24)|(dfl[dl+2]<<16)|(dfl[dl+1]<<8)|(dfl[dl+0]<<0);\n      // console.log(crc, UZIP.adler(data,2,data.length-6));\n      fls.push(CMPR.deflate(data, opts));\n    }\n\n    let ti; let\n      tsize = 1e9;\n    for (var i = 0; i < fls.length; i++) if (fls[i].length < tsize) { ti = i; tsize = fls[i].length; }\n    return fls[ti];\n  }\n  function _filterLine(data, img, y, bpl, bpp, type) {\n    const i = y * bpl; let\n      di = i + y;\n    data[di] = type; di++;\n\n    if (type == 0) {\n      if (bpl < 500) for (var x = 0; x < bpl; x++) data[di + x] = img[i + x];\n      else data.set(new Uint8Array(img.buffer, i, bpl), di);\n    } else if (type == 1) {\n      for (var x = 0; x < bpp; x++) data[di + x] = img[i + x];\n      for (var x = bpp; x < bpl; x++) data[di + x] = (img[i + x] - img[i + x - bpp] + 256) & 255;\n    } else if (y == 0) {\n      for (var x = 0; x < bpp; x++) data[di + x] = img[i + x];\n\n      if (type == 2) for (var x = bpp; x < bpl; x++) data[di + x] = img[i + x];\n      if (type == 3) for (var x = bpp; x < bpl; x++) data[di + x] = (img[i + x] - (img[i + x - bpp] >> 1) + 256) & 255;\n      if (type == 4) for (var x = bpp; x < bpl; x++) data[di + x] = (img[i + x] - paeth(img[i + x - bpp], 0, 0) + 256) & 255;\n    } else {\n      if (type == 2) { for (var x = 0; x < bpl; x++) data[di + x] = (img[i + x] + 256 - img[i + x - bpl]) & 255; }\n      if (type == 3) {\n        for (var x = 0; x < bpp; x++) data[di + x] = (img[i + x] + 256 - (img[i + x - bpl] >> 1)) & 255;\n\t\t\t\t\t\t  for (var x = bpp; x < bpl; x++) data[di + x] = (img[i + x] + 256 - ((img[i + x - bpl] + img[i + x - bpp]) >> 1)) & 255;\n      }\n      if (type == 4) {\n        for (var x = 0; x < bpp; x++) data[di + x] = (img[i + x] + 256 - paeth(0, img[i + x - bpl], 0)) & 255;\n\t\t\t\t\t\t  for (var x = bpp; x < bpl; x++) data[di + x] = (img[i + x] + 256 - paeth(img[i + x - bpp], img[i + x - bpl], img[i + x - bpp - bpl])) & 255;\n      }\n    }\n  }\n\n  function quantize(abuf, ps) {\n    const sb = new Uint8Array(abuf); const tb = sb.slice(0); const\n      tb32 = new Uint32Array(tb.buffer);\n\n    const KD = getKDtree(tb, ps);\n    const root = KD[0]; const\n      leafs = KD[1];\n\n    const len = sb.length;\n\n    const inds = new Uint8Array(len >> 2); let\n      nd;\n    if (sb.length < 20e6) // precise, but slow :(\n    {\n      for (var i = 0; i < len; i += 4) {\n        var r = sb[i] * (1 / 255); var g = sb[i + 1] * (1 / 255); var b = sb[i + 2] * (1 / 255); var\n          a = sb[i + 3] * (1 / 255);\n\n        nd = getNearest(root, r, g, b, a);\n        inds[i >> 2] = nd.ind; tb32[i >> 2] = nd.est.rgba;\n      }\n    } else {\n      for (var i = 0; i < len; i += 4) {\n        var r = sb[i] * (1 / 255); var g = sb[i + 1] * (1 / 255); var b = sb[i + 2] * (1 / 255); var\n          a = sb[i + 3] * (1 / 255);\n\n        nd = root; while (nd.left) nd = (planeDst(nd.est, r, g, b, a) <= 0) ? nd.left : nd.right;\n        inds[i >> 2] = nd.ind; tb32[i >> 2] = nd.est.rgba;\n      }\n    }\n    return { abuf: tb.buffer, inds, plte: leafs };\n  }\n\n  function getKDtree(nimg, ps, err) {\n    if (err == null) err = 0.0001;\n    const nimg32 = new Uint32Array(nimg.buffer);\n\n    const root = {\n      i0: 0, i1: nimg.length, bst: null, est: null, tdst: 0, left: null, right: null,\n    }; // basic statistic, extra statistic\n    root.bst = stats(nimg, root.i0, root.i1); root.est = estats(root.bst);\n    const leafs = [root];\n\n    while (leafs.length < ps) {\n      let maxL = 0; let\n        mi = 0;\n      for (var i = 0; i < leafs.length; i++) if (leafs[i].est.L > maxL) { maxL = leafs[i].est.L; mi = i; }\n      if (maxL < err) break;\n      const node = leafs[mi];\n\n      const s0 = splitPixels(nimg, nimg32, node.i0, node.i1, node.est.e, node.est.eMq255);\n      const s0wrong = (node.i0 >= s0 || node.i1 <= s0);\n      // console.log(maxL, leafs.length, mi);\n      if (s0wrong) { node.est.L = 0; continue; }\n\n      const ln = {\n        i0: node.i0, i1: s0, bst: null, est: null, tdst: 0, left: null, right: null,\n      }; ln.bst = stats(nimg, ln.i0, ln.i1);\n      ln.est = estats(ln.bst);\n      const rn = {\n        i0: s0, i1: node.i1, bst: null, est: null, tdst: 0, left: null, right: null,\n      }; rn.bst = { R: [], m: [], N: node.bst.N - ln.bst.N };\n      for (var i = 0; i < 16; i++) rn.bst.R[i] = node.bst.R[i] - ln.bst.R[i];\n      for (var i = 0; i < 4; i++) rn.bst.m[i] = node.bst.m[i] - ln.bst.m[i];\n      rn.est = estats(rn.bst);\n\n      node.left = ln; node.right = rn;\n      leafs[mi] = ln; leafs.push(rn);\n    }\n    leafs.sort((a, b) => b.bst.N - a.bst.N);\n    for (var i = 0; i < leafs.length; i++) leafs[i].ind = i;\n    return [root, leafs];\n  }\n\n  function getNearest(nd, r, g, b, a) {\n    if (nd.left == null) { nd.tdst = dist(nd.est.q, r, g, b, a); return nd; }\n    const pd = planeDst(nd.est, r, g, b, a);\n\n    let node0 = nd.left; let\n      node1 = nd.right;\n    if (pd > 0) { node0 = nd.right; node1 = nd.left; }\n\n    const ln = getNearest(node0, r, g, b, a);\n    if (ln.tdst <= pd * pd) return ln;\n    const rn = getNearest(node1, r, g, b, a);\n    return rn.tdst < ln.tdst ? rn : ln;\n  }\n  function planeDst(est, r, g, b, a) { const { e } = est; return e[0] * r + e[1] * g + e[2] * b + e[3] * a - est.eMq; }\n  function dist(q, r, g, b, a) {\n    const d0 = r - q[0]; const d1 = g - q[1]; const d2 = b - q[2]; const\n      d3 = a - q[3]; return d0 * d0 + d1 * d1 + d2 * d2 + d3 * d3;\n  }\n\n  function splitPixels(nimg, nimg32, i0, i1, e, eMq) {\n    i1 -= 4;\n    const shfs = 0;\n    while (i0 < i1) {\n      while (vecDot(nimg, i0, e) <= eMq) i0 += 4;\n      while (vecDot(nimg, i1, e) > eMq) i1 -= 4;\n      if (i0 >= i1) break;\n\n      const t = nimg32[i0 >> 2]; nimg32[i0 >> 2] = nimg32[i1 >> 2]; nimg32[i1 >> 2] = t;\n\n      i0 += 4; i1 -= 4;\n    }\n    while (vecDot(nimg, i0, e) > eMq) i0 -= 4;\n    return i0 + 4;\n  }\n  function vecDot(nimg, i, e) {\n    return nimg[i] * e[0] + nimg[i + 1] * e[1] + nimg[i + 2] * e[2] + nimg[i + 3] * e[3];\n  }\n  function stats(nimg, i0, i1) {\n    const R = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0];\n    const m = [0, 0, 0, 0];\n    const N = (i1 - i0) >> 2;\n    for (let i = i0; i < i1; i += 4) {\n      const r = nimg[i] * (1 / 255); const g = nimg[i + 1] * (1 / 255); const b = nimg[i + 2] * (1 / 255); const\n        a = nimg[i + 3] * (1 / 255);\n      // var r = nimg[i], g = nimg[i+1], b = nimg[i+2], a = nimg[i+3];\n      m[0] += r; m[1] += g; m[2] += b; m[3] += a;\n\n      R[0] += r * r; R[1] += r * g; R[2] += r * b; R[3] += r * a;\n\t\t\t\t\t\t   R[5] += g * g; R[6] += g * b; R[7] += g * a;\n\t\t\t\t\t\t\t\t\t\t  R[10] += b * b; R[11] += b * a;\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t R[15] += a * a;\n    }\n    R[4] = R[1]; R[8] = R[2]; R[9] = R[6]; R[12] = R[3]; R[13] = R[7]; R[14] = R[11];\n\n    return { R, m, N };\n  }\n  function estats(stats) {\n    const { R } = stats;\n    const { m } = stats;\n    const { N } = stats;\n\n    // when all samples are equal, but N is large (millions), the Rj can be non-zero ( 0.0003.... - precission error)\n    const m0 = m[0]; const m1 = m[1]; const m2 = m[2]; const m3 = m[3]; const\n      iN = (N == 0 ? 0 : 1 / N);\n    const Rj = [\n      R[0] - m0 * m0 * iN, R[1] - m0 * m1 * iN, R[2] - m0 * m2 * iN, R[3] - m0 * m3 * iN,\n      R[4] - m1 * m0 * iN, R[5] - m1 * m1 * iN, R[6] - m1 * m2 * iN, R[7] - m1 * m3 * iN,\n      R[8] - m2 * m0 * iN, R[9] - m2 * m1 * iN, R[10] - m2 * m2 * iN, R[11] - m2 * m3 * iN,\n      R[12] - m3 * m0 * iN, R[13] - m3 * m1 * iN, R[14] - m3 * m2 * iN, R[15] - m3 * m3 * iN,\n    ];\n\n    const A = Rj; const\n      M = M4;\n    let b = [Math.random(), Math.random(), Math.random(), Math.random()]; let mi = 0; let\n      tmi = 0;\n\n    if (N != 0) {\n      for (let i = 0; i < 16; i++) {\n        b = M.multVec(A, b); tmi = Math.sqrt(M.dot(b, b)); b = M.sml(1 / tmi, b);\n        if (i != 0 && Math.abs(tmi - mi) < 1e-9) break; mi = tmi;\n      }\n    }\n    // b = [0,0,1,0];  mi=N;\n    const q = [m0 * iN, m1 * iN, m2 * iN, m3 * iN];\n    const eMq255 = M.dot(M.sml(255, q), b);\n\n    return {\n      Cov: Rj,\n      q,\n      e: b,\n      L: mi,\n      eMq255,\n      eMq: M.dot(b, q),\n      rgba: (((Math.round(255 * q[3]) << 24) | (Math.round(255 * q[2]) << 16) | (Math.round(255 * q[1]) << 8) | (Math.round(255 * q[0]) << 0)) >>> 0),\n    };\n  }\n  var M4 = {\n    multVec(m, v) {\n      return [\n        m[0] * v[0] + m[1] * v[1] + m[2] * v[2] + m[3] * v[3],\n        m[4] * v[0] + m[5] * v[1] + m[6] * v[2] + m[7] * v[3],\n        m[8] * v[0] + m[9] * v[1] + m[10] * v[2] + m[11] * v[3],\n        m[12] * v[0] + m[13] * v[1] + m[14] * v[2] + m[15] * v[3],\n      ];\n    },\n    dot(x, y) { return x[0] * y[0] + x[1] * y[1] + x[2] * y[2] + x[3] * y[3]; },\n    sml(a, y) { return [a * y[0], a * y[1], a * y[2], a * y[3]]; },\n  };\n\n  function concatRGBA(bufs) {\n    let tlen = 0;\n    for (var i = 0; i < bufs.length; i++) tlen += bufs[i].byteLength;\n    const nimg = new Uint8Array(tlen); let\n      noff = 0;\n    for (var i = 0; i < bufs.length; i++) {\n      const img = new Uint8Array(bufs[i]); const\n        il = img.length;\n      for (let j = 0; j < il; j += 4) {\n        let r = img[j]; let g = img[j + 1]; let b = img[j + 2]; const\n          a = img[j + 3];\n        if (a == 0) r = g = b = 0;\n        nimg[noff + j] = r; nimg[noff + j + 1] = g; nimg[noff + j + 2] = b; nimg[noff + j + 3] = a;\n      }\n      noff += il;\n    }\n    return nimg.buffer;\n  }\n\n  UPNG.encode = encode;\n  UPNG.encodeLL = encodeLL;\n  UPNG.encode.compress = compress;\n  UPNG.encode.dither = dither;\n\n  UPNG.quantize = quantize;\n  UPNG.quantize.getKDtree = getKDtree;\n  UPNG.quantize.getNearest = getNearest;\n}());\n\nexport default UPNG;\n", "// https://github.com/marcosvega91/canvas-to-bmp/blob/77aaf2221647a6533b1926cb637c7cd2bc432d9b/src/canvastobmp.js\n\n/**\n * Static helper object that can convert a CORS-compliant canvas element\n * to a 32-bits BMP file (buffer, Blob and data-URI).\n *\n * @type {{toArrayBuffer: Function, toBlob: Function, toDataURL: Function}}\n * @namespace\n */\nconst CanvasToBMP = {\n\n  /**\n\t * Convert a canvas element to ArrayBuffer containing a BMP file\n\t * with support for 32-bit format (alpha). The call is asynchronous\n\t * so a callback must be provided.\n\t *\n\t * Note that CORS requirement must be fulfilled.\n\t *\n\t * @param {HTMLCanvasElement} canvas - the canvas element to convert\n\t * @param {function} callback - called when conversion is done. Argument is ArrayBuffer\n\t * @static\n\t */\n  toArrayBuffer(canvas, callback) {\n    const w = canvas.width;\n    const h = canvas.height;\n    const w4 = w << 2;\n    const idata = canvas.getContext('2d').getImageData(0, 0, w, h);\n    const data32 = new Uint32Array(idata.data.buffer);\n\n    const stride = ((32 * w + 31) / 32) << 2;\n    const pixelArraySize = stride * h;\n    const fileLength = 122 + pixelArraySize;\n\n    const file = new ArrayBuffer(fileLength);\n    const view = new DataView(file);\n    const blockSize = 1 << 20;\n    let block = blockSize;\n    let y = 0; let x; let v; let a; let pos = 0; let p; let\n      s = 0;\n\n    // Header\n    set16(0x4d42);\t\t\t\t\t\t\t\t\t\t// BM\n    set32(fileLength);\t\t\t\t\t\t\t\t\t// total length\n    seek(4);\t\t\t\t\t\t\t\t\t\t\t// skip unused fields\n    set32(0x7a);\t\t\t\t\t\t\t\t\t\t// offset to pixels\n\n    // DIB header\n    set32(0x6c);\t\t\t\t\t\t\t\t\t\t// header size (108)\n    set32(w);\n    set32(-h >>> 0);\t\t\t\t\t\t\t\t\t// negative = top-to-bottom\n    set16(1);\t\t\t\t\t\t\t\t\t\t\t// 1 plane\n    set16(32);\t\t\t\t\t\t\t\t\t\t\t// 32-bits (RGBA)\n    set32(3);\t\t\t\t\t\t\t\t\t\t\t// no compression (BI_BITFIELDS, 3)\n    set32(pixelArraySize);\t\t\t\t\t\t\t\t// bitmap size incl. padding (stride x height)\n    set32(2835);\t\t\t\t\t\t\t\t\t\t// pixels/meter h (~72 DPI x 39.3701 inch/m)\n    set32(2835);\t\t\t\t\t\t\t\t\t\t// pixels/meter v\n    seek(8);\t\t\t\t\t\t\t\t\t\t\t// skip color/important colors\n    set32(0xff0000);\t\t\t\t\t\t\t\t\t// red channel mask\n    set32(0xff00);\t\t\t\t\t\t\t\t\t\t// green channel mask\n    set32(0xff);\t\t\t\t\t\t\t\t\t\t// blue channel mask\n    set32(0xff000000);\t\t\t\t\t\t\t\t\t// alpha channel mask\n    set32(0x57696e20);\t\t\t\t\t\t\t\t\t// \" win\" color space\n\n    (function convert() {\n      // bitmap data, change order of ABGR to BGRA (msb-order)\n      while (y < h && block > 0) {\n        p = 0x7a + y * stride;\t\t\t\t\t\t// offset + stride x height\n        x = 0;\n\n        while (x < w4) {\n          block--;\n          v = data32[s++];\t\t\t\t\t\t// get ABGR\n          a = v >>> 24;\t\t\t\t\t\t\t// alpha\n          view.setUint32(p + x, (v << 8) | a); // set BGRA (msb order)\n          x += 4;\n        }\n        y++;\n      }\n\n      if (s < data32.length) {\n        block = blockSize;\n        setTimeout(convert, CanvasToBMP._dly);\n      } else callback(file);\n    }());\n\n    // helper method to move current buffer position\n    function set16(data) {\n      view.setUint16(pos, data, true);\n      pos += 2;\n    }\n\n    function set32(data) {\n      view.setUint32(pos, data, true);\n      pos += 4;\n    }\n\n    function seek(delta) { pos += delta; }\n  },\n\n  /**\n\t * Converts a canvas to BMP file, returns a Blob representing the\n\t * file. This can be used with URL.createObjectURL(). The call is\n\t * asynchronous so a callback must be provided.\n\t *\n\t * Note that CORS requirement must be fulfilled.\n\t *\n\t * @param {HTMLCanvasElement} canvas - the canvas element to convert\n\t * @param {function} callback - called when conversion is done. Argument is a Blob\n\t * @static\n\t */\n  toBlob(canvas, callback) {\n    this.toArrayBuffer(canvas, (file) => {\n      callback(new Blob([file], { type: 'image/bmp' }));\n    });\n  },\n\n  // /**\n\t//  * Converts a canvas to BMP file, returns an ObjectURL (for Blob)\n\t//  * representing the file. The call is asynchronous so a callback\n\t//  * must be provided.\n\t//  *\n\t//  * **Important**: To avoid memory-leakage you must revoke the returned\n\t//  * ObjectURL when no longer needed:\n\t//  *\n\t//  *     var _URL = self.URL || self.webkitURL || self;\n\t//  *     _URL.revokeObjectURL(url);\n\t//  *\n\t//  * Note that CORS requirement must be fulfilled.\n\t//  *\n\t//  * @param {HTMLCanvasElement} canvas - the canvas element to convert\n\t//  * @param {function} callback - called when conversion is done. Argument is a Blob\n\t//  * @static\n\t//  */\n  // toObjectURL(canvas, callback) {\n  //   this.toBlob(canvas, (blob) => {\n  //     const url = self.URL || self.webkitURL || self;\n  //     callback(url.createObjectURL(blob));\n  //   });\n  // },\n\n  // /**\n\t//  * Converts the canvas to a data-URI representing a BMP file. The\n\t//  * call is asynchronous so a callback must be provided.\n\t//  *\n\t//  * Note that CORS requirement must be fulfilled.\n\t//  *\n\t//  * @param {HTMLCanvasElement} canvas - the canvas element to convert\n\t//  * @param {function} callback - called when conversion is done. Argument is an data-URI (string)\n\t//  * @static\n\t//  */\n  // toDataURL(canvas, callback) {\n  //   this.toArrayBuffer(canvas, (file) => {\n  //     const buffer = new Uint8Array(file);\n  //     const blockSize = 1 << 20;\n  //     let block = blockSize;\n  //     let bs = ''; let base64 = ''; let i = 0; let\n  //       l = buffer.length;\n\n  //     // This is a necessary step before we can use btoa. We can\n  //     // replace this later with a direct byte-buffer to Base-64 routine.\n  //     // Will do for now, impacts only with very large bitmaps (in which\n  //     // case toBlob should be used).\n  //     (function prepBase64() {\n  //       while (i < l && block-- > 0) bs += String.fromCharCode(buffer[i++]);\n\n  //       if (i < l) {\n  //         block = blockSize;\n  //         setTimeout(prepBase64, CanvasToBMP._dly);\n  //       } else {\n  //         // convert string to Base-64\n  //         i = 0;\n  //         l = bs.length;\n  //         block = 180000;\t\t// must be divisible by 3\n\n  //         (function toBase64() {\n  //           base64 += btoa(bs.substr(i, block));\n  //           i += block;\n  //           (i < l)\n  //             ? setTimeout(toBase64, CanvasToBMP._dly)\n  //             : callback(`data:image/bmp;base64,${base64}`);\n  //         }());\n  //       }\n  //     }());\n  //   });\n  // },\n  _dly: 9,\t// delay for async operations\n};\nexport default CanvasToBMP;\n", "export default {\n  CHROME: 'CHROME',\n  FIREFOX: 'FIREFOX',\n  DESKTOP_SAFARI: 'DESKTOP_SAFARI',\n  IE: 'IE',\n  IOS: 'IOS',\n  ETC: 'ETC',\n};\n", "import BROWSER_NAME from './browser-name';\n\n// see: https://github.com/jhildenbiddle/canvas-size#test-results\n// see: https://developer.mozilla.org/en-US/docs/Web/HTML/Element/canvas#maximum_canvas_size\nexport default {\n  [BROWSER_NAME.CHROME]: 16384,\n  [BROWSER_NAME.FIREFOX]: 11180,\n  [BROWSER_NAME.DESKTOP_SAFARI]: 16384,\n  [BROWSER_NAME.IE]: 8192,\n  [BROWSER_NAME.IOS]: 4096,\n  [BROWSER_NAME.ETC]: 8192,\n};\n", "import UPNG from './UPNG';\nimport CanvasToBMP from './canvastobmp';\nimport MAX_CANVAS_SIZE from './config/max-canvas-size';\nimport BROWSER_NAME from './config/browser-name';\n\nconst isBrowser = typeof window !== 'undefined'; // change browser environment to support SSR\nconst inWebWorker = typeof WorkerGlobalScope !== 'undefined' && self instanceof WorkerGlobalScope;\n\n// add support for cordova-plugin-file\nconst moduleMapper = isBrowser && window.cordova && window.cordova.require && window.cordova.require('cordova/modulemapper');\nexport const CustomFile = (isBrowser || inWebWorker) && ((moduleMapper && moduleMapper.getOriginalSymbol(window, 'File')) || (typeof File !== 'undefined' && File));\nexport const CustomFileReader = (isBrowser || inWebWorker) && ((moduleMapper && moduleMapper.getOriginalSymbol(window, 'FileReader')) || (typeof FileReader !== 'undefined' && FileReader));\n\n/**\n * getFilefromDataUrl\n *\n * @param {string} dataUrl\n * @param {string} filename\n * @param {number} [lastModified=Date.now()]\n * @returns {Promise<File | Blob>}\n */\nexport function getFilefromDataUrl(dataUrl, filename, lastModified = Date.now()) {\n  return new Promise((resolve) => {\n    const arr = dataUrl.split(',');\n    const mime = arr[0].match(/:(.*?);/)[1];\n    const bstr = globalThis.atob(arr[1]);\n    let n = bstr.length;\n    const u8arr = new Uint8Array(n);\n    while (n--) {\n      u8arr[n] = bstr.charCodeAt(n);\n    }\n    const file = new Blob([u8arr], { type: mime });\n    file.name = filename;\n    file.lastModified = lastModified;\n    resolve(file);\n\n    // Safari has issue with File constructor not being able to POST in FormData\n    // https://github.com/Donaldcwl/browser-image-compression/issues/8\n    // https://bugs.webkit.org/show_bug.cgi?id=165081\n    // let file\n    // try {\n    //   file = new File([u8arr], filename, { type: mime }) // Edge do not support File constructor\n    // } catch (e) {\n    //   file = new Blob([u8arr], { type: mime })\n    //   file.name = filename\n    //   file.lastModified = lastModified\n    // }\n    // resolve(file)\n  });\n}\n\n/**\n * getDataUrlFromFile\n *\n * @param {File | Blob} file\n * @returns {Promise<string>}\n */\nexport function getDataUrlFromFile(file) {\n  return new Promise((resolve, reject) => {\n    const reader = new CustomFileReader();\n    reader.onload = () => resolve(reader.result);\n    reader.onerror = (e) => reject(e);\n    reader.readAsDataURL(file);\n  });\n}\n\n/**\n * loadImage\n *\n * @param {string} src\n * @returns {Promise<HTMLImageElement>}\n */\nexport function loadImage(src) {\n  return new Promise((resolve, reject) => {\n    const img = new Image();\n    img.onload = () => resolve(img);\n    img.onerror = (e) => reject(e);\n    img.src = src;\n  });\n}\n\n/**\n * getBrowserName\n *\n * Extracts the browser name from the useragent.\n *\n * ref: https://stackoverflow.com/a/26358856\n *\n * @returns {string}\n */\nexport function getBrowserName() {\n  if (getBrowserName.cachedResult !== undefined) {\n    return getBrowserName.cachedResult;\n  }\n  let browserName = BROWSER_NAME.ETC;\n  const { userAgent } = navigator;\n  if (/Chrom(e|ium)/i.test(userAgent)) {\n    browserName = BROWSER_NAME.CHROME;\n  } else if (/iP(ad|od|hone)/i.test(userAgent) && /WebKit/i.test(userAgent)) {\n    browserName = BROWSER_NAME.IOS;\n  } else if (/Safari/i.test(userAgent)) {\n    browserName = BROWSER_NAME.DESKTOP_SAFARI;\n  } else if (/Firefox/i.test(userAgent)) {\n    browserName = BROWSER_NAME.FIREFOX;\n  } else if (/MSIE/i.test(userAgent) || (!!document.documentMode) === true) { // IF IE > 10\n    browserName = BROWSER_NAME.IE;\n  }\n  getBrowserName.cachedResult = browserName;\n  return getBrowserName.cachedResult;\n}\n\n/**\n * approximateBelowCanvasMaximumSizeOfBrowser\n *\n * it uses binary search to converge below the browser's maximum Canvas size.\n *\n * @param {number} initWidth\n * @param {number} initHeight\n * @returns {object}\n */\nexport function approximateBelowMaximumCanvasSizeOfBrowser(initWidth, initHeight) {\n  const browserName = getBrowserName();\n  const maximumCanvasSize = MAX_CANVAS_SIZE[browserName];\n\n  let width = initWidth;\n  let height = initHeight;\n  let size = width * height;\n  const ratio = width > height ? height / width : width / height;\n\n  while (size > maximumCanvasSize * maximumCanvasSize) {\n    const halfSizeWidth = (maximumCanvasSize + width) / 2;\n    const halfSizeHeight = (maximumCanvasSize + height) / 2;\n    if (halfSizeWidth < halfSizeHeight) {\n      height = halfSizeHeight;\n      width = halfSizeHeight * ratio;\n    } else {\n      height = halfSizeWidth * ratio;\n      width = halfSizeWidth;\n    }\n\n    size = width * height;\n  }\n\n  return {\n    width, height,\n  };\n}\n\n/**\n * get new Canvas and it's context\n * @param width\n * @param height\n * @returns {[HTMLCanvasElement | OffscreenCanvas, CanvasRenderingContext2D]}\n */\nexport function getNewCanvasAndCtx(width, height) {\n  let canvas;\n  let ctx;\n  try {\n    canvas = new OffscreenCanvas(width, height);\n    ctx = canvas.getContext('2d');\n    if (ctx === null) {\n      throw new Error('getContext of OffscreenCanvas returns null');\n    }\n  } catch (e) {\n    canvas = document.createElement('canvas');\n    ctx = canvas.getContext('2d');\n  }\n  canvas.width = width;\n  canvas.height = height;\n  // ctx.fillStyle = '#fff'\n  // ctx.fillRect(0, 0, width, height)\n  return [canvas, ctx];\n}\n\n/**\n * drawImageInCanvas\n *\n * @param {HTMLImageElement} img\n * @param {string} [fileType=undefined]\n * @returns {HTMLCanvasElement | OffscreenCanvas}\n */\nexport function drawImageInCanvas(img, fileType = undefined) {\n  const { width, height } = approximateBelowMaximumCanvasSizeOfBrowser(img.width, img.height);\n  const [canvas, ctx] = getNewCanvasAndCtx(width, height);\n  if (fileType && /jpe?g/.test(fileType)) {\n    ctx.fillStyle = 'white'; // to fill the transparent background with white color for png file in jpeg extension\n    ctx.fillRect(0, 0, canvas.width, canvas.height);\n  }\n  ctx.drawImage(img, 0, 0, canvas.width, canvas.height);\n  return canvas;\n}\n\n/**\n * Detect IOS device\n * see: https://stackoverflow.com/a/9039885\n * @returns {boolean} isIOS device\n */\nexport function isIOS() {\n  if (isIOS.cachedResult !== undefined) {\n    return isIOS.cachedResult;\n  }\n  isIOS.cachedResult = [\n    'iPad Simulator',\n    'iPhone Simulator',\n    'iPod Simulator',\n    'iPad',\n    'iPhone',\n    'iPod',\n  ].includes(navigator.platform)\n  // iPad on iOS 13 detection\n  || (navigator.userAgent.includes('Mac') && typeof document !== 'undefined' && 'ontouchend' in document);\n  return isIOS.cachedResult;\n}\n\n/**\n * drawFileInCanvas\n *\n * @param {File | Blob} file\n * @returns {Promise<[ImageBitmap | HTMLImageElement, HTMLCanvasElement | OffscreenCanvas]>}\n */\nexport async function drawFileInCanvas(file, options = {}) {\n  let img;\n  try {\n    if (isIOS() || [BROWSER_NAME.DESKTOP_SAFARI, BROWSER_NAME.MOBILE_SAFARI].includes(getBrowserName())) {\n      throw new Error('Skip createImageBitmap on IOS and Safari'); // see https://github.com/Donaldcwl/browser-image-compression/issues/118\n    }\n    img = await createImageBitmap(file);\n  } catch (e) {\n    if (process.env.BUILD === 'development') {\n      console.error(e);\n    }\n    try {\n      const dataUrl = await getDataUrlFromFile(file);\n      img = await loadImage(dataUrl);\n    } catch (e2) {\n      if (process.env.BUILD === 'development') {\n        console.error(e2);\n      }\n      throw e2;\n    }\n  }\n  const canvas = drawImageInCanvas(img, options.fileType || file.type);\n  return [img, canvas];\n}\n\n/**\n * canvasToFile\n *\n * @param {HTMLCanvasElement | OffscreenCanvas} canvas\n * @param {string} fileType\n * @param {string} fileName\n * @param {number} fileLastModified\n * @param {number} [quality]\n * @returns {Promise<File | Blob>}\n */\nexport async function canvasToFile(canvas, fileType, fileName, fileLastModified, quality = 1) {\n  let file;\n  if (fileType === 'image/png') {\n    const ctx = canvas.getContext('2d');\n    const { data } = ctx.getImageData(0, 0, canvas.width, canvas.height);\n    if (process.env.BUILD === 'development') {\n      console.log('png no. of colors', 4096 * quality);\n    }\n    const png = UPNG.encode([data.buffer], canvas.width, canvas.height, 4096 * quality);\n    file = new Blob([png], { type: fileType });\n    file.name = fileName;\n    file.lastModified = fileLastModified;\n  } else if (fileType === 'image/bmp') {\n    file = await new Promise((resolve) => CanvasToBMP.toBlob(canvas, resolve));\n    file.name = fileName;\n    file.lastModified = fileLastModified;\n  } else if (typeof OffscreenCanvas === 'function' && canvas instanceof OffscreenCanvas) { // checked on Win Chrome 83, MacOS Chrome 83\n    file = await canvas.convertToBlob({ type: fileType, quality });\n    file.name = fileName;\n    file.lastModified = fileLastModified;\n  // some browser do not support quality parameter, see: https://developer.mozilla.org/en-US/docs/Web/API/HTMLCanvasElement/toBlob\n  // } else if (typeof canvas.toBlob === 'function') {\n  //   file = await new Promise(resolve => canvas.toBlob(resolve, fileType, quality))\n  } else { // checked on Win Edge 44, Win IE 11, Win Firefox 76, MacOS Firefox 77, MacOS Safari 13.1\n    const dataUrl = canvas.toDataURL(fileType, quality);\n    file = await getFilefromDataUrl(dataUrl, fileName, fileLastModified);\n  }\n  return file;\n}\n\n/**\n * clear Canvas memory\n * @param canvas\n * @returns null\n */\nexport function cleanupCanvasMemory(canvas) {\n  // garbage clean canvas for safari\n  // ref: https://bugs.webkit.org/show_bug.cgi?id=195325\n  // eslint-disable-next-line no-param-reassign\n  canvas.width = 0;\n  // eslint-disable-next-line no-param-reassign\n  canvas.height = 0;\n}\n\n// Check if browser supports automatic image orientation\n// see https://github.com/blueimp/JavaScript-Load-Image/blob/1e4df707821a0afcc11ea0720ee403b8759f3881/js/load-image-orientation.js#L37-L53\nexport async function isAutoOrientationInBrowser() {\n  if (isAutoOrientationInBrowser.cachedResult !== undefined) return isAutoOrientationInBrowser.cachedResult;\n\n  // black 2x1 JPEG, with the following meta information set:\n  // EXIF Orientation: 6 (Rotated 90° CCW)\n  const testImageURL = 'data:image/jpeg;base64,/9j/4QAiRXhpZgAATU0AKgAAAAgAAQESAAMAAAABAAYAAAA'\n    + 'AAAD/2wCEAAEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBA'\n    + 'QEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQE'\n    + 'BAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAf/AABEIAAEAAgMBEQACEQEDEQH/x'\n    + 'ABKAAEAAAAAAAAAAAAAAAAAAAALEAEAAAAAAAAAAAAAAAAAAAAAAQEAAAAAAAAAAAAAAAA'\n    + 'AAAAAEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwA/8H//2Q==';\n  const testImageFile = await getFilefromDataUrl(testImageURL, 'test.jpg', Date.now());\n\n  const testImageCanvas = (await drawFileInCanvas(testImageFile))[1];\n  const testImageFile2 = await canvasToFile(testImageCanvas, testImageFile.type, testImageFile.name, testImageFile.lastModified);\n  cleanupCanvasMemory(testImageCanvas);\n  const img = (await drawFileInCanvas(testImageFile2))[0];\n  // console.log('img', img.width, img.height)\n\n  isAutoOrientationInBrowser.cachedResult = img.width === 1 && img.height === 2;\n  return isAutoOrientationInBrowser.cachedResult;\n}\n\n/**\n * getExifOrientation\n * get image exif orientation info\n * source: https://stackoverflow.com/a/32490603/10395024\n *\n * @param {File | Blob} file\n * @returns {Promise<number>} - orientation id, see https://i.stack.imgur.com/VGsAj.gif\n */\nexport function getExifOrientation(file) {\n  return new Promise((resolve, reject) => {\n    const reader = new CustomFileReader();\n    reader.onload = (e) => {\n      const view = new DataView(e.target.result);\n      if (view.getUint16(0, false) != 0xFFD8) {\n        return resolve(-2); // not jpeg\n      }\n      const length = view.byteLength;\n      let offset = 2;\n      while (offset < length) {\n        if (view.getUint16(offset + 2, false) <= 8) return resolve(-1);\n        const marker = view.getUint16(offset, false);\n        offset += 2;\n        if (marker == 0xFFE1) {\n          if (view.getUint32(offset += 2, false) != 0x45786966) {\n            return resolve(-1);\n          }\n\n          const little = view.getUint16(offset += 6, false) == 0x4949;\n          offset += view.getUint32(offset + 4, little);\n          const tags = view.getUint16(offset, little);\n          offset += 2;\n          for (let i = 0; i < tags; i++) {\n            if (view.getUint16(offset + (i * 12), little) == 0x0112) {\n              return resolve(view.getUint16(offset + (i * 12) + 8, little));\n            }\n          }\n        } else if ((marker & 0xFF00) != 0xFF00) {\n          break;\n        } else {\n          offset += view.getUint16(offset, false);\n        }\n      }\n      return resolve(-1); // not defined\n    };\n    reader.onerror = (e) => reject(e);\n    reader.readAsArrayBuffer(file);\n  });\n}\n\n/**\n *\n * @param {HTMLCanvasElement | OffscreenCanvas} canvas\n * @param options\n * @returns {HTMLCanvasElement | OffscreenCanvas}\n */\nexport function handleMaxWidthOrHeight(canvas, options) {\n  const { width } = canvas;\n  const { height } = canvas;\n  const { maxWidthOrHeight } = options;\n\n  const needToHandle = isFinite(maxWidthOrHeight) && (width > maxWidthOrHeight || height > maxWidthOrHeight);\n\n  let newCanvas = canvas;\n  let ctx;\n\n  if (needToHandle) {\n    [newCanvas, ctx] = getNewCanvasAndCtx(width, height);\n    if (width > height) {\n      newCanvas.width = maxWidthOrHeight;\n      newCanvas.height = (height / width) * maxWidthOrHeight;\n    } else {\n      newCanvas.width = (width / height) * maxWidthOrHeight;\n      newCanvas.height = maxWidthOrHeight;\n    }\n    ctx.drawImage(canvas, 0, 0, newCanvas.width, newCanvas.height);\n\n    cleanupCanvasMemory(canvas);\n  }\n\n  return newCanvas;\n}\n\n/**\n * followExifOrientation\n * source: https://stackoverflow.com/a/40867559/10395024\n *\n * @param {HTMLCanvasElement | OffscreenCanvas} canvas\n * @param {number} exifOrientation\n * @returns {HTMLCanvasElement | OffscreenCanvas} canvas\n */\nexport function followExifOrientation(canvas, exifOrientation) {\n  const { width } = canvas;\n  const { height } = canvas;\n\n  const [newCanvas, ctx] = getNewCanvasAndCtx(width, height);\n\n  // set proper canvas dimensions before transform & export\n  if (exifOrientation > 4 && exifOrientation < 9) {\n    newCanvas.width = height;\n    newCanvas.height = width;\n  } else {\n    newCanvas.width = width;\n    newCanvas.height = height;\n  }\n\n  // transform context before drawing image\n  switch (exifOrientation) {\n    case 2:\n      ctx.transform(-1, 0, 0, 1, width, 0);\n      break;\n    case 3:\n      ctx.transform(-1, 0, 0, -1, width, height);\n      break;\n    case 4:\n      ctx.transform(1, 0, 0, -1, 0, height);\n      break;\n    case 5:\n      ctx.transform(0, 1, 1, 0, 0, 0);\n      break;\n    case 6:\n      ctx.transform(0, 1, -1, 0, height, 0);\n      break;\n    case 7:\n      ctx.transform(0, -1, -1, 0, height, width);\n      break;\n    case 8:\n      ctx.transform(0, -1, 1, 0, 0, width);\n      break;\n    default:\n      break;\n  }\n\n  ctx.drawImage(canvas, 0, 0, width, height);\n\n  cleanupCanvasMemory(canvas);\n\n  return newCanvas;\n}\n", "import {\n  canvasToFile,\n  cleanupCanvasMemory,\n  drawFileInCanvas,\n  followExifOrientation,\n  getExifOrientation,\n  getNewCanvasAndCtx,\n  handleMaxWidthOrHeight,\n  isAutoOrientationInBrowser,\n} from './utils';\n\n/**\n * Compress an image file.\n *\n * @param {File} file\n * @param {Object} options\n * @param {number} [options.maxSizeMB=Number.POSITIVE_INFINITY]\n * @param {number} [options.maxWidthOrHeight=undefined]\n * @param {boolean} [options.useWebWorker=true]\n * @param {number} [options.maxIteration=10]\n * @param {number} [options.exifOrientation] - default to be the exif orientation from the image file\n * @param {Function} [options.onProgress] - a function takes one progress argument (progress from 0 to 100)\n * @param {string} [options.fileType] - default to be the original mime type from the image file\n * @param {number} [options.initialQuality=1.0]\n * @param {boolean} [options.alwaysKeepResolution=false]\n * @param {AbortSignal} [options.signal]\n * @param {number} previousProgress - for internal try catch rerunning start from previous progress\n * @returns {Promise<File | Blob>}\n */\nexport default async function compress(file, options, previousProgress = 0) {\n  let progress = previousProgress;\n\n  function incProgress(inc = 5) {\n    if (options.signal && options.signal.aborted) {\n      throw options.signal.reason;\n    }\n    progress += inc;\n    options.onProgress(Math.min(progress, 100));\n  }\n\n  function setProgress(p) {\n    if (options.signal && options.signal.aborted) {\n      throw options.signal.reason;\n    }\n    progress = Math.min(Math.max(p, progress), 100);\n    options.onProgress(progress);\n  }\n\n  let remainingTrials = options.maxIteration || 10;\n\n  const maxSizeByte = options.maxSizeMB * 1024 * 1024;\n\n  incProgress();\n\n  // drawFileInCanvas\n  const [, origCanvas] = await drawFileInCanvas(file, options);\n\n  incProgress();\n\n  // handleMaxWidthOrHeight\n  const maxWidthOrHeightFixedCanvas = handleMaxWidthOrHeight(origCanvas, options);\n\n  incProgress();\n\n  // exifOrientation\n  const exifOrientation = options.exifOrientation || await getExifOrientation(file);\n  incProgress();\n  const orientationFixedCanvas = (await isAutoOrientationInBrowser()) ? maxWidthOrHeightFixedCanvas : followExifOrientation(maxWidthOrHeightFixedCanvas, exifOrientation);\n  incProgress();\n\n  let quality = options.initialQuality || 1.0;\n\n  const outputFileType = options.fileType || file.type;\n\n  const tempFile = await canvasToFile(orientationFixedCanvas, outputFileType, file.name, file.lastModified, quality);\n  incProgress();\n\n  const origExceedMaxSize = tempFile.size > maxSizeByte;\n  const sizeBecomeLarger = tempFile.size > file.size;\n  if (process.env.BUILD === 'development') {\n    console.log('outputFileType', outputFileType);\n    console.log('original file size', file.size);\n    console.log('current file size', tempFile.size);\n  }\n\n  // check if we need to compress or resize\n  if (!origExceedMaxSize && !sizeBecomeLarger) {\n    // no need to compress\n    if (process.env.BUILD === 'development') {\n      console.log('no need to compress');\n    }\n    setProgress(100);\n    return tempFile;\n  }\n\n  const sourceSize = file.size;\n  const renderedSize = tempFile.size;\n  let currentSize = renderedSize;\n  let compressedFile;\n  let newCanvas;\n  let ctx;\n  let canvas = orientationFixedCanvas;\n  const shouldReduceResolution = !options.alwaysKeepResolution && origExceedMaxSize;\n  while (remainingTrials-- && (currentSize > maxSizeByte || currentSize > sourceSize)) {\n    const newWidth = shouldReduceResolution ? canvas.width * 0.95 : canvas.width;\n    const newHeight = shouldReduceResolution ? canvas.height * 0.95 : canvas.height;\n    if (process.env.BUILD === 'development') {\n      console.log('current width', newWidth);\n      console.log('current height', newHeight);\n      console.log('current quality', quality);\n    }\n    [newCanvas, ctx] = getNewCanvasAndCtx(newWidth, newHeight);\n\n    ctx.drawImage(canvas, 0, 0, newWidth, newHeight);\n\n    if (outputFileType === 'image/png') {\n      quality *= 0.85;\n    } else {\n      quality *= 0.95;\n    }\n    // eslint-disable-next-line no-await-in-loop\n    compressedFile = await canvasToFile(newCanvas, outputFileType, file.name, file.lastModified, quality);\n\n    cleanupCanvasMemory(canvas);\n\n    canvas = newCanvas;\n\n    currentSize = compressedFile.size;\n    // console.log('currentSize', currentSize)\n    setProgress(Math.min(99, Math.floor(((renderedSize - currentSize) / (renderedSize - maxSizeByte)) * 100)));\n  }\n\n  cleanupCanvasMemory(canvas);\n  cleanupCanvasMemory(newCanvas);\n  cleanupCanvasMemory(maxWidthOrHeightFixedCanvas);\n  cleanupCanvasMemory(orientationFixedCanvas);\n  cleanupCanvasMemory(origCanvas);\n\n  setProgress(100);\n  return compressedFile;\n}\n", "function createWorkerScriptURL(script) {\n  const blobArgs = [];\n  if (typeof script === 'function') {\n    blobArgs.push(`(${script})()`);\n  } else {\n    blobArgs.push(script);\n  }\n  return URL.createObjectURL(new Blob(blobArgs));\n}\n\nconst workerScript = `\nlet scriptImported = false\nself.addEventListener('message', async (e) => {\n  const { file, id, imageCompressionLibUrl, options } = e.data\n  options.onProgress = (progress) => self.postMessage({ progress, id })\n  try {\n    if (!scriptImported) {\n      // console.log('[worker] importScripts', imageCompressionLibUrl)\n      self.importScripts(imageCompressionLibUrl)\n      scriptImported = true\n    }\n    // console.log('[worker] self', self)\n    const compressedFile = await imageCompression(file, options)\n    self.postMessage({ file: compressedFile, id })\n  } catch (e) {\n    // console.error('[worker] error', e)\n    self.postMessage({ error: e.message + '\\\\n' + e.stack, id })\n  }\n})\n`;\nlet workerScriptURL;\n\nexport default function compressOnWebWorker(file, options) {\n  return new Promise((resolve, reject) => {\n    if (!workerScriptURL) {\n      workerScriptURL = createWorkerScriptURL(workerScript);\n    }\n    const worker = new Worker(workerScriptURL);\n\n    function handler(e) {\n      if (options.signal && options.signal.aborted) {\n        worker.terminate();\n        return;\n      }\n      if (e.data.progress !== undefined) {\n        options.onProgress(e.data.progress);\n        return;\n      }\n      if (e.data.error) {\n        reject(new Error(e.data.error));\n        worker.terminate();\n        return;\n      }\n      resolve(e.data.file);\n      worker.terminate();\n    }\n\n    worker.addEventListener('message', handler);\n    worker.addEventListener('error', reject);\n    if (options.signal) {\n      options.signal.addEventListener('abort', () => {\n        reject(options.signal.reason);\n        worker.terminate();\n      });\n    }\n\n    worker.postMessage({\n      file,\n      imageCompressionLibUrl: options.libURL,\n      options: { ...options, onProgress: undefined, signal: undefined },\n    });\n  });\n}\n", "import copyExifWithoutOrientation from './copyExifWithoutOrientation';\nimport compress from './image-compression';\nimport {\n  canvasToFile,\n  drawFileInCanvas,\n  drawImageInCanvas,\n  getDataUrlFromFile,\n  getFilefromDataUrl,\n  loadImage,\n  getExifOrientation,\n  handleMaxWidthOrHeight,\n  followExifOrientation,\n  CustomFile,\n  cleanupCanvasMemory,\n  isAutoOrientationInBrowser,\n  approximateBelowMaximumCanvasSizeOfBrowser,\n  getBrowserName,\n} from './utils';\nimport compressOnWebWorker from './web-worker';\n\n/**\n * Compress an image file.\n *\n * @param {File} file\n * @param {Object} options\n * @param {number} [options.maxSizeMB=Number.POSITIVE_INFINITY]\n * @param {number} [options.maxWidthOrHeight=undefined]\n * @param {boolean} [options.useWebWorker=true]\n * @param {number} [options.maxIteration=10]\n * @param {number} [options.exifOrientation] - default to be the exif orientation from the image file\n * @param {Function} [options.onProgress] - a function takes one progress argument (progress from 0 to 100)\n * @param {string} [options.fileType] - default to be the original mime type from the image file\n * @param {number} [options.initialQuality=1.0]\n * @param {boolean} [options.alwaysKeepResolution=false]\n * @param {AbortSignal} [options.signal]\n * @param {boolean} [options.preserveExif] - preserve Exif metadata\n * @param {string} [options.libURL] - URL to this library\n * @returns {Promise<File | Blob>}\n */\nasync function imageCompression(file, options) {\n  const opts = { ...options };\n\n  let compressedFile;\n  let progress = 0;\n  const { onProgress } = opts;\n\n  opts.maxSizeMB = opts.maxSizeMB || Number.POSITIVE_INFINITY;\n  const useWebWorker = typeof opts.useWebWorker === 'boolean' ? opts.useWebWorker : true;\n  delete opts.useWebWorker;\n  opts.onProgress = (aProgress) => {\n    progress = aProgress;\n    if (typeof onProgress === 'function') {\n      onProgress(progress);\n    }\n  };\n\n  if (!(file instanceof Blob || file instanceof CustomFile)) {\n    throw new Error('The file given is not an instance of Blob or File');\n  } else if (!/^image/.test(file.type)) {\n    throw new Error('The file given is not an image');\n  }\n\n  // try run in web worker, fall back to run in main thread\n  // eslint-disable-next-line no-undef, no-restricted-globals\n  const inWebWorker = typeof WorkerGlobalScope !== 'undefined' && self instanceof WorkerGlobalScope;\n\n  if (process.env.BUILD === 'development') {\n    if ((useWebWorker && typeof Worker === 'function') || inWebWorker) {\n      console.log('run compression in web worker');\n    } else {\n      console.log('run compression in main thread');\n    }\n  }\n\n  if (useWebWorker && typeof Worker === 'function' && !inWebWorker) {\n    try {\n      // \"compressOnWebWorker\" is kind of like a recursion to call \"imageCompression\" again inside web worker\n      opts.libURL = opts.libURL || `https://cdn.jsdelivr.net/npm/browser-image-compression@${__buildVersion__}/dist/browser-image-compression.js`;\n      compressedFile = await compressOnWebWorker(file, opts);\n    } catch (e) {\n      if (process.env.BUILD === 'development') {\n        console.warn('Run compression in web worker failed:', e, ', fall back to main thread');\n      }\n      compressedFile = await compress(file, opts);\n    }\n  } else {\n    compressedFile = await compress(file, opts);\n  }\n\n  try {\n    compressedFile.name = file.name;\n    compressedFile.lastModified = file.lastModified;\n  } catch (e) {\n    if (process.env.BUILD === 'development') {\n      console.error(e);\n    }\n  }\n\n  try {\n    if (opts.preserveExif && file.type === 'image/jpeg' && (!opts.fileType || (opts.fileType && opts.fileType === file.type))) {\n      if (process.env.BUILD === 'development') {\n        console.log('copyExifWithoutOrientation');\n      }\n      compressedFile = copyExifWithoutOrientation(file, compressedFile);\n    }\n  } catch (e) {\n    if (process.env.BUILD === 'development') {\n      console.error(e);\n    }\n  }\n\n  return compressedFile;\n}\n\nimageCompression.getDataUrlFromFile = getDataUrlFromFile;\nimageCompression.getFilefromDataUrl = getFilefromDataUrl;\nimageCompression.loadImage = loadImage;\nimageCompression.drawImageInCanvas = drawImageInCanvas;\nimageCompression.drawFileInCanvas = drawFileInCanvas;\nimageCompression.canvasToFile = canvasToFile;\nimageCompression.getExifOrientation = getExifOrientation;\n\nimageCompression.handleMaxWidthOrHeight = handleMaxWidthOrHeight;\nimageCompression.followExifOrientation = followExifOrientation;\nimageCompression.cleanupCanvasMemory = cleanupCanvasMemory;\nimageCompression.isAutoOrientationInBrowser = isAutoOrientationInBrowser;\nimageCompression.approximateBelowMaximumCanvasSizeOfBrowser = approximateBelowMaximumCanvasSizeOfBrowser;\nimageCompression.copyExifWithoutOrientation = copyExifWithoutOrientation;\nimageCompression.getBrowserName = getBrowserName;\nimageCompression.version = __buildVersion__;\n\nexport default imageCompression;\n"], "mappings": ";;;;;;;;;;;;;;;AAIqB,SAAeA,2BAArBC,IAAAC,IAAAA;AAAM,SAAA,IAAAC,QAAA,SAAAC,IAAAC,IAAAA;AAAAA,QAAAA;AAAAA,WAAAA,eAAAA,EAAAA,EAAAA,KAAAA,SAAAA,IAAAA;AAAAA,UAAAA;AAAAA,eAAAA,KAAAA,IAAAA,GAAAA,IAAAA,KAAN,CAAAH,GAAAI,MAAA,GAAA,CAAA,GAAAC,IAAAL,GAAAI,MAAA,CAAA,CAAA,GAAA,EACbE,MAAA,aAAA,CAAA,CAAA;MAAA,SAAAC,IAAA;AAAA,eAAAC,GAAAD,EAAA;MAAA;IAAA,GAAAC,EAAA;EAAA,CAAA;AAAA;AAAA,IAOIC,iBAAOC,CAAAA,OAAA,IAAAT,QAAA,CAAAU,IAAAC,OACb;AAAA,QAAaC,KAAA,IAAAC;AACbD,EAAAA,GAAsBE,iBAAA,QAAA,CAAAC,EAAAA,QAAAA,EAAAC,QAAAC,GACtB,EAAA,MAAA;AAAA,UAAmBC,KAAA,IAAAC,SAAAF,EAAAA;AACnB,QAA2BG,KAAA;AAC3B,QAAA,UAAuBF,GAAAG,UAAAD,EAAAA,EAAA,QAAAT,GAAA,kBAAA;AAGrB,SAFFS,MAAwB,OAAA;AAGpB,YAAAE,KAAaJ,GAAIG,UAASD,EAC1B;AAAA,UAAA,UAAAE,GAAa;AACuB,YAAAC,KAAOL,GAAAA,UAAOE,KAAA,CAAA;AAAA,UAAA,UAClDE,MAAAA,eAAUJ,GAAAM,UAAAJ,KAAA,CAAA,GAAA;AAEV,cAAAK,KAAaL,KAAA;AACX,YAAAM;AAAAA,gBACIR,GAAAG,UAAWI,EAAK,GAAA;UAAA,KAAA;AAEpBC,YAAAA,KAAAA;AACA;UACE,KAAA;AACAA,YAAAA,KAAAA;AAAI;UAEF;AAAA,mBAAAC,GACE,qCAAA;QAAA;AAEF,YAAK,OAALT,GAAAG,UAAKI,KAAA,GAAAC,EAAAA,EAAA,QAAAf,GAAA,sCAEH;AAAA,cAAAiB,KAAAV,GAAAM,UAAAC,KAAA,GAAAC,EAAAA,GACFG,KAAAJ,KACEG,KAAAA,IAEuD,KAA3DV,GAAIG,UAAAI,KAAeG,IAAgBF,EAA0B;AAAA,iBAArEnB,KAAAkB,KAAAG,KAAA,GAAAtB,KAAAwB,IAAAxB,MAAA,IAAA;AAaU,cA/CE,OA8CFY,GAAcG,UAAKd,IAAAmB,EAAAA,GACc;AAC/B,gBA/CA,MA+CAR,GAAAG,UAAId,KAAA,GAAAmB,EAAAA,EAAwD,QAAAf,GAAA,kCAAA;AAAxE,gBAAA,MAAAO,GAAAM,UAAAjB,KAAA,GAAAmB,EAAA,EAAA,QAAAf,GAAA,mCAAA;AAE6DO,YAAAA,GAAAa,UAAAxB,KAAA,GAAA,GAAAyB,EAA7D;AAAA;UAAA;QAGY;AACZ,eAAAtB,GAAAO,GAAAd,MAAAiB,IAAAA,KAAA,IAAAG,EAAAA,CAAAA;MACA;AACQH,MAAAA,MAAA,IAAAG;IACR;AACM,WAAAb,GAAA,IAAUuB,MAAAA;EAAV,CAAA,GAAA1B,GAEK2B,kBAAYzB,EAAAA;AAAAA,CAAAA;AAAAA,IAAAA,IAAAA,CAAAA;AAAAA,IAAAA,IAAAA,EAAAA,IAAAA,UAAAA;AAAAA,SAAAA;AAAAA,GAAAA,IAAAA,QAAAA,IAAAA;AAAAA,MAAAA;AAAAA,EAAAA;AAAAA,CAAAA,SCnEO0B,IAAAA;AAK7BC,MAqjBAC,IAAiBC,IArjBjBF,QAAI,CAAA;AAAA,IAEJG,UAAWH,OAAAA,MAEwB,QAAA,SAAAI,IAAAC,IAKnC;AAAA,aALmCC,KAAAA,MAAAA,IAAAA,YAAAC,KAAAA,MAAAA,IAAAA,UAAAC,KAAAA,GAAAC,KAAAA,CAAAA,GAGnCC,KAAG,IAAAC,WAAAP,EAAAA,GACHQ,KAAAA,GAAAA,SAAAA,GAAAA,aACAL,GAAAA,IAAAA,EAAAA,IAAyBK,CAAAA;AAAGJ,IAAAA,KAAAA;AAC5BA,IAAAA,MAAA;AAEA,QAAAK,KAAAA,GAAAA,IAFoBL,MAAA,CAEOM;AAAAA,IAAAA,GAAAA,IAALN,MAAA,CAAA;AAAA,QACKO,KAAAA,GAAAA,IADGP,MAAA,CAAA,GAM7BQ,IAAAT,GAAAA,IAAAA,MAHD,CAGsBC;AAAAA,IAAAA,MAAA,GAAAA,KAAKQ;AAAAA,aAC1B7C,IAAAA,GAAAA,IAAAA,IAAAA,KAAAA;AACA8C,MAAAA,GAAAA,IAAAA,EAGAT,GAAAA,MAAA,GAAsBA,MAAA,GAAKA,MAAA,GAAArC,GACfqC,IADkBA,MAAA,CAAA;AAE9BO,MAAAA,KAAAA,GAAAA,IADsBP,MAAA,CACtB;AAAA,UAA2BU,IAAAA,GAAAA,IAALV,MAAA,CAEiDW,GAAAA,IAAAA,GAAAA,IAALX,MAAA,CAAA,GAAKY,IAAAA,GAAAA,IAAAA,KAAAA,CAAAC,GAAAA,IAAAA,GAAAA,IAAAA,KAAAA,CAAAA;AACvEb,MAAAA,MAAA;AAAA,UAAA,IAAArC,GAAAmD,IAE0Bd,MAAA,CAI5BA;AAAAA,MAAAA,MAAA,GAAAA,MAAA,IAAA,IAAA,GAKAR,MAAAA,WAAkBU,IAAAa,GAAuBd,IAAAM,IAAcG,GAAAb,EAAAA;IAGtD;AAAA,WAA2BI;EAAAA,GAC3BT,MAAAwB,aAAY,SAAUd,IAAAA,IAAAA,IAAAA,IAAAA,IAAAA,IAAAA;AAAA,QAAAJ,KAAAA,MAAAA,IAAAA,YAAAC,KAAAA,MAAAA,IAAAA;AAAQA,IAAAA,GAAAG,IAAAF,EAAAA,GACAF,GAAAI,IADAF,MAAA,CAAA,GAGAF,GAAAI,IAFAF,MAAA,CAAA;AAAA,QAIHiB,KAAGnB,GAAAI,IAFAF,MAAA,CAAA;AAO9BkB,IAAAA,GAAAA,IAL8BlB,MAAA,CAAA,GAOAmB,GAAAA,IAAAA,MAAH,CAC3BnB,GAAAA,MAAA;AAAA,QAEAoB,KAAAA,GAAYlB,IAFUF,MAAA,CAEyBqB,GAAAA,IAAAA,GAAAA,IAARrB,MAAA,CAItBA;AAAAA,IAAAA,MAAA;AAAAsB,QAAAA,IAAU9B,MAAA+B,IAAAC,SAAAtB,IAAAF,IAAAoB,EAAAA;AAAAA,QAAApB,MAAAoB,IAAApB,MAAAqB,GAAAA,GAAApB,CAAAA,GAAAA,CAAAA,IAAAA,EAAVtB,MAAAA,IAAW4B,OAAAA,GAAAA;SAAAA;AAAAA,UAA7BkB,IAAA,IAAAtB,WAAAD,GAAA7B,QAAA2B,EAAA;AAAA,UAAA,KAAAiB,GAcAhB,CAAAA,GAAAqB,CAAAA,IAAA,IAAA,WAAA,EAAA,OAAA,MAAAI,IAAAA,KAAA/D,EAAA,CAAA;WAAA;AAAA,YAAA,KAAAgE,GAIA,OAAA,iCAAAV;AAFA,YAAArB,IAAA,IAAAO,WAAAO,EAAAA;AAEAlB,QAAAA,MAAAA,WAAAA,GAAAA,CAAAA,GAAAS,GAAAA,CAAAL,IAAAA;MAAA;IAAA;EACA,GAAAJ,MACKoC,aAAM,SAASH,IAAM7B,IAAAA;AAAAA,WACzBJ,MAAUqC,EAAAA,QAAkBJ,IAAA7B,EAAAA;EAAAA,GAI7BJ,MAAAA,UAAAA,SAAAA,IAAAA,IAAAA;AAAAA,WACUiC,GAAAA,CAAAA,GAAAA,GAAA,CAAA,GAAWjC,MAAAoC,WAAA,IAAAzB,WAAAsB,GAAApD,QAAAoD,GAAAK,aAAA,GAAAL,GAAAM,SAAA,CAAA,GAAAnC,EAAAA;EAAAA,GAAAA,MAAAA,UAAAA,SAAAA,IAAAA,IAAAA;AAAC,YAAAoC,OAAAA,KAAAA,EAAAA,OAAAA,EAAAA;AAAAA,QAAAA,KAAAA,GACbpC,KAAWO,IAAAA,WAAA,KAAW8B,KAAGC,MAAAA,MAAAA,GAAAA,MACjCtC,CAAAA;AAAAA,IAAAA,GAAAA,EAAI,IAAA,KAAAjC,GAAWwE,KAAI,CAAA,IAAA,KAAApD,MAAa,GAChCoD,KAAA3C,MAAA4C,EAAAA,WAAAA,IAAAA,IAAAA,IAAAA,GAAAA,KAAAA;AAAAA,QACAC,KAAI7C,MAAM8C,MAAApC,IAAA,GAAAA,GAAA6B,MAAAA;AAAAA,WACVnC,GAAAuC,KAAI,CAAA,IAAAnC,OAAS,KAAM,KACnBJ,GAAAA,KAAAA,CAAAA,IAAAA,OAAAA,KAAAA,KACAA,GAAAuC,KAAA,CAAA,IAAaE,OAAO,IAAG,KACvBzC,GAAIuC,KAAA,CAAAE,IAAAA,OAAA,IAAA,KAAA,IAASlC,WAAAA,GAAAA,QAAAA,GAAAA,KAAAA,CAAAA;EAAAA,GAAAA,MAAAA,aAAAA,SAAAA,IAAAA,IAAAA;AACN,YAAP6B,OACDA,KAAA,EACAO,OAAA,EAAA;AAAA,QAAAxD,KAAA,IAAA,WAAA,KAAA,KAAA,MAAA,MAAArB,GAAA,MAECyE,CAAAA,GAAAA,KAAQ3C,MAAA4C,EAAAI,WAAAtC,IAAAN,IAAAuC,IAAAH,GAAAO,KAAAA;AAAAA,WAAAA,IAAApC,WAAAP,GAAAvB,QAAA,GAAA8D,EAAAA;EAAAA,GAER3C,MAAAiD,SAAO,SAAAC,IAAAC,IACR;AAAA,YAAAA,OAAAA,KAAA;AAGAC,QAAAA,KAAA,GAAAC,KAAAA,MAAAA,IAAAA,WAAAC,KAAAA,MAAAA,IAAAA,aAAAA,KAAAA,CAAAA;AAAAA,aAAAA,MAA4BJ,IAAAA;AAAQK,UAAAA,KAAAA,CAApCvD,MAAAA,QAAAA,EAAAA,KAAAA,CAAAA,IAAAI,KAAAA,GAAAA,EAAAA,GAAAyC,KAAAA,MAAAA,IAAAA,IAAAA,IAAAA,GAAAA,GAAAA,MAAAA;AAAAA,MAAAA,GAAoCW,EAAAA,IAAA,EAAA,KAAA5D,IAApCsB,OAAAA,GAAAA,QACkB2B,KAAAA,IAAOZ,MAAAsB,KAAAvD,MAAAgD,WAAA5C,EAAAA,IAAAA,GAAAA;IAAAA;AAAAA,aAAAA,MAAAA,GAIXgD,CAAAA,MAAAK,GAAHxB,EAAAA,EAAAA,KAAAA,SAAAA,KAAAA,KAAAA,IAAAA,MAAAA,IAAAA,SAAAA,EAAAA;AAAAA,IAAAA,MAAAA;AAAkBvB,QAAAA,IAAA,IAAlBC,WAAAA,EAAAA,GAAAH,IAAAA,GAA8BkD,IAAA,CAAA;AAAA,aAAApC,MAAAqC,IAAA;AAAA,UAAA,IAAAA,GAAArC,EAAA;AAAzCoC,QAAAE,KAAApD,CAAAA,GAAAA,IAEKR,MAAS6D,aAAAnD,GAAAF,GAAAgD,IAAAvB,GAAA,CAAA;IAAA;AAAA,QAAK9D,IAAA,GAAA2F,IAAAtD;AAAAA,aAAAA,MAAAA,IAAAA;AAGlByB,UAAWwB,GAAAA,EAGXC;AAAAA,QAAQE,KAAApD,CAAAA,GAAUA,IAAAA,MAAAA,aAAAA,GAAAA,GAAAA,IAAAA,GAAAA,GAAAA,EAAAA,GAAAA,CAAAA;IAAAA;AAAAA,QAAAA,IACOA,IAAAA;AAO1B,WAP+B6C,GAAA3C,GAAAA,GAAAA,SAAAA,GAAAA,KAAAA,GAE/B4C,GAAAA,GADW9C,KAAA,GACXrC,CAAAA,GAAAA,GAAAA,GAAAqC,KAAA,GACOrC,CAAAA,GAAAA,GACFuC,GADYF,KAAA,GACZE,CAAAA,GAAAA,GAAAA,GAAAA,KAAAA,GAAAA,CAGLF,GAAAA,KAAAA,GAAAA,KAAA,GAAAE,EAAAA;EAAAA,GAAAA,MAAAA,UAAAA,SAGcqD,IAAAA;AAAAA,QACbC,KAAKD,GAAAA,MAAA,GAAAE,EAAAA,IAAAA,EAAAC,YAAAA;AAAAA,WAAAA,MACL,mBAAaC,QAAAH,EAAAA;EAAAA,GAAAA,MACAH,eAAA,SAAAnD,IAAAF,IAAAgD,IAAAN,IAAAhB,IAAAX,IAAAA;AAAAA,QAAK8B,KAAKrD,MAAAA,IAAAA,WAAAsD,KAAAA,MAAAA,IAAAA,aAAArB,KAAAiB,GAAAjB;AAsBQzB,WAAAA,GAAAA,IAAAA,IAAAA,KAAAA,KAAAA,WAAAA,QArBlBA,GAAAA,MAAA,GAAc,KAAL0B,OAAK1B,MAAAA,IAAAA,GAC3BE,IAAAA,IAAU,EAAA,GAAAd,GAAA1B,IAAGsC,MAAA,GACR,CAAA,GACL8C,GAAA5C,IADKF,MAAA,GACE0C,GAAKK,MAAA,IAAA,CAAA,GACbF,GAAAA,IADa7C,MAAA,GACb,CAEA6C,GAAAA,GAAA3C,IAFAF,MAAA,GAEe0C,GAAAL,GAAAA,GAAAA,GAA8DnC,IAAAA,MAAjD,GAAwDuB,GAAAM,MAAAA,GAApFc,GAAAA,IAAoF7C,MAAAA,GAApF0C,GAAAA,KAAAA,GAAAA,GAAAA,IAAA1C,MAAA,GAIWR,MAAA+B,IAAAqC,SAAAZ,EAAAA,CAAAA,GAAAA,GAGV9C,IAAAA,MAAAA,GAAA,CAAA,GAA+CF,MAAA,GAC/C,KAAA0B,OAAM1B,MAAA,GAAA0B,MAAA,GACOmB,GAAAA,IAAb7C,MAAAA,GAAa6C,EAAAA,GAAM7C,MAAG,IAAA0B,MAAAlC,MAAA+B,IAAAsC,UAAA3D,IAAAF,IAAAgD,EACtB,GAAA,KAAAtB,OAAAA,GAAAA,IAAAA,IAAAA,EAAAA,GAAAA,MAI2BD,GAAAA,SACIzB;EAAAA,GACLR,MAAA6C,MAAA,EAAA,OAAA,WAAA;AAEc,aAAHyB,KAAA,IAAGC,YAAA,GAAA,GAAAC,KAAA,GAAAA,KAAA,KAAAA,MAAA;AAC3BC,eAAAA,KAAAA,IAAAA,KAAA,GAAAA,KAAA,GAAAA,KAAAA,KAAAA,KAEP/E,KACLA,aAAAA,OAAAA,IAAAA,QAAAA;AACA4E,MAAAA,GAAAA,EAAK5E,IAAAA;IACL;AAAK4E,WAAAA;EACL,EAX0BI,GAAAA,QAAAA,SAAAA,IAAAA,IAAAA,IAAAA,IAY5B;AAAA,aAAAvG,KAAA,GAAAwG,KAAAA,IAAAA,KAAAjF,CAAAA,KAAAM,MAAA6C,IAAA+B,MAAA,OAAAlF,KAAAU,GAAAuC,KAAAxE,EAAAA,EAAAA,IAAAuB,OAAA;AAAA,WAAAxB;EAAA,GAC6C2E,KAAAA,SAAIgC,IAAArE,IAAA2B,IAAAA;AAChD,WAAS,aAAHnC,MAAG6C,IAAA6B,OAAA,YAAAG,IAAArE,IAAA2B,EAAAA;EAAAA,EAAAA,GAAAA,MAAAA,QAAV,SAAAzB,IAAAF,IAAAmE,IAAAA;AAAAA,aACChB,KAAAA,GAAAkB,KAAO,GACRlC,KAAAnC,IAAAsE,KAAAtE,KAAAA,IAMAmC,KAAAA,MAAAA;AAEI,eAAA/C,KAAA,KAAA,IAAA+D,KAAA,MAAArC,EAAA,GAAAqB,KAAAA,KAEDkC,CAAAA,MAAAA,MAAAA,GAAAA,IAAAA;AAAAA,MAAAA,MAAAA,OAAAA,MAAAA;IAGa;AAAUA,WAAAA,MAAA,KAAAlB;EAAAA,GAC1B3D,MAAA+B,MAAA,EAAA,YAAAgD,SAAAA,IAAAA,IAAAA;AAAA,WAAAA,GAAAvB,EAAAA,IAAAuB,GAAAvB,KAAAA,CAAAA,KAAAA;EAAAA,GACGwB,aAAAA,SAASD,IAAAvB,IAAAA,IAAAA;AAAAuB,IAAAA,GAAAA,EAAAA,IAAA,MAAAxF,IAAArB,GAAAgE,KAAA,CAAA,IAAA3C,MAAA,IAAA;EAAZ,GAAA0F,UAAA,SAAAF,IAAAA,IAAAA;AAAA,WAAA,WAAAA,GAAAvB,KAAA,CAAAuB,KAAAA,GAAAvB,KAAA,CAAA,KAAA,KAAAuB,GAAAvB,KAAA,CAAA,KAAA,IAAAuB,GAAAvB,EAAAA;EAAAA,GAAA0B,WAAA,SAAAH,IAAAvB,IAAAA,IAAAA;AAAAA,IAAAA,GAAAA,EAAAA,IAAAA,MAAAA,IAAAA,GACEA,KAAA,CAAAgB,IAAAA,MAAO,IAAA,KAAAO,GAAAvB,KAAA,CAAA,IAAAgB,MAAA,KAAA,KAAAO,GAAAvB,KAAA,CAAA,IAAAgB,MAAA,KAAA;EAAA,GAAA,WAAA,SAAAO,IAAAvB,IAAArB,IAAAA;AAAT,aAAAhE,KAAA,IAVWA,KAAAA,GAAAA,KAAAgE,IAAAhE,KAAAmD,CAAAA,MAAA6D,OAAAC,aAAAL,GAAAvB,KAAArF,EAAAA,CAAAA;AAAAA,WAAAmD;EAAAA,GAWV+D,YAAS3E,SAAAA,IAAAA,IAAAA,IAAA;AAAA,aAAiBvC,KAAAA,GAAAA,KAAAA,GAAUoE,QAAApE,KAAAA,CAAAA,GAAAA,KAAAA,EAAAA,IAAAA,GAAAA,WAAAA,EAAAA;EAAAA,GAAAA,KACRqG,SAAAA,IAAAA;AAC3B,WAAAA,GAAAjC,SAAO,IAAA,MAAAiC,KAAAA;EAAAA,GAbExC,UAAA,SAAA+C,IAAAvB,IAAArB,IAAAA;AAeV,aAfUmD,IAAAhE,KAAA,IAeVnD,KAAe,GAAAwF,KAAApE,IAAAoE,KAAArC,CAAAA,MAAE,MAAEtB,MAAF+E,IAAAA,IAAAA,GAAAA,KAAM5G,EAAAoH,EAAAA,SAAA,EAAA,CAAA;AAAA,QAAA;AAAGD,MAAAA,KAAAA,mBAAAA,EAE3B;IAAA,SAAAnH,IAAA;AAAA,aAAA6B,MAAA,IAAA,UAAA9B,IAAAgE,IAAA3C,EAAA;IAAA;AACC,WAAA+F;EAAAA,GAAAA,WAAAA,SAGYP,IAASvB,IAAAgC,IAAUC;AAAAA,aAAAC,KAAAF,GAAAjD,QAAApE,KAAAA,GAAAsH,KAAAA,GAAAA,KAAAA,IAAAA,MAAAA;AAAAA,UAAAA,KACxBD,GAAAG,WAAAF,EAAAA;AAAAA,UAAU,MAAA,aAAAG,IAAAb,CAAAA,GAAAA,KAAAA,EAAAA,IAAAa,IAAAzH;eACL,MAAL,aAAAmD,IAAKyD,CAAAA,GAAA5G,KAAAA,EAAA,IAAA,MAAAyH,MAAA,GAAAb,GAAAvB,KAAArF,KAAA,CAAA,IAAA,MAAAyH,MAAA,IAAA,IAAAzH,MAAA;eAAA,MAAA,aAAAyH,IAAVb,CAAAA,GACAvB,KAAArF,EAAK,IAAA,MAAAyH,MAAA,IAAAb,GAAA5G,KAAAA,KAAA,CAAA,IAAA,MAAAyH,MAAA,IAAA,IAAAb,GAAAA,KAAAA,KAAA,CAAA,IAAA,MAAAa,MAAA,IAAA,IAAAzH,MAAA;WAAA;AAAA,YAAA,MAAA,aAAAyH,IAKI,OAAD;AALHb,QAAAA,GAAAvB,KAAArF,EAAA,IAAA,MAAAyH,MAAA,IAAA1H,GAAAgE,KAAA1B,KAAA,CAAA,IAAA,MAAAc,MAAA,KAAA,IACRyD,GAAAvB,KAAAA,KAAAA,CAAAA,IAAAA,MAAAA,MAAAA,IAAAA,IACEuB,GAAAA,KAAAA,KAAAA,CAAAA,IAAAA,MAAAA,MAAAA,IAAAA,IACA5G,MAAAA;MAES;IACX;AAEA,WAAKA;EAAAA,GAAAA,UAAMqH,SAAAA,IAAAA;AAAAA,aAAAE,KAAAF,GAAAjD,QAAApE,KAAAA,GAAAA,KAAAA,GAEGsH,KAAAC,IAAAD,MAAA;AAAA,UAAkBG,KAAAJ,GAAAG,WAAAF,EAAAA;AAAAA,UAAAA,MAAG,aAAAjF,IAAcrC,CAAAA;eAAyB,MAAZ,aAAAqC,IAAYrC,CAAAA,MAAA;eAAnB,MAAA,aAAAyH,IAF5CzH,CAAAA,MAAA;WAAA;AAAA,YAAA,MAAA,aAAAqC,IAAA,OAAA;AAGwBrC,QAAAA,MAAAA;MAAAA;IAElC;AAA+C,WAAAoB;EAAuB,EAAA,GAAAS,MAAA,IAOlD,CAAA,GAAAA,MAAA,EACnBgD,aAAQ,SAAAtC,IAAAD,IAAAoF,IAAAC,IAGR;AAAA,QAI8CC,KAJ9C,CAAI,CAAJ,GAAI,GAAA,GAAA,GAAA,CAAA,GAAA,CAAA,GAAA,GAAmB,GAAE,GAAA,CAAA,GAAA,CAAA,GAAA,GAAA,IAAA,GAAA,CAAA,GAAA,CAAA,GAAA,GAAA,IAAA,IAAA,CAAA,GAAA,CAAA,GAAA,IAAA,IAAA,IAAA,CAAA,GAAA,CAAA,GACzB,IAAI,IAAA,IAAI,CAAA,GAAM,CAAA,GAAA,IAAA,KAAS,KAAA,CAAA,GAAA,CAAA,GACvB,IAAA,KAAA,KAAA,CAAA,GAAA,CAAA,IAAA,KAAA,KAAA,MAAA,CAAA,GAAA,CAAA,IAAA,KAAA,KAAA,MAAA,CAE8CD,CAAAA,EAAAA,EAAAA,GAAAA,KAAA9F,MAAK4C,EAAAoD,GAALC,KAAAA,MAAkBrD,EAAAsD;AAAAC,IAAAA,MAAAA,EAAAA;AAAAC,QAAAA,KAAAA,MAAAA,EAAAA,QAAajI,KAAA,GAAAkI,KAAAA,MAAA,GAAAC,IAAA,GAAAC,IAAA7F,GAAAA;AAAAA,QAAA,KAAAvC,IAAA;AAAA,aAAAA,KAAAoI,KAAA;AAAA,QAAA3G,GACvEyG,IAAAA,IAAAA,MAAAA,IAAAA,KAAAA,IAAAA,OAAAA,IAAAA,EAAAA,MAAAA,IAAAA,IAAAA,CAAwCA,GAAAA,KAAAA,MAAAA,EAAAA,WAAAA,IAAAA,IAAAA,GAAAA,IAAAA,KAAAA,CAAAA,GAAqElI,MAAAwG;MAAAA;AAC7G,aAAA0B,OAAI;IAAA;AAAA,QAAA,IAAA1C,GAAA,MAAA,IAAAA,GAAA,MAAA,IAAAA,GAAA,MAAA,IAAA,GAAA,IAAA,GAAA,IAAA,GAAA,IAAA,GAAA,IAAA,GAAA,IAAA;AAAA,SAAoC4C,IAAA,MAA0GC,EAAAA,IAAAA,MAAAA,EAAAA,MAA9B9F,IAAA,CAA8B,CAAA,IAAA,IAAAyB,KAAA,GAAAA,KAAA,GAAAA,MAAA;AACoChE,UAAAA,IAAAA,GAAAA,KAAA,IAAAoI,IAAA,GAAA;AAAA,YAAAvG,MAAA,EAAA,MAAA9B,IAAAiE,KAAA,CAAA;AACtL,YAAAsE,IAAAtI,KAAA,IAAA;AACRuI,UAAAD,CAAAD,IAAAA,EAAAG,CAAAA,GAAAA,EACEA,CAAAF,IAAAA;MAAAA;AAAOtI,UAAAA,KAAAA,IAAAA;AAAAA,SAAAA,IAAA,QAAAyI,IAAA,UAAAL,IAAApI,KAAA,QAAA,IAAAgE,OA1BE0E,EAAAA,CAAAA,IAAA1I,KAAAmI,GAAAQ,KAAA,GAAAR,IAAAA,KAAAA,KA8BLtG,MAAAA,EAAAA,YAAAA,MAAAA,IAAAA,KAAAA,KAAAA,IAAAA,IAAAA,GAAAA,GAAAA,GAAAA,GAAAA,IAAAA,GAAAA,KAAAA,GAAAA,IAAAA,EAAAA,GAAAA,IAAAA,IAGH+G,IAAAA,GAASC,IAAA7I;AAGZ,YAAA8I,IAAA;AACQ9I,QAAAA,KAAAoI,IAAA,MACRU,IAAAjH,MAAA4C,EAAAsE,WAAAxG,IAAAvC,IAAAuI,GAAAhH,GAAA+C,KAAA0E,IAAApB,GAAA,CAAA,GAAAQ,IAAApI,EAAAA,GAAA4H,GAAA,CAAA,CAAA;AAAA,YAAApB,IAAAsC,MAAA,IAAAG,IAAAA,QAAAA;AACEH,YAAA,KAAAA,GAAA;AAAOG,cAAAA,QAAAA;AAAA,cAAAnB,IAAAA,GAAAtB,IAAAsC,MAAAA,IAAAjB,GAAAqB,GACTrB;AAAAA,UAAAA,GAAAsB,KAAA,MAAAC,CAAA;AAAA,cAAAC,IAAAvB,GAAAmB,GAAApB,GAAAyB,GAAAzB;AAAAA,UAAAA,GAAA0B,KAAAF,CAAAA,KAAAA,KAAAA,GAAAA,IAAAA,CAAAA,IAAAA,GAAAA,IAAAA,CAAAA,GAOAX,EAEOC,CAAAA,IAAAnC,KAAA,KAAAxG,KAAAmI,GAAAO,EAAAC,IAAA,CAAAM,IAAAA,KAAAA,KAAAA,KAAAA,IAAAA,GAAPN,KAAAA,GACCR,IAAAA,KAAAA;QAAAA,MAAAA,CAAAA,GAAAA,KAAAA,GAAAA,EAsBAM,CAAAA;AAAAA;MAAAA;IAAAA;AAK2C,SAAA,KALNzI,MAAAuC,KAAAA,GAAAA,WAAAA,IAAAA,OACrCmG,EAAAA,CAAA1I,IAAAA,KAAAmI,GAAgDQ,KAAAA,GAAAR,IAAAA,KAAAD,KAAArG,MAAAA,EAAAA,YAAAA,GAAAA,GAAAA,GAAAA,GAAAA,IAAAA,GAAAA,KAAAA,GAAAA,IAAAA,EAAAA,GAEhD8G,IAAAA,GACCF,IAAA,GAAQE,IAAAA,IAAAA,IAAAA,GAAAA,IAAAA,KACwC,MAAA,IAANT,MAAoDA,CAAAA;AAAAA,WAAAA,OACvF;EAAA,GAAArG,MAAA,EAAMkH,aAAA,SAAAxG,IAAAvC,IAAAuI,IAAAhH,IAAAiI,IAAAC,IACf;AAAA,QAAAnC,KAAA,QAAAtH,IAAA0J,KAAAA,GAAAA,EAECC,GAAAA,KAAAA,KAAWD,KAAE,QAAW;AACxB,QAAGA,MAAApC,MAAA/F,MAAQM,MAAA4C,EAAAmF,MAAArH,IAAAvC,KAAA2J,EAAAA,EAAAA,QAAAA;AAAAA,aAA6BE,KAAA,GAAAC,IAAAA,GAAzCC,IAAAzF,KAAA0E,IAAA,OAAAhJ,EAAAA,GAAAA,MACC+J,KAAA,KAAAN,EAAAA,MAAAC,MAAApC,MAAA;AAAA,UAAA,KAAA/F,MAASgB,GAAAA,KAAAA,EAAAA,KAAAA,GAAAA,KAAAA,KAAAA,EAAAA,GAAAA;AAAOW,YAAAA,IAAArB,MAAA4C,EAAAuF,SAAAzH,IAAAvC,IAAA2J,EAAAE;AAAAA,YAAAA,IAAAA,IAAAA;AAGf,cAAA,IADQF,KAATE,KAAA3G,MACKsG,GAEJ;AACCG,UAAAA,KAAAzG,IAAAA,MACAA,IAAAyG,KAAA;AAEA,mBADAM,IAAA,GACAC,IAAAA,GAAKA,IAAAhH,IAAA,GAAAgH,KAAA;AAAA,gBAALC,IAASnK,KAAA2J,KAAAO,IAAA,QAAA,OAATE,IAAAD,IAAS5B,GAAAA,CAAAA,IAAT,QAAA;AAAA,gBAAA,MAED0B,IAAAA,GAAAA,KAAAA;UAEgB;QAAkB;MAAA;AACuEN,MAAAA,OAD3GrC,KAAAoC,OACIA,KAAAA,GAAAA,EAAAA,KAAuG,QAAY;IAAA;AAAA,WAAOG,MAAA,KAAAC;EAAAA,GAAAA,MAC9HrF,EAAAuF,WAAA,SAAAzH,IAAAvC,IAAA2J,IAAAA;AAIG,QAAApH,GAAAvC,EAAGuC,KAAAA,GAAEvC,KAAF2J,EAAAA,KAAOpH,GAAAvC,KAAA,CAAAuC,KAAAA,GAAAvC,KAAA,IAAA2J,EAAApH,KAAAA,GAAAvC,KAAA,CAAA,KAAAuC,GAAAvC,KAAA,IAAA2J,EAAAA,EAAA,QAAA;AAAA,QAAA3J,KAAA+D,IAAA1B,KAAA,KAAA,IAAAtC,GAAA,QAAAgE,KAAA,GAQV;AAAA,SAAAA,MAAA,GAAA/D,KAAGgE,MAAAA,GAAQzB,EAAAA,KAAAA,GAAAA,KAAAA,EAAAA,IAAAA,CAAAA;AAAAA,WAAAA,KAAAA;EAAAA,GAAAA,MAAAA,EAOJqH,QAAA,SAAErH,IAAFvC,IAAAA;AAAV,YAAAuC,GAAAvC,EAAA,KAAA,IAAAD,GAAAC,KAAA,CAAAuC,MAAAA,GAAAA,KAAA,CAAA,KAAA,KAAA;EAAA,GAGAV,MAAAwI,QAAA,GACCxI,MAAA4C,EAAA6F,cAAAC,SAAAA,IAAAA,IAAAA,IAAAA,IAAAA,IAAAA,IAAAA,IAAAA,IAAAA,IAAY;AAAA,QAAgBC,IAAAC,GAAAA,GAAAA,GAAAA,GAAAA,GAAAA,GAAAA,GAAAA,GAAX5C,IAAAhG,MAAAA,EAAAA,GAAA6I,IAAAA,MAAAA,EAAAA,QAAAzC,IAAAA,MAAAA,EAAAA;AAAWJ,MAAAsB,KAAA,GAAA,KAAAsB,KAAAD,KAAA3I,MAAA4C,EAAAkG,SAAA,GAAA,CAAA,GAAAC,IAAAA,GAAA,CAAA,GAAAC,IAAAA,GAAA,CAAAC,GAAAA,IAAAN,GAAA,CAAA,GAAA,IACbA,GAAA,CAAA,GAAA,IAAkBA,GAAA,CAAA,GAAA,IAAQA,GAAA,CAA1CO,GAAAA,IAAAP,GAAA,CAAA;AAAA,QACyEQ,IAAG,MAAA,MAAA9C,KAAA,IAAA,KAAA,IAAA,KAAAA,KAAA,IAAA,OAAA+C,MAAA,IAAAC,IAAAtC,KAAA/G,MAAA4C,EAAA0G,SAAAtD,EAAAuD,QAAAvD,EAAAsB,IAAAA,IAAAtH,MAAA4C,EAAA0G,SAAAtD,EAAAwD,QAAAxD,EAAA0B,IAAAA,GAAA+B,IAAA1C,KAAA/G,MAAAA,EAAAA,SAAAA,EAAAA,OAAAA,EAAAA,IAAAA,IAAAA,MAAAA,EAAAA,SAAAA,EAAAA,OAAAA,EAAAA,IAAAyJ;AAAAA,SAAA,KAAA,IAAAC,IAAA1J,MAAA4C,EAAA0G,SAAAtD,EAAA2D,OAAA3D,EAAA4D,IAAAA,KAAA,IAAA5D,EAAA4D,KAAA,EAAA,IAAA,IAAA5D,EAAA4D,KAAA,EAAA,IAAA,IAAA5D,EAAA4D,KAAA,EAAA;AAAA,aAAAvB,IAAA,GAAAA,IAAA,KAAArC,IAAAA,GAAAsB,KAAAe,CAAA,IAAA;AAAA,SAAA,IAAA,GAAA,IAAA,IAAA,IAAA,GAAA,KAA8BA,CAAAA,IAAA;AAAA,SAAAA,IAAA,GAAAA,IAAA,IAAAA,IAAArC,GAAA4D,KAAAA,CAAAA,IAAAA;AAEzG,QAAAC,IAAAV,IAAAA,KAAAA,IAAAA,IAAAA,IAAAA,IAAAA,IAAAA,IAAAA;AAGD,QAAA,EAAAvJ,IAAAuC,IAHmBuG,EAAAA,GAAAA,EACXrC,IAAAA,KAAA,GAAAwD,CAAMxD,GAAAA,MAAA,GAGT,KAAAwD,GAAA;AAEJ,aAAyCxD,MAAAA,IAAAA,MAAAA,CAAAA;AAAaA,MAAAA,KAAArG,MAAAA,EAAG8J,WAAAA,IAAAA,IAAAA,IAAAA,IAAAA,EAAAA;IAAAA,OAAAA;AAAAA,UAAAA,GAAAA;AAAAA,UAA0B,KAAAD,MACnFE,IAAAA,EAAaR,QAAAS,IAAAhE,EAAAwD,SACe,KAAAK,GAAA;AAAA,QAAA7J,MAAA,EACjBiK,UAAAjE,EAAU4C,OAAAA,CAAAA,GAAF5I,MAAA4C,EAAyDsH,SAAAlE,EAAA4C,OAAAA,CAAAA,GAAAA,MAAAA,EAAAA,UAE1E5C,EAAAgE,OAAAjB,CAAAA,GAAS/I,MAAAA,EAAOkK,SAAPlE,EAAAgE,OAAAjB,CAAAA,GAER/I,MAAA4C,EAAAA,UAAGoD,EAAA2D,OAAAX,CAAAA,GAAAhJ,MAAA4C,EAAAsH,SAAAlE,EAAA2D,OAAAX,CAAAA,GAAKe,IAAAA,EAAAA,OAAGC,IAAAhE,EAAAA,OAAMI,EAAA3F,IAAA4F,IAAA4C,IAAG,GAAA,GACpB7C,EAAA3F,IADwB4F,MAAA,GACxB8D,IAAG,CAAM/D,GAAAA,EAAA3F,IAAAA,MAAAA,GAAA4F,IAAAA,CAAIA,GAAAA,MAAA;AACb,iBAAAlI,IAAA,GAAWA,IAAAuL,GAAAvL,IACXiI,GAAAA,IAAAA,KAAAA,IAAAA,GAAAA,EAAAA,MAAAA,KAAAA,EAAAA,KAAAA,CAAAA,KAAAA,EAAAA,CAAAA;AAAAA,QAAAA,MAAAA,IAAAA,GAGCC,KAAArG,MAAA4C,EAAIwH,UAAAC,GAAQrE,EAAAA,OAAAA,IAAAA,EACZK,GAAAA,KAAAA,MAAA+D,EAAAA,UAAGlB,GAAAlD,EAAK2D,OAAMlJ,IAAA4F,EAAAA;MAAAA;AAAnB,eAAsB1D,IAAA2H,IAAtBC,IAAA,GAAAA,IAAAzD,IAAAyD,KAAA,GAAA;AAEA5H,iBADA6H,IAAA3D,GAAA0D,CAAA5F,GAAAA,IAAAA,MAAA,IAAAG,IAAAnC,KAAA,UAAA6H,IACA7H,IAAAA,IACA0D,CAAAA,KAAArG,MAAA4C,EAAA6H,UAAA/J,GAAAiC,GAAAoH,GAAAA,GAAAtJ,IAAA4F,EAAAA;AAAA1B,YAAA,KAAAA,GAAA;AAEU,cAAA+F,IAAAA,GAAAA,IAAAA,CAAUtD,GAAAA,IAAAsD,KAAA,IAAAnD,IAAAA,KAAAA,IAAAA,KAAAC,IAAAA,MAAAA;AACUpB,YAAA3F,IAA5B4F,KAAArG,MAAA4C,EAAA6H,UAAS,MAASlD,GAAAA,GAAAA,IAAAA,EAAU5C,GAAAA,IAAAqB,EAAAqB,IAAAE,CAAAlB,CAAAA,GAAAA,MAAAL,EAAAA,IAAAA,CAAAA,GAE7B6C,EAAAA,IAAAA,KAAAA,MAAAA,EAAAA,UAAAA,GAAAA,GAAAA,IAAAA,EAAAA,GAAAA,IAAAA,EAAAA,IAAAA,CAAAA,CAAAA,GACDxC,MAAAL,EAAAA,IAAAwB,CAAAA,GAAAA,KAAAA;QAEkF;MACjF;AAAgDnB,MAAAA,KAAAA,MAAAzD,EAAAA,UAAA,KAAAmH,GAAAtJ,IAAA4F,EAEhD;IAAA;AAAA,WAAmCA;EAAAA,GAAArG,MAAA4C,EAAAkH,aAAApJ,SAAAA,IAAAA,IAAAA,IAAAA,IAAAA,IAAAA;AAAA,QAAAiK,KAAAtE,OAAA;AAoBZ,WApBY5F,GAAAkK,EAAAA,IAAAhG,IAAAA,GACnCgG,KAAO,CAAA,IAAEhG,OAAA,GAEVlE,GAAOkK,KAAA,CAAQ,IAAA,MAAAxM,GAASwM,EAAAA,GAAAA,GAAAA,KAAAA,CAAAA,IAAAA,MAAAA,GAAAA,KAAAA,CAAAA,GAgB2BA,MAAE,GACpDlK,GAAAmK,IAAAA,IAASjK,WAAED,GAAA7B,QAAA8D,IAAAgC,EAAAgG,GAAAA,EAAAA,GAAYtE,MAAK1B,KAAA,KAAA;EAAA,GAAA3E,MAAa8I,EAAAA,WAAK,WAAqC;AAAA,aAAjC9C,KAAAA,MAAOpD,EAAAoD,GAAI4C,KAAA5I,MAAO4C,EAAAA,SAAAA,GAAAA,MAAAoD,GAAA+D,OAAA,EAAA,GAAAhB,KAAA/I,MAAA4C,EAAAiI,SAAA7E,GAAA0B,MAAA1B,GAAAgE,OAAA,EAAA,GAAIK,KAAK,CAAA,GAAEpB,KAAAA,MAAArG,EAAAkI,UAAA9E,GAAA+D,OAAAM,EAAAA,GAAAnB,KAAA,CAAA,GAAAiB,KAAAnK,MAAA4C,EAAAkI,UAAA9E,GAAAgE,OAAAd,EAAS/K,GAAAA,KAAE,GAAAA,KAAAA,GAAAoE,QAAApE,MAAA,EAAI6H,CAAAA,GAAK4D,KAAAS,GAAAlM,EAAE,CAAA;AAAA,SAAAA,KAAA,GAAAA,KAAA+K,GAAA3G,QAAApE,MAAA,EAAA6H,CAAAA,GAAAA,KAAAA,GAAAA,EAIrG,CAAA;AAAA,aAFAgD,KAAIhJ,MAAa4C,EAAAiI,SAAI7E,GAAAA,MAAeA,GAAA2D,OAAA,CAAA,GACpCD,KAAA,IACIA,KAAU,KAAA,KAAQ1D,GAAA2D,MAAAA,KAAAA,GAAAA,KAAAA,KAAAA,CAAAA,KAAAA,EAAAA,IAAAA,CAAAA;AAAgB,WAAE,CAAQf,IAAAA,IAAlCI,IAAAA,IAAAA,IAAAA,IAA4CqB,IAAAnB,EAAAA;EAAAA,GAAAA,MAA4BtG,EAAAmI,YAAA,SAAApH,IAAAA;AACtF,aADAkB,KAAA,CAAA,GAAAtF,KACc,GAAcpB,KAAAwF,GAAApB,QAAApE,MAAA,EAAK0G,CAAAA,GAALjB,KAAAD,GAAAxF,KAAA,CAAA,CAAA;AAAA,WAAgB0G;EAAAA,GAAmCmG,MAAAA,EAAAA,UAAfrH,SAAAA,IAAAA;AAAAA,aAA8BkB,KAAA,IAE9F1G,KAAA,GAAAA,KAAAwF,GAAApB,QAAApE,MAAA,EAAU,MAAVwF,GAAAA,KAAU,CAAUkB,MAAAA,OAAA1G,MAAA,KAAA;AAAA,WAAA0G;EAAAA,GAAAA,MAAAjC,EAAA0G,WAAA,SAAA2B,IAAAC,IAAK;AAAA,aAAA3L,KAAA,GAAApB,KAAAA,GAAAA,KAAA+M,GAAA3I,QAAUpE,KAAKmD,CAAAA,MAAA4J,GAAQ/M,EAAE8M,IAAAA,GAAS,KAAN9M,MAAA,EAAWmD;AAAAA,WAAAA;EAAAA,GAAuC8I,MAAAA,EAAAA,YAAA,SAAAQ,IAAUK,IAAAxK,IAAA4F,IAAAA;AAEjH,aAAAlI,KAAA,GAAAqC,KAAAtC,GAAA,QAAAsC,MAAA,GAAA;AAAA,UAAAmD,KAAaiH,GAAAzM,EAAAA,GAAAA,KAAAyM,GAAAA,KAAAA,CAAAA;AAAAA,MAAAA,KAAD5K,MAAA4C,EAAA6H,UAAAtI,IAAA8I,IAAAxK,IAAA4F,EAAAA;AAAAA,UAAAA,KAAAA,MAAAA,KAAAA,IAAAA,MAAAA,KAAAA,IAAAA;AAAAA,MAAAA,KAAAA,OACcrG,MAAAA,EAAAA,OAAAA,IAAAA,IAAAA,IAAAA,EAAAA,GAAAA,MAAAA;IAG1B;AAAa,WAAAqG;EAAAA,GAAAA,MAAAA,EAAAA,YAAAA,SACD4E,IAAAA,IACXtG;AAAAA,aADkBA,KAAAA,GAAAA,QAClBA,KAAAA,MAAAA,KAAMsG,GAAAtG,KAAAA,CAAAA,IAAAA,CAAAA,MAEF;AACJxG,aAAAA,KAAAA,GAAIwG,KAAAA,IAAAxG,MAAJ,GAAW;AAAA,UAAAqC,KAAAyK,GAAA9M,KAAAA,CAAAgN,GAAAA,KAAAA,KAAAA,IAAAA,KAAAA,GAAAA,KAAAA,CAAAA,IAAAA,IAAAC,KAAAA,KAAAA,IAAAA,KAAAA,GAAAA,KAAAA,CAAAA,IAAAA,IAAAC,KAAAA,KAAAA,KAAAA,KAAAA,GAAAA,KAAAA,CAAAA;AACXlJ,UAAA,KAAAA,MAAGgJ,MAAOhJ,MAAGiJ,MAAHjJ,IAAG;AAAGmJ,iBAAAA,KAAAnN,KAAAA,GAAAmN,KAAM,IAAA/L,MAAE0L,GAAAA,KAAAA,CAAAA,KAAAA,KAAAA,CAAAA,MAAAA;AAAAA,SAASM,KAAA9I,KAAAA,IAAAA,KAAAA,IAAAA,OAAAA,GAAAA,GAAAA,KAAnC,KAEGmI,GAAAhH,KAAA,IAAO2H,KAAA,CACPX,IAAAA,GAAAhH,KAAA,IAAA2H,KAAA,EAAA,GAAApN,MAA0B,IAAAoN,KAAA;MAAM,WAAApJ,MAAAkJ,MAAAF,MAAAhJ,MAAyBiJ,MAAAjJ,IAAA;AACzB,aAAhCmJ,KAAAnN,KAAA,GAAgCmN,KAAA,IAEhCA,MAAAA,GAAAA,KAAAA,CAAAA,KAAAA,KAAAA,CAAAA,MAAAA;AAAkB,YAAAC,KAAA9I,KAAA0E,IAAAmE,KAAA,IAAAnN,OAAA,GAAA,CAAA;AAAA,QAAA+D,GAAA,KAAA,IAAAxC,KAAA,CAAA,GAESvB,MAAA,IAAAoN,KAAA;MAAA,MACAX,CAAAA,GAAKhH,KAAAzB,IAAA,CAChC;IAAA;AAAA,WAA2BwC,OAAA;EAAA,GAAKkG,MAAAA,EAAAA,WAAA,SAAAK,IAAAA,IAAAA,IAAAA;AAAAA,QAAAA,KAAAA,CAAAA,GAAAA,KAAAA,GAAAA,QAAAA,KAAAA,GAAAA,QAAAA,KAAAA;AAAAA,SAEhC/M,KAAA,GAAAA,KAAA6J,IAAA7J,MAAAA,EAAQ8M,CAAAA,GAAAA,EAAAA,IAAE,GAAKA,GAAM9M,KAAA,CAAA,IAAA;AAAA,SAAAA,KAAA,GAAKqN,KAAAA,IAAAA,KAAAA,MAAAA,GAAAA,EAAAA,KAAAA,GAAA5H,KAAA,EAAkD6H,KAAAtN,IAAAyB,GAAAsL,GAAA/M,EAAAA,EAAAA,CAAAA;AAAAA,QAAAA,KAAAA,GAAAA,QAAAA,KACrDqN,GAAAA,MAAA,CAAA;AAAA,QAAoB,KAAA5L,GAAA,QAAA;AAAA,QAAA,KAAAkF,IAAA;AAA3C,UAAA2G,KAAAD,GAAA,CAAA,EAAAC;AAAAC,MAAAA,KAAAA,KAAAA,KAAAA,IAAAA;AACA,aAAAT,GAAM,KAAAvL,MAAA,EAAA,IAAK,GAALuL,GAAiB,KAAA9I,MAAA,EAAA,IAAA,GAAvB;IACH;AAAAqJ,IAAAA,GAAAG,KAAA,SAAAhI,IAAAkB,IAAAA;AAAA,aAAAlB,GAAAA,IAAAA,GAAAA;IAAAA,CAAAA;AAAAA,QAAAA,IAAA6H,GAAAA,CAAAA,GAAA3G,IAAAA,GAAAA,CAAA+G,GAAAA,IAAAA,GAAAC,IAAAA,GAAAC,IAAAA;AAG8BD,SAH9BL,GAAA,CAAA,IAAA,EAAAC,KAAAA,IAAA,GAAA,EAAA,IAAA,EAAA,GAEEtJ,GAAAwB,GACApE,GAAAsF,GAA4BkH,GAAA,EAAAF,GAAAA,KAAA/G,KAAA,IAC3BnB,KADDiI,KAAAC,MAAAC,KAAAhH,MAAA0G,GAAAI,CAAAA,EAAAhM,IAAA4L,GAAAM,CAAAlM,EAAAA,KACC4L,GAAAI,GAAAA,IAAmDJ,GAAAA,GAAAA,GACnD3G,IAAAA,KAAAA,MAAAA,KAAAA,MAAAA,GAAAA,CAAAA,EAAAA,IAAAA,GAAAA,CAAAA,EAAAA,KAAA2G,GAAAI,GAAAA,IAAMJ,GAANM,GAAAA,GAAAA,GAAUD,GAAAA,IAAA,EAAA,KAAA,IAAA,GAAA,EAAA,IAAA,EAAA,GAEV1J,GAAAwB,GACCpE,GAAAsF,EAAAA;AAAA,QAAAmH,IAAAhM,MAAA4C,EAAAqJ,SAAAT,GAAAK,IAAA,CAAA,GAAA,CAAA;AAAA,SACAG,IAAAE,OAAMlM,MAAK4C,EAALuJ,cAAAT,IAAAQ,IAAqBF,CAAAA,GAAAA,IAAAA,KAAAA,KAAAA,GAI3B7N,KAAA2G,IAAA3G,KAAkC8M,CAAAA,GAAAQ,KAAAR,GAAA9M,EAAAsN,EAAAA,OAAAA,EAAAA,IAAAA,GAAAA,EAAAA,EAAAA;AAAAA,WAAAO;EAAAA,GAAAA,MAAAA,EAAAA,WAAAA,SAAAA,IAAAA,IAAAA;AAAAA,WAAkB,MAAAP,GAAAA,OAAAA,GAAAA,IAAAA,IAAAA,MAAAA,KAAAA,IAAAA,MAMhD7I,EAAAqJ,SAAA/J,GAAAC,GAAA4J,KAAA,CAAA/L,GAAAA,MAAA4C,EAAAqJ,SAAA/J,GAAA3C,GAAAwM,KAAA,CAAA,CAAA;EAAA,GAAA/L,MAAA,EAAA,gBAAA,SAAA9B,IAAAgE,IAAA3C,IAER;AAAA,QAAApB,KAAA,GAAOiO,KAAA,KAAsBJ,KAAAjD,IAAAsD,KAAAA;AAAAA,SAAAC,GAAAX,KAAAA,SAAAA,IAAa9G,IAAI;AAAA,aAAAA,GAAKkH,KAAApI,GAAAoI,IAAApI,GAAA/D,IAAAiF,GAAAjF,IAAAiF,GAAAkH,IAAApI,GAAAoI;IAAAA,CAAAA,GAAA5N,KAAA,GAAAA,KAAAmO,GAAA/J,UAAnD+J,GAAAnO,EAAA4N,EAAAA,IAAAhD,IAAmD5K,MAAnD;AAAmD,UAAAoO,KAAAD,GAAAnO,EAAA4N,EAAAA;AAClDO,MAAAA,GAAUvD,EAAAA,EAAAA,IAAAA,IACVsD,MAAAD,MAAA,KAAAJ,KAAAO;IAAS;AAAA,SAAAF,QAAAL,KAAAjD,IAAAsD,KAAA,KAAA;AAAA,OAATE,KAAAD,GAAAnO,EAAAA,EAAA4N,KAASQ,MAATD,GAAAA,EAAAA,EAAAA,KAAgBD,MAAO,KAAAtD,KAAAwD,KAAA,KAAUpO;IAAAA;AAAAA,WAAAA,MAAAA,GAAAA,KAAAmO,CAAAA,GAAAnO,EAAA4N,EAAAA,KAAAhD,MAAAsD,KAAA,MAAAC,GAAAA,EAAAP,EAAAA,KAAKM;AAAAA,SACtCA,MAAYG,QAAAC,IAAA,WAAA;EAAA,GAAwBzM,MAAA4C,EAAAsD,aAAWwG,SAAAA,IAAAA,IAAAA;AAAAA,QAAAvO,KAAA;AAU/C,WAV+CwO,GAAA,KAAAxO,EAAAA,KAAAuO,OAAAvO,MAAA,KAAAwO,GAAA,IAAAxO,EAAAuO,KAAAA,OAAAvO,MAAA,IAAAwO,GAAA,IAAAxO,EAAAuO,KAAAA,OAAAvO,MAAA,IAAAwO,GAAAA,IAAAA,EAAAA,KAAAA,OAAAA,MAAAA,IAGhDA,GAAA,IAAAxO,EAAAA,KAMKA,OAAAA,MAAE,IACNA;EACA,GAOA6B,MAAA4C,EAAW6H,YAAA,SAAAmC,IAAA7C,IAAAtJ,IAAA4F,IAAAA;AAAAA,WAAKrG,MAAA4C,EAAAiK,OAAApM,IAAA4F,IAAA0D,GAAA6C,MAAA,CAAmDvG,CAAAA,GAAAA,KAAAA,GAAA,KAAAuG,MAAA,EAAA;EAAA,GACnE5M,MAAA4C,EAAAP,UAAO,SAAa3B,IAAAA,IACrB;AAAA,QAAAoM,KAAAnM;AACiC,QAAA,KAAAD,GAAAA,CAAW,KAAA,KAALA,GAAK,CAAA,EAAA,QAAAN,MAAA,IAAA0M,GAAA,CAAIlK;AAAAA,QAAAA,KAAAA,MAASA,GAAEmK,KAAanK,GAAAoK,QAAbC,KAAarK,GAAAsK,QAAAC,KAAAA,GAAAA,aAAAlD,KAAAA,GAAAA,WAAAmD,KAAAA,GAAAA,WAAAC,KAAAA,GAAAA,QAAsBrH,IAAAA,GAAAA,GAA9FsH,IAAA,QAAAlN;AACAkN,UAA6ClN,KAAI,IAAA0M,GAAMpM,GAAA6B,WAAA,KAAA,CACvDmG;AAAAA,aADmG6E,GAAAC,GAAvB9E,IAAA,GAAAmB,IAAAA,GAAAA,IAAW,GAAA4D,IAAAA,GAAAC,IAAAA,GAAA9E,IAAAA,GAAAG,IAAAA,GAAEpG,IAAAA,GAAAA,IAAAA,GAClF,KAAP+F,IAAAA,KAAAA,IAA6CqE,GAAAA,IAAE1G,GAAAA,CAAA0G,GAAAA,IAAAA,GAAArM,IAAA2F,IAAA,GAAA,CAAA,GAAA,KAAQ,GAA4B,KAAAwD,GAAAA;AAAAA,UAAAA,MAK9CzJ,KAAAJ,MAAA4C,EAAA+K,OAAAvN,IAAAuC,KAAA,KAAA,GAAA,IAAA,KAAAkH,MAAnC0D,IAAAA,EAAAA,OACAC,IAAAA,EAAKI,OAAOhF,IAAAA,KAAoCG,IAAAA,KAAlD,KAAAc,GAAA;AAECgE,YAAAA,GAAAA,IAAAA,GAAAA,CAAAA,IAAAA,KACDJ,IAAAR,GAAAvM,IAAA2F,IAAAA,GAAA,CAAA,IAAA,GAAAqH,IAAAT,GAAAvM,IAAA2F,IAAA,IAAA,CAAA,IAAA,GAAA,KAAA;AACA,iBAAOlI,IAAAA,GAAAA,IAAAA,IAAAA,KAAAA,EAAAA,GACEwL,MAAAxL,CAAA,IAAA,GAAK6H,EAAAA,MAAA7H,IAAA,CAAA,IAAA;AAAA,YAAA6J,IAAA;AAAA,aAAA7J,IAAA,GAAAA,IAAAuP,GAAAvP,KAAA;AAAA,cAAA,IAAAwF,GAAAzF,IAAA,IAAA,IAAA,GAAA,CAAA;AAAA8H,YAAA2D,MAAA,KAAA3D,EAAA8H,KAAA3P,CAAAA,KAAA,EAAAgE,IAAAA,GAAeA,IAAA6F,MAA0BA,IAAK7F;QAE1D;AAAQkE,aAAO,IAAIqH,GAElBzD,GAAAA,EAAAA,OAASjC,CAAAA,GAAAA,GACHhC,EAAA2D,OAAN3B,GAAMhC,EAAY+H,IAAYR,GAAAA,IAAAvH,EAAAuH,MAAGC,IAAAA,EAAAA,MAAInH,IAAA8G,GAAAnH,EAAAA,OAAAA,KAAAA,KAAAA,GAAAA,IAAAA,GAAAA,IAAAA,GAAAA,EAAAA,KAAAA;AAE3B,YAAAgI,IAAApL,GAAIqL,SAAAjI,EAAAkI,OAAA,GAAAL,GAAA7H,EAAA+D,KAAAA;AAAAA,aAAAA,KAAAA,KAAAA;AAAAA,YAAAA,IAIVnH,GAAGqL,SAAAjI,EAAAkI,OAAAL,GAAAJ,GAAAzH,EAAAgE,KAAAA;AACPjB,aAAAA,KAAAA,KAAS,GAAAnJ,GAAA,EAAA,OAAA,CAETwN,GAAAA,GAAAA,EAAAA,OAAAA,GAAAA,CAAAA,GACAnD,GAAAjE,EAAAA,OAAAA,CAAAA,GAAgBoH,GAAApH,EAAAgE,OAAAmE,GAAAX,CAAAA;MAAAA;AAEnB,iBAAA;AAAA5H,YAAAA,IAAAA,EAAAyH,GAAAA,IAAAA,CAAAA,IAAAA,CAAAA;AAAAA,aACO,KAAAzH;AAAY6F,YAAAA,IAAAA,MAAAA;AAAAA,YACnBA,MAAA,KAAA,EAAA,CAAAvJ,GAAA,GAAA,IACcuJ;aACd;AAAA,cAAA,OAAAA,EAAAA;AAAA3G,cAAAA,IAAAnC,IAAA8I,IAAAA;AAAA,cAAAA,IAAA,KAAA;AAGK,gBAAK2C,IAAApI,EAAEqI,KAAAA,IAAU,GAAA;AAAAvJ,gBAAAsJ,KAAAA,MAAAA,KAAAA,GAAAA,IAAAA,GAAAA,IAAAA,CAAAA,GAAG/H,KAAK+H,IAAAA;UAAO;AAAU,cAAAE,IAAAd,EAAAA,GAAAA,IAAAA,CAAAA,IAAAA,CAAAA;AACMnH,eAAA,KAAAiI;AAAEC,cAAAA,IAAID,MAAA,GAAA,IAAA,EAAA,KAAA,CAAA,GAAA,KAAA,MAAA,KAAA9N,GAAAtC,IAAA,GAAA,KAAA,CAAA;AAAA,eAAA,KAAA,KAAA,GAAA,MAAAgE,KAAAlC,MAAA,EAAA,OAAAkC,IAAA,KAAA,KAAA,GAAA,IAAA,IAAA,IAAA,CAAAA,GAAA,CAAA,IAAAA,GAAA,MAAA,CAAA,GAAAA,GACtDS,CAAAA,IAAAvC,GAAAuC,MAAAyE,CAAAA,GAAAA,GAAAA,CAAAA,IAAAA,GAAAA,MAAAA,CAEJhH,GAAAA,GAAAA,CAAAA,IAAAA,GAAAA,MAAAA,CAAAA;AAADuC,cAAAmC;QAAAA;MACyB;IAAA,OAAA;AAxCqE,YAAP,IAAAuB,OAAwBA,KAAO,KAAA,IAAAA;AAErH,UAAIsE,IAAS,KAALtE,MAAK,IAAA1B,IAAAA,GAAAA,IAAAA,CAAAA,IAAAA,GAAAA,IAAAA,CAAAA,KAAAA;AAAE2I,YAAYlN,KAAAA,MAAAA,EAAAA,OAAAA,IAAAA,IAAAA,CAAAA,IAAAA,GAAAA,IAAAA,IAAAA,GAAAA,GAAAA,QAAAA,GAAAA,aAAAA,GAAAA,CAAAA,GAAAA,CAAAA,GAAAA,IAAAA,IAAAA,KAAAA,GAE1BuC,KAAMgC;IAAAA;AAoCR,WAAAvE,GAAAmC,UAAAI,IAAAvC,KAAAA,GAAArC,MAAA,GAAA4E,CAAAA;EAAAA,GAAAA,MACCgL,EAAAA,SAAI,SAAEvN,IAAoBuE,IAAAA;AAAY,QAAG6J,KAAApO,GAAAmC;AAAAA,QAAAA,MAAAA,GAAAA,QAAAA;AAAkC,QAAAkM,KAAAA,IAAAA,WAAtBhM,KAAAiM,IAAAA,MAAA,GAAA/J,EAAAA,CAAAA;AAAAA,WAAAA,GAAAA,IAAAA,IAA4B,CAAA,GAAAxG;EAAA,GAAA6B,MAAA,EAEhF2O,cAAG,SAAAzQ,IAAW0Q,IAAAjK,IAAAjE,IAAA2F,IAAA4E,IAAAA;AAAAA,aAAWgC,KAAAjN,MAAA4C,EAAAA,QAAAyK,KAAAA,MAAAA,EAAAA,QAA2BlP,KAAA,GAAAgE,KAAuBwC,MAAA;AAAAiB,UAAAA,KAAA2H,GAAAF,GAAA3M,IAAA2F,EAAAA,IAAAuI,EAAAA;AAAAvI,MAAAA,MAAA,KAAAT;AAAAA,UAAG6F,IAAA7F,OAAA;AAAhF6F,UAAAA,KAAAA,GACER,CAAAA,GAAAQ,EAAAA,IAAAA,GAAoDtN;WAAO;AAAA,YAAA0Q,IAAA,GAAArK,IAAA;AAAmB,cAAA,KAAhFA,IAAA,IAAAyI,GAAAvM,IAAA2F,IAAA,CAAAA,GAAAA,MAAA,GACEwI,IAAAA,GAAgB1Q,KAAA,CAAS,KAAA,MAANsN,KAARjH,IAAAyI,IAAAA,GAAAA,IAAAA,IAAAA,CAAAA,GAAAA,MAAAA,KAAyB,MAAFxB,MAAAA,IAAAA,KAAAA,GAAAA,IAAAA,IAAAA,CAAAA,GAAAA,MAAAA;AAAAA,iBAAAA,IAAAA,KAAAA,GAAAA,KAAAA,IAEnCR,CAAAA,GAAAA,EAAAA,IAAAA,GACG9M;MAAqD;IAAzD;AAAA,WACCkI;EAAAA,GAAgB4H,MAAAA,EAAAA,WAAA,SAAAa,IAAAA,IAAAA,IAAAA,IAAAA;AAAAA,aAAAC,KAAA,GAAA5Q,KAAA,GAAA6J,KAAAiD,GAAAA,WAAAA,GAAA9M,KAAAwG,MAAAA;AAAAA,UAAA+H,KAAAoC,GAAAA,KAAAA,EAAAA;AAAA7D,MAAAA,GAAAA,MAAAA,CAAAA,IAAAA,GAAAA,GAAAA,KAAAA,MAAAA,EAAAA,IAAAA,IAAKyB,KAAAqC,OAAMA,KAAArC,KAAAA;IAAAA;AAAAA,WAAAA,KAAAA,KAAAA,CAAAA,GAC3BvO,MAAA,CAAA,IAAA,GACD8M,GAAAA,KAAAA,MAAAA,EAAAA,IAAAA,GAAAA;AAAAA,WAG0B8D;EAAAA,GAE1B/O,MAAA4C,EAAAqH,YAAA,SAAAgB,IAAAA,IAIC;AAAA,aAFDrF,IAAAoJ,IAAAxK,IAAAG,IAFAqB,KAAAhG,MAAA4C,EAAAoD,GAAAiJ,KAAAhE,GAAA1I,QAEgC2M,KAAAA,GAASA,UAExC/Q,KAAA,GAAAuB,MAAAwC,IAAAxC,KAADwP,CAAAA,GAAA/Q,EAAA,IAAA;AAEC,SAAAA,KAAI,GAAJA,KAA4B+Q,IAAAA,MAAAA,EAAAA,CAAAA,GAAAA,GAAY/Q,EAAAA,CAAAA;AAAOgR,QAAAA,IAAAA,GAAcA;AAAsD,SAAlDvJ,KAAA,GAAAzD,GAAA,CAAA,IAAA,GAAkD6M,KAAA,GAAAA,MAAAA,IAAAA,KAAAA,CAAAA,KACnHpJ,KAAAA,GAAAA,KAAAA,CAAAA,KAAAA,GACAuJ,EAAMH,EAAIpJ,IAAAA;AAAwB,SAAApB,KAAA,GAAAA,KAAAyK,IAAAzK,MAAA,EAAA,OAAAb,KAAAzF,GAAAsC,KAAA,CAAA,OAAAtC,GAAAsC,EAAA,IAAA,EAAAmD,EAAyDwL,GAAAA,EAAAxK,EAAAA;EAAAA,GACvDyI,MAAAA,EAAAA,YAAOnC,SAAAA,IAAAA,IAAAA,IAA5C;AAAA,aAAA9M,KAA+C8M,GAAA1I,QAAa6M,KAAApP,MAAA4C,EAAAoD,EAAAqJ,OAA5DlR,KAAAA,GAAAA,KAAA8Q,IAAA9Q,MAAA,EAAA,KAAkF,KAAAD,GAAAyF,KAAA,CAIjF,EAAA,UAHD8H,KAAAA,MAAA,GAEApK,KAAA4J,GAAO9M,KAAA,CAAAmR,GAAAA,KAAAA,MAAAA,IAAAA,IAAAC,KAAAA,KAAAA,IAAA3D,IAAAA,GAAAA,EAAAA,KAAAA,IAAAC,IAAAA,KAAAA,KAAAA,KACND,KAAAC,KAAA;AAAA,MAAAtM,GAAAiB,GAAA,CAAA,MAAA,KAAA0B,EAAA,IAAAC,IAAmCyJ;IAAgB;EAAA,GAAoB1B,MAAAA,EAAAA,WAAA,SAAAe,IAAAuE,IAAAA;AAAAA,aAAAA,KAAAA,MAAAA,EAAAA,EAAAA,OAAAA,KAAAA,KAAAA,IAAAA,KAAAA,GAAAA,KAAAA,GAAAA,QAAAA,MAAAA,GAAAA;AAAiC,UAAA7L,KAAAzF,GAAasR,EAAAA,KAAAA,KAAAvE,GAAAA,KAAAA,CAAAA;AAAAA,MAAAA,GAAAA,EAAAmE,IAAAA,GAAAxD,EAAA6D,MAAAA;IAAA;EAAA,GAAAzP,MAAA4C,EAAA8M,SAAA,SAAAC,IAAAA,IAAAA,IAAAA;AAAAA,IAAAA,OAAAA,IAAAA;AAAG,QAAAnP,KAAAA,OAAG;AAAAmP,IAAAA,GAAAnP,EAAA8O,KAAAA,IAAAK,GAAAnP,KAAA,CAAA8O,KAAAA,OAAA;EAAA,GAAAtP,MAAA4C,EAAAiK,SAAA,SAAA8C,IAAAA,IAAAA,IAAAL;AAAAA,IAAAA,OAAA,IAAAjJ;AAAAA,QAAAA,KAAAA,OAAAA;AAC5HsJ,IAAAA,GAAAA,EAAAL,KAAAA,IAAAK,GAAAA,KAAA,CAAAL,KAAAA,OAAA,GAAAK,GAAAA,KAAAA,CAAAA,KAAAA,OAAAA;EACA,GAA4C3P,MAAA4C,EAAAsK,SAAK,SAAAyC,IAAAtJ,IAAA9D,IAAjD;AAAA,YAAAoN,GAAAtJ,OAAA,CAAAsJ,IAAAA,GAAA,KAAAtJ,OAAA,EAAA,KAAA,QAAA,IAAAA,OAAA,KAAA9D,MAAA;EAaC,GACAvC,MAAA4C,EAAAoK,SAAG,SAAA2C,IAAAtJ,IAAc9D,IAAAA;AAAAA,YAAAA,GAAAA,OAAAA,CAAAA,IAAAA,GAAAA,KAAAA,OAAAA,EAAAA,KAAAA,IAAAA,GAAAA,KAAAA,OAAAA,EAAAA,KAAAA,SAAAA,IAAAA,OAAAA,KAAAA,MAAAA;EAAAA,GAAAA,MAEbK,EAAAgN,SAAI,SAAJD,IAAMtJ,IAEV;AAAA,YAAAsJ,GAAAtJ,OAAA,CAAAsJ,IAAAA,GAAA,KAAAtJ,OAAA,EAAA,KAAA,IAAAsJ,GAAA,KAAAtJ,OAAA,EAAA,KAAA,SAAA,IAAAA;EAAAA,GAAAA,MACUwJ,EAAAA,SAAAA,SAAcF,IAAAtJ,IAAAA;AAExB,YAAIsJ,GAAAtJ,OAAA,CAAA,IAAAsJ,GAAA,KAAAtJ,OAAA,EAAA,KAAA,IAAAsJ,GAAA,KAAAtJ,OAAA,EAAA,KAAA,KAAAsJ,GAAA,KAAAtJ,OAAA,EAAA,KAAA,SAAA,IAAAA;EAAAA,GAAAA,MAAAA,EAEJL,KAEA/F,KAAM6P,aAAW5P,KAAAqE,aAChB,EAA4B4K,WAAA,IAAAlP,GAAA,EAAA,GAAA,UAC5B,IAASA,GAAA,EAAwB6N,GAAAA,MAAA,CAAA,IAAA,IAAK,IANnC,GAAA,GAAA,GAAA,GAAA,GAAA,IAAA,GAAA,IAAA,GAAA,IAAA,GAAA,IAAA,GAAA,IAAA,GAAA,EASHzG,GAAAA,KAAG,CAAA,GAAA,GAAA,GAAA,GAAA,GAAO,GAAA,GAAG,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,KAAA,KAAA,KAAA,KAAA,KAAA,KAAA,KAAA,KAAA,GACZ0I,GAAAA,KAAA,CAAA,GAAA,GAAA,GAAA,GAAI,GAAA,GAAD,GAAA,GAAS,GAAA,GAAA,GAAA,GAAA,GAAA,GAAA,GAAA,GAAA,GAAA,GAAA,GAAA,GAAA,GAAA,GAAA,GAAA,GAAA,GAAA,GAAA,GAAA,GAAA,GAAA,GAAA,GAAA,CAAA1B,GAAAA,MAAA,IAVVpO,GAAAA,EAUawH,GAAAA,KAAA,CAAA,GAAA,GAAA,GAAA,GAAA,GAVb,GAWF,GAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,KAAA,KAAA,KAAA,KAAA,KAAA,KAAA,MAAA,MAAA,MAAA,MAAI,MAXF,MAAA,MAAA,OAAA,OAAA,OAAA,OAAA,KAAA,GAWQuI,KAAS,CAAA,GAAA,GAAA,GAAA,GAAA,GAAA,GAAA,GAAA,GAAA,GAAA,GAAA,GAAA,GAAA,GAAA,GAAA,GAAA,GAAA,GAAA,GAAA,GAAA,GAAA,GAAA,GAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,GAAA,CAAA,GAChBC,MAZD/P,IAAAA,GAAAA,EAAAA,GAYQgQ,OAAA,IAAAjQ,GAZR,GAAA,GAYYsJ,QAZZ,CAAA,GAY0BqE,OAAK,IAZ/B3N,GAAAA,EAAAA,GAaFuJ,QAAA,CAAQ+D,GAAAA,MAAoB,IAb1BtN,GAAAA,KAAAA,GAAAA,OAgBF,CAAOiO,GAAAA,OAAU,CAAgBV,GAAAA,MAAA,IAAAvN,GAAA,KACpC+J,GAAAA,OAAA,CAAA,GAAA+D,MAAA,IAAA9N,GAjBK,GAAA,GAiBL0J,OAAA,CAAA,GAAA,OACE,IAAG1J,GAlBA,KAAA,GAkBOqH,MAAA,IAAApH,GAAI,GAAA,GACdwH,MAAA,IAAGxH,GAAA,EAAA,GAAA,MAAA,IAAA/B,GAAA,EArBD0I,GAAAA,MAAAA,IAAAA,GAAAA,IAAAA,GAAAA,MAqBgE,IAAA5G,GAAA,KAApEyG,GAAAA,MAAAzG,IAAAA,GAAAA,KAGG,EAAA,IAAA,WAAA;AAAA,aACA+F,KAAAhG,MAAA4C,EAAAoD,GAEA7H,KAAA,GAAA+D,KAFqC,OAErC/D,MAAA;AACA,UAAAgS,KAAAhS;AACSgS,MAAAA,MAAA,cAATA,MAAAA,cAAAA,MAAAA,cAD4BA,MAAA,aAAAA,QAAW,KAAX,aAAA5Q,OAAW,QAAA,KAAA,YAAAA,OAAA,QAC9B,KAAA,YAAA4Q,OAAA,QAAA,KAAA,WAAAA,OAAA,GAAAnK,GAAAqJ,MAAAlR,EAAAA,KAAAgS,OAAA,KAAAA,MAAA,QAAA;IAAT;AAAA,aAAA,MAAAjS,IAAAgE,IAAA3C,IAC8B;AAAA,aAAA,KAAAiF,OAAM4L,CAAAA,GAAAxM,KAAY,GAAAyM,EAAAA;IAAAA;AAAAA,SAA8ClS,KAAAA,GAAAA,KAAG,IAAAA,KAAE6H,CAAAA,GAAAqI,KAAArI,EAAAA,IAAAA,GAAAA,IAAA7H,EAAAA,KAAA,IAAA6H,GAAA+J,IAAA5R,EAAAA,GAAA6H,GAAAiK,KAAAjK,EAAAA,IAAAA,GAAAA,IAAA7H,EAAAA,KAAA,IAAA6H,GAAAgK,IAAA7R,EAAAA;AAAAA,UAMnG6H,GAAAA,QAAM,KAAA,CAAA,GACNsK,MAAAtK,GAAAA,QAAI,KAAQ,CAAA,GAAA,MAA0CA,GAAAA,QAAA,IAAS,CAAA,GAAA,MACTA,GAAAA,QAAAA,GAAM,CAAA,GAI5DhG,MAAA4C,EAAAqH,UAAAjE,GAAYuD,QAAA,CAAA,GAAAvJ,MAAA,EAGZoN,UAAApH,GAAAuD,QAAAA,GAAmBvD,GAAAkK,KAAAA,GACnBlQ,MAAA4C,EAAAsH,SAAAlE,GAAYuD,QAAA,CAAA,GAGb+G,MAAAtK,GAAAA,QAAAA,IAAAA,CAAAA,GAAYhG,MAAA4C,EAAAqH,UAAAT,GAAAA,QAAAA,CAAAA,GAAAxJ,MAAA4C,EAAAwK,UAAApH,GAAAwD,QAAA,GAAAxD,GAAA4H,KAAAA,GAAA5N,MAAA4C,EAAAsH,SAAAlE,GAAAwD,QAAA,CAAA,GAAA,MAAAtL,GAAA,OAAA,IAAA,CAAA,GAAA,MAAAA,GAAA,OAAA,KAAA,CAAA,GAAA,MAAAA,GAAA,OAAA,IAAA,CAAA,GAAA,MAAAA,GAAA,OAAA,KAAA,CAAA;EAAA,EAzBX;AAAA,EAAA;AAAA,IAAA,OAAA,iBAAA,EAAA,WAAA,MAAA,SAAA,EAAA,GAAA,CAAA,CAAA,CAAA;AAAA,IAAA,OAAA,WAAA;AAAA,MAAAA,KCxkBsD,EAAzDqS,SADa7P,IAAA8C,IAAAA;AAAA,WAAA,KAAA9C,GAAA8C,EAAAA,IAAAA,CAAAA;AAAA,WAAAA;EAC4C,GAAA,YAAA,CAC1CuB,IAAAA,OAASA,GAAAvB,EAAA,KAAA,IAAAuB,GAAAvB,KAAA,CAAEwB,GAAAA,YAAAD,IAAAA,IAAAA,IAAmBA;AAAAA,IAAAA,GAAKvB,EAAKgB,IAAAA,MAAI,IAAA,KAAAO,GAAAvB,KAAA,CAAA,IAAA,MAAAgB;EADF,GACzDS,UAAAA,CAFaF,IAAAvB,OAAAA,WAAAuB,GAAAvB,EAAAA,KAAAuB,GAAAvB,KAAA,CAAA,KAAA,KAAAuB,GAAAvB,KAAA,CAAA,KAAA,IAAAuB,GAAAvB,KAAA,CAGT0B,IAAAA,UAAAH,IAAAA,IAAkBP,IAAGO;AAAAA,IAAAA,GAAGvB,EAAAgB,IAAAA,MAAA,KAAA,KAAAO,GAAAvB,KAAA,CAAA,IAAAgB,MAAA,KAAA,KAAAO,GAAAvB,KAAA,CAAA,IAAAgB,MAAA,IAAA,KAAAO,GAAAvB,KAAA,CAAA,IAAA,MAAAgB;EAF6B,GAE3BgM,UAAAzL,IAAAA,IAAW5C,IAAAA;AAAD,QAAWb,KAAA;AAAA,aAAAnD,KAAA,GAAAA,KAAAgE,IAAAhE,KAAAmD,CAAAA,MAAA6D,OAAAC,aAAAL,GAAAvB,KAAArF,EAAA,CAAA;AAAA,WAAAmD;EAFM,GAED+D,WAAA3E,IAAAA,IAAcY,IAAAA;AAAA,aAAInD,KAAA,GAAAA,KAAAmD,GAAAiB,QAAApE,KAAAuC,CAAAA,GAAA8C,KAAArF,EAAAmD,IAAAA,GAAAqE,WAAAxH,EAAAA;EAFjB,GAEzDsS,UAHa1L,IAAAA,IAAA5C,IAAAA;AAAA,UAAAwK,KAAA,CAAA;AAAA,aAAAxO,KAAA,GAAAA,KAAAgE,IAAAhE,KAAAwO,CAAAA,GAAA/I,KAAAmB,GAAAvB,KAAArF,EAAAA,CAAAA;AAAA,WAAAwO;EAC4C,GAAA,KAGrDnI,CAAAA,OAASA,GAAAA,SAAS,IAAA,IAAAA,EAAAA,KAAAA,IAAAA,SAAUO,IAAAA,IAAA5C,IAAAA;AAAhC,QACImD,IALShE,KAAA;AAKe,aAAAnD,KAAA,GAAWA,KAAAgE,IAADhE,KAAYmD,CAAAA,MAAA,IAAAoP,GAAAC,IAAA5L,GAAAvB,KAAArF,EAAAA,EAAAoH,SAAA,EAAA,CAAA,CAAA;AAAK,QAAAD;AAAAA,MAAAA,KAAKsL,mBAAqBtP,EAAAA;IAA1B,SAA0BpD,IAAAA;AAAA,aAAAwS,GAAAF,UAAAzL,IAAAvB,IAAArB,EAAAA;IAA1B;AAA+B,WAAAmD;EAA8B,EAAA;AAAA,WAAA,YAUvG5E,IAAAmQ,IAAAC,IAAArQ,IACb;AAAA,UAAAsQ,KAAAF,KAAAC,IAAAA,KAAAA,QAAAA,EAAAA,GAGEE,KAAAA,KAAiBC,KAAKJ,KAAAK,KAAA,CAAA,GACCC,KACf,IAAAxQ,WAAI,IAAAoQ,EAAAA,GACVK,KAAAA,IAAQ7M,YAAa4M,GAAAtS,MAAAA,GAAAA,EAAAA,OAAMwS,EAAAA,IAAAA,IAAAA,EAE3BC,OAAAA,EAAAA,IAAa7Q,IACT8Q,IAAAb,GAAAc;AAEuF,QACzF,KADyFH,GACzF;AACF,YAAAI,KAAaV,MAAO;AAClB,UAAM,KAANO,EAAM,UAAM,IAAW,GAAAnT,IAAAsT,IAAAtT,KAAA,EAAAgT,CAAAA,GAAAhT,CAAAA,IAAAuC,GAAAvC,CAAAA,GAAAgT,GAAAhT,IAAA,CAAAuC,IAAAA,GAAAvC,IAAA,CAAAgT,GAAAA,GAAAhT,IAAA,CAAA,IAAAuC,GAAAvC,IAAA,CAAAgT,GAAAA,GAAAhT,IAAA,CAAA,IAAAuC,GAAAvC,IAAA,CACvB;AAAA,UAAM,MAANmT,EAAW,MAASnT,IAAA,GAAAA,IAAAsT,IAAAtT,IAAAgT,CAAAA,GAAAhT,CAAAuC,IAAAA,GAAAvC,KAAA,CAAA;IAAA,WAAc,KAAXkT,GAAe;AAAQ,YAAAK,KAAAjR,GAAMkR,KAAKC;AAAgB,UAAA,QAAAF,IACvE;AAAA,YAAA,KACFJ,EAAAA,MAAcnT,IAAA,GAAA,IAAgB4S,IAAAA,KAAc;AAAA,cAAAc,IAAA,IAAA1T;AAAAiT,UAAAA,GAAAjT,CAAA,IAAA,OAAA,KAAAuC,GAAAmR,IAAA,CAAA,KAAA,KAAAnR,GAAAmR,IAAA,CAAA,KAAA,IAAAnR,GAAAmR,CAAAA;QAAAA;AAAAA,YAEnC,MAATP,EAAS,MAAAnT,IAAA,GAAAA,IAAA4S,IAAA5S,KAAA;AAAA0T,cAAA,IAAA1T;AAAAiT,UAAAA,GAAAjT,CAAA,IAAA,OAAA,KAAAuC,GAAAmR,IAAA,CAAA,KAAA,KAAAnR,GAAAmR,IAAA,CAAA,KAAA,IAAAnR,GAAAmR,CAAAA;QAAAA;MAAAA,OAAAA;AAAiC,YAAAC,IAAAJ,GAAA,CAAA;AAAA,cAAcK,KAAAL,GAAA,CAExDM,GAAAA,KAAAN,GAAI,CAAA;AAAA,YAAgB,KAAA,EACf,MAAAvT,IAAI,GAAIA,IAAJ4S,IAAa5S,KAAA;AAAA,cAAG8T,IAAA9T,KAAU;AAEnC0T,cAAU,IAAA;AAAIT,UAAAA,GAAJjT,CAAAA,IAAAA,OAAiB,KAAAuC,GAAAmR,IAAA,CAAA,KAAA,KAAAnR,GAAAmR,IAAA,CAAA,KAAA,IAAAnR,GAAAmR,CAEvBnR,GAAAA,GAAAmR,CAAAC,KAAAA,KAAkBpR,GAA5BmR,IAAAA,CAA4CE,KAAAA,MAAJrR,GAAemR,IAAA,CAAAG,KAAAA,OAAAb,GAAAc,IAAA,CAAA,IAAA;QAAA;AAAA,YACjB,MAAA,EAAyB,MAAA9T,IAAA,GAAA,IAAc4S,IAAA5S,KAAA;AAC7E8T,cAAA9T,KAAA,GACW0T,IAAA,IAAA1T;AAAAiT,UAAAA,GAAAjT,CAAAA,IAAA,OAAA,KAAAuC,GAAAmR,IAAA,CAAA,KAAA,KAAAnR,GAAAmR,IAAA,CAAA,KAAA,IAAAnR,GAAAmR,CACXN,GAAAA,EAAA7Q,IAAAmR,CAAAA,KAAAC,KAAAP,EAAA7Q,IAAAmR,IAAA,CAAAE,KAAAA,MAAAR,EAAA7Q,IAAAmR,IAAA,CAAAG,KAAAA,OAAAb,GAAAc,IAAA,CAAA,IAAA;QAAA;MAEI;IAAA,WACQ,KAAAZ,GAAA;AACR,YAAA7N,KAAM/C,GAAMkR,KAAKO,MAEjBC,KAAAA,GAAeR,KAAAC,MAAsB5J,KAC5BmK,KAAAA,GAAI5P,SAAJ;AAAA,UAES,KAAlB+O,EACA,UAAWc,IAAA,GAAKA,IAAAtB,IAAAsB,KAGhB;AAAMC,YAAAA,IAAAD,IAAOpB,IAETsB,IAAAF,IAASvB;AACX,aAAM1S,IAAA,GAAQA,IAAA0S,IAAA1S,KAAQ;AAClB8T,cAAAK,IAASnU,KAAA;AAAb,cAAgBoU,IAAA,KAAHlK,IAAA3H,GAAA2R,KAAAlU,KAAA,EAAA,KAAA,MAAA,IAAAA,MAAA,KAAA;AAAgBgT,UAAAA,GAAAc,CAAOzO,IAAAA,GAAO+O,CAAApB,GAAAA,GAAAc,IAAQ,CAAA,IAAAzO,GAAA+O,IAAA,CAAApB,GAAAA,GAAAc,IAAA,CAAA,IAAAzO,GAAA+O,IAAA,CAAApB,GAAAA,GAAAc,IAAA,CAAA,IAAA5J,IAAAL,KAAAmK,GAAA9J,CAAA,IAAA;QAAE;MAJ1C2I;AAI6G,UAAG,KAAHM,EAA9H,MAAAc,IAAA,GAAAA,IAAAtB,IAAAsB,IAAAA,MACUC,IAAAD,IAAApB,IAAAA,IAAkBoB,IAAAvB,IAAgC1S,IAAH,GAAA,IAAa0S,IAAA1S,KAAK;AAA3E8T,YAAAK,IAAAnU,KAAA,GACAoU,IAAe,KADflK,IAAA3H,GAAA2R,KAAAlU,KAAA,EAAA,KAAA,MAAA,IAAAA,MAAA,KAAA;AACegT,QAAAA,GAAYc,CAAAA,IAAAzO,GAAA+O,CAAAA,GAAApB,GAAAc,IAAA,CAAAzO,IAAAA,GAAA+O,IAAA,CAAA,GAAApB,GAAAc,IAAA,CAAAzO,IAAAA,GAAA+O,IAAA,CAAA,GAAApB,GAAAc,IAAA,CAAA,IAAA5J,IAAAL,KAAAmK,GAAA9J,CAAAA,IAAA;MACrB;AAAA,UAAA,KAAA,EAEmD,MAAA+J,IAAA,GAAIA,IAAAtB,IAAKsB,IAAAA,MAAOC,IAAAD,IAAApB,IAAzEsB,IAAAF,IAAAvB,IACY1S,IAAA,GAAAA,IAAS0S,IAAA1S,KAAA;AAAA,YAASmU,IAAAnU,KAAQ,GAAoBoU,IAAA,KAAPlK,IAAA3H,GAAK2R,KAAAlU,KAAA,EAAA,KAAA,MAAA,IAAAA,MAAA,KAAA;AAAMgT,QAAAA,GAAKc,CAAAA,IAAIzO,GAAA+O,CAAAA,GAAApB,GAAAc,IAAA,CAAAzO,IAAAA,GAAA+O,IAAA,CAAA,GAAApB,GAAAc,IAAA,CAAAzO,IAAAA,GAAA+O,IAAA,CAAA,GAAApB,GAAAc,IAAA,CAAA,IAAA5J,IAAAL,KAAAmK,GAAA9J,CAAAA,IAAA;MAAG;AAAA,UAElE,KAAA,EAAA,MAAgBlK,IAAA,GAAWA,IAAA4S,IAAG5S,KAAA;AAAA,YACpBkK;AAAAA,YADwBlK,KAC3B,GACPoU,IAAA,KADUlK,IAAA3H,GAAAvC,CACNgT;AAAAA,QAAAA,GAAAc,CAAAA,IAAYzO,GAAA+O,CAAAA,GAAApB,GAAAc,IAAA,CAAAzO,IAAAA,GAAA+O,IAAA,CAAA,GAAApB,GAAAc,IAAA,CAAAzO,IAAAA,GAAA+O,IAAA,CAAA,GAAApB,GAAAc,IAAA,CAAA5J,IAAAA,IAAAL,KAAAmK,GAAA9J,CAAA,IAAA;MAAA;IAAA,WAEK,KAAAgJ,GAAA;AACH,UAAA,KAAAC,EACd,MAAAnT,IAAA,GAAAA,IAAI4S,IAAA5S,KAAA;AAAA,YAAA,KAAA;AAAA,YAChBqU,IAAA9R,GAD+E+R,IAAKtU,KAAK,CACzFgT;AAAAA,QAAAA,GAAAc,CAAAO,IAAAA,GAAArB,GAAAc,IAAA,CAAAO,IAAAA,GAAArB,GAAAc,IAAA,CAAAO,IAAAA,GAAArB,GAAAc,IAAA,CAAA,IAAAvR,GAAA+R,IAAA,CACA;MAAA;AAAA,UAAA,MAAA,EAGY,MAAAtU,IAAA,GAAAA,IAAI4S,IAAK5S,KAAK;AAAG,YACfsU;AADeR,YAAA9T,KAAA,GACHqU,IAAA9R,GAAZ+R,IAAKtU,KAAI,CAAA;AAAGgT,QAAAA,GAAAc,CAAAA,IAAWO,GAAArB,GAADc,IAAe,CAAA,IAAKO,GAALrB,GAAAc,IAAf,CAAsCO,IAAAA,GAAKrB,GAAKc,IAAV,CAAqBvR,IAAAA,GAAK+R,IAAA,CAAA;MACxF;IAAA,WACZ,KAAApB,EAEA,MADAS,IAAArR,GAAAkR,KAAAC,OAAAnR,GAAAkR,KAAAC,OAAAA,IACAQ,IAAA,GAAAA,IAAAtB,IAAAsB,KAAA;AACA,YAAAzP,KAAeyP,IAAApB,IACT0B,KAAAN,IAAMvB;AACN,UAAM,KAANS,EACA,UAAWnB,IAAA,GAAKA,IAAAU,IAAYV,KAAA;AAE5B,YAAA,KAAIqC,IAAA,OAAY9R,GAAAiC,MAAAwN,MAAA,EAAA,MAAA,KAAA,IAAAA,KAAA,OACE,MAAP2B,IAAc,IAAK;AAAAV,QAAAA,GAAAsB,KAAAvC,CAAAA,IAAAwC,KAAA,KAAAH,KAAA,KAAAA,KAAA,IAAAA;MAC1B;eACE,KADgBlB,EAAAA,MAAAA,IAEb,GAAAnB,IAAIU,IAAIV,KAAG;AACUwC,aAAAA,IAAAA,MAApBjS,GAAMiC,MAADwN,MAAY,EAAA,MAAA,MAAA,IAAAA,MAAA,KAAA,OAAO,KAAJ2B,IAAe,IAAA;AAAMV,QAAAA,GAAXsB,KAAwBvC,CAAMwC,IAAAA,KAAD,KAA9BH,KAAgD,KAAAA,KAAA,IAAAA;MAAI;eACvE,KAAAlB,EAAgB,MAAAnB,IAAA,GAAAA,IAAGU,IAAHV,KAAa;AAAmCwC,aAAxBH,IAAA,MAAG9R,GAAHiC,MAAewN,MAAK,EAAA,MAAA,MAAA,IAAAA,MAAA,KAAA,QAAO,KAAH2B,IAAc,IAAI;AAAMV,QAAAA,GAAQsB,KAAAvC,CAAAA,IAAAwC,KAAA,KAAAH,KAAA,KAAAA,KAAA,IAAAA;MAC1H;eACA,KAAAlB,EACA,MAAAnB,IAAA,GAAAA,IAAAU,IAAAV,KAAA;AAAA,aACUqC,IAAA9R,GAAYiC,KAAAwN,CAAAA,MACL2B,IAAO,IAAO;AAAKV,QAAAA,GAAAsB,KAAAvC,CAAAA,IAAAwC,KAAA,KAAAH,KAAA,KAAAA,KAAA,IAAAA;MAC1B;eACE,MADgBlB,EAAAA,MAAAA,IAEb,GAAAnB,IAAIU,IAAIV,KAAG;AAAA,YAAAjO,GACdS,MAAUwN,KAAK,EAAA,GAASwC,IAAApB,EAAA7Q,IAAIiC,MAAMwN,KAAK,EAAW2B,KAAAA,IAAhB,IAAwB;AAAMV,QAAAA,GAADsB,KAA9BvC,CAAgDwC,IAAAA,KAAA,KAAAH,KAAA,KAAAA,KAAA,IAAAA;MAAI;IACvD;AAC1C,WAAArB;EACA;AAAA,WAAA,YAsFiD1Q,IAAAmS,IAAA/B,IAAAC,IAAAA;AAEzC,UAAAI,KAAA2B,QAAYpS,EAAAA,GAAKuQ,KAAMvO,KAAAwO,KAAAJ,KAAAK,KAAA,CAAA,GACvBnM,KAAA,IAAApE,YAAYqQ,KAAAA,IAAWvQ,GAAAqS,aAAShC,EAAAA;AAIxC,WAHkB8B,KAAVnS,GAAAkR,KAAAoB,OAAU3Q,GAAAwQ,IAAA7N,EAAAA,IAAAA,SACN6N,IAAA7N,EAAAA,GACK,KAATtE,GAAAqS,YAAAF,KAAiBI,YAAAJ,IAAAnS,IAAA,GAAAoQ,IAAAC,EACR,IAAA,KAAjBrQ,GAAAqS,cAAiCF,KAAAA,SAAAA,eA+DdlS,IAASD,IACpB;AAAA,YAAAoQ,KAAApQ,GAAAwS,OAAAA,KAAAA,GAAAA,QAAkD/B,KAAA2B,QAAIpS,EAAAA,GAAAA,KAAgByQ,MAAU,GACxFF,KAAAvO,KAAAwO,KAAiBJ,KAAAK,KAAgB,CACzBgC,GAAAA,KAAA,IAAIvS,WAAAA,KAAaqQ,EAAAA;AAAG,UAAAyB,KAAA;AAAA,YAAA/S,KAAA,CAAA,GACc,GAAA,GAAT,GAAiB,GAAI,GAAM,CAAA,GAC/CyT,IAAA,CAAI,GAAA,GAAA,GAAA,GAAA,GAAa,GAAA,CAAA,GAAA,IAAG,CAAA,GAAA,GAAA,GAAA,GAAkB,GAAI,GAAA,CAEvDC,GAAAA,IAAiB,CAAA,GAAA,GAAQ,GAAA,GAAA,GAAA,GAAA,CAAA;AAAA,UAAA,IAAA;AAAA,aAAA,IACM,KAAA;AACpB,cAAAC,IAAAC,EAAoBC,CAAAA,GACvB9N,IAAA2N,EAAIG,CAAAA;AAAAA,YAAAA,IAAAA,GACCC,IAAA,GAAA,IAAA9T,GAAsC6T,CAAA;AAAA,eAAsBE,IAAA3C,KAAe2C,MAAAJ,GAAUG;AACrF,YAAAE,IAAAP,EAAII,CAAAA;AAAA,eAAaG,IAAA7C,KAAA6C,MAAAjO,GAAAkO;AAAAA,cAAAA,IAAAA,KAAO1C,KAAK0C,IAATzC,KAAAA,CACjC8B;AAAAA,oBAAAtS,IAAiBD,IAAAgS,IAAgBkB,GAAAH,CAAAA;AAEjC,YAAApB,IAAA,GAAA,IAEMwB,GAAUL,CAAAA;AACV,eAAAM,IAAY/C,MAAA;AAAA,cAA4BgD,KAAAX,EAAUI,CACxDQ,GAAAA,KAAAtB,KAAAL,IAAA4B,KAAA;AAEM,iBAAAF,KAASjD,MAAA;AAEf,gBAAA;AAAA,gBADM,KAAAK,GACN5B,MAAAA,IAAA5O,GAAAqT,MAAA,CAAA,MAAA,KAAA,IAAAA,MAAA,GACQb,GAAJW,IAAW7C,MAAA8C,MAAiB,EAAA,KAAQxE,KAAAA,MAAW,IAAAwE,OAAA;AAE1B,gBAAW,KAAX5C,GAAiC5B,MAAAA,IAAA5O,GAAAqT,MAAA,CAAA,MAAA,KAAA,IAAAA,MAAA,GAC/Cb,GAAAW,IAAA7C,MAAA8C,MAAA,EAAAxE,KAAAA,KAAA,MAAA,IAAAwE,OAAA;AAAA,gBAAA,KAAAtT,GAG2B8O,MAAAA,IAAAA,GAAVyE,MAAI,CAAM,MAAA,KAAA,IAAAA,MAAA,IAC9Bb,GAAAW,IAAO7C,MAAA8C,MAAA,EAAA,KAAAxE,KAAA,MAAA,IAAAwE,OAAA;AACe,gBAAM5C,MAAM,GAAA;AAAwB,oBACrDzK,KAAAoN,IAAAA,KAAAC,KAAgBG;AACrB,uBAAS5L,KAAA,GAAAA,KAAA4L,IAAA5L,KAAA6K,CAAAA,GAAAzM,KAAA4B,EAAAA,IAAA3H,IAAAqT,MAAA,KAAA1L,EAAAA;YAAM;AAAA,YAAAlK,MAAAqC,IAAA0B,MAAA;UACd;AAILkQ,eAAIyB,KAAOR;QACX;AAAA,YAAwBG,KAAK,MAAAf,MAAgBe,KAAA,IAAA,KACxCD,KAAA;MAAwB;AAAA,aAEtBL;IACX,EA3GiCN,IAAAnS,EAAAA,IAAjCmS;EAAAA;AAAAA,WAAAA,SAEQlS,IAAQqE,IAAAA;AAAA,WAAA3C,GAAA,IAAAzB,WAAAD,GAAA7B,QAAA,GAAA6B,GAAA6B,SAAA,CAAA,GAAAwC,EAAAA;EAAAA;AAER,MAAA3C,KAAI,WAAe;AAAA,UAAA8R,KAAA,EAAAA,GAAA,CAAA,EAAA;AAwD3B,WAxD2BA,GAAAA,EAAYC,IAAI,SAAMA,IAAxBC,IAAiC;AAAA,YAAAC,KAAA1T;AAAA,UAA8B+L,IAAA4H,IAAAA,KAAf,GAAMC,KAAA,GAASC,KAAA,GAAA1D,KAAA,GAAA2D,IAAA,GAAAC,IAAA,GAAAC,IAAA,GAAA9D,IAAA,GAAA9E,IAAA;AAAA,UAAA,KAAA7J,GAAA,CAAA,KAAA,KAAAA,GAAA,CAAA,EAAA,QAAA3C,MAAA,IAAApB,GAAA,CAAA;AAAA,YAAA,IAAAD,GAAA,GAAA,IAAA,EAAA,GAAA,IAAA,EAAA,GAAA,IAAA,EAAA,GAAA,IAAA,EAAA,GAAA,IAAA,EAAA,GAAA,IAAA,EAAA,GAAA,IAAA,EAAA,GAAA,IAAA,QAAAqB;AACvD,WAAzBqV,MAAAR,KAAA,IAAAC,GAAAA,GAAAA,WAAK,KAAe,CAAA,IAAK,KAAAlW,KACA,KAAjCA,KAAAqG,EAAA2P,IAAApI,GAAiB,CAAAwI,GAAAA,KAAA/P,EAAA2P,IAAgBpI,IAAA,GAAA,CAAAA,GAAAA,KAAA,GAAA,KAAAwI,IAAA;AAG8D,YAAA,MAAAhV,KAAP2U,GAAAA,EAAAE,EAAAA,IAAOvD,KAAA,KAAA,GAAA,IAAA,KAAA0D,OAAA7H,KAAA7H,EAAA2P,GAAAF,KAAAzP,EAAAiM,GAAA4D,IAAAA,KAAAC,IAAAA,KAAA,KAAAJ,IAAA;AAC/FC,UAAAA,KAAAK,EAAAV,IAAApI,GAAA,CAAA,IAAA,KACQ+E,KAAA+D,EAAAV,IAAApI,IAAY,GAAA,CAAA,IAAA,GAAA0I,IAAAI,EAAAV,IAAApI,IAAA,IAAA,CAAA,IAAA,GAAAA,KAAA;AAAA,cAAA1D,KAAA;AAAA,mBAAA3I,IAAA,GAAAA,IAAA,IAAAA,KAAA,EAAAmF,GAAA4P,EAAA/U,CAAAA,IAAA,GAAAmF,EAAA4P,EAAA/U,IAAA,CAAA,IAAA;AAAA,eAAAA,IAAA,GAAA,IAAA,GAAA,KACP;AAAA,kBAAUoV,KAAAD,EAAAV,IAAApI,IADH,IAAArM,GAAA,CAAA;AAAAmF,cAAA4P,EAAA,KAAA5P,EAAA6P,EAAAhV,CAAAA,KAAA,EAAAoV,IAAAA,IAAAA,KAAAzM,OAAAA,KAAAyM;UAAAA;AAAA/I,eAAA,IAAA0I,GAAAM,EAAAlQ,EAAA4P,GAAApM,EAAA2M,GAAAA,EAAAnQ,EAAA4P,GAAApM,IAAAxD,EAAA8P,CAAAjI,GAAAA,KAAA7H,EAAAgM,GAAAyD,KAAAzP,EAAAkH,GAAAA,IAAAA,EAAAA,EAAAA,IACoB,KAAI1D,MAAM,GAAAmM,KAAA1D,IAASqD,IADvCpI,GAAAlH,EAAA6H,CAAA;AAAA,gBAAAnN,KAAA0V,EAAAA,EAAApQ,EAAA6H,GAAA,GAAA8H,IAAA3P,EAAAyP,CAAAI;AAAAA,eAAA,KAAAnV,MAAA;AAAA,gBAAA2V,KAAAD,EAAAA,EAAApQ,EAAA6H,GAAA8H,IAAA1D,IAAAjM,EAAAsQ,CAAAA;AAAAR,eAAA,KAAAO,MAAA,GAAAH,EAAAlQ,EAAAyP,GAAA/U,EAAAA,GAAAA,EAAAA,EAAAA,GAAAA,IAC4CmN,EAAAA,GAAOqI,EAAAlQ,EAAAsQ,GAAID,EAAAA,GAAMF,EAAAnQ,EAAAsQ,GAAAD,IAAAZ,EAAAA;QAAa;AAAA,mBAAA;AAAA,gBAAApW,KAAAsC,GAAA,EAAA0B,IAAA,CAAA,IAAA,CAAA;AAAA,eAAA,KAAAhE;AAAA,gBAAAC,KAAAD,OAAA;AAAA,cAAAC,OAAA,KAAA,EAAA,CAAAoB,GAAA,GAAA,IAAApB;eAAA;AAAA,gBAAA,OAAAA,GAAA;AAAA;AAAA,kBAEtFiX,KAAIvE,IAAAA,KAAM;AAAA,kBAAwBrN,KAAA,KAAM;AAAU,sBAAA6R,KAAAxQ,EAAAyQ,EAAS9R,KAAnB,GAAA;AAA0B4R,gBAAAA,KAAOvE,KAAUwE,OAAA,KAAAR,EAAAV,IAAApI,GAAA,IAAAsJ,EAAAtJ,GAAAA,KAAA,IAAAsJ;cAAAA;AAAA,oBAAAE,KAAAjB,GAAApW,EAAAiW,IAAApI,CAAAA,IAAA4I,CAAA5I;AAAAA,mBAAA,KAAAwJ;AAAA,oBAAAjU,KAAAiU,OAAA,GAAAC,KAAA3Q,EAAAnF,EAAA4B,EAAAA,GAAAqC,MAAA6R,OAAA,KAAAhR,EAAA2P,IAAApI,GAAA,KAAAyJ,EAAAA;AACvE,mBAAZzJ,KAAA,KAAAyJ,IAAY3E,IAAAuE,KAAAhB,CAAAA,GAAAvD,CAAAuD,IAAAA,GAAAvD,MAAAlN,EAAAA,GAAAyQ,GAAAvD,CAAAA,IAAAuD,GAAAvD,MAAAlN,EAAAyQ,GAAAA,GAAAvD,CAAAuD,IAAAA,GAAAvD,MAAAlN,EAAAA,GAAAyQ,GAAAvD,CAAAuD,IAAAA,GAAAvD,MAAAlN,EAAAA;AAAAkN,kBAAAuE;YACV;UAAA;QAAA;MAAA,OAVuB;AACrB,cAAJ,IAAArJ,OAAIA,KAAW,KAAA,IAAAA;AACb,cAAAoJ,KAAS,KAATpJ,MAAI,IAAgBuJ,KAAAnB,GAAAgB,KAAW,CAAXhB,IAAAA,GAAAgB,KAAoB,CAAA,KAAA;AAAAP,cAAAR,KAAAF,GAAAA,EAAAE,EAAAA,IAAAvD,IAAAyE,EAAAlB,IAAAA,GAAAxJ,IAAA,IAAAyJ,GAAAF,GAAAtV,QAAAsV,GAAA7R,aAAA6S,IAAAG,EAAAA,GAAAzE,CAAA9E,GAAAA,IAAAoJ,KAAAG,MAAA,GAAA,KAAA3R;MAAA;AAQE,aAAAyQ,GAAA7R,UAAAsO,IAAAA,KAAcuD,GAAArW,MAAS,GADvD8S,CAAAA;IAC4D,GAAA3S,GAAA,EAAA,IAAA,SAAAA,IAAAgE,IAAA;AAAA,YAAA3C,KAAArB,GAAA;AAAA,UAAAgE,MAAA3C,GAAA,QAAArB;AAAA,YAAAC,KAAA,IAAA,WAAAoB,MAAA,CAAA;AAAA,aAAApB,GAAA,IAAAD,IAAA,CAAA,GAAAC;IAAA,GAGxE+V,GAAAA,EAAAG,IAAA,SAAIF,IAAOC,IAAAC,IAAKY,IAAAzQ,IAAAqQ,IAAAA;AACxB,YAAA1S,KAAA+R,GAAAA,EAAiBhW,GAAA6W,KAAgBb,GAAAA,EAAAU;AAAA,UAAAI,KAAA;AAAA,aAAAA,KAAAX,MAAA;AACzB,cAAAnW,KAAAiW,GAAAY,GAAKE,IAAIzQ,EAAAA,IAAI4P,EAAG5P;AAAAA,QAAAA,MAAAA,KAAItG;AAAS,cAAA2G,KAAA3G,OAAA;AAAK,YAAA2G,MAAA,GAAGgQ,CAAAA,GAAAG,EAAAA,IAAOnQ,IAAVmQ;aAAAA;AAClC,cAAAJ,KAAA,GAAQL,KAAA;AAAM,gBAAA1P,MAAA0P,KAAA,IAAApS,GAAA8S,IAAAzQ,IAAA,CAAA,GAAAA,MAAA,GAAAoQ,KAAAC,GAAAG,KAAA,CAAA,KAAA,MAAAnQ,MACtB0P,KAAA,IAAiBpS,GAAA8S,IAAAzQ,IAAA,CAAA,GAAAb,MACT,KAAmF,MAALkB,OAAK0P,KAAS,KAAApS,GAAA8S,IAAAzQ,IAAA,CAAAA,GAAAA,MAAA;AAAA,gBAAAgQ,KAAAQ,KAAAT;AAAA,iBAAAS,KAAAR,KAAAK,CAAAA,GAAAG,EAAAJ,IAAAA,IAAAI;QACpG;MACQ;AAAA,aAAAxQ;IAAAA,GAAAA,GAAAA,EAAAA,IAAAA,SACS2P,IAAIC,IAAAA,IAAOa,IAAQ;AAAA,UAAAzQ,KAAA,GAAAqQ,KAAA;AAAA,YAAA1S,KAAe8S,GAAK1S,WAAAA;AACxD,aAAAsS,KAAAR,MAAiB;AAAA,cAAAU,KAAQZ,GAAAU,KAART,EAAAA;AAAkBa,QAAAA,GAAAJ,MAAQ,CAAQ,IAAA,GAAAI,GAAA,KAAAJ,MAAA,EAAAE,IAAAA,IAAAA,KAAAvQ,OAAAA,KAAAuQ,KAAAF;MAAnD;AAAmD,aAAAA,KAAA1S,KAAA8S,CAAAA,GAAAJ,MAAA,CAAA,IAAA,GAAAI,GAAA,KAAAJ,MAAA,EAAA,IAAA,GAAAA;AAAA,aAAArQ;IAC3C,GAA4B0P,GAAAA,EAAA1P,IAAA,SAAA2P,IAAAC,IAAAA;AAC5B,YAAAC,KAAAH,GAAAA,EAAIK,GAASU,KAAAd,GAAA5R;AAAe,UAAAiC,IAAAqQ,IAAA1S;AAAA,UAAA6S;AAAA,YAAA9W,KAAAmW,GAAAhM;AAAA,eAAA0M,IAAA,GAAAA,KAAAX,IAAAW,IAAA7W,CAAAA,GAAA6W,CAAA,IAAA;AAAA,WAAAA,IAAA,GAAAA,IAAAE,IAAAF,KAAA,EAAA7W,CAAAA,GAAAiW,GAAAY,CAAAA,CAAAA;AAAAA,YAAAA,IAC5BV,GAAAS;AAAe,WAAJtQ,KAAA,GAAAtG,GAAA,CAAI,IAAA,GAAU2W,KAAM,GAAQA,MAAAT,IAAAS,KAAKrQ,CAAAA,KAAAA,KAAAtG,GAAA2W,KAAA,CAAA,KAAA,GAAAhQ,EAAAgQ,EAAAA,IAAArQ;AAAA,WAAArC,KAAA,GAAAA,KAAA8S,IAAA9S,MAAA,EAC5C6S,CAAAA,KAAAb,GAAAhS,KAAA,CAAA,GAAI,KAAA6S,OAAMb,GAAAhS,EAAAA,IAAA0C,EAAAmQ,EAAAA,GAAAA,EAAAA,EAAAA;IAAAA,GAAAA,GAAAA,EAAAA,IAAAA,SAAAA,IAAAA,IAIEX,IAAAA;AAAAA,YAAAA,KAAAA,GAAAA,QACkBQ,KAAAA,GAAAA,EAARN,EAAYhV;AAAA,eAAA4C,KAAA,GAAAA,KAAA8S,IAAA9S,MAAA,EAC1C,KAAA,KAAAgS,GAAAhS,KAAA,CAAA,GAAA;AAAA,cACQ4S,KAAI5S,MAAK,GAAT6S,KAAAb,GAAuBhS,KAAA,CAAA,GAAAjE,KAAA6W,MAAA,IAAAC,IAAAnQ,KAAAuP,KAAAY;AAAA,YAAAJ,KAAAT,GAAAhS,EAAA0C,KAAAA;AAAA,cAAA0P,IAAAK,MAAA,KAAA/P;AAC/B,eAAA+P,MAAiBL,KAAA;AAAgBF,UAAAA,GAAAQ,GAAAD,EAAAA,MAAA,KAAAR,EAAAA,IAAAlW,IAAA0W;QAAjC;MACQ;IACA,GAAAV,GAAAA,EAAA/R,IAAA,SAASgS,IAAAC,IAAAA;AAAG,YAAAC,KAAAH,GAAAA,EACVK,EAAMhV,GAAA0V,KAAA,KAAAb;AAAA,eAAA5P,KAAA,GAAAA,KAAA2P,GAAA5R,QACRiC,MAAA,GAAA;AAAA,cAAKqQ,KAAIV,GAAA3P,EAAAA,KAAe4P,KAAAD,GAAA3P,KAAA,CAAA2P;AAAAA,QAAAA,GAAA3P,EAAA6P,IAAAA,GAAAQ,EAAAI,MAAAA;MAAxB;IAAA,GAAA/W,GAAA,EAAA,IAAA,SACWiW,IAAAA,IAAIE,IAAAA;AAAAA,MAAAA,OAAgB,IAAND;AAAW,YAAKa,KAAAb,OAAA;AAAAD,MAAAA,GAAAc,EAAAA,KAAAZ,IAAAF,GAAAc,KAAA,CAAA,KAAAZ,OAAA;IAAA,GAAMH,GAAAA,EAAAc,IAAA,SAAMb,IAAAA,IAAKE,IAAAA;AAAAA,MAAAA,OAAA,IAAAD;AAAA,YAAAa,KAAAb,OAAA;AAAAD,MAAAA,GAAAc,EAAAZ,KAAAA,IAAAF,GAAAc,KAAA,CAAAZ,KAAAA,OAAA,GAAAF,GAAAc,KAAA,CAAA,KAAAZ,OAAA;IAAX,GAAWH,GAAAA,EAAAhW,IAAA,SAAAiW,IAAAA,IAAAE,IAAA;AAAA,cAAAF,GAAAC,OAAA,CAAAD,IAAAA,GAAA,KAAAC,OAAA,EAAA,KAAA,QAAA,IAAAA,OAAA,KAAAC,MAAA;IAAX,GAAAnW,GAAA,EAAA,IAAA,SACzCiW,IAAAA,IAAQE,IAAA;AAAA,cAAKF,GAAAC,OAAA,CAAA,IAAAD,GAAA,KAAAC,OAAA,EAAA,KAAA,IAAAD,GAAA,KAAAC,OAAA,EAAA,KAAA,SAAA,IAAAA,OAAA,KAAAC,MAAA;IAAA,GAAAH,GAAAA,EAAAU,IAAA,SAAAT,IAAAC,IAAA;AAAA,cAAAD,GAAAC,OAAA,CAAAD,IAAAA,GAAA,KAAAC,OAAA,EAAA,KAAA,IAAAD,GAAA,KAAAC,OAAA,EAAA,KAAA,SAAA,IAAAA;IAAAA,GAAMF,GAAAA,EAAA/V,IAAA,SACvBgW,IAAQC,IAAA;AAAA,cAAKD,GAAAC,OAAM,CAAAD,IAAAA,GAAA,KAAAC,OAAA,EAAA,KAAA,IAAAD,GAAA,KAAAC,OAAA,EAAA,KAAA,KAAAD,GAAA,KAAAC,OAAA,EAAA,KAAA,SAAA,IAAAA;IADI,GACJF,GAAAA,EAAAK,IAAA,WAAA;AAAA,YAAArW,KAAI4R,aAAOsE,KAAA7P;AAChC,aAAA,EAAA,GAAA,IAAArG,GACA,EAAA,GAAMmK,GAAO,IAAA8L,GAAA,EAAA,GAAIO,GAAA,CAAA,IAAU,IAAA,IAAW,GAAA,GAAA,GAAK,GAAA,GAAA,IAAA,GAAA,IAAA,GAAA,IAAA,GAAA,IAAA,GAAA,IAAA,GAAA,EAAA,GAAAQ,GAAA,CAAA,GAAA,GAAA,GAAA,GAAA,GAAA,GAAA,GAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,KAAA,KAAA,KAAA,KAAA,KAAA,KAAA,KAAA,KAAA,GAAA,GAAAvM,GAAA,CAAA,GAAA,GAAA,GAAA,GAAA,GAAA,GAAA,GAAA,GAAA,GAAA,GAAA,GAAA,GAAA,GAAA,GAAA,GAAA,GAAA,GAAA,GAAA,GAAA,GAAA,GAAA,GAAA,GAAA,GAAA,GAAA,GAAA,GAAA,GAAA,GAAA,GAAA,GAAA,CAAA2M,GAAAA,GAAA,IAAAnB,GAAA,EAAA3Q,GAAAA,GAAA,CAAA,GAAA,GAAA,GAAA,GAAA,GAAA,GAAA,GAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,KAAA,KAAA,KAAA,KAAA,KAAA,KAAA,MAAA,MAAA,MAAA,MAAA,MAAA,MAAA,MAAA,OAAA,OAAA,OAAA,OAAA,KAAA,GAAA4R,GAAA,CAAA,GAAA,GAAA,GAAA,GAAA,GAAA,GAAA,GAAA,GAAA,GAAA,GAAA,GAAA,GAAA,GAAA,GAAA,GAAA,GAAA,GAAA,GAAA,GAAA,GAAA,GAAA,GAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,GAAA,CAAA1V,GAAAA,GAAA,IAAA0U,GAAA,EAAAI,GAAAA,GAAA,IAAAL,GAAA,GAAAkB,GAAAA,GAAA,CAAA,GAAAvE,GAAA,IAAAqD,GAAA,EAAA,GAAAoB,GAAA,CAAA,GAAA1E,GAAA,IAAAsD,GAAA,KAAA,GAAAG,GAAA,CAAA,GAAA5H,GAAA,CAAA,GAAAX,GAAA,IAAAoI,GAAA,KAAAgB,GAAAA,GAAA,CAAAR,GAAAA,GAAA,IAAAR,GAAA,GAAAM,GAAAA,GAAA,CAAAlV,GAAAA,GAAA,IAAA4U,GAAA,KAAA7S,GAAAA,GAAA,IAAA8S,GAAA,GAAAoB,GAAAA,GAAA,IAAApB,GAAA,EAAAzQ,GAAAA,GAAA,IAAAyQ,GAAA,EAAAlS,GAAAA,GAAA,IAAAkS,GAAA,IAAA,GAAA3P,GAAA,IAAA0P,GAAA,KAAA,GAAAsB,GAAA,IAAAtB,GAAA,KAAA,EAAA;IAAA,EAFtB,GAAA,WAI4B;AAAA,YAAAA,KAAAD,GAAAA,EAAAK;AAAW,eAAAF,KAAA,GAAAA,KAAA,OAAAA,MAAA;AAC5D,YAAAY,KAAAZ;AAAAY,QAAAA,MAAI,aAAAA,QAAA,KAAA,aAAAA,OAAA,GAAA/W,MAAM,aAAAA,QACI,KAAM,YAAA+W,OAAA,GAAAA,MAAA,aAAAA,QAAA,KAAA,YAAAA,OAAA,GAAAA,MAAA,aAAAA,QAAA,KAAA,WAAAA,OAAA,GAAA/S,GAAA,EAAA3C,EAAA,KAChB0V,OAAA,KAASA,MAAA,QAAA;MAAA;AAAA,eAAA,EAAA/W,IAAGiE,IAAO4S,IAAAA;AAAA,eAAA,KAAA5S,OAAmB0S,CAAAA,GAAKjR,KAAA,GAAAmR,EAAAA;MAAAA;AAAA,WAAAV,KAAA,GAAAA,KAAA,IAAAA,KAC1CF,CAAAA,GAAAmB,EAAAjB,EAAAA,IAAAF,GAAAe,EAAAb,EAAA,KAAA,IAAAF,GAAAxL,EAAA0L,EAAAA,GACHF,GAAAzU,EAAA2U,EAAAF,IAAAA,GAAA3Q,EAAA6Q,EAAAA,KAAI,IAAMF,GAAAiB,EAAAf,EAAAA;AAAAA,QAAAA,GAAAA,GAAAA,KAAAA,CACV7P,GAAAA,EAAA2P,GAAOkB,GAAA,KAAa,CAAA,GAAK7Q,EAAG2P,GAAAkB,GAAI,IAAA,CAAA7Q,GAAAA,EAAA2P,GAAAkB,GAAA,GAAA,CAAAnB,GAAAA,GAAAA,EAAA1P,EAAA2P,GAAAkB,GAAA,CAC1CnB,GAAAA,GAAAA,EAAAW,EAAAV,GAAAkB,GAAA,GAAAlB,GAAAK,CAAAA,GAAAN,GAAAA,EAAA/R,EAAAgS,GAAAkB,GAAA,CAAA,GAAA7Q,EAAA2P,GAAAoB,GAAA,IAAA,CAAArB,GAAAA,GAAAA,EAAA1P,EAAA2P,GAAAoB,GAAA,CAAA,GAAArB,GAAAA,EAAAW,EAAAV,GAAAoB,GAAA,GAAApB,GAAArD,CAAAA,GAAAoD,GAAAA,EAAA/R,EAAAgS,GAAAoB,GAAA,CAAA,GAAA/Q,EAAA2P,GAAAM,GAAA,IAAA,CAAAjQ,GAAAA,EAAA2P,GAAAG,GAAA,KAAA,CAAA,GAAA,EAAApS,GAAA,GAAA,IACQ,CAAA,GAAA,EAAAA,GAASwK,GAAA,KAAT,CAAA;IAAA,EACRwH,GAAAA,GAAaA,EAAIC;EAAAA,EAxDL;AAAA,WAyGDtB,QAAApS,IAAAA;AAAAA,WAAmC,CAAA,GAAA,MAAA,GAAA,GAAW,GAAI,MAAA,CAAA,EAAAvC,GAAWmT,KAAgD5Q,IAAAA,GAAA6Q;EAAxH;AAAA,WAAA,YAEgC5Q,IAAAD,IAAAkC,IAAAA,IAAAmO,IAAAA;AAC5B,QAAAI,KAAM2B,QAAIpS,EAAAA;AAAA,UAAIuQ,KAAAvO,KAAMwO,KAAAJ,KAAAK,KAAA,CACI;AAAA,QAAA/S,IAAAsU;AAAAA,IAAAA,KADEhQ,KAAFwO,KAAQC,KAAU,CACV;AAAA,QAAAjT,KAAAyC,GAAAiC,EAAGwN,GAAAA,IAAA;AAAiC,QAAXlS,KAAA,MAAQyC,GAAAiC,EAAA,IAAA,CAAA,GAAA,GAAA,CAAA1E,EAAAA,KAAA,CAAO,IAAA,KAAJA,GAAQ,MAAAkS,IAAAe,IAAAf,IAAAa,IAAAb,IAAAzP,CAAAA,GAAAyP,IAAA,CAAAzP,IAAAA,GAAAyP,IAAA,CAAA,KAAAzP,GAAAyP,IAAA,IAAAe,EAAAA,MAAA,KAAA;AAAc,aAAAkB,KAAQ,GAAAA,KAAAtB,IAAAsB,KAAgC,KAA7BjU,KAAAwE,KAAIyP,KAAIpB,IAAAyB,KAAAtU,KAAAiU,KAAA,GAAGnU,KAAAyC,GAAQ+R,KAAA,CAAAtC,GAAAA,IAAA,GAAc,KAAJlS,GAAI,QAAAkS,IAAAa,IAAAb,IAAAzP,CAAAA,GAAAvC,KAAAgS,CAAAzP,IAAAA,GAAA+R,KAAAtC,CAAAA;aAChI,KAAJlS,IAAI;AAAwB,aAAAkS,IAAAe,IAAOf,IAAKzP,CAAAA,GAAMvC,KAAAgS,CAAAA,IAAAzP,GAAA+R,KAAAtC,CAAAA;AAAAA,aAAIA,IAAUa,IAAEb,IAAAzP,CAAAA,GAAAvC,KAAAgS,CAAAA,IAAAzP,GAAA+R,KAAAtC,CAAAzP,IAAAA,GAAAvC,KAAAgS,IAAAe,EAAAA;IAAAA,WAAe,KAAZjT,GAAY,QAAAkS,IAAAa,IAAAb,IAAAzP,CAAAA,GAAAvC,KAAAgS,CAAAzP,IAAAA,GAAA+R,KAAAtC,CAAAA,IAAAzP,GAAAvC,KAAAgS,IAAAa,EAAAA;aAAA,KAAA/S,IAAA;AAAA,aAAGkS,IAAUe,IAAEf,IAAAzP,CAAAA,GAAAvC,KAAAgS,CAAAA,IAAAzP,GAAA+R,KAAAtC,CAAAzP,KAAAA,GAAAvC,KAAAgS,IAAAa,EAAA,MAAA;AAAA,aAAGb,IAAUa,IAAEb,IAAAzP,CAAAA,GAAAvC,KAAAgS,CAAAzP,IAAAA,GAAA+R,KAAAtC,CAAAA,KAAAzP,GAAAvC,KAAAgS,IAAAa,EAAAA,IAAAtQ,GAAAvC,KAAAgS,IAAAe,EAAAA,MAAA;IAAA,OAAG;AAAA,aAAef,IAAUe,IAAEf,IAAAzP,CAAAA,GAAAvC,KAAAgS,CAAAzP,IAAAA,GAAA+R,KAAAtC,CAAAA,IAAAuF,OAAA,GAAAhV,GAAAvC,KAAAgS,IAAAa,EAAAA,GAAA,CAAA;AAAA,aAAGb,IAAUa,IAAEb,IAAAzP,CAAAA,GAAAvC,KAAAgS,CAAAA,IAAAzP,GAAA+R,KAAAtC,CAAAuF,IAAAA,OAAAhV,GAAAvC,KAAAgS,IAAAe,EAAAA,GAAAxQ,GAAAvC,KAAAgS,IAAAa,EAAAtQ,GAAAA,GAAAvC,KAAAgS,IAAAe,KAAAF,EAAAA,CAAAA;IAAAA;AAAAA,WAAAA;EAClJ;AACJ,WAAA0E,OAAA/R,IAAIkB,IAAQnF,IAAAA;AAAAA,UAAAA,KAAAA,KAAImF,KAAMnF,IAAUiW,KAAAnS,KAAAG,IAAAiS,KAAApS,KAAAqB,IAAAA,KAAAA,KAAInF;AAAQ,WAAAiW,KAAAA,MAAAC,KAAIA,MAAQD,KAAAA,MAAAE,KAAAA,KAAAlS,KACtDiS,KAAAA,MAAAC,KAAAA,KAAehR,KAAAA;EAAAA;AAAAA,WAAAA,MAAAA,IACkD7F,IAAAyB,IAAEA;AAAAA,IAAAA,GAAAwS,QAAAvC,GAAAzL,SAAMvE,IAAI1B,EAAOA,GAAAA,MAAA,GAAIyB,GAAAqV,SAAApF,GAAEzL,SAAIvE,IAAgB1B,EAAAA,GAAAA,MAAA,GAAAb,GAAA,QAAA+D,GAA0BlD,EAAAA,GAAIA,MAAAA,GAAAA,QAAAA,GAC5IA,EAAAA,GAAKA,MAAGyB,GAAAsV,WAAArV,GAAA1B,EAAAA,GAAAA,MAClByB,GAAAuV,SAAAtV,GAAA1B,EAAAA,GAAAA,MAAAA,GAAAA,YAAc0B,GAAA1B,EAAAA,GAAAA;EAAE;AAA2C,WAAAiX,UAAAC,IAAMvC,IAAAH,IAAAA,IAAA2C,IAAAC,IAAAC,IAAAC,IAAAC,IAAG;AAAA,UAAA1F,KAAApO,KAAA0E,IAAAwM,IAAMwC,EAAAA,GAAAA,IAAAA,KAAAA,IAAAA,IAAGC,EAAAA;AAAAA,QAAAA,IAAAA,GAA7EvE,IAAA;AAAmH,aAAAO,KAAA,GAAIA,KAAAtB,GAAQsB,KAAAA,UAAAA,KAAAA,GACrHjC,KAAMU,IAAGV,KAAAA,KACTkG,MAAA,KAAIC,MAAS,KAAT/L,IAAiB6H,KAAAuB,KAAAxD,MAAA,GAAA0B,KAAAyE,KAAAlE,MAAA+D,KAAAE,KAAAlG,MAAA,MAAA5F,KAAAA,CAAA+L,KAAAlE,MAAAuB,KAAA0C,KAAAlG,MAAA,GAAA0B,IAAAO,KAAA+D,KAAAhG,MAAA,IAA4B,KAAAhO,GAAK6P,CAAAA,GAAAH,CAAAqE,IAAAA,GAAA3L,CAAAyH,GAAAA,GAAAH,IAAA,CAAA,IAAAqE,GAAA3L,IAAA,CAAAyH,GAAAA,GAAAH,IAAA,CAAA,IAAAqE,GAAA3L,IAAA,CAAA,GAAAyH,GAAAH,IAAA,CAAAqE,IAAAA,GAAA3L,IAAA,CAAA;aAAA,KAAAgM,IAAA;AAAI,UAAAC,IAAAN,GAAA3L,IAAU,CAAA,KAAA,IAAA,MAAAkM,IAAAP,GAAA3L,CAAAiM,IAAAA,GAAAE,IAAAR,GAAA3L,IAAA,CAAA,IAAAiM,GAAGG,IAAAT,GAAA3L,IAAQ,CAAA,IAAAiM,GAAGI,IAAA5E,GAAAH,IAAK,CAAQ,KAAA,IAAG,MAAQgF,IAAQ7E,GAAAH,CAAAA,IAAA+E,GAAAE,IAAA9E,GAAAH,IAAA,CAAA+E,IAAAA,GAAEG,IAAA/E,GAAAH,IAAA,CAAA,IAAA+E;AAA9H,YAAAI,KAAA,IAAAR,GAAAS,KAAAT,IAAAI,IAAAI,IAAAA,KAAiK,KAAAzX,KAAI,IAAI,IAC7J0X;AAAcjF,MAAAA,GAAAH,IAAA,CAAA,IAAA,MAAAoF,IAA8BjF,GAAAH,IAAA,CAAA,KAAA4E,IAAKI,IAAAG,MAADE,IAAAA,GAAAA,IAAAA,CAAAA,KAAwBR,IAAII,IAAIE,MAAAE,IAAAA,GAAAA,IAAAA,CAAAA,KAAAA,IAAEH,IAAIC,MAAAE;IAAAA,WAAlG,KAAAX,IAAA;AAAsGC,UAAAN,GAAA3L,IAAK,CAAIkM,GAAAA,IAAAP,GAAA3L,CAAAA,GAAAmM,IAAAR,GAAA3L,IAAA,CAAA,GAAA,IAAArM,GAAGqM,IAAO,CAAA,GAAIqM,IAAA5E,GAAEH,IAAK,CAAA,GAAKgF,IAAA7E,GAAAH,CAAAiF,GAAAA,IAAA9E,GAAAH,IAAA,CAAA,GAAIkF,IAAA/E,GAAAH,IAAI,CAAA;AAAK2E,WAAAI,KAAMH,KAAAI,KAAAH,KAAAI,KAAAH,KAAAI,KAAA/E,GAAAH,CAAAA,IAAA,GAAAG,GAAAH,IAAA,CAAA,IAAA,GAAAG,GAAAH,IAAA,CAAA,IAAA,GAAAG,GAAAH,IAAA,CAAA,IAAA,MAAAG,GAAAH,CAAAA,IAAA4E,GAAAzE,GAAAH,IAAA,CAAA6E,IAAAA,GAAA1E,GAAAH,IAAA,CAAA8E,IAAAA,GAAA3E,GAAAH,IAAA,CAAA2E,IAAAA;IAAAA,WAC5I,KAAArU,IAAE;AAAoCqU,UAAAN,GAAA3L,IAAM,CAAQkM,GAAAA,IAAKP,GAAM3L,CAAAA,GAAEmM,IAAAR,GAAA3L,IAAA,CAAA,GAAA,IAAArM,GAAIqM,IAAK,CAAaqM,GAAAA,IAAA5E,GAAAH,IAAM,CAAQgF,GAAAA,IAAK7E,GAAMH,CAAAA,GAAEiF,IAAA9E,GAAAH,IAAA,CAAA,GAAA,IAAA1T,GAAI0T,IAAK,CAAA;AAAA,UAAA,KAAa+E,KAAOH,KAAAI,KAAAH,KAAAI,KAAAH,KAAAI,EAAA;AAAA,UAAA,IAAA,OAChIH,IAAA,GAAA,QAAA;IAAI;AAAA,WAAA;EAEC;AAAA,SAAA,EAAoDO,QA1NlF,SAAAA,OAAIpS,IAAAA;AAAAA,UAAAA,KACG,IAAApE,WAAWoE,EAAAA;AAAO,QAAK/F,KAAA;AAAA,UAAA+C,KAAA2O,IAAApQ,KAAAyB,GAAAyP,YAC1BjR,KAAAwB,GAAAkD,UAAkBxE,KAAA,EAAAkR,MAChB,CADgB,GAChByF,QAAS,CAAA,EAAA,GAAA1X,KAAA,IAAA,WACFgB,GAAO6B,MAAY;AAAA,QACF8U,GAAAA,IAAAA,GAA8DC,IAAA;AACxD,UAAAC,IAAA,CAAA,KAAA,IAAG,IAAH,IAAe,IAAK,IAAA,IAAA,EAAI;AAAA,aAAApZ,IAAA,GAAAA,IAAA,GAAGA,IAAK,KAARuC,GAAevC,CAAAA,KAAKoZ,EAAApZ,CAAAA,EAAA,OAAA;AACtF,WAAAa,KAAA0B,GAAA6B,UAAA;AACA,YAAAoC,KAAA5C,GAAAkD,SAAAvE,IAAA1B,EAAAA;AAAAA,MAAAA,MAAA;AACA,YAAAf,KAAA8D,GAAAyO,UAAA9P,IAAA1B,IAAA,CAGU;AAAA,UAHVA,MAAA,GAGU,UAAAf,GAAIuZ,OAAU9W,IAAA1B,IAAAyB,EAAAA;eAAA,UAAAxC,IAAA;AAAW,iBAAA,IAAAuC,IAAK,KAAAE,GAAAiC,CAAAA,IAAAA;AAAIZ,QAAAA,GAChCyO,UAAS9P,IAAA1B,IAAA2D,IAAA3D,EAAG0B,GAAAA,GAAAiC,IAAW,CAAK;AAAA,cAAA8U,KAAA/W,GAAG3C,MAAH4E,IAAe,GAAK3D,KAAA2F,EAAI;AAAA,YAAA+S,KAAA;AAAwB,YAAAA;AAAAA,UAAAA,KAAAC,SAAAA,EAAAA;QAAA,SAAwBzZ,IAAQwZ;AAAAA,UAAAA,KAAAtV,GAAAqV,EAAAA;QAAhC;AACxFhX,QAAAA,GAAAkR,KAAA1T,EAAAA,IAAAyZ;MAAAA,WACA,UAAAzZ,GAAAwC,CAAAA,GAAAkR,KAAA1T,EAAAyC,IAAAA,GAAA3C,MAAAiB,IAAAA,KAAA,CAAA;eAAA,UAAAf,IAAA;AACA,aAAeE,IAAA,GAAAA,IAASwG,IAAGxG,IAAAyU,CAAAA,GAAAgF,IAAAzZ,CAAAA,IAAAuC,GAAA1B,KAAAb,CACrByZ;AAAAA,aAAIjT;MAAAA,WACO,UAAJ1G,GACHwC,CAAAA,GAAAkR,KAAA1T,EAAI,IAAA,EAAA4Z,YAAUtX,GAAAG,IAAA1B,EAAAA,GAAA8Y,WAAAvX,GAAAG,IAAA1B,KAAA,CAAGqY,EAAAA,GAAAA,IAAA,IAAA1W,WAAID,GAAK6B,MAAAA;eACnB,UAAAhD,IAAA;AAAU,YAAA,KAAA+X,EAAAA,EAAAA,IAAAA,GAAgBF,OAAH3W,GAAa2W,OAAA7U,SAAA,CAAA,GAAA,OAAIwV,YAAAtX,IAAa4W,EAAAtZ,MAAA,GAAAuZ,CAAAA,GAAAb,EAAAuB,KAAA/E,OAAAwD,EAAAuB,KAAAlC,MAAAA,GAAAwB,IAAA;AACtE,cAAAW,KAAA,EACA9H,GAAA5P,GAAAG,IAAA1B,KAAA,EAAAoT,GAAAA,GAAA7R,GAAAG,IAAA1B,KAAA,EAAA,GAAAiU,OAAA1S,GAAAG,IAAA1B,KAAA,CAAA,GAAA8W,QAAAvV,GAAAG,IAAA1B,KAAA,CAAA,EAAA;AAAA,YAAAkD,KAEa5B,GAAAI,IAAI1B,KAAiB,EAAAkZ;AAAAA,QAAAA,KAAK5X,GAAAI,IAAA1B,KAAA,EAAA,KAAA,KAAAkZ,KAAA,MAAAA;AAC7B,cAAAC,KAAA,EAAiBH,MAAAC,IAAAG,OAAI3V,KAAK4V,MAAK,MAAAH,EAAAI,GAAAA,SAAA5X,GAAA1B,KAAA,EAAAuZ,GAAAA,OAAA7X,GAAA1B,KAAA,EAAA,EAAA;AAAA,QAAAmD,GAAA,OAAA,KAAA5C,EAAA;MAAA,WACmB,UAAAA,IAAU;AAAI,aAAApB,IAAA,GAAGA,IAAKwG,KAAK,GAAAxG,IAAKkZ,GAAKC,IAAAnZ,CAAAA,IAAAuC,GAAA1B,KAAAb,IAAA,CAAA;AACjGmZ,aAAA3S,KAAA;MAAA,WACA,UAAA1G,GACAwC,CAAAA,GAAAkR,KAAe1T,EAAAA,IAAA,CAAA8D,GAAYkD,SAAAvE,IAAA1B,EAAA+C,GAAAA,GAAAkD,SAAAvE,IAAA1B,KAAA,CAAA,GAAA0B,GAAA1B,KAAA,CACrB,CAAA;eAAa,UAATf,IAAK;AAAA,QAAAkE,GACTwP,KAAK1T,EAAQ,IAAA,CAAA;AACX,aAAAE,IAAA,GAAMA,IAAM,GAAAA,IAAIsC,CAAAA,GAAAkR,KAAA1T,EAAA2F,EAAAA,KAAA7B,GAAAkD,SAAAvE,IAAA1B,KAAA,IAAAb,CAAK,CAAA;MAAA,WACnB,UADmBF,MACV,UAAAA,IAAA;AACK,gBAAhBwC,GAAAkR,KAAI1T,EAAYwC,MAAAA,GAAAkR,KAAA1T,EAAAA,IAAA,CAAA;AAAA,YAAA,IAAA0F,GACT4M,SAAI7P,IAAO1B,EAAAA,GACdwZ,IAAAzW,GAAAyO,UAAS9P,IAAA1B,IAAayZ,IAAOzZ,EACMgJ,GAAAA,IAAAhJ,KAAA2F,KAAK8T,IAAL;AAC/C,YAAA,UAAAxa,GAAAya,KAAA3W,GAAAyO,UAAA9P,IAAA+X,IAAA,GAAAzQ,CACA;aAAA;AAAA,cAAA,IACU2P,SAASjX,GAAO3C,MAAO0a,IAAK,GAAAA,IAAA,IAAAzQ,CAAAA,CAAAA;AAC1B0Q,cAAA3W,GAAAC,SAAI2W,GAAK,GAAOA,EAAApW,MAAAA;QAAsD;AACpC9B,QAAAA,GAAAkR,KAAA1T,EAAAA,EAAAua,CAAKE,IAAAA;MAAAA,WACnD,UAAAza,IAAA;AACmB,gBAAnBwC,GAAAkR,KAAA1T,EAAAA,MAA4BwC,GAAGkR,KAAA1T,EAAA,IAAA,CAAA;AAAA,YAAA,GAEnB0E,IAAA3D;AAAuEyZ,YAAA1W,GAAAwO,SAAA7P,IACrEiC,CAAAA;AAAgC6V,YAAAzW,GAAAyO,UAAK9P,IAAWiC,GAAA8V,IAAD9V,CAC7D;AAAA,cAAAiW,KAAAlY,GAD2EiC,IAAa8V,IAAA,CAAA;AAMxF,YAAAC;AAJAhY,QAAAA,GAAeiC,IAAI,CAAAA,GAAAA,KAAY,GAAA,IAAAgB,GAAA,SAChBjD,IAAIiC,CAAAA,GACPZ,GAAAyO,UAAS9P,IAAKiC,GAAM8V,IAAA9V,CAAAA,GAAAA,IAAA8V,IAAA,GAAA,IAAA9U,GAAA,SAAIjD,IAAAA,CACKqB,GAAAA,GAAAC,SAAAtB,IAAgBiC,GAAAA,IAAaA,CAAAA;AAEtEqF,YAAArD,OAF4EhC,IAAO8V,IAA3B,KAErCzZ;AAAAA,YACJ,KAAAkD,GAAAwW,KAAI3W,GAAOC,SAAYtB,IAAAiC,GAAAqF,CAAAA;aAAAA;AACK2Q,cAAAhB,SACvBjX,GAAA3C,MAAS4E,GAAAA,IAAOqF,CAA2B0Q,CAAAA;AAAAA,cAAA3W,GAAAC,SAAK2W,GAAL,GAAAA,EAAgBpW,MAC/E;QAAA;AACA9B,QAAAA,GAAAkR,KAAA1T,EAAAua,EAAAA,CAAAA,IAAAE;MAAAA,WACA,UAAAza,GACAwC,CAAAA,GAAAkR,KAAA1T,EAAA8D,IAAAA,GAAA0O,UAAA/P,IAAA1B,IAAA2F,EAAAA;eAEW,UAAA1G,IAAA;AACX,cAAA4a,KAAApY,GAAAkR,KAAAO,KAAA3P,SAAA;AAAA,QAAAJ,GAAA,KAAA5C,EAAA,IAAA,CAAA;AAAA,aAAA,IAAA,GAAA,IAAArB,IAAA,IAAA,CAAAiE,GAAA,KAAA5C,EAAA,EAAA,KAAA+B,GAAAnD,IAAAqC,KAAA,IAAA,CAAA,CAAA;MAAA,WAEwB,UAAAjB,GACH,MAAjBkB,GAAM4Q,QAAW5Q,GAAAkR,KAAW1T,EAAAA,IAAA8D,GAAA0O,UAAA/P,IAAA1B,IAAA2F,EAAoB,IAAA,KAAAlE,GAAA4Q,QAAA5Q,GAAAkR,KAAA1T,EAAAqC,IAAAA,GAAAI,IAAA1B,EAAAA,IAAe,KAAAyB,GAAA4Q,UAAA5Q,GAAAkR,KAAA1T,EAAA,IAAA,CAAAqC,GAAAI,IAAA1B,EAAAsB,GAAAA,GAAAI,IAAA1B,KAAA,CAAAsB,GAAAA,GAAAI,IAAA1B,KAAA,CAAA,CAAA;eACnD,UAAJf,GAAIwC,CAAAA,GAAAkR,KAAA1T,EAAAA,IAAA8D,GAAAkD,SAAAvE,IAAA1B,EAAAA,IAAA;eACA,UAAAf,GAAAwC,CAAAA,GAAAkR,KAAA1T,EAAAyC,IAAAA,GAAA1B,EAAE;eAAF,UAAEf,GAAkB,MAARwC,GAAQ4Q,SAAA,KAAA5Q,GAAA4Q,QAAA5Q,GAAAkR,KAAA1T,EAAAA,IAAA,CAAAqC,GAAAI,IAAA1B,EAAAA,CAAAA,IAAAA,KAAAA,GAAAA,SAAAA,KAAAA,GAAAA,QAAAA,GAAAA,KAAAA,EAAAA,IAAAA,CAAAA,GAAAA,IAAAA,EAAAA,GAAAA,GAAAA,IAAAA,KAAAA,CAAAA,GAAAA,GAAAA,IAAAA,KAAAA,CAAAA,CAAAA,IACrB,KAAAyB,GAAA4Q,UAAA5Q,GAAAA,KAAoBxC,EAAAyC,IAAAA,GAAA1B,EACtB;eAAA,UAAAf,GACT;AAAA,MAAAuC,MAIKmE,IAAAA,GAAgCM,SAAMvE,IAAK1B,EAAAA,GAAAA,MAAA;IAAA;AAG9C,QAAAyX;AAIN,WAAA,KALIa,OACEb,IAAAhW,GAAY2W,OAAA3W,GAAI2W,OAAS7U,SAAM,CAAA,GAAA,OAASwV,YAAUtX,IAAA4W,EAAAtZ,MAAA,GAAAuZ,CAAAA,GAAAb,EAAAuB,KAAA/E,OAAAwD,EAAAuB,KAAAlC,MAAAA,IAAAA,GAAAA,OACLiC,YAAUtX,IAAAmS,IAAAnS,GAAAwS,OAAAxS,GAAAqV,MAAAA,GAAAA,OAAAA,GAAAA,UAAAA,OAGbrV,GAAAqS,WAAAA,OAAArS,GAAAuV,QAAhDvV;EACQ,GAsIRqY,SArU4B,SAAAA,QAAQrY,IAAAA;AAAAA,UAAAA,KAASA,GAAAwS,OAAuBnC,KAAArQ,GAAAqV;AAAAA,QAA8C,QAAA5X,GAAPyT,KAAOoH,KAAA,QAAA,CAAAC,YAAAvY,GAAAC,MAAAmQ,IAAAC,IAAArQ,EAAA5B,EAAAA,MAAAA;AAC9G,UAAAoa,KAAA,CAAyC;AAAA,YAAA/a,GAAhBkZ,OAAK,CAAA,EAAI1W,SAAaD,GAAA2W,OAAQ,CAAA,EAAA1W,OAAAD,GAAAC;AAA3D,UAPaiE,KAAAkM,KAAAC,KAAA,GAAAoC,KAAA,IAAAvS,WAAAgE,EAAAuU,GAAAA,KAAA,IAAAvY,WAAAgE,EAQT+B,GAAAA,KAAA,IAAA/F,WAAsBgE,EAAE;AAAA,aAAAxG,KAAA,GAAYA,KAAAsC,GAAA2W,OAAA7U,QAAApE,MAAA;AAAA,YAAIga,IAAK1X,GAAQ2W,OAAGjZ,EAAAA,GAAAA,IAAYga,EAAAA,KAAShI,GAASgJ,IAAAhB,EAAAH,KAAA5F,GAAAgH,IAAAjB,EAAAH,KAAA/E,OAAAA,IAAYkF,EAAAH,KAAAlC,QARzFuD,IAAAL,YAAAb,EAAAzX,MAAA0Y,GAAAE,GAAA7Y,EAAAA;AAAAA,UASO,KAAAf,GAAA,UAAA2I,KAAA,GAAeA,KAAI1D,IAAM0D,KAAA3B,CAAAA,GAAA2B,EAAA6K,IAAAA,GAAA7K,EAAAA;AAAAA,UAC1B,KAAf8P,EAAAI,QAAkBtC,UAAGoD,GAAAD,GAAAE,GAAApG,IAAArC,IAAAC,IAAAyI,GAAAJ,GAAA,CAAA,IACX,KAARhB,EAAQI,SAAAtC,UAAAoD,GAAAD,GAAAE,GAAApG,IAAArC,IAAAC,IAAAyI,GAAAJ,GAAA,CAAA,GAAAhb,GAAA,KAEH+U,GAAArU,OAAWd,MAAI,CAAA,CAAA,GAChB,KAAA,EAAAua,QAAA;eAAA,KAAAH,EAAAG,QAAArC,WAAAiD,IAAAE,GAAAE,GAAApG,IAAArC,IAAAC,IAAAyI,GAAAJ,GAAA,CAAA;eAAO,KAALhB,EAAAG,QAAK,MAAmBjQ,KAAA,GAAAA,KAAA1D,IAAA0D,KAAA6K,CAAAA,GAAA7K,EAAA3B,IAAAA,GAAA2B,EAApC;IAAA;AAAsD,WAAA4Q;EAAtD,GA6T2HvD,QAAAA,WAAAA,MAAAA,GAAAA;AAAAA,EAAAA;AAAAA,CAAAA,WAE3H;AAAA,QAAA,EAAAO,WAAAA,GAAAuD,IAAAA,MAAAA,EAAAA,MAAAA,GAAAA,IAAAA,MAA+HC,KAAAD,KAAA9D;AAAa,MAAAgE,KAAA,EAAmB9U,OAAA,WAAkB;AAAA,UAAAN,KAAA,IAAAC,YAAW,GAAA;AAChL,aAAAC,KAAA,GAAAA,KAAA,KAAKA,MAAI;AAAI,UAAA9E,KAAA8E;AAAAA,eAAAA,KAAAA,GAAAA,KAAgB,GAAAC,KAAAA,KAAAA,KAAAA,KAAAA,aAA4B/E,OAAM,IAAAH,QAAA;AAA3E+E,MAAAA,GAAAE,EAAAA,IAAA9E;IAAAA;AACA,WAAA4E;EAAAA,EAF+J,GAI/JI,OAAAhF,IAAAU,IAAAuC,IAAAgC,IAAQ;AAAA,aAAAxG,KAAO,GAAAA,KAAAwG,IAAYxG,KAAQuB,CAAAA,KAAAga,GAAQ9U,MAAG,OAAAlF,KAAAU,GAAAuC,KAAAxE,EAAAuB,EAAAA,IAAAA,OAAA;AAC9C,WAAAA;EAL4I,GAAA,KAAA,CAAAxB,IAAAgE,IAM9GC,OAAY,aAAAuX,GAAAhV,OAAA,YAAAG,IAAArE,IAAA2B,EAAAA,EAAAA;AAAAA,WAAAA,OAAoCwX,IAAQ5H,IAAAF,IAAAjS,IAAAA;AAAAA,IAAAA,GAAAA,EAAoB+Z,KAAAA,GAAM,CAAG/Z,IAAAA,MAAA,GAAAmS,GAAAF,KAAA,CAAA,KAAA8H,GAAA,CAAA,IAAA/Z,MAAA,GAAAmS,GAAAF,KAAA,CAAA,KAAA8H,GAAA,CAAA,IAAA/Z,MAAA,GAAAmS,GAAAF,KAAA,CAAA8H,KAAAA,GAAA,CAAA/Z,IAAAA,MAAA;EAAA;AAAnH,WAAAuU,EAAAhE,IAAAA;AAAA,WAAA1N,KAAAiM,IAAA,GAAAjM,KAAA0E,IAAA,KAAAgJ,EAAAA,CAAAA;EAAA;AACI,WAAAgF,EAAAxR,IAAkBkB,IAChB;AAAA,UAAA+U,KAAAjW,GAAA,CAAUkB,IAAAA,GAAE,CAAEgV,GAAAA,KAAAlW,GAAA,CAAA,IAAAkB,GAAA,CAAA,GAAAiV,KAAAnW,GAAA,CAAA,IAAAkB,GAAA,CAAA,GAAGkV,KAAApW,GAAA,CAAMkB,IAAAA,GAAA,CAAM;AAAA,WAAE+U,KAAAA,KAAAC,KAAAA,KAAAC,KAAAA,KAAAC,KAAAA;EAAG;AACN,WAAAC,OAAA9D,IAAKrF,IAAAA,IAAIoJ,IAAAjI,IAAAA,IAAAkI,IAAI;AAAA,YAAAA,OAAAA,KAAU;AAClC,UAAArE,KAAAoE,GAAA1X,QAAO4X,KAAA,CAAA;AAAA,aAAAza,KAAe,GAAAvB,KAAA0X,IAAA1X,MAAA;AACnC,YAAAuB,KAAAua,GAAA9b,EAAAA;AAAWgc,MAAAA,GAAAvW,KAAA,CAAAlE,OAAQ,IAAA,KAAAA,OAAA,IAAA,KAAAA,OAAA,KAAA,KAAAA,OAAA,KAAA,GAAA,CAAA;IAAG;AAAA,SAAAA,KAAA,GAAAA,KAAemW,IAAI1X,MAAI;AAAA,UAAAD,KAAA;AAAvD,eAAA,IAAA,GAAAmK,IAAA,GAAAA,IAAAwN,IAAoGxN,KAAA;AAAS,YAAA+R,IAAAjF,EAAAgF,GAAAhc,EAAAgc,GAAAA,GAAA9R,CAAAA,CAAAA;AAAAA,aAAAlK,MAAAic,IAAAC,OAAAA,KAAAD,GAAAE,IAAAjS;MAA7G;IAGA;AAAA,UAAA,IAAA,IAAA,YAA0D2J,GAAAnT,MAAAA,GAA1D0b,IAAA,IAAAC,WAAA3J,KAAAC,KAAA,CAAA,GAAA,IAAA,CAGA,GAAA,GAAA,GAAA,IAAO,IAAE,GAAF,IAAkB,GACnB,GAAA,IAAA,GAAA,GAAW,IAAA,GAAA,IAAQ,CAAG;AAAA,SAAA3S,KAAA,GAAUA,KAAE4W,EAAFxS,QAAApE,KAAa4W,GAAA5W,EAAA,IAAA,QAAA4W,EAAA5W,EAAAA,IAAA,OAAA+W,KAAA;AAC7B,aAAA9C,KAAA,GAAAA,KAAUtB,IAAAsB,KAAAA,UAAAA,IAAY,GAAAjC,IAAFU,IAAYV,KAAA;AAAG,UAAA;AAAAhS,MAAAA,KAAa,KAAbiU,KAAGvB,KAAKV;AAAAA,UAA8B,KAAA7O,GAAAoS,KAAA,CAAAS,EAAA+B,GAAA/X,EAAAA,IAAAoc,EAAApc,EAAAA,CAAAA,GAAAgW,EAAA+B,GAAA/X,KAAA,CAAA,IAAAoc,EAAApc,KAAA,CAAA,CAAA,GAAAgW,EAAA+B,GAAA/X,KAAA,CAAA,IAAAoc,EAAApc,KAAA,CAAAgW,CAAAA,GAAAA,EAAA+B,GAAA/X,KAAA,CAAAoc,IAAAA,EAAApc,KAAA,CAA/F,CAAA,CAAA;WAAA;AAAsGic,YAAOrF,EAH7G,KAGoHG,IAAP9C,OAAO8C,IAAA/E,EAAAA;AAAAA,YAAAA,CAAEgE,EAAE+B,GAAA/X,EAAAA,IAAFic,CAAAA,GAAYjG,EAAA+B,GAAA/X,KAAA,CAAA,IAAAic,CAAAjG,GAAAA,EAAA+B,GAAA/X,KAAA,CAAAic,IAAAA,CAAAA,GAAAjG,EAAA+B,GAAA/X,KAAA,CAAA,IAAAic,CAAG,CAAA;MAAA;AAArIE,UAAA;AAAA,UAAA,IAAwK;AACxK,WAAAjS,IAAA,GAAAA,IAAAwN,IAAAxN,KAAA;AACM,cAAMoS,KAAAtF,EAAAzB,GAAayG,GAAG9R,CACtBoS,CAAAA;AAAAA,QAAAA,KAAMC,MAAIA,IAAID,IAAAH,IAAAjS;MAAAA;AAA8B,YAAA1B,IAAIwT,GAAAG,CAAAA,GAAGX,IAAI,CAAAjG,EAAA,CAAA/M,IAAAA,EAAA,CAAA+M,GAAAA,EAAA,CAAA/M,IAAAA,EAAA,CAAA+M,GAAAA,EAAA,CAAA/M,IAAAA,EAAA,CAAA+M,GAAAA,EAAA,CAAA,IAAA/M,EAAA,CAAA,CAAA;AAAc,WAAJuT,OAA2B/J,KAAAU,KAAQ,KAAA8J,OAAQhB,GAAGY,GAAApc,KAAA,GAAA,CAAIiU,GAAAA,MAAEtB,KAAK,MAAY,KAATX,KAAYwK,OAAOhB,GAAAY,GAAKpc,KAAA,IAAA0S,KAAA,GAAA,CAAA,GAAA,OAAI8I,GAAFY,GAAApc,KAAA,IAAA0S,IAAA,CAAA,GACvJV,KAAUU,KAAE,KAAA8J,OAAAhB,GAAAY,GAAApc,KAAA,IAAA0S,KAAA,GAAA,CAAA,KAAAlN,GAAyBxF,MAAA,CAAImc,IAAAA,GAAGM,EAAQzc,MAAK,CAAA8b,IAAAA,GAAAK,CAAE;IAAA;EAAjE;AAgBO,WAAAO,MAAQC,IAAUjK,IAAAC,IAAAA,IAASa,IAAE;AAAA,YAAAA,OAAWA,KAAA,CAAA;AAAI,UAAA9O,EAAAA,KAAAA,GAAM6W,IAAAA,IAAarW,KAAAqN,GAAQxL,WAAG5B,KAAEoN,GAAF1L,aAAjF+V,IAAArK,GAAArL;AACI,QAAArG,IAAQ;AAAA,UAAUgc,IAASF,GAAA1D,OAAA7U,SAAA;AAAA,QAAoC0Y,GAAlCC,IAAAA,OAAkEC,IAAE,MAAgBH,IAAA,KAAA;AAArH,QAAA,QAAArJ,GAAAyJ,SAAAD,KAAA,KAAoI,QAARxJ,GAAA0J,SAAqBF,KAAM,KAAU,QAARxJ,GAAA2J,SAAAA,IAAwBC,KAAAC,QAAH7J,GAAAA,IAAAA,GAA4BwJ,KAA1C,KAA8DF,EAAA1Y,SAAA,IAA9N,KAAAuY,GAAAzJ,OAAA;AACiC,eAA3BoK,IAAFX,GAAQb,KAAA1X,QAAqBpE,IAAA,GAAUA,IAAAsd,GAAFtd,IAAgB2c,CAAAA,GAADb,KAAF9b,CAAAA,MAAwB,MAAG,QAAD+c,IAAxC;AAAxCC,WAAA,IAAA,IAAAM,IAAA,KAAAP,IAAA,IAAA,IAAAO,IAAA,IAAA;IAAA;AAAA,aAAuKpT,IAAA,GAAAA,IAAEyS,GAAM1D,OAAQ7U,QAAD8F,KAAF;AAC9K2S,YAAMG,KAAA,KAAkBA,MAD9B1E,IAAAqE,GAAA1D,OAAA/O,CAAAA,GAC8BqT,KAAUnZ,SAAF,IAAtC,KAAA8F,MAAA8S,KAAA;IAA4I;AACtIA,SAAA;AAAA,UAAA,IACO,IAAAxa,WAAAwa,CAAAA,GACLQ,IAAA,CAAA,KAAO,IADF,IAAA,IAAA,IAAA,IAAA,IAAA,EAAA;AACS,SAAAxd,IAAA,GAAOA,IAAJ,GAAMA,IADlBuC,GAAAvC,CAAAA,IAAAwd,EAAAxd,CAAAA;AAC+sB,QAAAgE,GAAA,GAAA,GAA1mB,EAAA,GAAA,KAAsB,GAAA,EAAA,GAAA,GAAiE,MAAA,GAAA,KAAA,GAAyCkB,GAAA3C,GAAA1B,GAAO6R,EAAAA,GAAM7R,KAAe,GAAqEqE,GAAA3C,GAAA1B,GADtU8R,EAAA9R,GAAAA,KAAA,GAC0U0B,EAAA1B,CAAG8b,IAAAA,GAAAxJ,OAD7UtS,KAAAA,EAAAA,CAAAA,IAC2V8b,GAAOzJ,OAASrS,KAAAA,EAAAA,CAAAA,IAAAA,GAA4EA,KAAAA,EAAAA,CAAAA,IAAqE,GAAIA,KAAAA,EAAAA,CAAAA,IAAAA,GAA2FA,KAAWqE,GAAA3C,GAAA1B,GAAO6D,GAAEnC,GAD/mB1B,IAAA,IAAA,EAAA,CAAA,GAAAA,KAAA,GACipB,QAAP2S,GAAAyJ,SAAc/X,GAAA3C,GAAA1B,GADxpB,CAAAA,GAAAA,KAAA,GAC+pB+b,EAAAra,GAAA1B,GAAG,MAAA,GADlqBA,KAAA,GACgrB0B,EAAA1B,CAAAA,IADhrB2S,GAAAyJ,MAAApc,KACurBqE,GAAA3C,GAAA1B,GADvrB6D,GAAAnC,GAAA1B,IAAA,GAAA,CAAA,CAAA,GAAAA,KAAA,IAAA,QAC+sB2S,GAAA2J,MAD/sB;AACstB,YAAAM,KAAA,KAASX,EAD/tB1Y;AACquBc,MAAAA,GAAA3C,GAAA1B,GADruB4c,EAAAA,GAAA5c,KAAA,GAAA,EAAA,GAC4uBA,GAAG,MAAA,GAD/uBA,KAAA,GAC+vB+b,EAAAra,GAAA1B,GAAG,aAAA,GADlwBA,KAAA,IAAAA,KAAA,GAC8wB0B,EAAAkK,IAAAqQ,GAAOjc,CAAAA,GADrxBA,KAAAic,EAAA1Y,QAC4xBc,GAAA3C,GAAA1B,GAAG6D,GAD/xBnC,GAAA1B,KAAA4c,KAAA,IAAAA,KAAA,CAAA,CAAA,GAAA5c,KAAA;IAC0yB;AAAA,QAAmB,QAAAsC,GAAA,SAAgB+B,GAAA3C,GAAA1B,GAAG,CAAA,GAAA,KAAW,GAAA,EAAA,GAAA,GAAA,MAAA,GAAA,KAAA,GAAAmD,GAHntBzB,GAAA1B,GAAA2S,GAAA0J,KAAA,CAAArc,CAAAA,GAAAA,KAAA,GAAAmD,GAMpIzB,GAAA1B,GAAA2S,GAAA0J,KAAA,CAAArc,CAAAA,GAAAA,KAAA,GACX0B,EAAA1B,CAAM2S,IAAAA,GAAQ0J,KAAA,CAAA,GAAArc,KAAGqE,GAAA3C,GAAA1B,GAAU6D,GAAKnC,GAAA1B,IAAA,IAAA,EAAAA,CAAAA,GAAAA,KAAA,IAAA,MAAAmD,GAAA,GAE9BnD,GAAK,CAADA,GAAAA,KAAA,GAAA,EAAA,GAA+CA,GAAK,MAADA,GAAAA,KAAA,GAAAmD,GAAA,GAA+CnD,GAAK8b,GAAD1D,OAAA7U,MAAAA,GAAAA,KAA8B,GAAAJ,GAAA,GACxInD,GAAqB,QAArB2S,GAAUkK,OAAgBlK,GAAjBkK,OAAyB,CAAA7c,GAAAA,KAAA,GAC1CqE,GAAA3C,GAAA1B,GAAA6D,GAAAnC,GAAA1B,IAAA,IAAA,EAAA,CAAA,GAAAA,KAAA,IAAgD,KAAAd,GAAA,OAAF;AAAA,MAAAiE,GAAA,GAAA,GAAA,KAA9CsZ,IAAAX,GAAAb,KAAA1X,OAAAA,GAAAA,KAAAA,GAAAA,EAAAA,GAAmEvD,GAAI,MAAA,GAAeA,KAAK;AAAA,WAAA,IACnF,GAAAb,IAASsd,GAAAA,KAAU;AAAA,cAAAvZ,KACnB,IAAA/D,GAASuB,KAAAob,GAAgBb,KAAE9b,CAAAA,GAAAoB,KAAA,MAAAG,IAAA+V,KAAA/V,OAAA,IAAA,KACnCmF,KAAAnF,OAAA,KAAA;AAAOgB,UAAA1B,IAAY6S,KAAA,CAAA,IAAAtS,IAAAmB,EAAA1B,IAAA6S,KAAA,CAAA,IAAA4D,IAAA/U,EAAA1B,IAAA6S,KAAA,CAAAhN,IAAAA;MAAAA;AAAAA,UAAAA,KAAAA,IAA8B4W,GAAAA,GAAAA,GAAsBzc,GAAG6D,GAAWnC,GAAA1B,IAAA,IAAAyc,IAAA,GAAA,IAAAA,IAAA,CAAAzc,CAAAA,GAAAA,KAAA,GAAA,GACvE;AAAYqE,QAAAA,GAAA3C,GAAM1B,GAAKyc,CAAAA,GAAAzc,KAAA,GAAI+b,EAAAra,GAAI1B,GAAO,MAAA,GAAAA,KAAA;AAAI,aAAAb,IAAW,GAAAA,IAAAsd,GAAAtd,IAAAuC,GAAA1B,IAAAb,CAAAA,IAAA2c,GAAAb,KAAA9b,CAAA,MAAA,KAAA;AAAA,aAAUsd,GAAcpY,GAAA3C,GAAM1B,GAAK6D,GAAAnC,GAAA1B,IAAAyc,IAAA,GAAAA,IAAA,CAAA,CAAA,GAAAzc,KAAA;MAAI;IAAe;AACpG,QAAA8c,IAAA;AAAA,SAVhBzT,IAAA,GAAAA,IAAAyS,GAAA1D,OAAA7U,QAAA8F,KAAA;AAAA,UAWYoO,IAAAqE,GAAI1D,OAAA/O,CA7ED2S;AAAAA,YAgFT3X,GAAA3C,GAAA1B,GAAA,EAAqBA,GAAAA,KAAK,GACjC+b,EAAAra,GAAU1B,GAAI,MAAAA,GAAAA,KAAA,GAAOqE,GAAA3C,GACf1B,GAAI8c,GAAA9c,GAAAA,KAAA,GACVqE,GAAA3C,GAAY1B,GAAAyX,EAAQuB,KAAA/E,KAAAA,GAAAjU,KAAA,GAAMqE,GAAA3C,GAAM1B,GAAAA,EAAcgZ,KAAAlC,MAAAA,GAAA9W,KAAA,GAAGqE,GAAA3C,GAC/C1B,GAAMyX,EAAAuB,KAAc7H,CAAJnR,GAAAA,KAAU,GAC5BqE,GAAA3C,GAAAA,GAAgB+V,EAAAuB,KAAJ5F,CAAepT,GAAAA,KAAI,GAC/BsE,GAAI5C,GAAK1B,GAAA+c,GAAA1T,CAAAA,CAAAA,GAAArJ,KAAA,GAETsE,GAAA5C,GAAAA,GAAAA,GAAAA,GAAAA,KAAwC,GACxCA,EAAA1B,CAAAA,IAAAA,EAAAA,SAA+BA,KAC/B0B,EAAA1B,CAAAA,IAAAA,EAAAA,OAA0BA,KAC1BqE,GAAA3C,GAAAA,GAAAA,GAAsBA,GAAU1B,IAAS,IAAA,EAAA,CAAA,GAAAA,KAAA;AAAA,YAIvCgd,KAAWvF,EAAAiF;AAEXrY,MAAAA,GAAA3C,GAAI1B,IAAAA,IAF4Bgd,GAC9BzZ,WACO,KAAA8F,IAAA,IAAA,EAAArJ,GAAAA,KAAA;AAAG,YAAA8E,KACL9E;AAAAA,QAAAA,GACHA,GAAK,KAAAqJ,IAAa,SAAA,MAAArJ,GAAAA,KAAA,GAAO,KAAAqJ,MAAOhF,GAAK3C,GAAG1B,GAAA8c,GAAA9c,GAAAA,KAAA,IAAA,EAAA,IAAAkD,IAAElD,CAAAA,GAAAA,KAAUyc,GAA9DpY,GAAA3C,GAAA1B,GAAA6D,GAAAnC,GAAAoD,IAAA9E,IAAA8E,EAAAA,CAAAA,GAAA9E,KAAA;IAAA;AAGM,WAAAmD,GAAA,GAAA,GAFoD,CAAAnD,GAAAA,KAAA,GAAA,EAAA,GAAA,GAAI,MAAAA,GAAAA,KAAA,GAA9DqE,GAAA3C,GAAA1B,GAAA6D,GAAAnC,GAAA1B,IAAA,GAAA,CAAA,CAAA,GAAAA,KAAA,GAEM0B,EAAA7B;EAEA;AAEA,WAAAod,YAAOxb,IAASuV,IAAAkG,IAAAA;AAAAA,aAAAA,KACd,GAAI/d,KAAMsC,GAAA2W,OAAA7U,QAAapE,MAAA;AACvB,YAAAga,KAAA1X,GAAI2W,OAAOjZ,EAAkBga;AAAAA,MAAAA,GAAAH,KAAA/E;AAAA,YAE7BkJ,KAAAhE,GAAAH,KAAAlC,QACEuD,KAAA,IAAI1Y,WAAUwb,KAAAhE,GAAAnH,MAAAmL,EACZhE;AAAAA,MAAAA,GAAAuD,OAAA1I,YAAUmF,GAAAjF,KAAAiJ,IAAKhE,GAAOjH,KAAAiH,GAAAnH,KAAAqI,IAAArD,IAAAkG,EAAI;IAAA;EAC1B;AAEF,WAAAnG,UAAAqG,IAAIvL,IAAAC,IAAAA,IACFuL,IACA;AAAA,UAAAC,KAAAD,GAAA,CAAIE,GAAAA,KAAaF,GAAjB,CAAA,GAAwCG,KAAKH,GAAa,CAAAI,GAAAA,KAAAJ,GAAA,CAAA,GAAAK,IAAAL,GAAA,CACtEM,GAAAA,IAAAN,GAAA,CAAA;AAEY,QAAAhL,IAAA,GAAAC,IAAU,GAAgBsL,IAAA;AAEtC,aAAAvU,IAAA,GAAAA,IAAA+T,GAAA7Z,QAAA8F,KAAA;AACU,YAAA6K,KAAA,IAAAvS,WAAcyb,GAAA/T,CAAAA,CAAAA;AAAAA,eACZwU,IAAA3J,GAAA3Q,QAAAA,IAAAA,GAAAA,IACKsa,GAAA1e,KAAQ,EAAGye,MAAU1J,GAAA/U,IAAA,CAAK;IAAA;AAC3C,UAAA2e,IAAA,OAAAF,GAAAA,IAmEgB,SAAAG,QACLX,IAAAvL,IAAAC,IAAAkM,IAAAA,IAAAR,IAAAA;AAGX,YAAAvD,KAAA,CAEQ;AAAA,eAAA5Q,KAAA,GAAIA,KAAA+T,GAAW7Z,QAAA8F,MAAA;AAAE,cAAAqT,KAAA,IAAG/a,WAASyb,GAAA/T,EAAAA,CAAAA,GAAK4U,KAAA,IAAA1Y,YAAamX,GAAG7c,MAAAA;AAAS,YAAAic;AAAnE,YAAAoC,KAAA,GAAAC,KAAgI,GAAAC,KAAWvM,IAAAsL,KAAArL,IACjIyH,KAAAyE,KAAS,IAAG;AAAqB,YAAA,KAAA3U,IAAA;AAAA,gBAAAxD,KAAsB2X,MAAYQ,MAAU,KAAA3U,MAAA,KAAA4Q,GAAA5Q,KAAA,CAAAiQ,EAAAA,UAAA,IAAA;AAAA,cAAA+E,KAAA,GAAAC,KAAA;AAE7E,mBAAAC,KAAA,GAAIA,KAAKC,IAAGD,MAAH;AAAwB,gBAAAE,KAAA,IAAS9c,WAAAyb,GAAS/T,KAAA,IAAAkV,EAAA,CAAA;AAAA,kBAAA/c,KAAA,IAAI+D,YAAY6X,GAAH/T,KAAa,IAAAkV,EAAAA,CAAAA;AAAAA,gBAAAA,KAAI1M,IAC1E6M,KAAG5M,IAAHpC,KAAAA,IAAaiP,KAAAA;AAEpB,qBAAAvL,KAAA,GAAMA,KAAAtB,IAAMsB,KAAAA,UAAQjC,KAAA,GAAAjO,KAAW2O,IAAAV,MAAK;AAEpC8M,cAAAA,GAF8C9e,KAAAiU,KAC5CvB,KAAAV,EACFyN,KAAAA,GAAazf,EACbgS,MAAAA,KAAAA,OAAA0N,KAAc1N,KAAKA,KAANzB,OAAkBA,KAAAyB,KAC/BiC,KAAAA,OAAAsL,KAActL,KAAKA,KAANuL,OAAkBA,KAAAvL;YAEzC;AAAA,kBAC+B1S,OAASme,KAAAH,KAAGhP,KAAAiP,KAAA,IAAKpB,OAAiB,MAAL,IAAHsB,OAAQA,MAAA,MAAA,IAAAH,OAAAA;AAAI,kBAAAI,MACpDpP,KAAAmP,KAAQ,MAAAF,KAAAD,KAAA;AACfI,YAAAA,KAAAC,OAAAA,KAAqBD,IAAST,KAAGE,IAAKL,KAAAW,IAAAV,KAASO,IAAGN,KAAK1O,KAAAmP,KAAA,GAAA1B,KAAAwB,KAAAD,KAAA;UAEvD;AAAA,UAAA/I,KAAA,IAAA,WAAgFyH,GAAa/T,KAAA,IAAAgV,EAAAA,CAAAA;AAAAA,eAAAA,OAAAA,GAAAA,KAAW,CAAA,EAAR/E,UAAa,IAAYwC,KAAA,IAAAna,WAAAyc,KAASjB,KAAA,CAAA,GAAAje,GAAAyW,IAAI9D,IAAGC,IAAAgK,IAAHsC,IAAajB,IAAAA,CAAAe,IAAAC,CAAAA,IAAA,CAAA,GAAAzQ,KAAAxO,GAAqBwd,IAAQ7K,IAARC,IAAagK,IAAAsC,IAAAjB,IAAAe,CAAAA,IAAAA,CAAAC,IAAA,CAAA,IAAA,IAAA,GAA/L,KAAA5E,KACAyF,aAAetC,IAAI7K,IAAAC,IAAQgK,IAAG,EACpB3K,GAAA+M,IAAA9K,GAAA+K,IAASlK,OAAGmK,IAAKtH,QAAAqG,GAAAA,CAAAA,IAAqBlG,GAASyF,IAAA7K,IAAGC,IAAKgK,IAAAsC,IAAAjB,IAAAe,CAAAA,IAAAA,CAAAC,IAAA,CAAA;QAAIrC,MAAAA,CAAAA,KAAAY,GACpD3d,MAAA,CAAA;AAAA,QAAA6B,GAAA,KAAA,EAC+BoY,MAAA,EAAqB7H,GAAA+M,IAAA9K,GAAA+K,IACzDlK,OAAKmK,IAAGtH,QAAKqG,GAAAA,GACmCjJ,KAAA4H,IAAAA,OAAAA,IAAAA,SAAAA,EAAAA,CAAAA;MAI5D;AAAA,UAEIkC,GACJ,MAAA3U,KAAA,GAAAA,KAAA4Q,GAAA1W,QAAA8F,MAAA;AAAA,YAAA,MAAAwM,KAAAjV,GAAAuC,EAAA,GAAA,MAAA;AAES,cAAA8b,KAAA9F,GAAAH,MACLkG,KADKjF,GAAA5Q,KAAA,CAAA,EAAA2P,MAELmG,KAFK1b,KAAA0E,IAAA8W,GAAA9N,GAAA+N,GAAA/N,CAAAA,GAGLiO,KAHK3b,KAAA0E,IAAA8W,GAAA7L,GAAA8L,GAAA9L,CAAAA,GAAAA,KAAAA,EAniBKjC,GAAAgO,IAAA/L,GAAAgM,IAAAnL,OAmiBLxQ,KAAAiM,IAAAuP,GAAA9N,IAAA8N,GAAAhL,OAAAiL,GAAA/N,IAAA+N,GAAAjL,KAniBKkL,IAAAA,IAAArI,QAwiBVrT,KAAAiM,IAAAuP,GAAA7L,IAAA6L,GAAAnI,QAAAoI,GAAA9L,IAAA8L,GAAApI,MAxiBUsI,IAAAA,GAAAA;AA8iBZnF,QAAAA,GAAiB5Q,KAAA,CAAA,EAAAiQ,UAAA,GACHjQ,KAAA,KAAA,KAAKgW,aAAAjC,IAAAvL,IAAAC,IAAAmI,IAAA5Q,KAAA,GAAA9I,IAAAgd,EACf8B,GAAAA,aAASjC,IAAAvL,IAAAC,IAAAmI,IAAA5Q,IAAA9I,IAAAgd,EACX;MAAA;AAAA,UAAAzL,KAEM;AACF,UAAQ,KAARsL,GAAA7Z,OAAAA,UAAAA,KACK,GAAApE,KAAI8a,GAAO1W,QAAOpE,MAAK;AAAA,YAAA0W;AAAA,QAAA/D,OAAA+D,KAAAjV,GACtBzB,EAAAA,GAAAA,KAAO8U,QAAIkF,GAAAH,KAAclC;MAG/B;AAGR,aAAAlW;IACI,EAhIgBwc,IAAAvL,IAAAC,IAAAwL,IAAAC,IAAAC,EAAAA,GAAAA,IAEI,CAAA,GAAYvC,IAAI,CAAA,GAAA,IAClC,CAAA;AAAA,QAEK,KAAPqE,IAAO;AACX,YAAAC,KAAA,CAAA;AAAA,WAAApgB,IAAA,GAAAA,IAAA8a,EAAA1W,QAAApE,IAAAogB,CAAAA,GAAA3a,KAAAqV,EAAA9a,CAAAA,EAAA+U,IAAArU,MAAAA;AAEE,YAAA2f,KAiVE,SAAAC,WAAarC,IACX;AAAA,YAAAsC,KAAA;AACA,iBAAAvgB,KAAU,GAAAA,KAAAie,GAAA7Z,QAAApE,KAAAugB,CAAAA,MAAAtC,GAAAje,EAAAA,EAAAwgB;AAAAA,cAAAA,KACR,IAAA,WAAkBD,EAAAA;AAAA,YAAAle,KAAA;AAAA,aAAAjB,KAClB,GAAIpB,KAAAA,GAAcoE,QAAApE,MAAA;AAAA,gBAAA+D,KAAS,IAAAvB,WAAUyb,GAAAje,EAAAA,CAAAA,GAAAA,KAAAA,GACrCoE;AAAAA,mBAAAA,KAAyB,GAAA8F,KAAUuW,IAAAvW,MAAA,GAAA;AACnC,gBAAA9I,KAAA2T,GAAI7K,EAAMoN,GAAAA,KAAAvC,GAAQ7K,KAAAA,CAAQxD,GAAAA,KAAAqO,GAAA7K,KAAA,CAAA;AAAA,kBAAAzI,KAAAsC,GAAQmG,KAAA,CAAA;AAClC,iBAAA1E,OAAIpE,KAAMkW,KAAA5Q,KAAA,IAAA1G,GAAAqC,KAAyB6H,EAAAA,IAAA9I,IAAUub,GAAA+D,KAAAxW,KAAA,CAAAoN,IAAAA,IAAAqF,GAAA+D,KAAAxW,KAAA,CAAA,IAAAxD,IAAAiW,GAAA+D,KAAAxW,KAAA,CAAA,IAAA1E;UAC7C;AAAA,UAAAnD,MAAAmD;QACA;AAAA,eAAAxF,GAA8BU;MAC9B,EA3VgB0f,EACpBO,GAAAA,KAAMC,SAAgBP,IAAGF,EAAAA;AAE7B,WAAAngB,IAAA,GAAAA,IAAA2gB,GAAA7E,KAAA1X,QAAApE,IAAA8b,GAAArW,KAAAkb,GAAA7E,KAAA9b,CAAA6gB,EAAAA,IAAAC,IAEW;AAAA,UAAAC,KAAA;AAAA,WACG/gB,IAAA,GAAAA,IAAQ8a,EAAA1W,QAAApE,KAAA;AACV,cAAUghB,MADMhH,IACtBc,EAAM9a,CAAAA,GAAoB+U,IAAA3Q;AAAA,YAC5B6c,IAAM,IAAA,WAAgBN,GAAAO,KAAAxgB,QAAAqgB,MAAA,GAAAC,MAAA,CAAA;AAAAE,UAAAzb,KAAAwb,CAEtB;AAAA,cAAIrI,KAAA,IAAApW,WAAAme,GAAAN,MAAAU,IAAAC,EAGAxC;AAAAA,aAAA3C,OAAO7B,EAAAjF,KAAAiF,EAAAH,KAAA/E,OAAAkF,EAAAH,KAAAlC,QAAAmE,GAAAlD,IAAAqI,CACXjH,GAAAA,EAAIjF,IAAAtI,IAAQmM,EAAAmI,GAAAA,MAAAC;MAAAA;IAAAA,MAAAA,MAIV9W,IAAO,GAAAA,IAAK4Q,EAAK1W,QAAA8F,KAAA;AAAA,UAAI8P,IAAIc,EAAA5Q,CAAA;AAAA,YAAAiX,KAAA,IAAA/a,YAAA4T,EAAAjF,IAAArU,MAAA;AAAA,UAAAue,IAAAjF,EAAAH,KAAA/E;AAAAA,UAErBqM,GAAA/c,QAAW6c,IAAA,IAAAze,WAAgBkc,CAAAA;AAAAwC,QAAAzb,KAAAwb,CAAK;AAAA,WAAAjhB,IAAK,GAAAA,IAAL0e,GAAc1e,KAAK;AAClD,cAAAuB,KAAA4f,GAAInhB,CACP;AAAA,YAAA,KAAAA,KAAOuB,MAAAA,GAASvB,IAAA,CAAA,EAAAihB,GAAAjhB,CAAAA,IAAAihB,EAAAjhB,IAAA,CAAK;iBAAAA,IAAAif,KAAS1d,MAAK4f,GAAKnhB,IAAKif,CAAAA,EAAAgC,GAAAjhB,CAAAA,IAAAihB,EAAAjhB,IAAAif,CAC3C;aAAA;AAAqB,cAAAmC,KAAAC,EAAK9f,EAAAA;AACpC,cAAiB,QAAjB6f,OAAyBC,EAAG9f,EAAAA,IAAA6f,KAAAtF,EAAA1X,QAAA0X,EAAArW,KAAAlE,EAAAA,GAAAua,EAAA1X,UAAA,KAAA;AAAE6c,YAAAjhB,CAAAohB,IAAAA;QAAqB;MAAnD;IACQ;AAAA,UAAA,IAEKtF,EAAA1X;AACLmR,SAAA,OAAgB,KAAhBgJ,MAAqBpL,IAAAoC,KAAA,IAAK,IAAeA,KAAK,IAAY,IAAYA,KAAU,KAAA,IAAA,GAC9EpC,IAAA7O,KAAAiM,IAAO4C,GAAAA,EAAAA;AAEjB,SAAAjJ,IAAA,GAAAA,IAAA4Q,EAAA1W,QAAA8F,KAAA;AAAA,OACW8P,IAAAc,EAAA5Q,CAAA2P,GAAAA,KAAA7H,GAAAgI,EAAAH,KAAA5F;AAAAgL,UAAAjF,EAAAH,KAAA/E;AAAA,YACXkJ,KAAAhE,EAAAH,KAAAlC;AAAAA,UAAAA,KAAAA,EAAAA;AAEkB,UAAGvR,YAAMmX,GAAA7c,MAAAA;AAAAA,UACvBmS,KAAAA,IAAUoM,GAAWlM,KAAM;AAAc,UAAAwC,KAAM,OAAU,KAAAgJ,GAAA;AAAI1L,QAAAA,KAC3DvO,KAAMwO,KAAIK,IAAA8L,IAAA,CAAA;AACZ,YAAItC,IAAA,IAAWna,WAAWqQ,KAAKmL,EAAAA;AAAAA,cAAsBsD,KAAAJ,EAAAhX,CAAAA;AACrD,iBAAS+J,KAAL,GAAWA,KAAK+J,IAAA/J,MAAA;AAAWjU,cAAAiU,KAAApB;AAAA,gBACxBvK,KAAA2L,KAAAgL;AACX,cAAA,KAAA9L,EAAA,UAAAnB,IAAA,GAAAA,IAAAiN,GAAAjN,IAAA2K,GAAA3c,IAAAgS,CAAAsP,IAAAA,GAAAhZ,KAAA0J,CAAAA;mBAAAA,KAAAA,EAAAA,MAAAA,IAAAA,GAAAA,IAAAA,GAAAA,IAAAA,GAAAA,KAAAA,KAAAA,EAAAA,KAAAA,GAAAA,KAAAA,CAAAA,KAAAA,IAAAA,KAAAA,IAAAA;mBAEuB,KAANmB,EAAc,MAAKnB,IAAA,GAAAA,IAAAiN,GAAAjN,IAAA2K,GAAA3c,KAAAgS,KAAA,EAAAsP,KAAAA,GAAAhZ,KAAA0J,CAAAA,KAAA,IAAA,KAAA,IAAAA;mBACpB,KAAZmB,EAAY,MAAoBnB,IAAA,GAAAA,IAAAiN,GAAAjN,IAAA2K,GAAA3c,KAAAgS,KAAA,EAAA,KAAAsP,GAAAhZ,KAAA0J,CAAA,KAAA,IAAA,KAAA,IAAAA;QAAAA;AAChCuL,QAAAA,KAAAA,GAAarK,IAAA,GAAAH,KAAoB;MAAA,WAAmB,KAAV4L,KAAU,KAAA7D,EAAA1W,QAAA;AAChDuY,YAAJ,IAAA,WAAiBsC,IAAAjB,KAAA,CAAA;AAAA,cAASpL,KAAAqM,IAAAjB;AAC1B,aAAAhe,IAAAA,GAAYA,IAAK4S,IAAA5S,KAAA;AAAS,gBAAA0T,KAAA,IAAA1T,GACtB8T,KAAJ,IAAA9T;AAAAA,YAAoB0T,EAAAA,IAAA6J,GAAAzJ,EAAAA,GAAA6I,EAAAjJ,KAAA,CAAA6J,IAAAA,GAAAzJ,KAAA,CAAA6I,GAAAA,EAAAjJ,KAAA,CAAA,IAAA6J,GAAAzJ,KAAA,CAAA;QAAA;AACpByJ,QAAAA,KAAAZ,GAAazJ,IAAK,GAAAH,KAAA,GAAAF,KAAA,IAAAoM;MAAAA;AAClBjF,QAAAjF,MAAAwI,IAAAvD,EAAAA,MAAqBnH,IAAAmH,EAAAjH,MAAAA;IAAAA;AAGvB,WAAA,EACEG,OAAAC,GAAAA,OAAAA,GAAU2I,MAAA7C,GAAAA,QAAa6B,EAEvB;EAAA;AAAA,WAAA,aAiEemD,IAAGvL,IAAAA,IAASoI,IAAA9a,IAAAA,IAAAoe,IAAK;AAAA,UAAAmD,KAAA/e,YAAAA,KAC9B4D,aAfSkZ,IAAA,IAAAiC,GAAAtD,GAAAje,KAAA,CAAA,CAAA,GAAAwhB,IAAA,IAAAC,GAAAxD,GAAAje,KAAA,CAiBX2c,CAAAA,GAAAA,IAAO3c,KAAG,IAAGie,GAAA7Z,SAAA,IAAAmd,GAAAtD,GAAAje,KAAA,CAAA,CAAA,IAAA,MAAA,IAAS,IAAAuhB,GAAAtD,GAAOje,EAAAA,CAAAA,GAAjC8e,IAAA,IAAA2C,GAAAlE,EAAA7c,MAGE;AAAA,QAAAgf,IAAAA,IAAoBH,IAAI5M,IAAOpC,IAAA,IAAAiP,IAAAA;AAC7B,aAAGvL,KAAH,GAAWA,KAAA7S,GAAQuW,QAAM1D,KAAG,UAAGjC,KAAH,GAAeA,KAAAA,GAAA8C,OAAc9C,MAAA;AAAG,YAAG0P,KAAHtgB,GAAA4Q,IAAkBA,IAAc2P,KAAGvgB,GAAK6S,IAARA,IAChG/J,KAAAyX,KAAAjP,KAAAgP,IAAAA,KAAAA,EAAAA,EACyB;AAAA,WAAAnM,MAA0B,KAAjBuF,GAAG9a,KAAA,CAASma,EAAAA,WAAKqH,EAAAtX,EAAAA,KAAAqL,OAAA,QAAAoH,KAAA,KAAAA,EAAA,IAAAzS,KAAA,CAAnDwX,OAAAA,KAAAhC,MAAAA,IAAAgC,KAAAA,KAAAnR,MAAAA,IAAAmR,KAAAA,KAAAA,MAAAA,IAAAA,KAAAA,KAAAA,MAAAA,IAAAA;IAAAA;AAAAA,UAEoDnR,MAAWmP,IAAAH,IAAShP,IAAAiP,IAAA,IAAIpB,OACxD,MAAA,IAATsB,MAASA,KAAA,MAAA,IAAAH,MAAAA,MAAAA,KAAI,EACxBvN,GAAA0N,GAAAzL,GAAAsL,GAAAzK,OAAAvE,IAAAmP,IAAA,GAAA/H,QAAA6H,IAAAD,IAAA,EAAA;AAAA,UAIQjH,IAAAwC,GAAO9a,EAAAA;AAAAsY,MAAAuB,OAAAzY,IAAAkX,EAAA8B,QAAA,GAAA9B,EAAAvD,MAAA,IAAAvS,WAAApB,GAAA0T,QAAA1T,GAAAuW,SAAA,CAAA,GAAY,KAAAtV,GAANrC,KAAA,CAAA,EAAMma,WAAAA,GAEZmF,GAAA5M,IAAKC,IAAA2F,EAAAvD,KAAA3T,GAAA0T,OAAA1T,GAAAuW,QAAAA,CAAAvW,GAAA4Q,GAAAA,CAAA5Q,GAAA6S,GAAA,CAAQ4L,GAAAA,aAAatC,GAAA7K,IAAAC,IAAA2F,EAAAvD,KAAA3T,EAAAA,KAAI0W,GAChCyF,GAAA7K,IAAAC,IAAA2F,EAAAvD,KAAA3T,GAAA0T,OAAA1T,GAAAuW,QAAAvW,CAAAA,GAAA4Q,GAAA5Q,CAAAA,GAAA6S,GAAA,CAAA;EAAA;AAAA,WAAA,aAEGsJ,IAAK7K,IAAAC,IAAAA,IAAAiP,IAAAA;AACf9J,IAAAA,GAAAyF,IAAA7K,IAAaC,IAAAgK,IAADiF,GAAY9M,OAAc8M,GAAKjK,QAAAA,CAAQiK,GAAD5P,GAAAA,CAAa4P,GAAQ3N,GAAA,CAC7E;EAAA;AAEM,WAAAY,YAASE,IAAApC,IAAAA,IAAAE,IAAAtQ,IAAAA,IAAAwb,IAAAA;AAAY,UAAA8D,KACnB,CAAK;AAAA,QAAA7d,IAAAzC,KACF,CAAA,GAAA,GAAA,GAAI,GAAO,CAAA;AAAA,UAAeiE,KAAAjE,KAAA,CAAAiE,EAA8BmN,KAAAA,KAAAE,KAAI,OAAmB,KAATE,QAAS+O,KAAA,CAAA,CAAA,IAAA3e,OAAOkB,KAAA,EAAAO,OAAA,EAAjG;AAAA,UAAAmd,IAAAlgB;AAAAA,aAEM7B,IAAK,GAALA,IAAW8hB,GAAE1d,QAAKpE,KAAA;AACxB,eAAAiU,KAAA,GAAAA,KAAAtB,IAAAsB,KAAA+N,aAAAzf,IAAAwS,IAAAd,IAAApB,IAAAE,IAAA+O,GAAA9hB,CAAAA,CAAAA;AAAAA,MAAAA,GAAAA,KAgBuC+hB,EAAO1E,QAAQ9a,IAAO8B,EAAAA,CAAAA;IAAAA;AAIrD,QAAAqP,GAEAuO,IAAA;AAAA,SAAA,IACI,GAAAjiB,IAAA6hB,GAAOzd,QAAApE,IAAA6hB,CAAAA,GAAA7hB,CAAAoE,EAAAA,SAAA6d,MAAAvO,IAAA1T,GAAAiiB,IAAAJ,GAAA7hB,CAAAA,EAAAoE;AAAAA,WAAAA,GAAAA,CAAkF;EAAA;AACxF,WAAA4d,YAAAzf,IAAAwS,IAAAd,IAAApB,IAAAA,IAAA/S,IAAAA;AAAAA,UAAAA,KAAAA,KAAAA;AACH,QAAAkE,KAAAvC,KAAAzB;AAIU,QAFpBuC,GAAA+R,EAAAA,IAAAxU,IAAAwU,MAEoB,KAAAxU,GAAAA,KAAAA,KAEZ,IAAK,UAAWkS,KAAA,GAAQA,KAAKa,IAAAb,KAAAzP,CAAAA,GAAA+R,KAAAtC,EAAAA,IAAA+C,GAAA/U,KAAAgS,EAC3BzP;QAAAA,CAAAA,GAAAkK,IAAA,IAAAjK,WAAauS,GAAAA,QAAS/U,IAAA6S,EAAAyB,GAAAA,EAAAA;aACtB,KAAAxU,IAAI;AAAA,WAAAyB,KAAA,GAAAA,KAAWwR,IAAKf,KAAAzP,CAAAA,GAAA+R,KAAAtC,EAAAA,IAAA+C,GAAA/U,KAAAgS,EAAAA;AAAAA,WAAAA,KAAAA,IAAIA,KAAAa,IAAKb,KAAAzP,CAAAA,GAAA+R,KAAAtC,EAAA+C,IAAAA,GAAA/U,KAAAgS,EAAAA,IAAA+C,GAAA/U,KAAAgS,KAAAe,EAAAA,IAAA,MAAA;IAAA,WAAvC,KAAAkB,IAAA;AACA,WAAAjC,KAAA,GAAAA,KAAAe,IAAAf,KAAAzP,CAAAA,GAAA+R,KAAAtC,EAAA+C,IAAAA,GAAA/U,KAAAgS,EAAAA;AAGQ,UAAA,KAAAlS,GAAA,MAAYkS,KAAAe,IAAWf,KAAIa,IAAAb,KAAWzP,CAAAA,GAAI+R,KAAAtC,EAAAA,IAAW+C,GAAI/U,KAAAgS,EAEzD;AAAA,UAAA,KAAAlS,GAAI,MAAUkS,KAAAe,IAAAf,KAAAa,IAAAb,KAAAzP,CAAAA,GAAA+R,KAAAtC,EAAAA,IAAA+C,GAAA/U,KAAAgS,EAAA+C,KAAAA,GAAA/U,KAAAgS,KAAAe,EAAAA,KAAA,KAAA,MAAA;AAEZ,UAAA,KAAAjT,GAAA,MAASkS,KAAIe,IAAAf,KAAAa,IAAAb,KAAAzP,CAAAA,GAAA+R,KAAAtC,EAAAA,IAAA+C,GAAA/U,KAAAgS,EAAAsJ,IAAAA,GAAAvG,GAAA/U,KAAAgS,KAAAe,EAAAA,GAAA,GAAA,CAAA,IAAA,MAAA;IAAA,OAAG;AAAA,UAAA,KAAA5P,GACZ,MAAK6O,KAAO,GAAAA,KAAAa,IAAAb,KAAAzP,CAAAA,GAAA+R,KAAAtC,EAAAA,IAAA+C,GAAA/U,KAAAgS,EAAA,IAAA,MAAA+C,GAAA/U,KAAAgS,KAAAa,EAAA,IAAA;AAAA,UAAA,KAAA1P,IAAA;AACF,aAAA6O,KAAA,GAAAA,KAAAe,IAAAf,KAAOzP,CAAAA,GAAS+R,KAAItC,EAAAA,IAAJ+C,GAAe/U,KAAAgS,EAAA,IAAA,OAAA+C,GAAA/U,KAAAgS,KAAAa,EAAAA,KAAA,KAAA;AAAA,aAAAtR,KAAAiE,IAC5CwM,KAAAa,IAAWb,KAAKzP,CAAAA,GAAI+R,KAAOtC,EAAAA,IAAA+C,GAAA/U,KAAAgS,EAAA,IAAA,OAAA+C,GAAA/U,KAAAgS,KAAAa,EAAAA,IAAAkC,GAAA/U,KAAAgS,KAAAe,EAAAA,KAAA,KAAA;MAC1B;AAAgB,UAAA,KAAAjT,IAAA;AAC5B,aAAAkS,KAAA,GAAAA,KAAAe,IAAAf,KAAAzP,CAAAA,GAAA+R,KAAAtC,EAAA+C,IAAAA,GAAA/U,KAAAgS,EAAAA,IAAA,MAAAsJ,GAAA,GAAAvG,GAAA/U,KAAAgS,KAAAa,EAAA,GAAA,CAAA,IAAA;AACA,aAAAb,KAAAe,IAAAf,KAAAa,IAAAb,KAAAzP,CAAAA,GAAA+R,KAAAtC,EAAA+C,IAAAA,GAAA/U,KAAAgS,EAAAA,IAAA,MAAAsJ,GAAAvG,GAAA/U,KAAAgS,KAAAe,EAAAgC,GAAAA,GAAA/U,KAAAgS,KAAAa,EAAAkC,GAAAA,GAAA/U,KAAAgS,KAAAe,KAAAF,EAAAA,CAAAA,IAAA;MAAA;IAAA;EAEA;AAEA,WAAA+N,SAAAP,IAAAF,IAAAA;AAAAA,UAAAA,KAAAA,IAAAA,WAAAA,EAAAA,GAAAA,KAAAA,GAAAA,MAAAA,CAEE1D,GAAAA,KAAS,IAAOrW,YAASyN,GAAGnT,MAAAA,GAAAA,KACVwhB,UAAKrO,IAAAsM,EACjBgC,GAAAA,KAAAC,GAAA,CAAA,GAAA3gB,KAAoB2gB,GAAA,CAGxB5b,GAAAA,KAAAuR,GAAA3T,QAGJ8c,KAAA,IAAA1e,WAAAgE,MAAA,CAAA;AAAA,QAAA;AAEE,QAAAuR,GAAA3T,SAAkB,IAAA,UACMpE,IAAA,GAAAA,IAAKwG,IAAAxG,KAAU,GAAA;AAErCuc,UAAA8F,WAAaF,IAFoD/gB,IAAA2W,GAApD/X,CAAAA,KAAA,IAAA,MAAAsX,IAAAS,GAAA/X,IAAA,CAAA,KAAA,IAAA,MAAA0G,IAAAqR,GAAA/X,IAAA,CAAA,KAAA,IAAA,MAAA,IAAmE+X,GAAA/X,IAAA,CAAA,KAAA,IAAA,IAGhFkhB,GAAAA,GAAAlhB,KAAAA,CAAAA,IAAcuc,EAAA0E,KAADxE,GAAYzc,KAAA,CAAAuc,IAAAA,EAAAsE,IAAAC;IAAO;QAG9B,MAAA9gB,IAAK,GAAAA,IAAOwG,IAAKxG,KAAA,GAAA;AAAA,UAAA,IACf+X,GAAM/X,CAAAA,KAAA,IAAA,MAAAsX,IAAAS,GAAA/X,IAAA,CAAA,KAAA,IAAA,MAAA0G,IAAAqR,GAAA/X,IAAA,CAAA,KAAA,IAAA,MACJwF,IAAAuS,GAAA/X,IAAA,CADI,KAAA,IAAA;AACQ,WAAAuc,IAAA4F,IAAA5F,EADR+F,OAAA/F,KAAAgG,SAAAhG,EAAAsE,KAAAzf,GAAAkW,GAAA5Q,GAAAlB,CAAAA,KAAA,IAAA+W,EAAA+F,OAAA/F,EAAAiG;AAAAA,MAAAA,GAAAA,KAAAA,CAC0BjG,IAAAA,EAAA0E,KAAAxE,GAAAzc,KAAA,CAAA,IAAAuc,EAAAsE,IAAAC;IAAAA;AAGhC,WAAA,EAAAT,MAAAxM,GAAAnT,QALewgB,MAAApF,IAAAA,MAAA2G,GAMf;EAAA;AAEA,WAAAP,UAAKvF,IAAKwD,IAAK/D,IAAAA;AAAAA,YAAAA,OAAAA,KAAAA;AAEvB,UAAAsG,KAAA,IAAAtc,YAAAuW,GAAAjc,MAAAA,GAIIyhB,KAAY,EAAA,IACL,GAAAzU,IAAAiP,GAAAvY,QAAAue,KAAA,MAAA9B,KAAA,MAAA+B,MAAA,GAAAN,MAAA,MAAAE,OAAA,KAAA;AAAA,IAAAngB,GAAA,MAAA,MAAAtC,IAAAsC,GAAA,IAAAA,GAAA,EAAA,GAAAA,GAAA,MAAA,OAAAA,GAAA,GAGT;AAAA,UAASogB,KAAA,CAAMN,EAAAA;AAAAA,WAAAA,GACK/d,SAAO+b,MAAA;AAAA,UACzB0C,KAAgB,GAChBC,KAAA;AAAA,eACY9iB,KAAA,GAAAA,KAAKyiB,GAAAre,QAAApE,KAAAyiB,CAAAA,GAAAziB,EAAA6gB,EAAAA,IAAAkC,IAAAF,OAAAA,KAAAJ,GAAAziB,EAAA6gB,EAAAA,IAAAkC,GAAAD,KAAA9iB;AAAAA,UACjB6iB,KAAYzG,GAAA;AACZ,YAAI4G,KAASP,GAAAK,EACX5O,GAAAA,KAAW+O,YAAAtG,IAAA+F,IAAAM,GAAAvV,IAAAuV,GAAAtV,IAAAsV,GAAAnC,IAAA9gB,GAAAijB,GAAAnC,IAAAqC,MAAAA;AAKb,UAHIF,GAAAvV,MAAAyG,MAAA8O,GAAAtV,MAAAwG,IAGA;AAAA8O,QAAAA,GAAanC,IAAAkC,IAAA;AAAA;MAAjB;AACA,YAAII,KAAK,EAAA,IAAcH,GAAAvV,IAAQC,IAAAwG,IAAQyO,KAAA,MAAA9B,KAAA,MAAA+B,MAAA,GAAAN,MAAA,MAAAE,OAAA,KAAA;AACvCW,MAAAA,GAAIR,MAAKS,MAALzG,IAAmBwG,GAAA1V,IAAA0V,GAAAzV,EAAAA,GAAAA,GAAAA,MAAE2V,OAAOF,GAAAR,GAAAA;AAAAA,YAAyBW,IAAA,EAA7D7V,IAAAyG,IAAAxG,IAAAsV,GAAAtV,IAAAiV,KAAA,MAAA9B,KAAA,MAAA+B,MAAA,GAAAN,MAAA,MAAAE,OAAA,KAAA;AACIc,QAAIX,MAAK,EAAAzM,GAAL,CAAiBE,GAAAA,GAAA,CAAAJ,GAAAA,GAAAgN,GAAAL,IAAA3M,IAAAmN,GAAAR,IAAA3M,EACnB;AAAA,WAAAhW,KAAI,GAAKA,KAAK,IAALA,KAAUsjB,GAAAX,IAAAzM,EAAAlW,EAAAgjB,IAAAA,GAAAL,IAAAzM,EAAAlW,EAAAA,IAAAmjB,GAAAR,IAAAzM,EAAAlW,EAAAA;AAAAA,WACdA,KAAA,GAAQA,KAAA,GAAGA,KAAIsjB,GAAIX,IAAAvM,EAAApW,EAAAA,IAAAgjB,GAAAL,IAAAvM,EAAApW,EAAAA,IAAAmjB,GAAAR,IAAAvM,EAAApW,EAAAA;AAAAA,QAAAA,MAAAA,OAAUsjB,EAAKX,GACvCK,GAAAA,GAAAV,OAAAa,IAASH,GAAIR,QAALc,GACdb,GAAAK,EAAAA,IAAAK,IAAAV,GAAAhd,KAAA6d,CAAAA;IACI;AACEb,IAAAA,GAAAjV,KAAIhI,CAAAA,IAAKkB,OAAAA,GAAAA,IAAAA,IAAYlB,GAAAmd,IAAA3M,CAAAA;AAAAA,SACjBhW,KAAA,GAAAA,KAAAyiB,GAAAre,QAAApE,KAAAyiB,CAAAA,GAAAziB,EAAAihB,EAAAA,MAAAjhB;AAAAA,WAAAA,CAAAA,IAAMyiB,EAAAA;EAAAA;AAAAA,WAAAA,WAAAA,IAEUrhB,IAAAkW,IAAAA,IAAA9R,IAC1B;AAAA,QAAA,QAAA+W,GAAA+F,KAAA,QAAA/F,GAAAqG,OAAAA,SAU0BW,KAAAA,IAAUniB,IAAAkW,IAAAA,IAAA9R,IAChC;AAAA,YAAAge,KAAApiB,KAAA+V,GAAA,CAAoBsM,GAAAA,KAAAnM,KAAAH,GAAA,CAAAuM,GAAAA,KAAAhd,KAAAyQ,GAAA,CAAA,GAAAnT,KAAOwB,KAAA2R,GAAA,CAAA;AAAA,aAAAqM,KAAAA,KAAAC,KAAAA,KAAAC,KAAAA,KAAAC,KAAAA;IAC3B,EAZJpH,GAAAsE,IAAA1J,GAAA/V,IAAAkW,IAAA5Q,IAAAlB,EAAA+W,GAAAA;AAAAA,UACIqH,KAAArB,SAAQhG,GAAAsE,KAAAzf,IAAAkW,IAAA5Q,IAAAlB,EAAAA;AAAAA,QAGRqe,KAAAA,GAAWvB,MAAAA,KACN/F,GAAIiG;AAAmBoB,IAAAA,KAAA,MAAAC,KAAatH,GAAAiG,OAAAsB,KAAAvH,GAAA+F;AAAAA,UAElBa,KAAAd,WAAUwB,IAAAziB,IAAAkW,IAAA5Q,IAAAlB,EAAAA;AAAAA,QACjC2d,GAAAP,QAAkBgB,KAAAA,GAAA,QAAAT;AAAAA,UAASG,KAAAjB,WAAUyB,IAAA1iB,IAAAkW,IAAA5Q,IAAAlB,EAAAA;AAAAA,WACrC8d,GAAAA,OAAkBH,GAAAP,OAAAU,KAAAH;EAAAA;AAAAA,WAClBZ,SAAkB1B,IAAAzf,IAAAkW,IAAAA,IAAA9R,IAAA;AAAA,UAAA,EAAAzF,GAAAA,GAAAA,IAAA8gB;AAAA,WAAA9gB,GAAA,CAAA,IAAAqB,KAAArB,GAAA,CAAAuX,IAAAA,KAAAvX,GAAA,CAAA,IAAA2G,KAAA3G,GAAA,CAAAyF,IAAAA,KAAAqb,GAAAkD;EAAAA;AAGlB,WAAAd,YAAetG,IAAA+F,IAAAjV,IAAAC,IAAAA,IAAAqW,IAAAA;AAAAA,SAAAA,MAAG,GACAtW,KAAAC,MAAA;AAClB,aAAKsW,OAALrH,IAAelP,IAAA1N,EAAAgkB,KAAAA,KAAAtW,CAAAA,MAAA;AAAA,aAAGuW,OAAArH,IAAAjP,IAAA3N,EAAAA,IAAAgkB,KAAArW,CAAAA,MAAA;AAAA,UAClBD,MAAUC,GAAA;AAGV,YAAI3J,KAAA2e,GAAAjV,MAAmB,CAAAiV;AAAAA,MAAAA,GAAAjV,MAAA,CAAA,IAAAiV,GAAAhV,MAAA,CAAAgV,GAAAA,GAAAhV,MAAA,CAAA,IAAA3J,IAAAA,MACC,GAAA2J,MAAA;IAAA;AAAA,WAAA,OACKiP,IAAUlP,IAAA1N,EAAAgkB,IAAAA,KAAAtW,CAAAA,MAAA;AACrC,WAAAA,KAAA;EAAA;AAAA,WAAA,OACUkP,IAAA3c,IAAQD,IAAAA;AAAAA,WAAAA,GAA2BC,EAAAD,IAAAA,GAAU,CAAA4c,IAAAA,GAAA3c,KAAA,CAAA,IAAAD,GAAA,CAAA,IAAA4c,GAAA3c,KAAA,CAAA,IAAAD,GAAA,CAAA,IAAA4c,GAAA3c,KAAA,CAAAD,IAAAA,GAAA,CAC7D;EAAA;AACI,WAAAqjB,MAASzG,IAALlP,IAAmBC,IAAAA;AAAAA,UAAAA,KACrB,CAAA,GAAM,GAAK,GAAA,GAAA,GAAA,GAAS,GAAA,GAAK,GAAA,GAAA,GAAA,GAAA,GAAA,GAAA,GAAA,CAAA,GAAArL,KACzB,CAAA,GAAA,GAAA,GAAkB,CAAA,GAAAmD,KAAKkI,KAAAA,MAAU;AAAA,aACjC1N,KAAAA,IAAAA,KAAkB0N,IAAA1N,MAAA,GAAA;AAAA,YAASoB,KAAAub,GAAA3c,EAAU,KAAA,IAAA,MAAAsX,KAAAqF,GAAA3c,KAAA,CAAA,KAAA,IAAA,MAAA0G,KAAAiW,GAAA3c,KAAA,CAAA,KAAA,IAAA,MAAAyB,KACrCkb,GAAAA,KAAAA,CAAAA,KAAkB,IAAA;AAAA,MAAAta,GAAA,CAA8BjB,KAAAA,IAAAgV,GAAA,CAAA,KAAUkB,IAAAlB,GAAA,CAAA1P,KAAAA,IAAA0P,GAAA,CAAA,KAAA5Q,IAClC0Q,GAAA,CAAA9U,KAAAA,KAAAA,IAAA8U,GAAU,CAAK9U,KAAAA,KAAAkW,IAAApB,GAAA,CAAA9U,KAAAA,KAAAsF,IAAAwP,GAAA,CAAA9U,KAAAA,KAAAoE,IACvC0Q,GAAA,CAAA,KAAUoB,KAAAA,IAAApB,GAAA,CAAA,KAAA9U,KAAYsF,IAAAwP,GAAAA,CAAAA,KAAMoB,KAAU9R,IAAmB0Q,GAAA,EAAA,KAAAxP,KAAUA,IAAAwP,GAAA,EAAAxP,KAAAA,KAAAlB,IACzE0Q,GAAA,EAAA1Q,KAAAA,KAAAA;IACI;AAAA,WAAAxF,GAAA,CAAA,IAAAA,GACE,CAAA,GAAAA,GAAA,CAAA,IAAkBkW,GAAA,CAAA,GAAAA,GAAA,CAAA,IAAAA,GAAA,CAAA,GAAAA,GAAA,EAAA,IAAAA,GAAA,CAAA,GAAAA,GAAA,EAAA,IAAAA,GAAA,CAAA,GAAAA,GAAA,EAAA,IAAAA,GAAA,EAAA,GAAA,EAClBA,GAAAA,IAAIE,GAAAA,IAAAA,GAAAA,GAAAA;EAAAA;AACJ,WAAAiN,OAAUD,IAAAA;AAAAA,UAAAA,EAAAA,GAAuBlN,GAAAkN,IAAAA,IAAAA,EACjChN,GAAAA,GAAUgN,IAAAA,IAAAA,EAAAA,GAAuBpN,GAAAA,IAAAoN,IAAAA,KAEjChN,GAAU,CAAA,GAAA6N,KAAQ7N,GAAI,CAAM8N,GAAAA,KAAS9N,GAAI,CAAA,GAAA+N,KAAA/N,GAAA,CAAA,GAAA7U,KAAM,KAAAyU,KAAA,IAAU,IAAAA,IAC/DoO,IAAA,CAEIlO,GAAA,CAAA,IAAUmO,KAAAA,KAAAC,IAAApO,GAAA,CAAA,IAAAmO,KAAAJ,KAAAK,IAAApO,GAAA,CAAAmO,IAAAA,KAAAH,KAAAI,IAAApO,GAAA,CAAA,IAAAmO,KAAAF,KAAAG,IAAAA,GAAAA,CACRL,IAAAA,KAAAA,KAAkBK,IAAApO,GAAA,CAAA,IAAA+N,KAAAA,KAAAK,IAAApO,GAAA,CAAA+N,IAAAA,KAAAC,KAAAI,IAAApO,GAAA,CAAA+N,IAAAA,KAAAE,KAAAG,IAAAA,GAAAA,CAAAA,IAAIJ,KAAAG,KAAUC,IAAApO,GAAA,CAAAgO,IAAAA,KAAAD,KAAAK,IAAApO,GAAA,EAAAgO,IAAAA,KAAAA,KAAAI,IAAApO,GAAA,EAAA,IAAAgO,KAAAC,KAAAG,IAAAA,GAAAA,EAChCH,IAAAA,KAAAA,KAAkBG,IAAApO,GAAA,EAAAiO,IAAAA,KAAAF,KAAAK,IAAApO,GAAA,EAAA,IAAAiO,KAAAD,KAAAI,IAAApO,GAAA,EAAAiO,IAAAA,KAAAA,KAAAG,EAAAA,GAAAA,IACqBF,GACvCxN,IAAA2N;AAAAA,QAAAA,IAAsD,CAAAjgB,KAAAkgB,OAAUlgB,GAAAA,KAAAkgB,OAAAlgB,GAAAA,KAAAkgB,OAAAlgB,GAAAA,KAAAkgB,OAAA1B,CAAAA,GAAAA,IAAA,GAAA,IAChE;AACN,QAAA,KAAA9M,GAEI,UAAAjW,KAAS,GAALC,KAAc,OAChB0G,IAAAkQ,EAAA6N,QAAS/N,GAAAhQ,CAAAA,GAAAA,IAAUpC,KAAAogB,KAAA9N,EAAA+N,IAAAje,GAAAA,CAAAA,CAAAA,GAAAA,IAAAkQ,EAAAgO,IAAA,IAAAC,GAAAne,CAAAA,GAAAA,EACT,KAAV1G,MAAUsE,KAAAwgB,IAAQD,IAAK/B,CAAA,IAAA,QAFJ9iB,KAEI8iB,KAAA+B;AAAAA,UAAAA,IAElB,CAAAR,KAAAC,IAAIL,KAAOK,IAAQJ,KAAKI,IAAAH,KAAAG,EAAAA;AACe,WAAA,EAAA,KAAA,GAAA,GAE1CvkB,GAAAA,GAAA2G,GAA2Bqc,GAAAD,GAA2BI,QAFtDtM,EAAA+N,IAAM/N,EAAKgO,IAAI,KAAAzN,CAAAzQ,GAAAA,CAAAA,GAGvBqd,KAAAnN,EAAA+N,IAAAje,GAAAyQ,CACM2J,GAAAA,OAAAxc,KAAA4V,MAAU,MAAK/C,EAAA,CAAA,CAAA,KAAA,KAAA7S,KAAA4V,MAAA,MAAA/C,EAAA,CAAA,CAAA,KAAA,KAAA7S,KAAA4V,MAAA,MAAA/C,EAAA,CAAA,CAAA,KAAA,IAAA7S,KAAA4V,MAAA,MAAA/C,EAAA,CAAA,CAAA,KAAA,OAAA,EAAA;EAAA;AAGf,MAAAoN,KAAA,EAAA,SAAA,CAAAxkB,IACEwO,OAAAA,CAAAA,GAAAA,CAAAA,IAAAA,GACA,CAAA,IAAAxO,GAAA,CAAA,IAAkBwO,GAAA,CAAA,IAAA6H,GAAA,CAAA,IAAA7H,GAAA,CAAA,IAAA6H,GAAA,CAAA,IAAA7H,GAAA,CAAA,GAAAxO,GAAA,CAAA,IAAAgE,GAAS,CAAAqS,IAAAA,GAAAA,CAAU7H,IAAAA,GAAA,CAAA6H,IAAAA,GAAA,CAAA7H,IAAAA,GAAA,CAAA6H,IAAAA,GAAA,CAAA,IAAA7H,GAAA,CAAA,GAAAxO,GAAA,CAAA,IAAAgE,GAChC,CAAA,IAAAqS,GAAA,CAAA,IAAQ7H,GAAG,CAAA,IAAI6H,GAAA,EAAA,IAAI7H,GAAA,CAAA,IAAA6H,GAAA,EAAA7H,IAAAA,GAAA,CAAA,GAAAxO,GAAA,EAAA,IAAAgE,GAAA,CAAKqS,IAAAA,GAAK,EAAA7H,IAAAA,GAAA,CAAL6H,IAAAA,GAAoB,EAAA7H,IAAAA,GAAA,CAAA6H,IAAAA,GAAA,EAAuB7H,IAAAA,GAAA,CAAA,CAAA,GAAA,KAAA,CAAAxO,IAAAgE,OAEjBiO,GAAA,CAAUiC,IAAAA,GAAA,CAAAjC,IAAAA,GAAA,CAAAiC,IAAAA,GAAA,CAAA,IAAAjC,GAAA,CAAA,IAAAiC,GAAA,CAAA,IAAAjC,GAAA,CAAA,IAAAiC,GAAA,CAAA,GACzE2Q,KAAAA,CAAApf,IAAAyO,OAAA,CAAAzO,KAAAyO,GAAA,CAAAzO,GAAAA,KAAAyO,GAAA,CAAA,GAAAzO,KAAAyO,GAAA,CAAAzO,GAAAA,KAAAyO,GAAA,CAAA,CAAA,EAAA;AAAA,OAAA,SAAA,SAAA,OA5aQgK,IAAMvL,IAAAA,IAAIyN,IAAAvC,IAAAA,IAAAW,IAAAA;AAAI,YAAA4B,OAAIA,KAAK,IACnB,QAAF5B,OAAOA,KAAE;AAEnB,UAAA5B,KAAA/E,UAAAqG,IAAAvL,IAAAC,IAAAwN,IAAA,CAAA,OAAA,OAAA,OAAA,GAAA5B,IAAA,KAAA,CAAA;AAEO,WADPT,YAAAnB,IAAAA,EACOD,GAAAA,MAAQC,IAAAjK,IAAUC,IAAGiL,IAAMpK,EAAAA;EAAAA,GAAAA,KAAAA,WAAAA,SAAAA,SACmByK,IAAEvL,IAAAC,IAAAA,IAAAoS,IAAA5R,IAAAA,IAAAK,IAAG;AAAA,UAAAmJ,KAAK,EAAAzJ,OAAQ,KAAU,KAAHqC,KAAW,IAAA,MAAA,KAAAwP,KAAA,IAAA,IAAA5R,OAAAA,IAAA8F,QAAA,CAAA,EAAA,GAE7D+L,MAAAzP,KAAAwP,MAAU5R,IAAAA,IAAAA,KAAUT;AAAsB,aAAA1S,KAAA,GAAAA,KAAAie,GAAM7Z,QAAQpE,KAAG2c,CAAAA,GAAA1D,OAAAxT,KAAA,EAAA,MAAA,EAC7EuM,GAAA,GAAAiC,GAAA,GAAAa,OAAOpC,IAAKiF,QAAGhF,GAAAA,GAA6BoC,KAAA,IAAAvS,WAAAyb,GAAOje,EAAAA,CAAAA,GAAAA,OAAAA,GAA7Dma,SAAA,GACApH,KAAAzO,KAAAwO,KAAAkS,KAAA,CACAnS,GAAAA,KAAAvO,KAAAwO,KAAAmS,IAAA,CAAA,EAAA,CAAA;AAAA,WAEyBnH,YAAAnB,IAAU,GAAA,IACjBD,GAAAA,MAAMC,IAAMjK,IAAFC,IAAQiL,IAAMpK,EAAAA;EAA1C,GA8aQ6H,KAAAvW,OAAA8S,WAAAA,WAAAA,KAAAA,OAAAA,SAA2BiE,QAAAA,KAAAA,WACF+E,UAAAA,KAAAA,SACzBsB,YAAUA,WAAAA,KAAAA,SAAqCG,aAAUA;AAtdsB,EAAA;ACpTnF,IAAA6C,IAAM,EAWNC,cAAMC,IAAAC,IACN;AAAA,QAAA3S,KAAK0S,GAAAtQ,OACLnC,KAAMyS,GAAAzN,QAGN2N,KAAM5S,MAAA,GACN6S,KAAMH,GAAAI,WAAA,IAAA,EAAAC,aAAA,GAAA,GAAA/S,IAAAC,EACN+S,GAAAA,KAAAA,IAAatf,YAAAmf,GAAAhjB,KAAA7B,MAEbilB,GAAAA,MAAM,KAAAjT,KAAA,MAAA,MAAA,GACNkT,KAAMD,KAAAhT,IACNkT,IAAM,MAAAD,IAEN9hB,IAAM,IAAAgiB,YAAAD,CAAAA,GACNllB,IAAK,IAAAC,SAAAkD,CAAAA,GACLiiB,IAAM,KAAA;AACN,MACM/T,GAAAzD,GAAA/I,GAAAH,GADN2gB,IAAMD,GACN9R,IAAA,GAAM/L,IAAA,GACN/E,IAAA;AAAA,WAAA,MAAApD,IAAA;AAAA,MAAA,UAAA,GAAAA,IAAA,IAAA,GAAA,KAAA;EAAA;AAAA,WAAA,MAAAA,IAAA;AAAA,MAAA,UAAA,GAAAA,IAAA,IAAA,GAAA,KAAA;EAAA;AAAA,WAAA,KAAAA,IAAA;AAAA,SAAAA;EAAA;AAKEkmB,QAAA,KAAA,GAAA,MAAA,CAAA,GAAA,KAAA,CAIEC,GAAAA,MAAA,GAGEA,GAAAA,MAAA,GACAA,GAAAA,MAAAxT,EAAAA,GAAAA,MAAAA,CAAAA,OAAAA,CAAAA,GAEVuT,MAAA,CAAA,GAAA,MAAA,EAAA,GAEAC,MAAA,CAAA,GAEMA,MAAAN,EAAAA,GAAAA,MAAAA,IAAAA,GAAAA,MAAAA,IAAAA,GAGNO,KAAA,CAAA,GAAaD,MAAA,QAAA,GAAA,MAnBR,KAuBDA,GAAAA,MAAA,GACEA,GAAAA,MAAA,UAAA,GAAA,MAAA,UAAA,GAAA,SAAA,UAKA;AAAA,WAAAjS,IAAKtB,MAAAqT,IAAU,KAAW;AAI5B,WAAA,IAHE,MAAO/R,IAAA0R,IACb3T,IAAA,GAEIA,IAAAA,KAAAA,MAAJzD,IAAAmX,GAAAviB,GAAAA,GAAAA,IAAAA,MAAAA,IAvFoBxC,EAAAylB,UAAA/gB,IAAA2M,GAAAzD,KAAA,IAAA/I,CAAAA,GAqGXwM,KAAA;AAEHiC;IAAAA;AACN9Q,QAAAuiB,GAAAthB,UAxGoB4hB,IAAAD,GAgLZM,WAAAC,SAAApB,EAAAqB,IAAAA,KAAAA,GAAAA,CAAAA;EAAAA,EA1JE;AAAA,GAAA,OAAAxmB,IAAAgE,IAAA;AAAA,OAAA,cAAAhE,IAAA,CAAAA,OAAA;AAAA,IAAAgE,GAAA,IAAA,KAAA,CAAAhE,EAAA,GAAA,EAAA,MAAA,YAAA,CAAA,CAAA;EAAA,CAAA;AAAA,GAAA,MAAA,EAAA;AC/BV,IAAeymB,IAAA,EACbC,QAAA,UACAC,SAAA,WACAC,gBAAA,kBACAC,IAAA,MACAC,KAAA,OAAA,KACK,MAAA;AANP,ICOGC,IAAA,EAAA,CAAA,EACAL,MAAAA,GAAkB,OAAA,CAAA,EAClBC,OAAAA,GAAmB,OACnBF,CAAAA,EAAAG,cAAAA,GAAmB,OAAA,CAAA,EAAA,EAAA,GAAA,MAVtBH,CAAAA,EAAAK,GAAAA,GAAA,MAAA,CAAA,EAAA,GAAA,GAAA,KCMA;AAAA,IAAME,IAAAA,eAAAA,OAAcC;AAApB,IAGMC,IAAe,eAAA,OAAAC,qBAAsCC,gBAAmBD;AAH9E,IAeOE,IAA4BL,KAAAC,OAAAK,WAAmBL,OAAAK,QAAeC,WAAYN,OAAAK,QAAAC,QAAA,sBAAA;AAfjF,IAgBaC,cAASR,KAAYE,OAAAG,KAAAA,EAAAI,kBAAAR,QAAA,MAAA,KAAA,eAAA,OAAAS,QAAAA;AAhBlC,IAiBIC,oBAAYX,KAAcE,OAAAG,KAAAA,EAAAI,kBAAAR,QAAA,YAAA,KAAA,eAAA,OAAA1mB,cAAAA;AAAAA,SAAAA,mBAAAA,IAAAA,IAAAA,KAAAA,KAAAA,IAS1B,GAAA;AAAA,SAAA,IAAKb,QAAOU,CAAAA,OACZ;AAAA,UAAAqO,KAAKmZ,GAAAC,MAAe,GAAA,GACpBC,KAAQrZ,GAAA,CAAAsZ,EAAAA,MAAA,SAAA,EAAA,CAAA,GAcZC,KAAAC,WAAAC,KAAAzZ,GAAA,CAAA,CAAA;AACA,QAAAnI,KAAA0hB,GAAA3jB;AAAAA,UAAAA,KAAAA,IAAAA,WAAAA,EAQO;AAAA,WAAAiC,OACL6hB,CAAAA,GAAAA,EAAAA,IAAOH,GAAAvgB,WAAsBnB,EAAAA;AAAAA,UAE3BvC,KAAAA,IAAApC,KAAAA,CAAAA,EAAsB,GAAA,EAAA5B,MAAQ+nB,GAAAA,CAAAA;AAAAA,IAAAA,GAC9BlkB,OAAAA,IAAAA,GACAwkB,eAAAA,IACJhoB,GAAA2D,EAAAA;EAAAA,CAAAA;AA8BA;AAQI,SAAAskB,mBAA2BtkB,IAAAA;AAC/B,SAAA,IAAarE,QAAAU,CAAAA,IAA4BC,OAAAA;AACrC,UAAAC,KAAA,IAAcqnB;AAClBrnB,IAAAA,GAASgoB,SAAIloB,MAAAA,GAAAA,GAAAM,MAAAA,GACTJ,GAAAioB,UAAcvoB,CAAAA,OAAAK,GAAaL,EAAAA,GAC/BM,GAAAkoB,cAAAzkB,EAAAA;EAAAA,CAAAA;AAEE;AAkBA,SAAW0kB,UAAQ7X,IAAAA;AACnB,SAAA,IAAclR,QAAAU,CAAAA,IAAiBC,OAAAA;AAE/B,UAAO2U,KAAA,IAAA;AAAA,IAAA/U,GACLqoB,SAAAA,MAAAA,GAAuBtT,EAAAA,GAAAA,GACvBuT,UAAAA,CAAAA,OAAAA,GAAwBvoB,EACxBgV,GAAAA,GAAApE,MAAIA;EAAAA,CAAAA;AAEF;AAYN,SAAA8X,iBAAAA;AAAAA,MAAAA,WAAAA,eAAAA,aAQA,QAAgBA,eAAAC;AAEd,MAAAC,KAAInC,EAAAoC;AAAAA,QACAC,EAAAA,WAAAA,GAAAC,IAAAA;AAeN,SAdI,gBAAaC,KAAAF,EAAAA,IACbF,KAAMnC,EAAAC,SACF,kBAAcsC,KAAAF,EAAAA,KAAA,UAAAE,KAAAF,EAAAA,IAAAA,KACVrC,EAAUK,MACtB,UAAAkC,KAAAF,EAAAA,IACAF,KAAcnC,EAAAG,iBACV,WAASoC,KAAAA,EAAAA,IACTJ,KAAMnC,EAAAE,WACV,QAAAqC,KAAAF,EAAA,KAAA,QAAA,CAAA,CAAAG,SAAAC,kBACEN,KAAAnC,EAAeI,KAIf6B,eAAgBC,eAAAC,IAClBF,eAAAC;AAAAA;AAAAA,SAAAA,2CAAAA,IAAAA,IAAAA;AAyBA,QAAAtnB,KAAwBqnB,eAClBS,GAAAA,KAAApC,EAAkC6B,EAAAA;AAExC,MAAA7T,KAAAqU,IACExR,KAAAA,IAKE3W,KAAA8T,KALmB6C;AAUrB,QAAAyR,KAAOtU,KAAM6C,KAAAA,KAAA7C,KAAAA,KAAA6C;AAAAA,SAAAA,KAAAA,KAAAA,MAAAA;AASf,UAAsB0R,MAAAH,KAAiCpU,MAAA,GAAhDwU,MAAAJ,KAAAvR,MAAA;AAAA,IAAA5X,KAAAgE,MAAAZ,KAAAY,IAAAyB,KAAAzB,KAAAC,OAsBL2T,KAAA0R,KAAOD,IAAAA,KAAAA,KAAAA,KAAAA,KAAAA;EAAAA;AAdL,SAAA,EAAA,OAAA5jB,IAAA,QAAArC,GAAA;AAAA;AAAA,SAAA,mBAAApD,IAAAgE,IAAA;AAOE,MAAAqhB,IAAAA;AAEN,MAAA;AAAA,QAAAhkB,KAAA,IAAA,gBACY0T,IAAA6C,EAAAA,GAAAA,KAAAA,GAAAA,WAAAA,IAAAA,GAAAA,SAAAA,GAAAA,OAAAA,IAAAA,MAAAA,4CAAAA;EAAAA,SAAAA,IAAAA;AAAAA,IAAAA,KAAAA,SAAAA,cAAAA,QAAAA,GAAAA,KAAAA,GAAAA,WANgB,IAAA;EAAA;AAAA,SAAAvW,GAAA,QAAArB,IAAAqB,GAAA,SAAA2C,IAAA,CAAA3C,IAAApB,EAAA;AAAA;AAEf,SAAAupB,kBAAAxU,IAAAyU,IAKb;AAAA,QAAA,EAAA1U,OAAAA,IAAAA,QAAA6C,GAAAA,IAAA8R,2CAAA1U,GAAAD,OAAAC,GAAA4C,MAAAA,GAAAA,CAAAA,IAAAA,EAAAA,IAAAA,mBAAAA,IAAAA,EAAAA;AAAAA,SAAAA,MAAAA,QAAAA,KAAAA,EAAAA,MAAAA,GAAAA,YAAAA,SACA+R,GAAAC,SAAA,GAAA,GAAAvE,GAAAtQ,OAAAsQ,GAAAzN,MAjBI+R,IAAAA,GAAAE,UAAI7U,IAAA,GAAA,GAAWqQ,GAACtQ,OAAasQ,GAAgBzN,MAAAA,GAAAA;AAEjD;AAAA,SAAA,QAAA;AAAA,SAAA,WAAA,MAAA,iBAeAkS,MAAAnB,eAAA,CAAA,kBAAA,oBAAA,kBAeO,QAAA,UAAA,MAELoB,EAAAA,SAAIhB,UAAAiB,QAAAA,KAAAA,UACUlB,UAAOiB,SAAP,KAAA,KAAkB,eAAAd,OAAAA,YAAA,gBAAAA,WAAAA,MA/BvBN;AAAAA;AAAAA,SAAAA,iBAAAA,IAAAA,KAAAA,CAAAA,GAAAA;AAAAA,SAAAA,IAAAA,QAAAA,SAAAA,IAAAA,IAAAA;AAAAA,QAAAA,IAAAA;AAAAA,QAAAA,cAAAA,WAAAA;AAAAA,UAAAA;AAAAA,eA6CAtD,KAAAmE,kBAAAxU,IAAAiV,GAAAR,YAAA1lB,GAAAhE,IAAAA,GAAAA,GAAAA,CAAAA,IAAAA,EAAAA,CAAAA;MAAAA,SAAAA,IAAAA;AAAAA,eAAAA,GAAAA,EAAAA;MAAAA;IAAAA,GAAAA,eAAAA,SAAAA,IAAAA;AAAAA,UAAAA;AAAAA;AAAAA,YAAAA,eAAAA,SAAAA,IAAAA;AAAAA,cAAAA;AAAAA,kBAAAA;UAAAA,SAAAA,IAAAA;AAAAA,mBAAAA,GAAAA,EAAAA;UAAAA;QAA6B;AAAA,YAAA;AAAA,cAAAiE;AAAA,iBAAA,mBAAAhE,EAAA,EAAA,KAAA,SAAAA,IAAA;AAAA,gBAAA;AAAA,qBAAAgE,KAAAhE,IAAA,UAAAgE,EAAA,EAAA,KAAA,SAAAhE,IAAA;AAAA,oBAAA;AAAA,yBAAAyF,KAAAzF,IAAA,WAAA;AAAA,wBAAA;AAAA,6BAAA,YAAA;oBAAA,SAAAA,IAAA;AAAA,6BAAAsC,GAAAtC,EAAA;oBAAA;kBAAA,EAAA;gBAAA,SAAAA,IAAA;AAAA,yBAAA,aAAAA,EAAA;gBAAA;cAAA,GAAA,YAAA;YAAA,SAAAA,IAAA;AAAA,qBAAA,aAAAA,EAAA;YAAA;UAAA,GAAA,YAAA;QAAA,SAAAA,IAAA;AAAA,uBAAAA,EAAA;QAAA;MAAA,SAAAA,IAAA;AAAA,eAAAsC,GAAAtC,EAAA;MAAA;IAAgB;AANpD,QAAA;AAAA,UAAA,MAAA,KAAA,CAAA,EAAA,gBAAA,EAAA,aAAA,EAAA,SAAA,eAAA,CAAA,EAAA,OACK,IAAAkqB,MAAA,0CAAA;AAAA,aACEC,kBAAMpmB,EAAAA,EAAAA,KAAAA,SAAAA,IAAAA;AAAAA,YAAAA;AAAAA,iBAAAA,KAAAA,IAAAA,YAAAA;QAAAA,SAAAA,IAAAA;AAAAA,iBAAAA,aAAAA;QAAAA;MAAAA,GAAAA,YAAAA;IAIuC,SAAA/D,IAAA;AAAA,mBAAA;IAAA;EAAA,CAAA;AAAA;AAAA,SAAA,aAAAA,IAAAgE,IAAA/D,IAAAqC,IAAAmD,KAAA,GAAA;AAAA,SAAA,IAAA,QAAA,SAAArC,IAAA1B,IAAA;AAAA,QAAAuC;AAAA,QAAA,gBAAAD,IAAA;AAAA,UAAAxC,IAAA,GAAA;AAAA,aAAAA,KAAAxB,GAAA,WAAA,IAAA,GAAA,EAAA,MAAA,EAAA,IAAAwB,GAAA,aAAA,GAAA,GAAAxB,GAAA,OAAAA,GAAA,MAAA,GAAA,IAAA,KAAA,OAAA,CAAA,EAAA,MAAA,GAAAA,GAAA,OAAAA,GAAA,QAAA,OAAAyF,EAAA,GAAAxB,KAAA,IAAA,KAAA,CAAA,CAAA,GAAA,EAAA,MAAAD,GAAA,CAAA,GAAAC,GAAA,OAAAhE,IAAAgE,GAAA,eAAA3B,IAAA,MAAA,KAAA,IAAA;IAAA;AASxD;AAaE,UAAA,QAAA,WAAA;AAAA,eAAA,MAAA,KAAA,IAAA;MAAA;AAbF,UAAA,gBAAAmnB,GAAAA,QAAAA,IAAAA,QAAAA,CAAAA,OAAAA,EAAAA,OAAAA,IAAAA,EAAAA,CAAAA,EAAAA,MAAAA,SAAAA,IAAAA;AAAAA,YAAAA;AAAAA,iBAAAA,KAAAA,IAAAA,GAAAA,OAAAA,IAAAA,GAAAA,eAAAA,IAAAA,MAAAA,KAAAA,IAAAA;QAAAA,SAAAA,IAAAA;AAAAA,iBAAAA,GAAAA,EAAAA;QAAAA;MAAAA,GAAAA,KAAAA,IAAAA,GAAAA,EAAAA;AAAAA;AAaE,YAAA,QAAA,WAAA;AAAA,iBAAA,MAAA,KAAA,IAAA;QAAA;AAbFA,YAAAA,cAAAA,OAAAA,mBAAAA,cAAAA,gBAAA,QAAApE,GAAA+E,cAAA,EAAArqB,MAAA0pB,IAAAY,SAAAC,GAAAA,CAAAA,EAAAA,MAAAA,SAAAA,IAAAA;AAAAA,cAAAA;AAAAA,mBAAAvmB,KAAAwmB,IAAAA,GAAAA,OAAAA,IAAAA,GAAAA,eACSC,IAAAA,MAAAA,KAAAA,IAAAA;UAAAA,SAAAA,IAAAA;AAAAA,mBAAAA,GAAAA,EAAAA;UAAAA;QAAAA,GAAAA,KAAAA,IAAAA,GAAAA,EAAAA;AAAAA;AAAAA,cAAAA;AAQF,iBAAA,IAAAxqB,GAAA,UAAAgE,IAAAyB,EAAA,GAAA,mBAA6BmiB,GAAQ6C,IAAAD,EAArCF,EAAAA,MAAAA,SAAAA,IAAAA;AAAAA,gBAAAA;AAAAA,qBAAAvmB,KAAA2mB,IAAAA,MAAAA,KAAAA,IAAAA;YAAAA,SAAAA,IAAAA;AAAAA,qBAAAA,GAAAA,EAAAA;YAAAA;UAAAA,GAAAA,KAAAA,IAAAA,GAAAA,EAIL;QAAA;MAAA;IAAA;AAAA,aAAA,QAAA;AAEA,aAAA3mB,GAAAA,EAAAA;IAAAA;EAAAA,CAAAA;AAAAA;AAUM,SAAA4mB,oBAAAA,IAAAA;AAAAA,EAAAA,GAAAA,QAQyB,GAAzBtF,GAAAzN,SAAA;AAAA;AAAA,SAAA,6BAAA;AAAA,SAAA,IAAA,QAAA,SAAA5X,IAAAgE,IAAA;AAAA,QAAA3C,IAAApB,IAAAqC,IAAAmD,IAAArC;AAAA,WAAA,WAAA,2BAAA,eAAApD,GAAA,2BAAA,YAAA,KAAAqB,KAAA,2ZAAA,mBAAA,2ZAAA,YAAA,KAAA,IAAA,CAAA,EAAA,KAAA,SAAAA,IAAA;AAAA,UAAA;AAAA,eAAApB,KAAAoB,IAAA,iBAAApB,EAAA,EAAA,KAAA,SAAAoB,IAAA;AAAA,cAAA;AAAA,mBAAAiB,KAAAjB,GAAA,CAAA,GAAA,aAAAiB,IAAArC,GAAA,MAAAA,GAAA,MAAAA,GAAA,YAAA,EAAA,KAAA,SAAAoB,IAAA;AAAA,kBAAA;AAAA,uBAAAoE,KAAApE,IAAA,oBAAAiB,EAAA,GAAA,iBAAAmD,EAAA,EAAA,KAAA,SAAApE,IAAA;AAAA,sBAAA;AAAA,2BAAA+B,KAAA/B,GAAA,CAAA,GAAA,2BAAA,eAAA,MAAA+B,GAAA,SAAA,MAAAA,GAAA,QAAApD,GAAA,2BAAA,YAAA;kBAAA,SAAAA,IAAA;AAAA,2BAAAgE,GAAAhE,EAAA;kBAAA;gBAAA,GAAAgE,EAAA;cAAA,SAAAhE,IAAA;AAAA,uBAAAgE,GAAAhE,EAAA;cAAA;YAAA,GAAAgE,EAAA;UAAA,SAAAhE,IAAA;AAAA,mBAAAgE,GAAAhE,EAAA;UAAA;QAAA,GAAAgE,EAAA;MAAA,SAAAhE,IAAA;AAAA,eAAAgE,GAAAhE,EAAA;MAAA;IAAA,GAAAgE,EAAA;EAAA,CAAA;AAAA;AAyBR,SAAA4mB,mBAAA7mB,IAAAA;AAAAA,SAAAA,IACMrE,QAAeU,CAAAA,IAAKC,OAAAA;AACpB,UAAAC,KAAa,IAAAqnB;AACbrnB,IAAAA,GAAAgoB,SAAOtoB,CAAAA,OAAAA;AACL,YAAAY,KAAA,IAAIC,SAAAb,GAAAS,OAAeC,MAAAA;AAAAA,UAAyC,SAAAW,GAAA,UAAT,GAAA,KAAA,EACnD,QAAAjB,GAAAA,EAEA;AAAA,YAAAiE,KAAIzD,GAAA6f;AACF,UAAA3f,KAAA;AAAA,aAAAwB,KAAArC,MACS;AACnB,YAAAW,GAAAG,UAAAD,KAAA,GAAA,KAAA,KAAA,EAAA,QAAAV,GAAAA,EAEU;AAAA,cAAAY,KAAAJ,GAAMG,UAASD,IAAAA,KAEf;AAAA,YAAAwB,MAAA,GAAA,SAAAtB,IAAa;AAAA,cACH,cAAAK,GAAA,UAAVP,MAAU,GAAA,KAAA,EAAA,QAAAkD,GAAA,EACL;AAGf,gBAAA6mB,KAAA,SAAAjqB,GAAAG,UAAAD,MAAA,GAAA,KAAA;AACAA,UAAAA,MAAAF,GAAAM,UAAAJ,KAAA,GAAA+pB,EAAAA;AAAAA,gBACAC,KAAelqB,GAAKG,UAADD,IAAA+pB,EACT/pB;AAAAA,UAAAA,MAAA;AAAA,mBACKb,KAAA,GAAAA,KAAA6qB,IAAA7qB,KACL,KAAiC,OAAjCW,GAAAG,UAAAA,KAAyB,KAAVd,IAAkB4qB,EAC3C,EAAA,QAAAzqB,GAAAQ,GAAAG,UAAAD,KAAA,KAAAb,KAAA,GAAA4qB,EAAAA,CAAAA;QAGA,OAAA;AAAA,cAAA,UAAA,QAAA7pB,IACI;AAEJF,UAAAA,MAAAF,GAAAG,UAAAD,IAAAA,KACA;QAAA;MAAA;AAQA,aAAgBV,GAAAA,EAAA;IAAA,GAEdE,GAAQioB,UAAWvoB,CAAAA,OAAAK,GAAAL,EACnBM,GAAAA,GAAQsB,kBAAqBmC,EAAAA;EAAAA,CAI7B;AAAA;AASI,SAAAgnB,uBAA2B1F,IAAU4E,IACrC;AAAA,QAAA,EAAAlV,OAAAA,GAAAA,IAAAsQ,IACNzN,EAAAA,QAAAA,GAAAyN,IAAAA,IAAAA,EACI2F,kBAAAA,GAAAA,IAAsBf;AAM1B,MAAAxkB,IAAAwlB,KAAA5F;AA8BI,SAjCJ6F,SAAAF,EAAAjW,MAAAA,KAAAiW,MAAApT,KAAAoT,QAeEC,CAAAA,IAAmBtB,EAAAwB,IAAAA,mBAAApW,IAAA6C,EAAAA,GAEnB7C,KAAO6C,MAGPqT,GAAIlW,QAAAiW,IACFC,GAAArT,SAAkBA,KAAA7C,KAAAiW,OAEbC,GAAAlW,QAAAA,KAAA6C,KAAAoT,IACLC,GAAArT,SAAAoT,KAEJrB,GAAAE,UAAAxE,IAAA,GAAA,GAAA4F,GAAAlW,OAAAkW,GAAArT,MAAAA,GAII+S,oBAAKtF,EAAAA,IAGA4F;AAAAA;AAWH,SAAAG,sBAAA/F,IAAAgG,IACF;AAAA,QAAA,EAAAtW,OAAKA,GAAAA,IAAAsQ,IAAAA,EAAAA,QAAAA,GAAAA,IAAAA,IAAAA,CAGA4F,IAAAtB,EAAAA,IAAAwB,mBAAApW,IAAA6C,EAAAA;AAAAA,UAGLyT,KAAA,KAAAA,KAAA,KACEJ,GAAAlW,QAAA6C,IAAAA,GAAAA,SAAAA,OAKJqT,GAAAlW,QAAAA,IAEAkW,GAAOrT,SAAAA,KAAAA,IAAAA;IAAAA,KAAAA;AAAAA,MAAAA,GAAAA,UAAAA,IAAAA,GAAAA,GAAAA,GAAAA,IAAAA,CAAAA;AAAAA;IAAAA,KAAAA;AAAAA,MAAAA,GAAAA,UAAAA,IAAAA,GAAAA,GAAAA,IAAAA,IAAAA,EAAAA;AAAAA;IAAAA,KAAAA;AAAAA,MAAAA,GAAAA,UAAAA,GAAAA,GAAAA,GAAAA,IAAAA,GAAAA,EAAAA;AAAAA;IAAAA,KAAAA;AAAAA,MAAAA,GAAAA,UAAAA,GAAAA,GAAAA,GAAAA,GAAAA,GAAAA,CAAAA;AAAAA;IAAAA,KAAAA;AAAAA,MAAAA,GAAAA,UAAAA,GAAAA,GAAAA,IAAAA,GAAAA,IAAAA,CAAAA;AAAAA;IAAAA,KAAAA;AAAAA,MAAAA,GAAAA,UAAAA,GAAAA,IAAAA,IAAAA,GAAAA,IAAAA,EAAAA;AAAAA;IAAAA,KAAAA;AAAAA,MAAAA,GAAAA,UAAAA,GAAAA,IAAAA,GAAAA,GAAAA,GAAAA,EAAAA;EAAAA;AAAAA,SAAAA,GAAAA,UAAAA,IAAAA,GAAAA,GAAAA,IAAAA,EAAAA,GAAAA,oBAAAA,EAAAA,GAAAA;AAAAA;AAAAA,SC9YPC,SAAA9T,IAAAkmB,IAAAqB,KAAA,GAAA;AAAA,SAAA,IAAA,QAAA,SAAArrB,IAAAqC,IAAA;AAAA,QAAAmD,IAAArC,IAAA1B,IAAAuC,IAAAzC,IAAA,GAAA,GAAA,GAAA,GAAA,GAAA,GAAA,GAAA,GAAA,GAAA,GAAA,GAAA,GAAA,GAAA,GAAA;AAGwB,aAAA+pB,YAAAC,KAAA,GAAA;AAAA,UAAAxnB,GAAA,UAAiCimB,GAAAwB,OAAAC,QAAAA,OAAAA,GAAAA,OAAAA;AAAAA,MAAAA,MAAAA,IAAAA,GAAAA,WAAAA,KAAAA,IAAAA,IAAAA,GAAAA,CAAAA;IAAAA;AAAAA,aAAAA,YAAAA,IAAAA;AAAAA,UAAAA,GAAAA,UAAAA,GAAAA,OAAAA,QAAAA,OAAAA,GAAAA,OAAAA;AAAAA,MAAAA,KAAAA,KAAAA,IAAAA,KAAAA,IAAAA,IAAAA,EAAAA,GAAAA,GAAAA,GAAAA,GAAAA,WAAAA,EAAAA;IAAAA;AAKrD,WALoBC,KAAAL,IAAAA,KAAAA,GAAAA,gBAAAA,IAAAA,KACxB,OAAAtnB,GAAA4nB,YAAA,MAAA,YAIIC,GAAAA,iBAAU9nB,IAAAkmB,EAAAA,EAAVK,MAAAA,SAAAA,IAAAA;AAAAA,UAAAA;AAAAA,eAAAwB,CAAAA,EAAAA,EAAAC,IAAAA,IAAAA,YAAAA,GAAAA,KAAAA,uBAIaD,IAAA7B,EAAAA,GAGXsB,YAAAA,GAAAA,IAAAA,QAAAA,SAAAA,IAAAA,IAAAA;AAAAA,cAAAA;AAAAA,cAAAA,EAAAA,KAAAA,GAAAA,iBAGJ,QAAAX,mBAA8B7mB,EAAAA,EAA9BumB,MAAAA,SAAAA,IAAAA;AAAAA,gBAAAA;AAAAA,qBAAA0B,KAAAA,IAAAA,MAAAA,KAAAA,IAAAA;YAAAA,SAAAA,IAAAA;AAAAA,qBAAAA,GAAAA,EAAAA;YAAAA;UAAAA,GAAAA,KAAAA,IAAAA,GAAAA,EAAAA;AAAAA,mBAAAA,QAAAA;AAAAA,mBAAAA,GAAAA,EAAAA;UAAAA;AAAAA,iBAAAA,MAAAA,KAAAA,IAAAA;QAAAA,CAAAA,EAAAA,MAAAA,SAAAA,IAAAA;AAAAA,cAEA;AAAA,mBAAA,IAAA3qB,IADAkqB,YACAU,GAAAA,2BAAAA,EAAA3B,MAAAA,SAAAA,IAAAA;AAAAA,kBAAAA;AAAAA,uBAAA4B,IAAAC,KAAYC,KAA8BhB,sBAAAgB,IAAAf,CAAAA,GAC9CE,YAKIlB,GAAAA,IAAAJ,GAAAoC,kBAAA,GAEJC,IAAArC,GAAAR,YAAA1lB,GAAAhE,MAAAA,aAAAA,GAEWusB,GAAAvoB,GAAAH,MAAAG,GAAAqkB,cAAAiC,CAAAA,EAAAA,MAAAA,SAAAA,IAAAA;AAAAA,sBAAAA;AAAAA;AAmBPkC,0BAAAA,UAAAA,WAAAA;AAAAA,4BAAAA,SAAAA,IAAAA,MAcAC,IAASC,IAAAA;AAAAA,8BAAAA,IAAAA;AAAAA,iCAETC,KAAAC,IAAA,OAAAtH,EAAAtQ,QAAAsQ,EAActQ,OAAAA,KAAAA,IAEd,OAAA,EAAA,SAAAsQ,EAAiBzN,QAAAA,CAAAA,GAAAA,CAAAA,IAAAA,mBAAAA,IAAAA,EAAAA,GAAAA,EAAAA,UAAAA,GAAAA,GAAAA,GAAAA,IAAAA,EAAAA,GAAAA,KAAAA,gBAAAA,IAAAA,OAAAA,MAAAA,aAAAA,GAAAA,GAAAA,GAAAA,MAAAA,GAAAA,cAAAA,CAAAA,EAAAA,KAAAA,SAAAA,IAAAA;AAAAA,gCAAAA;AAAAA,qCAAAA,IAAAA,IAAAA,oBAAAA,CAAAA,GAAAA,IAAAA,GAAAA,IAAAA,EAAAA,MAAAA,YAAAA,KAAAA,IAAAA,IAAAA,KAAAA,OAAAA,IAAAA,MAAAA,IAAAA,MAAAA,GAAAA,CAAAA,CAAAA,GAAAA;4BAAAA,SAAAA,IAAAA;AAAAA,qCAAAA,GAAAA,EAAAA;4BAAAA;0BAAAA,GAAAA,EAAAA;wBAAAA;AAAAA,+BAAAA,CAAAA,CAAAA;sBAAAA,GANjBgV,eAAAA,WAAAA;AAAAA,+BAAAA,oBAAAA,CASFjC,GAAAA,oBAAAM,CAAAA,GACAN,oBAAAyB,EAAAA,GACAzB,oBAAAuB,CAAAA,GACAvB,oBAAAmB,EAAAA,GAGAe,YAAA,GAAA,GAAA5sB,GAAA,CAAA;sBAAA;AA9CSoqB,0BAAAA,IAAAA,IACXkB,YAGQqB,GAAAA,IAAAL,EAAAtrB,OAAA6rB,IAAAA,IAAAA,EAAAA,OAAAA,GACY7rB,MAAAA,CAAAA,KAAAA,CAAAA,EAchB,QADJ4rB,YAAA,GACIN,GAAAA,GAAAA,CAAAA;AAAAA,0BAAAA;AAkBiB3U,6BAAAA,IAAAA,GAAAA,MAAAA,IAAAA,EAAAA,MAAAA,IAAAA,GAAAA,IAAAA,GANjB+U,IAAA1C,CAAAA,GAAA8C,wBAAAH,IAAAA,MAAAA,SAAAA,IAAAA;AAAAA,+BAAAA,MAAAA;AAAAA,8BAAAA,GAAAA,KAAAA,QAAAA,KAAAA,GAAAA,KAAAA,IAAAA,EAAAA;AAAAA,8BAAAA;AAAAA,gCAAAA,GAAAA,KAAAA;AAAAA,kCAAAA,GAAAA,OAAAA,QAAAA,GAAAA,IAAAA,IAAAA,aAAAA,KAAAA,IAAAA,IAAAA;AAAAA,8BAAAA,KAAAA;4BAAAA,MAAAA,CAAAA,KAAAA,GAAAA,KAAAA,IAAAA;0BAAAA,SAAAA,IAAAA;AAAAA,mCAAAA,GAAAA,EAAAA;0BAAAA;wBAAAA;sBAAAA,GAAAA,KAAAA,IAAAA,GAAAA,OAAAA;oBAeF;kBAAA,SAAAnW,IAAA;AAAA,2BAAAnU,GAAAmU,EAAA;kBAAA;gBAAA,GAAA,KAAA,IAAA,GAAAnU,EAAA;cAAA,SAAAtC,IAAA;AAAA,uBAAAsC,GAAAtC,EAAA;cAAA;YAAA,GAAA,KAAA,IAAA,GAAAsC,EAAA;UAAA,SAAAtC,IAAA;AAAA,mBAAAsC,GAAAtC,EAAA;UAAA;QAAA,GAAA,KAAA,IAAA,GAAAsC,EAAA;MAAA,SAAAtC,IAAA;AAAA,eAAAsC,GAAAtC,EAAA;MAAA;IAAA,GAAA,KAAA,IAAA,GAAAsC,EAAA;EAAA,CAAA;AAAA;AChIF,IAAM0qB,IAAe;AAAA,IAAA;AAAA,SAAA,oBAAAhtB,IAAAgE,IAAA;AA6DrBtE,SAAAA,IAAAA,QAAA,CAAAU,IAAAC,OAAAA;AACA4sB,UAAAA,IAxEA,SAASC,sBAAsBC,IAC7B;AAAA,YAAAC,KAAiB,CAAA;AAMjB,aALI,cAAA,OAAAptB,KACFotB,GAAA1nB,KAAS,IAAKynB,EAAAA,KAAAA,IAAAA,GAEdznB,KAAAA,EAAAA,GAEF2nB,IAAAA,gBAAW,IAAgB1rB,KAAAA,EAAAA,CAAAA;IAC7B,EAAA,CAAA;AAAA,UAAAW,KAAA,IAAA,OAAA,CAAA;AAAA,IAAAA,GAAA,iBAAA,WAAA,SAAA,QAAAtC,IAAA;AAAA,UAAAgE,GAAA,UAAAA,GAAA,OAAA,QAAA,CAAA1B,GAAA,UAAA;eAAA,WAAAtC,GAAA,KAAA,UAAA;AAAA,YAAAA,GAAA,KAAA,MAAA,QAAAC,GAAA,IAAA,MAAAD,GAAA,KAAA,KAAA,CAAA,GAAA,KAAAsC,GAAA,UAAA;AAAA,QAAAjB,GAAArB,GAAA,KAAA,IAAA,GAAAsC,GAAA,UAAA;MAAA,MAAA,CAAA0B,GAAA,WAAAhE,GAAA,KAAA,QAAA;IAAA,CAAA,GARAstB,GAAAA,iBAAAA,SAAAA,EAAAA,GAAAA,GAAAA,UAAAA,GAAAA,OAAAA,iBAAAA,SAAAA,MAAAA;AAAAA,MAAAA,GAAAA,GAAAA,OAAAA,MAAAA,GAAAA,GAAAA,UAAAA;IAAAA,CAAAA,GAAAA,GAAAA,YAAAA,EAAAA,MAAAA,IAAAA,wBAAAA,GAAAA,QAAAA,SAAAA,EAAAA,GAAAA,IAAAA,YAAAA,QAAAA,QAAAA,OAAAA,EAAAA,CAAAA;EAAAA,CAAAA;AAAAA;AAAAA,SAAAA,iBAAAA,IAAAA,IAAAA;AAAAA,SAAAA,IAAAA,QAAAA,SAAAA,IAAAA,IAAAA;AAAAA,QAAAA,IAAAA,IAAAA,IAAAA,IAAAA,IAAAA;AAAAA,QAAAA,KAAAA,EAAAA,GAAAA,GAAAA,GAAAA,KAAAA,GAAAA,ECgFMC,YAAIjpB,GAAAA,IAAAA,IAEVA,GAAAsnB,YAAAtnB,GAAAsnB,aAAA4B,OAAAC,mBAAAA,KACuB,aAAMnpB,OAAAA,GAAAopB,gBAANppB,GAAAopB,cAAAA,OAAAA,GAAAA,cAAAA,GAAAA,aAAAA,CAAAA,OAAAA;AAAAA,MAAAA,KAAAA,IAAAA,cAAAA,OAAAA,MAAAA,GAAAA,EAAAA;IAAAA,GAAAA,EAAAA,cAAAA,QAAAA,cAAAA,YAAAA,QAAAA,GAAAA,IAAAA,MAAAA,mDACvB,CAAA;AAAA,QAAA,CAAA,SAAA1E,KAAAjlB,GAAAhE,IAAAA,EAAAA,QAAAA,GAAAA,IATQmqB,MAAA,gCAAA,CAAA;AAAA,QAAA1oB,KAAA,eAAA,OAAA,qBAAA,gBAAA,mBAAA,CAAAyC,MAAA,cAAA,OAAA,UAAAzC,GAiBG,QAAGqW,SAAA9T,IAAAO,EAAAA,EAAHgmB,MAAAA,SAAAA,IAAAA;AAAAA,UAAAA;AAAAA,eAAXqD,KAAWC,IAAAA,MAAAA,KAAAA,IAAAA;MAAAA,SAAAA,IAAAA;AAAAA,eAAAA,GAAAA,EAAAA;MAAAA;IAAAA,GAAAA,KAAAA,IAAAA,GAAAA,EAAAA;AAAAA,QAAAA,KAAAA,WAAAA;AAAAA,UAAAA;AAAAA,eAAAA,MAAAA,KAAAA,IAAAA;MAAAA,SAAAA,IAAAA;AAAAA,eAAAA,GAAAA,EAAAA;MAAAA;IAAAA,GAAAA,KAAAA,IAAAA,GAAAA,eAAAA,SAAAA,IAAAA;AAAAA,UAAAA;AAHL,eAAA/V,SAAA9T,IAAAO,EAAAA,EAAAgmB,KAAAA,SAAAA,IAAAA;AAAAA,cAAAA;AAAAA,mBAAAA,KAAAyB,IAAAA,EAAAA;UAAAA,SAAAA,IAAAA;AAAAA,mBAAAA,GAAAA,EAAAA;UAAAA;QAAAA,GAAAA,EAAAA;MAAAA,SAAAA,IAAAA;AAAAA,eAAAA,GAAAA,EAAAA;MAAAA;IACF;AAAA,QAAA;AAAA,aAAAzpB,GAAA,SAAAA,GAAA,UAAA,kGAAA,oBAAAtC,IAAAsC,EAAA,EAAA,KAAA,SAAAtC,IAAA;AAAA,YAAA;AAAA,iBAAAyF,KAAAzF,IAAA,EAAA;QAAA,SAAAA,IAAA;AAAA,iBAAA,aAAA;QAAA;MAAA,GAAA,YAAA;IAAA,SAAAA,IAAA;AAAA,mBAAA;IAAA;AAAA,aAAA,QAKJ;AAAA,UAAA;AACA2tB,QAAAA,GAAA/pB,OAAAG,GAAAH,MAAAA,GAEMwkB,eAAArkB,GAAAqkB;MAKA,SAJFpoB,IAIE;MAAA;AAEN,UACIsE;AAAAA,QAAAA,GAAAupB,gBAAyC,iBAArC9pB,GAAAhE,SAAAA,CAAqCuE,GAAAmlB,YAAAnlB,GAAAmlB,YAAAnlB,GAAAmlB,aAAA1lB,GAAAhE,UAAAA,KAKpCR,2BAAAwE,IAAA4pB,EAGT;MAAA,SAAA3tB,IAAA;MAAA;AAEA,aAAA2tB,GAAAA,EAAAA;IAAAA;EAAAA,CAAAA;AAAAA;AAGAG,iBAAiBzF,qBAAeA,oBAChCyF,iBAAiBC,qBAAqBA,oBAEtCD,iBAAiBrF,YAAAA,WACjBqF,iBAAiBtE,oBAAAA,mBACjBsE,iBAAiBjC,mBAAjBA,kBACAiC,iBAAiBE,eAAAA,cACjBF,iBAAiBlD,qBAAAA,oBAEjBkD,iBAAiB/C,yBAAiBA,wBAClC+C,iBAAiB1C,wBAAUA,uBAE3B0C,iBAAenD,sBAAAA,qBAnIfmD,iBAAA7B,6BAAAA,4BAAAA,iBAAAA,6CAAAA,4CAAAA,iBAAAA,6BAAAA,4BAAAA,iBAAAA,iBAAAA,gBAAAA,iBAAAA,UAAAA;", "names": ["copyExifWithoutOrientation", "srcBlob", "destBlob", "Promise", "$return", "$error", "slice", "exif", "type", "e", "i", "getApp1Segment", "blob", "resolve", "reject", "reader", "FileReader", "addEventListener", "target", "result", "buffer", "view", "DataView", "offset", "getUint16", "marker", "size", "getUint32", "tiffOffset", "littleEndian", "r", "ifd0Offset", "endOfTagsOffset", "c", "setUint16", "f", "Blob", "readAsA<PERSON>y<PERSON><PERSON>er", "module", "UZIP", "u16", "u32", "exports", "buf", "onlyNames", "rUs", "rUi", "o", "out", "data", "Uint8Array", "eocd", "cnu", "cnt", "csize", "coffs", "sign", "usize", "nl", "el", "cl", "s", "roff", "_readLocal", "cmpr", "time", "crc32", "nlen", "elen", "name", "bin", "readUTF8", "file", "t", "l", "inflateRaw", "inflate", "byteOffset", "length", "opts", "Math", "floor", "off", "F", "crc", "<PERSON><PERSON>", "level", "deflateRaw", "encode", "obj", "noCmpr", "tot", "wUi", "wUs", "cpr", "p", "zpd", "fof", "a", "push", "_writeHeader", "ioff", "fn", "ext", "pop", "toLowerCase", "indexOf", "sizeUTF8", "writeUTF8", "tab", "Uint32Array", "n", "k", "update", "len", "table", "b", "end", "buff", "writeUshort", "readUint", "writeUint", "String", "fromCharCode", "writeASCII", "ns", "toString", "str", "ci", "strl", "charCodeAt", "code", "opos", "lvl", "opt", "U", "goodIndex", "_goodIndex", "hash", "putsE", "pos", "cvrd", "dlen", "strt", "ii", "prev", "nc", "lc", "lits", "li", "ebits", "bs", "mch", "_bestMatch", "min", "dst", "of0", "lhst", "lgi", "dgi", "df0", "dhst", "nice", "chain", "pi", "dif", "_hash", "tl", "td", "dlim", "_howLong", "maxd", "j", "ei", "curd", "saved", "_writeBlock", "BFINAL", "T", "ML", "putsF", "getTrees", "MD", "MH", "numl", "dset", "cstSize", "l0", "fxdSize", "contSize", "fltree", "fdtree", "dynSize", "numh", "itree", "ihst", "BTYPE", "_copyExact", "ltree", "dtree", "makeCodes", "revCodes", "numd", "_codeTiny", "lset", "o0", "si", "qb", "_writeLit", "qc", "p8", "set", "_hufTree", "_lenCodes", "getSecond", "nonZero", "tree", "hst", "nxt", "nnxt", "prv", "lz", "zc", "list", "lit", "l2", "sort", "i0", "i1", "i2", "d", "maxl", "<PERSON><PERSON><PERSON><PERSON>", "MAXL", "restrictDepth", "bCost", "dbt", "dps", "od", "console", "log", "v", "arr", "ch", "_putsF", "u8", "bitsF", "_bitsF", "bitsE", "_bitsE", "decodeTiny", "codes2map", "get17", "noBuf", "lmap", "dmap", "HDIST", "HCLEN", "_check", "fdmap", "HLIT", "ordr", "imap", "mx0", "_copyOut", "ttree", "mx1", "ebs", "ldef", "dcode", "dlit", "bl", "nbuf", "max", "_decodeTiny", "LL", "ll", "src", "mx", "bits", "max_code", "bl_count", "next_code", "r15", "rev15", "val", "rest", "MAX_BITS", "imb", "_putsE", "dt", "_get17", "_get25", "Uint16Array", "exb", "dxb", "ddef", "flmap", "x", "tgt", "sv", "pushV", "nextZero", "readASCII", "readBytes", "_bin", "pad", "decodeURIComponent", "w", "h", "area", "bpl", "ceil", "bpp", "bf", "bf32", "ctype", "depth", "rs", "readUshort", "qarea", "ts", "tabs", "tRNS", "ti", "tr", "tg", "tb", "qi", "PLTE", "ap", "y", "s0", "t0", "cj", "gr", "di", "to", "al", "dd", "_getBPP", "interlace", "CgBI", "_filterZero", "width", "img", "starting_col", "col_increment", "ri", "row_increment", "pass", "sh", "cr", "cc", "sw", "starting_row", "row", "col", "cdi", "bpll", "cbpp", "H", "N", "W", "R", "C", "m", "J", "Q", "X", "u", "Z", "A", "K", "M", "I", "V", "S", "D", "z", "_", "q", "$", "Y", "g", "_paeth", "pa", "pb", "pc", "height", "compress", "filter", "_copyTile", "sb", "tw", "th", "xoff", "yoff", "mode", "fa", "fr", "fg", "fb", "ba", "br", "bg", "bb", "ifa", "oa", "ioa", "decode", "frames", "fd", "foff", "mgck", "_IHDR", "fil", "res", "_inflate", "doff", "num_frames", "num_plays", "_decompress", "rect", "rct", "del", "frm", "delay", "round", "dispose", "blend", "keyw", "nz", "text", "bfr", "cflag", "pl", "toRGBA8", "acTL", "decodeImage", "frms", "empty", "fy", "fw", "fdata", "fh", "fx", "UPNG", "paeth", "crcLib", "er", "dr", "dg", "db", "da", "dither", "plte", "MTD", "nplt", "ce", "ne", "ni", "err", "Int16Array", "cd", "nd", "addErr", "tb32", "_main", "nimg", "wAs", "anim", "cicc", "pltAlpha", "leng", "sRGB", "pHYs", "iCCP", "pako", "deflate", "dl", "cimg", "wr", "sl", "loop", "fi", "dels", "imgd", "compressPNG", "levelZero", "nh", "bufs", "prms", "onlyBlend", "evenCrd", "forbidPrev", "minBits", "forbidPlte", "dith", "alphaAnd", "ilen", "got<PERSON><PERSON><PERSON>", "framize", "alwaysBlend", "cimg32", "nx", "ny", "nw", "tstp", "E", "it", "tlim", "pimg", "miy", "may", "p32", "mix", "sarea", "tarea", "_prepareDiff", "r0", "r1", "miX", "miY", "_updateFrame", "ps", "nbufs", "abuf", "concatRGBA", "tlen", "byteLength", "il", "noff", "qres", "quantize", "est", "rgba", "cof", "bln", "ind", "inds", "img32", "cmc", "cmap", "inj", "U8", "pimg32", "U32", "cx", "cy", "rec", "fls", "ftry", "CMPR", "_filterLine", "tsize", "getKDtree", "root", "KD", "getNearest", "left", "planeDst", "right", "leafs", "nimg32", "bst", "tdst", "maxL", "mi", "L", "node", "splitPixels", "eMq255", "ln", "stats", "estats", "rn", "dist", "d0", "d1", "d2", "d3", "pd", "node0", "node1", "eMq", "vecDot", "m1", "m2", "m3", "<PERSON><PERSON>", "m0", "iN", "M4", "random", "multVec", "sqrt", "dot", "sml", "tmi", "abs", "ac", "bipp", "bipl", "CanvasToBMP", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "canvas", "callback", "w4", "idata", "getContext", "getImageData", "data32", "stride", "pixelArraySize", "fileLength", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "blockSize", "block", "set16", "set32", "seek", "setUint32", "setTimeout", "convert", "_dly", "BROWSER_NAME", "CHROME", "FIREFOX", "DESKTOP_SAFARI", "IE", "IOS", "MAX_CANVAS_SIZE", "<PERSON><PERSON><PERSON><PERSON>", "window", "inWebWorker", "WorkerGlobalScope", "self", "moduleMapper", "<PERSON><PERSON>", "require", "CustomFile", "getOriginalSymbol", "File", "CustomFileReader", "dataUrl", "split", "mime", "match", "bstr", "globalThis", "atob", "u8arr", "lastModified", "getDataUrlFromFile", "onload", "onerror", "readAsDataURL", "loadImage", "getBrowserName", "cachedResult", "browserName", "ETC", "userAgent", "navigator", "test", "document", "documentMode", "maximumCanvasSize", "initWidth", "ratio", "halfSizeWidth", "halfSizeHeight", "drawImageInCanvas", "fileType", "approximateBelowMaximumCanvasSizeOfBrowser", "ctx", "fillRect", "drawImage", "isIOS", "includes", "platform", "options", "Error", "createImageBitmap", "convertToBlob", "quality", "then", "$await_11", "fileLastModified", "fileName", "$await_12", "cleanupCanvasMemory", "getExifOrientation", "little", "tags", "handleMaxWidthOrHeight", "maxWidthOrHeight", "newCanvas", "isFinite", "getNewCanvasAndCtx", "followExifOrientation", "exifOrientation", "previousProgress", "incProgress", "inc", "signal", "aborted", "progress", "maxSizeMB", "drawFileInCanvas", "origC<PERSON><PERSON>", "$await_5", "$await_6", "isAutoOrientationInBrowser", "orientationFixedCanvas", "$await_8", "maxWidthOrHeightFixedCanvas", "initialQuality", "outputFileType", "tempFile", "currentSize", "sourceSize", "newWidth", "shouldReduceResolution", "origExceedMaxSize", "setProgress", "maxSizeByte", "alwaysKeepResolution", "workerScript", "workerScriptURL", "createWorkerScriptURL", "script", "blob<PERSON><PERSON>s", "URL", "worker", "onProgress", "Number", "POSITIVE_INFINITY", "useWebWorker", "compressedFile", "$await_7", "preserveExif", "imageCompression", "getFilefromDataUrl", "canvasToFile"]}