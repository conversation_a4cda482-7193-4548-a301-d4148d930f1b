"use strict";
const electron = require("electron");
electron.contextBridge.exposeInMainWorld("ipcRenderer", {
  on(...args) {
    const [channel, listener] = args;
    return electron.ipcRenderer.on(channel, (event, ...args2) => listener(event, ...args2));
  },
  off(...args) {
    const [channel, ...omit] = args;
    return electron.ipcRenderer.off(channel, ...omit);
  },
  send(...args) {
    const [channel, ...omit] = args;
    return electron.ipcRenderer.send(channel, ...omit);
  },
  invoke(...args) {
    const [channel, ...omit] = args;
    return electron.ipcRenderer.invoke(channel, ...omit);
  },
  removeAllListeners: (channel) => electron.ipcRenderer.removeAllListeners(channel)
});
electron.contextBridge.exposeInMainWorld("electronAPI", {
  closeWindow: () => electron.ipcRenderer.send("window-close"),
  minimizeWindow: () => electron.ipcRenderer.send("window-minimize"),
  toggleMaximizeWindow: () => electron.ipcRenderer.send("window-toggle-maximize"),
  isFullScreen: () => electron.ipcRenderer.invoke("is-fullscreen"),
  selectFile: (options) => electron.ipcRenderer.invoke("select-file", options),
  triggerMenuAction: (action) => electron.ipcRenderer.send("menu-action", action),
  onExecuteAction: (callback) => electron.ipcRenderer.on("execute-action", (event, action) => callback(action)),
  getPlatform: () => process.platform,
  getHomeDir: () => electron.ipcRenderer.invoke("get-home-dir"),
  createWebView: (id, url) => electron.ipcRenderer.invoke("create-webview", id, url),
  hideWebView: (id) => electron.ipcRenderer.invoke("hide-webview", id),
  changeViewSize: (id, size) => electron.ipcRenderer.invoke("change-view-size", id, size),
  onWebviewNavigated: (callback) => electron.ipcRenderer.on("webview-navigated", (event, id, url) => callback(id, url)),
  showWebview: (id) => electron.ipcRenderer.invoke("show-webview", id),
  getActiveWebview: () => electron.ipcRenderer.invoke("get-active-webview"),
  setSize: (size) => electron.ipcRenderer.invoke("set-size", size),
  hideAllWebview: () => electron.ipcRenderer.invoke("hide-all-webview"),
  getShowWebview: () => electron.ipcRenderer.invoke("get-show-webview"),
  webviewDestroy: (webviewId) => electron.ipcRenderer.invoke("webview-destroy", webviewId),
  exportLog: () => electron.ipcRenderer.invoke("export-log"),
  // mcp
  mcpInstall: (name, mcp) => electron.ipcRenderer.invoke("mcp-install", name, mcp),
  mcpRemove: (name) => electron.ipcRenderer.invoke("mcp-remove", name),
  mcpUpdate: (name, mcp) => electron.ipcRenderer.invoke("mcp-update", name, mcp),
  mcpList: () => electron.ipcRenderer.invoke("mcp-list"),
  envWrite: (email, kv) => electron.ipcRenderer.invoke("env-write", email, kv),
  envRemove: (email, key) => electron.ipcRenderer.invoke("env-remove", email, key),
  getEnvPath: (email) => electron.ipcRenderer.invoke("get-env-path", email),
  // command execution
  executeCommand: (command, email) => electron.ipcRenderer.invoke("execute-command", command, email),
  // file operations
  readFile: (filePath) => electron.ipcRenderer.invoke("read-file", filePath),
  deleteFolder: (email) => electron.ipcRenderer.invoke("delete-folder", email),
  getMcpConfigPath: (email) => electron.ipcRenderer.invoke("get-mcp-config-path", email),
  // install dependencies related API
  installDependencies: () => electron.ipcRenderer.invoke("install-dependencies"),
  frontendReady: () => electron.ipcRenderer.invoke("frontend-ready"),
  checkInstallBrowser: () => electron.ipcRenderer.invoke("check-install-browser"),
  onInstallDependenciesStart: (callback) => {
    electron.ipcRenderer.on("install-dependencies-start", callback);
  },
  onInstallDependenciesLog: (callback) => {
    electron.ipcRenderer.on("install-dependencies-log", (event, data) => callback(data));
  },
  onInstallDependenciesComplete: (callback) => {
    electron.ipcRenderer.on("install-dependencies-complete", (event, data) => callback(data));
  },
  onUpdateNotification: (callback) => {
    electron.ipcRenderer.on("update-notification", (event, data) => callback(data));
  },
  startBrowserImport: (args) => electron.ipcRenderer.invoke("start-browser-import", args),
  // remove listeners
  removeAllListeners: (channel) => {
    electron.ipcRenderer.removeAllListeners(channel);
  }
});
function domReady(condition = ["complete", "interactive"]) {
  return new Promise((resolve) => {
    if (condition.includes(document.readyState)) {
      resolve(true);
    } else {
      document.addEventListener("readystatechange", () => {
        if (condition.includes(document.readyState)) {
          resolve(true);
        }
      });
    }
  });
}
const safeDOM = {
  append(parent, child) {
    if (!Array.from(parent.children).find((e) => e === child)) {
      return parent.appendChild(child);
    }
  },
  remove(parent, child) {
    if (Array.from(parent.children).find((e) => e === child)) {
      return parent.removeChild(child);
    }
  }
};
function useLoading() {
  const className = `loaders-css__square-spin`;
  const styleContent = `
@keyframes square-spin {
  25% { transform: perspective(100px) rotateX(180deg) rotateY(0); }
  50% { transform: perspective(100px) rotateX(180deg) rotateY(180deg); }
  75% { transform: perspective(100px) rotateX(0) rotateY(180deg); }
  100% { transform: perspective(100px) rotateX(0) rotateY(0); }
}
.${className} > div {
  animation-fill-mode: both;
  width: 50px;
  height: 50px;
  background: #fff;
  animation: square-spin 3s 0s cubic-bezier(0.09, 0.57, 0.49, 0.9) infinite;
}
.app-loading-wrap {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #282c34;
  z-index: 9;
}
    `;
  const oStyle = document.createElement("style");
  const oDiv = document.createElement("div");
  oStyle.id = "app-loading-style";
  oStyle.innerHTML = styleContent;
  oDiv.className = "app-loading-wrap";
  oDiv.innerHTML = `<div class="${className}"><div></div></div>`;
  return {
    appendLoading() {
      safeDOM.append(document.head, oStyle);
      safeDOM.append(document.body, oDiv);
    },
    removeLoading() {
      safeDOM.remove(document.head, oStyle);
      safeDOM.remove(document.body, oDiv);
    }
  };
}
const { appendLoading, removeLoading } = useLoading();
domReady().then(appendLoading);
window.onmessage = (ev) => {
  ev.data.payload === "removeLoading" && removeLoading();
};
setTimeout(removeLoading, 4999);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
