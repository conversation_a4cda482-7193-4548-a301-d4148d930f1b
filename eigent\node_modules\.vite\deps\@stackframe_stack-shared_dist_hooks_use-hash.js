import {
  suspendIfSsr
} from "./chunk-BN4UP6KC.js";
import "./chunk-NK5UW6NX.js";
import "./chunk-GVADET3V.js";
import "./chunk-QNRW4OZD.js";
import "./chunk-REXVUZ33.js";
import "./chunk-UFCUR7KH.js";
import "./chunk-JBDVY4WB.js";
import "./chunk-LWWYEWPG.js";
import "./chunk-Z26222H5.js";
import "./chunk-4BLY47KI.js";
import "./chunk-GMJRCDEU.js";
import {
  require_react
} from "./chunk-6OBIWUOU.js";
import {
  __toESM
} from "./chunk-7UVSMXVG.js";

// package/@stackframe/react/node_modules/@stackframe/stack-shared/dist/esm/hooks/use-hash.js
var import_react = __toESM(require_react());
var useHash = () => {
  suspendIfSsr("useHash");
  return (0, import_react.useSyncExternalStore)(
    (onChange) => {
      const interval = setInterval(() => onChange(), 10);
      return () => clearInterval(interval);
    },
    () => window.location.hash.substring(1)
  );
};
export {
  useHash
};
//# sourceMappingURL=@stackframe_stack-shared_dist_hooks_use-hash.js.map
