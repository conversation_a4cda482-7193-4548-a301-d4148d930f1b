{"version": 3, "file": "index.js", "sources": ["../../electron/main/update.ts", "../../electron/main/utils/process.ts", "../../electron/main/init.ts", "../../electron/main/webview.ts", "../../electron/main/fileReader.ts", "../../electron/main/utils/mcpConfig.ts", "../../electron/main/utils/envUtil.ts", "../../node_modules/universalify/index.js", "../../node_modules/graceful-fs/polyfills.js", "../../node_modules/graceful-fs/legacy-streams.js", "../../node_modules/graceful-fs/clone.js", "../../node_modules/graceful-fs/graceful-fs.js", "../../node_modules/fs-extra/lib/fs/index.js", "../../node_modules/at-least-node/index.js", "../../node_modules/fs-extra/lib/mkdirs/make-dir.js", "../../node_modules/fs-extra/lib/mkdirs/index.js", "../../node_modules/fs-extra/lib/util/utimes.js", "../../node_modules/fs-extra/lib/util/stat.js", "../../node_modules/fs-extra/lib/copy-sync/copy-sync.js", "../../node_modules/fs-extra/lib/copy-sync/index.js", "../../node_modules/fs-extra/lib/path-exists/index.js", "../../node_modules/fs-extra/lib/copy/copy.js", "../../node_modules/fs-extra/lib/copy/index.js", "../../node_modules/fs-extra/lib/remove/rimraf.js", "../../node_modules/fs-extra/lib/remove/index.js", "../../node_modules/fs-extra/lib/empty/index.js", "../../node_modules/fs-extra/lib/ensure/file.js", "../../node_modules/fs-extra/lib/ensure/link.js", "../../node_modules/fs-extra/lib/ensure/symlink-paths.js", "../../node_modules/fs-extra/lib/ensure/symlink-type.js", "../../node_modules/fs-extra/lib/ensure/symlink.js", "../../node_modules/fs-extra/lib/ensure/index.js", "../../node_modules/jsonfile/utils.js", "../../node_modules/jsonfile/index.js", "../../node_modules/fs-extra/lib/json/jsonfile.js", "../../node_modules/fs-extra/lib/output/index.js", "../../node_modules/fs-extra/lib/json/output-json.js", "../../node_modules/fs-extra/lib/json/output-json-sync.js", "../../node_modules/fs-extra/lib/json/index.js", "../../node_modules/fs-extra/lib/move-sync/move-sync.js", "../../node_modules/fs-extra/lib/move-sync/index.js", "../../node_modules/fs-extra/lib/move/move.js", "../../node_modules/fs-extra/lib/move/index.js", "../../node_modules/fs-extra/lib/index.js", "../../electron/main/copy.ts", "../../electron/main/index.ts"], "sourcesContent": ["import { app, ipcMain } from 'electron'\nimport { createRequire } from 'node:module'\nimport type {\n  ProgressInfo,\n  UpdateDownloadedEvent,\n  UpdateInfo,\n} from 'electron-updater'\n\nconst { autoUpdater } = createRequire(import.meta.url)('electron-updater');\n\nexport function update(win: Electron.BrowserWindow) {\n\n  // When set to false, the update download will be triggered through the API\n  autoUpdater.autoDownload = false\n  autoUpdater.disableWebInstaller = false\n  autoUpdater.allowDowngrade = false\n\n  autoUpdater.forceDevUpdateConfig = true\n\n  // start check\n  autoUpdater.on('checking-for-update', function () { })\n  // update available\n  autoUpdater.on('update-available', (arg: UpdateInfo) => {\n    if (win && !win.isDestroyed()) {\n      win.webContents.send('update-can-available', { update: true, version: app.getVersion(), newVersion: arg?.version })\n    }\n  })\n  // update not available\n  autoUpdater.on('update-not-available', (arg: UpdateInfo) => {\n    if (win && !win.isDestroyed()) {\n      win.webContents.send('update-can-available', { update: false, version: app.getVersion(), newVersion: arg?.version })\n    }\n  })\n  console.log('Current version:', autoUpdater.currentVersion.version)\n  console.log('Update config path:', autoUpdater.getUpdateConfigPath?.())\n  console.log('User data path (where config lives):', app.getPath('userData'))\n  if (app.isPackaged) {\n    autoUpdater.checkForUpdatesAndNotify()\n  }\n\n  if (!app.isPackaged) {\n    const updateUrl = 'http://dev.eigent.ai/public/win'\n    const feed = {\n      provider: 'generic',\n      url: updateUrl\n    }\n\n    console.log('[DEV] setFeedURL:', updateUrl)\n    autoUpdater.setFeedURL(feed)\n    autoUpdater.checkForUpdates()\n  }\n\n}\n\n/**\n * Registers update-related IPC handlers\n * Should be called once when the app starts\n */\nexport function registerUpdateIpcHandlers() {\n  // Checking for updates\n  ipcMain.handle('check-update', async () => {\n  \n\n    try {\n      return await autoUpdater.checkForUpdatesAndNotify()\n    } catch (error) {\n      return { message: 'Network error', error }\n    }\n  })\n\n  // Start downloading and feedback on progress\n  ipcMain.handle('start-download', (event: Electron.IpcMainInvokeEvent) => {\n    startDownload(\n      (error, progressInfo) => {\n        if (error) {\n          // feedback download error message\n          if (!event.sender.isDestroyed()) {\n            event.sender.send('update-error', { message: error.message, error })\n          }\n        } else {\n          // feedback update progress message\n          if (!event.sender.isDestroyed()) {\n            event.sender.send('download-progress', progressInfo)\n          }\n        }\n      },\n      () => {\n        // feedback update downloaded message\n        if (!event.sender.isDestroyed()) {\n          event.sender.send('update-downloaded')\n        }\n      }\n    )\n  })\n\n  // Install now\n  ipcMain.handle('quit-and-install', () => {\n    autoUpdater.quitAndInstall(false, true)\n  })\n}\n\nfunction startDownload(\n  callback: (error: Error | null, info: ProgressInfo | null) => void,\n  complete: (event: UpdateDownloadedEvent) => void,\n) {\n  autoUpdater.on('download-progress', (info: ProgressInfo) => callback(null, info))\n  autoUpdater.on('error', (error: Error) => callback(error, null))\n  autoUpdater.on('update-downloaded', complete)\n  autoUpdater.downloadUpdate()\n}\n", "import { spawn } from 'child_process'\nimport log from 'electron-log'\nimport fs from 'fs'\nimport os from 'os'\nimport path from 'path'\nimport { app } from 'electron'\n\n\nexport function getResourcePath() {\n  return path.join(app.getAppPath(), 'resources')\n}\n\nexport function getBackendPath() {\n  if (app.isPackaged) {\n    //  after packaging, backend is in extraResources\n    return path.join(process.resourcesPath, 'backend')\n  } else {\n    // development environment\n    return path.join(app.getAppPath(), 'backend')\n  }\n}\n\nexport function runInstallScript(scriptPath: string): Promise<boolean> {\n  return new Promise<boolean>((resolve, reject) => {\n    const installScriptPath = path.join(getResourcePath(), 'scripts', scriptPath)\n    log.info(`Running script at: ${installScriptPath}`)\n\n    const nodeProcess = spawn(process.execPath, [installScriptPath], {\n      env: { ...process.env, ELECTRON_RUN_AS_NODE: '1' }\n    })\n\n    nodeProcess.stdout.on('data', (data) => {\n      log.info(`Script output: ${data}`)\n    })\n\n    nodeProcess.stderr.on('data', (data) => {\n      log.error(`Script error: ${data}`)\n    })\n\n    nodeProcess.on('close', (code) => {\n      if (code === 0) {\n        log.info('Script completed successfully')\n        resolve(true)\n      } else {\n        log.error(`Script exited with code ${code}`)\n        reject(false)\n      }\n    })\n  })\n}\n\nexport async function getBinaryName(name: string): Promise<string> {\n  if (process.platform === 'win32') {\n    return `${name}.exe`\n  }\n  return name\n}\n\nexport async function getBinaryPath(name?: string): Promise<string> {\n  if (!name) {\n    return path.join(os.homedir(), '.eigent', 'bin')\n  }\n  const binaryName = await getBinaryName(name)\n  const binariesDir = path.join(os.homedir(), '.eigent', 'bin')\n  const binariesDirExists = await fs.existsSync(binariesDir)\n  \n  return binariesDirExists ? path.join(binariesDir, binaryName) : binaryName\n}\n\nexport async function isBinaryExists(name: string): Promise<boolean> {\n  const cmd = await getBinaryPath(name)\n\n  return await fs.existsSync(cmd)\n}\n", "import { getBackendPath, getB<PERSON>ryPath, isBinaryExists, runInstallScript } from \"./utils/process\";\nimport { spawn } from 'child_process'\nimport log from 'electron-log'\nimport fs from 'fs'\nimport path from 'path'\nimport * as net from \"net\";\nimport { ipc<PERSON>ain, BrowserWindow } from 'electron'\n\n// helper function to get main window\nfunction getMainWindow(): BrowserWindow | null {\n    const windows = BrowserWindow.getAllWindows();\n    return windows.length > 0 ? windows[0] : null;\n}\n\n\nexport async function checkToolInstalled() {\n    return new Promise(async (resolve, reject) => {\n        if (!(await isBinaryExists('uv'))) {\n            resolve(false)\n        }\n\n        if (!(await isBinaryExists('bun'))) {\n            resolve(false)\n        }\n\n        resolve(true)\n    })\n\n}\n\n/**\n * Check if command line tools are installed, install if not\n */\nexport async function installCommandTool() {\n    return new Promise(async (resolve, reject) => {\n        let isAllInstalled = true\n        console.log('Checking if command line tools are installed, installing if not')\n        if (!(await isBinaryExists('uv'))) {\n            console.log('start install uv')\n            await runInstallScript('install-uv.js')\n            const uv_installed = await isBinaryExists('uv')\n            const mainWindow = getMainWindow();\n            if (mainWindow && !mainWindow.isDestroyed()) {\n                if (uv_installed) {\n\n                    mainWindow.webContents.send('install-dependencies-log', { type: 'stdout', data: '' });\n                } else {\n                    isAllInstalled = false\n                    mainWindow.webContents.send('install-dependencies-complete', { success: false, code: 2, error: `Script exited with code ${2}` });\n                }\n            }\n        }\n\n        if (!(await isBinaryExists('bun'))) {\n            console.log('start install bun')\n            await runInstallScript('install-bun.js')\n            const bun_installed = await isBinaryExists('bun')\n            const mainWindow = getMainWindow();\n            if (mainWindow && !mainWindow.isDestroyed()) {\n                if (bun_installed) {\n                    mainWindow.webContents.send('install-dependencies-log', { type: 'stdout', data: '' });\n                } else {\n                    isAllInstalled = false\n                    mainWindow.webContents.send('install-dependencies-complete', { success: false, code: 2, error: `Script exited with code ${2}` });\n                }\n            }\n        }\n        resolve(isAllInstalled)\n    })\n\n}\n\nexport async function installDependencies() {\n    return new Promise<boolean>(async (resolve, reject) => {\n        console.log('start install dependencies')\n\n        // notify frontend start install\n        const mainWindow = getMainWindow();\n        if (mainWindow && !mainWindow.isDestroyed()) {\n            mainWindow.webContents.send('install-dependencies-start');\n        }\n\n        const isInstalCommandTool = await installCommandTool()\n        if (!isInstalCommandTool) {\n            resolve(false)\n            return\n        }\n        const uv_path = await getBinaryPath('uv')\n        const backendPath = getBackendPath()\n\n        // ensure backend directory exists and is writable\n        if (!fs.existsSync(backendPath)) {\n            fs.mkdirSync(backendPath, { recursive: true })\n        }\n\n        // touch installing lock file\n        const installingLockPath = path.join(backendPath, 'uv_installing.lock')\n        fs.writeFileSync(installingLockPath, '')\n        const proxy = ['--default-index', 'https://pypi.tuna.tsinghua.edu.cn/simple']\n        function isInChinaTimezone() {\n            const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;\n            return timezone === 'Asia/Shanghai';\n        }\n        console.log('isInChinaTimezone', isInChinaTimezone())\n        const node_process = spawn(uv_path, ['sync', '--no-dev', ...(isInChinaTimezone() ? proxy : [])], { cwd: backendPath })\n        node_process.stdout.on('data', (data) => {\n            log.info(`Script output: ${data}`)\n            // notify frontend install log\n            const mainWindow = getMainWindow();\n            if (mainWindow && !mainWindow.isDestroyed()) {\n                mainWindow.webContents.send('install-dependencies-log', { type: 'stdout', data: data.toString() });\n            }\n        })\n\n        node_process.stderr.on('data', (data) => {\n            log.error(`Script error: uv ${data}`)\n            // notify frontend install error log\n            const mainWindow = getMainWindow();\n            if (mainWindow && !mainWindow.isDestroyed()) {\n                mainWindow.webContents.send('install-dependencies-log', { type: 'stderr', data: data.toString() });\n            }\n        })\n\n        node_process.on('close', async (code) => {\n            // delete installing lock file \n            if (fs.existsSync(installingLockPath)) {\n                fs.unlinkSync(installingLockPath)\n            }\n\n            if (code === 0) {\n                log.info('Script completed successfully')\n\n                // touch installed lock file\n                const installedLockPath = path.join(backendPath, 'uv_installed.lock')\n                fs.writeFileSync(installedLockPath, '')\n                console.log('end install dependencies')\n\n\n                spawn(uv_path, ['run', 'task', 'babel'], { cwd: backendPath })\n                resolve(true);\n                // resolve(isSuccess);\n            } else {\n                log.error(`Script exited with code ${code}`)\n                // notify frontend install failed\n                const mainWindow = getMainWindow();\n                if (mainWindow && !mainWindow.isDestroyed()) {\n                    mainWindow.webContents.send('install-dependencies-complete', { success: false, code, error: `Script exited with code ${code}` });\n                    resolve(false);\n                }\n            }\n        })\n    })\n}\n\n\nexport async function startBackend(setPort?: (port: number) => void): Promise<any> {\n    console.log('start fastapi')\n    const uv_path = await getBinaryPath('uv')\n    const backendPath = getBackendPath()\n    const port = await findAvailablePort(5001);\n    if (setPort) {\n        setPort(port);\n    }\n\n    const env = {\n        ...process.env,\n        SERVER_URL: \"https://dev.eigent.ai/api\",\n        PYTHONIOENCODING: 'utf-8'\n    }\n    return new Promise((resolve, reject) => {\n        const node_process = spawn(\n            uv_path,\n            [\"run\", \"uvicorn\", \"main:api\", \"--port\", port.toString(), \"--loop\", \"asyncio\"],\n            { cwd: backendPath, env: env, detached: false }\n        );\n\n\n        let started = false;\n\n\n        node_process.stdout.on('data', (data) => {\n            log.info(`fastapi output: ${data}`);\n            // check output content, judge if start success\n            if (!started && data.toString().includes(\"Uvicorn running on\")) {\n                started = true;\n                resolve(node_process);\n            }\n        });\n\n        node_process.stderr.on('data', (data) => {\n            log.error(`fastapi stderr output: ${data}`);\n            if (!started && data.toString().includes(\"Uvicorn running on\")) {\n                started = true;\n                resolve(node_process);\n            }\n        });\n\n        node_process.on('close', (code) => {\n            if (!started) {\n                reject(new Error(`fastapi exited with code ${code}`));\n            }\n        });\n    });\n    // const node_process = spawn(\n    //     uv_path,\n    //     [\"run\", \"uvicorn\", \"main:api\", \"--port\", port.toString(), \"--loop\", \"asyncio\"],\n    //     { cwd: backendPath, env: env, detached: false }\n    // );\n\n    // node_process.stdout.on('data', (data) => {\n    //     log.info(`fastapi output: ${data}`)\n    // })\n\n    // node_process.stderr.on('data', (data) => {\n    //     log.error(`fastapi stderr output: ${data}`)\n    // })\n\n    // node_process.on('close', (code) => {\n    //     if (code === 0) {\n    //         log.info('fastapi start success')\n    //     } else {\n    //         log.error(`fastapi exited with code ${code}`)\n\n    //     }\n    // })\n    // return node_process\n}\n\nfunction checkPortAvailable(port: number): Promise<boolean> {\n    return new Promise((resolve) => {\n        const server = net.createServer();\n\n        server.once('error', (err: any) => {\n            if (err.code === 'EADDRINUSE') {\n                resolve(false); // port occupied\n            } else {\n                resolve(false);\n            }\n        });\n\n        server.once('listening', () => {\n            server.close(() => {\n                console.log('try port', port)\n                resolve(true)\n            }); // port available, close then return\n        });\n\n        // force listen all addresses, prevent judgment\n        server.listen({ port, host: \"127.0.0.1\", exclusive: true });\n    });\n}\n\nexport async function findAvailablePort(startPort: number, maxAttempts = 50): Promise<number> {\n    let port = startPort;\n    for (let i = 0; i < maxAttempts; i++) {\n        const available = await checkPortAvailable(port);\n        if (available) {\n            return port;\n        }\n        port++;\n    }\n    throw new Error('No available port found');\n}", "import { ip<PERSON><PERSON><PERSON>, <PERSON><PERSON>onte<PERSON>View, <PERSON><PERSON>er<PERSON>indow } from 'electron'\n\ninterface WebViewInfo {\n  id: string\n  view: WebContentsView\n  initialUrl: string\n  currentUrl: string\n  isActive: boolean\n  isShow: boolean\n}\n\ninterface Size {\n  x: number\n  y: number\n  width: number\n  height: number\n}\n\nexport class WebViewManager {\n  private webViews = new Map<string, WebViewInfo>()\n  private win: BrowserWindow | null = null\n  private size: Size = { x: 0, y: 0, width: 0, height: 0 }\n  constructor(window: BrowserWindow) {\n    this.win = window\n  }\n\n  // Remove automatic IPC handler registration from constructor\n  // IPC handlers should be registered once in the main process\n\n  public async captureWebview(webviewId: string) {\n    const webContents = this.webViews.get(webviewId);\n    if (!webContents) return null;\n\n    const image = await webContents.view.webContents.capturePage();\n    const jpegBuffer = image.toJPEG(10);\n    return 'data:image/jpeg;base64,' + jpegBuffer.toString('base64');\n  }\n\n  public setSize(size: Size) {\n    this.size = size\n    this.webViews.forEach((webview) => {\n      if (webview.isActive && webview.isShow) {\n        this.changeViewSize(webview.id, size)\n      }\n    })\n  }\n\n  public getActiveWebview() {\n    const activeWebviews = Array.from(this.webViews.values()).filter(webview => webview.isActive)\n\n    return activeWebviews.map(webview => webview.id)\n  }\n\n\n\n  public async createWebview(id: string = '1', url: string = 'about:blank?use=0') {\n    try {\n      // If webview with this id already exists, return error\n      if (this.webViews.has(id)) {\n        return { success: false, error: `Webview with id ${id} already exists` }\n      }\n      const view = new WebContentsView({\n        webPreferences: {\n          nodeIntegration: false,\n          contextIsolation: true,\n        },\n      })\n      view.webContents.on('did-finish-load', () => {\n        view.webContents.executeJavaScript(`\n          window.addEventListener('mousedown', (e) => {\n            if (!(e.target instanceof HTMLButtonElement || e.target instanceof HTMLInputElement)) {\n              e.preventDefault();\n            }\n          }, true);\n        `);\n      });\n\n      // Set to muted state when created\n      view.webContents.audioMuted = true\n      let newId = Number(id)\n      view.setBounds({ x: -9999 + newId * 100, y: -9999 + newId * 100, width: 100, height: 100 })\n      view.setBorderRadius(16)\n\n      await view.webContents.loadURL(url)\n\n      const webViewInfo: WebViewInfo = {\n        id,\n        view,\n        initialUrl: url,\n        currentUrl: url,\n        isActive: false,\n        isShow: false,\n      }\n      // view.webContents.on(\"did-navigate\", (event, url) => {\n      //   const win = BrowserWindow.fromWebContents(event.sender);\n      //   win?.webContents.send(\"url-updated\", url);\n      // });\n\n      view.webContents.on(\"did-navigate-in-page\", (event, url) => {\n        if (webViewInfo.isActive && webViewInfo.isShow && url !== 'about:blank?use=0' && url !== 'about:blank') {\n          console.log(\"did-navigate-in-page\", id, url)\n          this.win?.webContents.send(\"url-updated\", url);\n          return\n        }\n      });\n      // Listen for URL change events\n      view.webContents.on('did-navigate', (event, navigationUrl) => {\n\n        webViewInfo.currentUrl = navigationUrl\n        if (navigationUrl !== webViewInfo.initialUrl) {\n          webViewInfo.isActive = true\n        }\n        console.log(`Webview ${id} navigated to: ${navigationUrl}`)\n        if (webViewInfo.isActive && webViewInfo.isShow && navigationUrl !== 'about:blank?use=0' && navigationUrl !== 'about:blank') {\n          console.log(\"did-navigate\", id, url)\n          this.win?.webContents.send(\"url-updated\", url);\n          return\n        }\n        webViewInfo.view.setBounds({ x: -1919, y: -1079, width: 1920, height: 1080 })\n        const activeSize = this.getActiveWebview().length\n        const allSize = Array.from(this.webViews.values()).length\n        if (allSize - activeSize <= 3) {\n          const newId = Array.from(this.webViews.keys()).length + 2\n          this.createWebview(newId.toString(), 'about:blank?use=0')\n          this.createWebview((newId + 1).toString(), 'about:blank?use=0')\n          this.createWebview((newId + 2).toString(), 'about:blank?use=0')\n        }\n\n        // setTimeout(() => {\n        //   let newId = Number(id)\n        //   view.setBounds({ x: -9999 + newId * 100, y: -9999 + newId * 100, width: 100, height: 100 })\n        // }, 500)\n        // Notify frontend when URL changes\n        if (this.win && !this.win.isDestroyed()) {\n          this.win.webContents.send('webview-navigated', id, navigationUrl)\n        }\n      })\n\n\n      view.webContents.setWindowOpenHandler(({ url }) => {\n        view.webContents.loadURL(url)\n\n        return { action: 'deny' }\n      })\n      // Store in Map\n      this.webViews.set(id, webViewInfo)\n\n      this.win?.contentView.addChildView(view)\n      return { success: true, id, hidden: true }\n    } catch (error: any) {\n      console.error(`Failed to create hidden webview ${id}:`, error)\n      return { success: false, error: error.message }\n    }\n  }\n\n\n  public changeViewSize(id: string, size: Size) {\n    try {\n      const webViewInfo = this.webViews.get(id)\n      if (!webViewInfo) {\n        return { success: false, error: `Webview with id ${id} not found` }\n      }\n\n      const { x, y, width, height } = size\n      if (webViewInfo.isActive && webViewInfo.isShow) {\n        webViewInfo.view.setBounds({ x, y, width: Math.max(width, 100), height: Math.max(height, 100) })\n      } else {\n        let newId = Number(id)\n        webViewInfo.view.setBounds({ x: -9999 + newId * 100, y: -9999 + newId * 100, width: Math.max(width, 100), height: Math.max(height, 100) })\n      }\n\n      return { success: true }\n    } catch (error: any) {\n      console.error(`Failed to resize all webviews:`, error)\n      return { success: false, error: error.message }\n    }\n  }\n\n\n  public hideWebview(id: string) {\n    const webViewInfo = this.webViews.get(id)\n    if (!webViewInfo) {\n      return { success: false, error: `Webview with id ${id} not found` }\n    }\n    let newId = Number(id)\n    webViewInfo.view.setBounds({ x: -9999 + newId * 100, y: -9999 + newId * 100, width: 100, height: 100 })\n    webViewInfo.isShow = false\n\n    return { success: true }\n  }\n  public hideAllWebview() {\n    this.webViews.forEach(webview => {\n      let newId = Number(webview.id)\n      webview.view.setBounds({ x: -9999 + newId * 100, y: -9999 + newId * 100, width: 100, height: 100 })\n      webview.isShow = false\n    })\n  }\n\n  public showWebview(id: string) {\n    const webViewInfo = this.webViews.get(id)\n    if (!webViewInfo) {\n      return { success: false, error: `Webview with id ${id} not found` }\n    }\n    const currentUrl = webViewInfo.view.webContents.getURL();\n    this.win?.webContents.send(\"url-updated\", currentUrl);\n    webViewInfo.isShow = true\n    this.changeViewSize(id, this.size)\n    console.log(\"showWebview\", id, this.size)\n    if (this.win && !this.win.isDestroyed()) {\n      this.win.webContents.send('webview-show', id)\n    }\n\n    return { success: true }\n  }\n  public getShowWebview() {\n    return JSON.parse(JSON.stringify(Array.from(this.webViews.values()).filter(webview => webview.isShow).map(webview => webview.id)))\n  }\n\n  public destroyWebview(id: string) {\n    try {\n      const webViewInfo = this.webViews.get(id)\n      if (!webViewInfo) {\n        return { success: false, error: `Webview with id ${id} not found` }\n      }\n\n      // remove webview from parent container\n      if (this.win?.contentView) {\n        this.win.contentView.removeChildView(webViewInfo.view)\n      }\n\n      // destroy webview\n      webViewInfo.view.webContents.close()\n\n      // remove from Map\n      this.webViews.delete(id)\n\n      console.log(`Webview ${id} destroyed successfully`)\n      return { success: true }\n    } catch (error: any) {\n      console.error(`Failed to destroy webview ${id}:`, error)\n      return { success: false, error: error.message }\n    }\n  }\n\n  public distroy() {\n    // TODO: Destroy all webviews\n  }\n}\n\n", "import { ip<PERSON><PERSON><PERSON>, BrowserWindow, app } from 'electron'\nimport fs from 'fs'\nimport path from 'path'\nimport mammoth from 'mammoth'\nimport Papa from 'papaparse'\nimport * as unzipper from 'unzipper'\nimport { parseStringPromise } from 'xml2js'\n\n\nexport class FileReader {\n\tprivate win: BrowserWindow | null = null\n\tconstructor(window: BrowserWindow) {\n\t\tthis.win = window\n\t}\n\n\t// Remove automatic IPC handler registration from constructor\n\t// IPC handlers should be registered once in the main process\n\n\tprivate async parseDocx(filePath: string): Promise<string> {\n\t\ttry {\n\t\t\tconst result = await mammoth.convertToHtml({ path: filePath })\n\t\t\treturn result.value // The generated HTML\n\t\t} catch (error) {\n\t\t\tconsole.error('DOCX parsing error:', error)\n\t\t\tthrow error\n\t\t}\n\t}\n\n\tprivate async parseDoc(filePath: string): Promise<string> {\n\t\ttry {\n\t\t\tconst result = await mammoth.convertToHtml({ path: filePath })\n\t\t\treturn result.value // The generated HTML\n\t\t} catch (error) {\n\t\t\tconsole.error('DOC parsing error:', error)\n\t\t\tthrow error\n\t\t}\n\t}\n\n\tprivate async parsePptx(filePath: string): Promise<string> {\n\t\ttry {\n\t\t\tconst directory = await unzipper.Open.file(filePath)\n\t\t\tconst slideFiles = directory.files.filter((f: any) => f.path.match(/^ppt\\/slides\\/slide\\d+\\.xml$/))\n\n\t\t\tlet html = '<div style=\"font-family: sans-serif;\">'\n\n\t\t\tfor (let i = 0; i < slideFiles.length; i++) {\n\t\t\t\tconst file = slideFiles[i]\n\t\t\t\tconst contentBuffer = await file.buffer()\n\t\t\t\tconst content = contentBuffer.toString('utf-8')\n\t\t\t\tconst parsed = await parseStringPromise(content)\n\n\t\t\t\thtml += `<h3>Slide ${i + 1}</h3><ul>`\n\n\t\t\t\tconst texts = parsed['p:sld']['p:cSld'][0]['p:spTree'][0]['p:sp'] || []\n\n\t\t\t\tfor (const textNode of texts) {\n\t\t\t\t\tconst paras = textNode?.['p:txBody']?.[0]?.['a:p'] || []\n\t\t\t\t\tfor (const para of paras) {\n\t\t\t\t\t\tconst runs = para?.['a:r'] || []\n\t\t\t\t\t\tfor (const run of runs) {\n\t\t\t\t\t\t\tconst text = run?.['a:t']?.[0]\n\t\t\t\t\t\t\tif (text) {\n\t\t\t\t\t\t\t\thtml += `<li>${text}</li>`\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\thtml += '</ul><hr/>'\n\t\t\t}\n\n\t\t\thtml += '</div>'\n\t\t\treturn html\n\t\t} catch (error) {\n\t\t\tconsole.error('PPTX unzip parse error:', error)\n\t\t\tthrow error\n\t\t}\n\t}\n\n\tprivate async parseCsv(filePath: string): Promise<string> {\n\t\ttry {\n\t\t\tconst fileContent = fs.readFileSync(filePath, 'utf-8')\n\t\t\tconst result = Papa.parse(fileContent, {\n\t\t\t\theader: true,\n\t\t\t\tskipEmptyLines: true,\n\t\t\t\tdelimiter: \",\"\n\t\t\t})\n\n\t\t\t// Convert to HTML table\n\t\t\tif (result.data && result.data.length > 0) {\n\t\t\t\tconst headers = Object.keys(result.data[0] as string[])\n\t\t\t\tlet html = '<table style=\"border-collapse: collapse; width: 100%; font-family: monospace;\">'\n\n\t\t\t\t// Header row\n\t\t\t\thtml += '<thead><tr style=\"background-color: #f5f5f5;\">'\n\t\t\t\theaders.forEach(header => {\n\t\t\t\t\thtml += `<th style=\"border: 1px solid #ddd; padding: 8px; text-align: left;\">${header}</th>`\n\t\t\t\t})\n\t\t\t\thtml += '</tr></thead>'\n\n\t\t\t\t// Data rows\n\t\t\t\thtml += '<tbody>'\n\t\t\t\tresult.data.forEach((row: any) => {\n\t\t\t\t\thtml += '<tr>'\n\t\t\t\t\theaders.forEach(header => {\n\t\t\t\t\t\thtml += `<td style=\"border: 1px solid #ddd; padding: 8px;\">${row[header] || ''}</td>`\n\t\t\t\t\t})\n\t\t\t\t\thtml += '</tr>'\n\t\t\t\t})\n\t\t\t\thtml += '</tbody></table>'\n\n\t\t\t\treturn html\n\t\t\t}\n\t\t\treturn '<p>Empty CSV file</p>'\n\t\t} catch (error) {\n\t\t\tconsole.error('CSV parsing error:', error)\n\t\t\tthrow error\n\t\t}\n\t}\n\n\tpublic openFile(type: string, filePath: string, isShowSourceCode: boolean) {\n\t\treturn new Promise(async (resolve, reject) => {\n\t\t\ttry {\n\t\t\t\tif (type === 'md') {\n\t\t\t\t\tconst content = fs.readFileSync(filePath, 'utf-8')\n\t\t\t\t\tresolve(content)\n\t\t\t\t} else if (isShowSourceCode && type === 'html') {\n\t\t\t\t\tconst content = fs.readFileSync(filePath, 'utf-8')\n\t\t\t\t\tresolve(content)\n\t\t\t\t} else if ([\"pdf\", \"html\"].includes(type)) {\n\t\t\t\t\tresolve(filePath)\n\t\t\t\t} else if (type === \"csv\") {\n\t\t\t\t\ttry {\n\t\t\t\t\t\tconst htmlContent = await this.parseCsv(filePath)\n\t\t\t\t\t\tresolve(htmlContent)\n\t\t\t\t\t} catch (error) {\n\t\t\t\t\t\tconsole.warn('CSV parsing failed, reading as text:', error)\n\t\t\t\t\t\tconst content = fs.readFileSync(filePath, 'utf-8')\n\t\t\t\t\t\tresolve(content)\n\t\t\t\t\t}\n\t\t\t\t} else if (type === \"docx\") {\n\t\t\t\t\ttry {\n\t\t\t\t\t\tconst htmlContent = await this.parseDocx(filePath)\n\t\t\t\t\t\tresolve(htmlContent)\n\t\t\t\t\t} catch (error) {\n\t\t\t\t\t\tconsole.warn('DOCX parsing failed, reading as text:', error)\n\t\t\t\t\t\tconst content = fs.readFileSync(filePath, 'utf-8')\n\t\t\t\t\t\tresolve(content)\n\t\t\t\t\t}\n\t\t\t\t} else if (type === \"doc\") {\n\t\t\t\t\ttry {\n\t\t\t\t\t\tconst htmlContent = await this.parseDoc(filePath)\n\t\t\t\t\t\tresolve(htmlContent)\n\t\t\t\t\t} catch (error) {\n\t\t\t\t\t\tconsole.warn('DOC parsing failed, reading as text:', error)\n\t\t\t\t\t\tconst content = fs.readFileSync(filePath, 'utf-8')\n\t\t\t\t\t\tresolve(content)\n\t\t\t\t\t}\n\t\t\t\t} else if (type === 'pptx') {\n\t\t\t\t\ttry {\n\t\t\t\t\t\tconst htmlContent = await this.parsePptx(filePath)\n\t\t\t\t\t\tresolve(htmlContent)\n\t\t\t\t\t} catch (error) {\n\t\t\t\t\t\tconsole.warn('PPTX parsing failed, reading as binary string:', error)\n\t\t\t\t\t\tconst content = fs.readFileSync(filePath, 'base64') //  backup processing\n\t\t\t\t\t\tresolve(`<pre>${content}</pre>`)\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\tconst content = fs.readFileSync(filePath, 'utf-8')\n\t\t\t\t\tresolve(content)\n\t\t\t\t}\n\t\t\t} catch (error) {\n\t\t\t\treject(error)\n\t\t\t}\n\t\t})\n\t}\n\n\tpublic getFileList(email: string, taskId: string): FileInfo[] {\n\n\t\tconst safeEmail = email.split('@')[0].replace(/[\\\\/*?:\"<>|\\s]/g, \"_\").replace(/^\\.+|\\.+$/g, \"\");\n\n\t\tconst userHome = app.getPath('home');\n\t\tconst dirPath = path.join(userHome, \"eigent\", safeEmail, `task_${taskId}`);\n\n\t\ttry {\n\t\t\tif (!fs.existsSync(dirPath)) {\n\t\t\t\treturn [];\n\t\t\t}\n\t\t\tconst files = fs.readdirSync(dirPath);\n\n\t\t\treturn files.filter(file=>!file.startsWith(\".\")).map(file => {\n\t\t\t\treturn {\n\t\t\t\t\tpath: path.join(dirPath, file),\n\t\t\t\t\tname: file,\n\t\t\t\t\ttype: file.split('.').pop()?.toLowerCase() || '',\n\t\t\t\t}\n\t\t\t});\n\t\t} catch (err) {\n\t\t\tconsole.error(\"Load file failed:\", err);\n\t\t\treturn [];\n\t\t}\n\t}\n}\n\n", "import fs from 'fs';\nimport path from 'path';\nimport os from 'os';\n\nconst MCP_CONFIG_DIR = path.join(os.homedir(), '.eigent');\nconst MCP_CONFIG_PATH = path.join(MCP_CONFIG_DIR, 'mcp.json');\n\ntype McpServerConfig = {\n  command: string;\n  args: string[];\n  env?: Record<string, string>;\n} | {\n  url: string;\n};\n\ntype McpServersConfig = {\n  [name: string]: McpServerConfig;\n};\n\ntype ConfigFile = {\n  mcpServers: McpServersConfig;\n};\n\nexport function getMcpConfigPath() {\n  return MCP_CONFIG_PATH;\n}\n\nfunction getDefaultConfig(): ConfigFile {\n  return { mcpServers: {} };\n}\n\nexport function readMcpConfig(): ConfigFile {\n  try {\n    if (!fs.existsSync(MCP_CONFIG_PATH)) {\n      // init config file\n      writeMcpConfig(getDefaultConfig());\n      return getDefaultConfig();\n    }\n    const data = fs.readFileSync(MCP_CONFIG_PATH, 'utf-8');\n    const parsed = JSON.parse(data);\n    // compatible with old format\n    if (!parsed.mcpServers || typeof parsed.mcpServers !== 'object') {\n      return getDefaultConfig();\n    }\n    return parsed;\n  } catch (e) {\n    return getDefaultConfig();\n  }\n}\n\nexport function writeMcpConfig(config: ConfigFile): void {\n  if (!fs.existsSync(MCP_CONFIG_DIR)) {\n    fs.mkdirSync(MCP_CONFIG_DIR, { recursive: true });\n  }\n  fs.writeFileSync(MCP_CONFIG_PATH, JSON.stringify(config, null, 2), 'utf-8');\n}\n\nexport function addMcp(name: string, mcp: McpServerConfig): void {\n  const config = readMcpConfig();\n  if (!config.mcpServers[name]) {\n    config.mcpServers[name] = mcp;\n    writeMcpConfig(config);\n  }\n}\n\nexport function removeMcp(name: string): void {\n  const config = readMcpConfig();\n  console.log('removeMcp', name)\n  if (config.mcpServers[name]) {\n    delete config.mcpServers[name];\n    writeMcpConfig(config);\n  }\n}\n\nexport function updateMcp(name: string, mcp: McpServerConfig): void {\n  const config = readMcpConfig();\n  config.mcpServers[name] = mcp;\n  writeMcpConfig(config);\n} ", "import fs from 'fs';\nimport path from 'path';\nimport os from 'os'\n\nexport const ENV_START = '# === MCP INTEGRATION ENV START ===';\nexport const ENV_END = '# === MCP INTEGRATION ENV END ===';\n\nexport function getEnvPath(email: string) {\n  const tempEmail = email.split(\"@\")[0].replace(/[\\\\/*?:\"<>|\\s]/g, \"_\").replace(\".\", \"_\")\n  const envPath = path.join(os.homedir(), '.eigent', '.env.' + tempEmail)\n  const defaultEnv = path.join(process.resourcesPath, 'backend', '.env');\n  if (!fs.existsSync(envPath) && fs.existsSync(defaultEnv)) {\n    fs.copyFileSync(defaultEnv, envPath);\n    fs.chmodSync(envPath, 0o600);\n  }\n\n  return envPath;\n}\n\nexport function parseEnvBlock(content: string) {\n  const lines = content.split(/\\r?\\n/);\n  let start = lines.findIndex(l => l.trim() === ENV_START);\n  let end = lines.findIndex(l => l.trim() === ENV_END);\n  if (start === -1) start = lines.length;\n  if (end === -1) end = lines.length;\n  return { lines, start, end };\n}\n\nexport function updateEnvBlock(lines: string[], kv: Record<string, string>) {\n  //  Extract block \n  let start = lines.findIndex(l => l.trim() === ENV_START);\n  let end = lines.findIndex(l => l.trim() === ENV_END);\n  if (start === -1 || end === -1 || end < start) {\n    //  No block, append\n    lines.push(ENV_START);\n    Object.entries(kv).forEach(([k, v]) => {\n      lines.push(`${k}=${v}`);\n    });\n    lines.push(ENV_END);\n    return lines;\n  }\n  //  Parse block content\n  const block = lines.slice(start + 1, end);\n  const map: Record<string, string> = {};\n  block.forEach(line => {\n    const m = line.match(/^([A-Z0-9_]+)=(.*)$/);\n    if (m) map[m[1]] = m[2];\n  });\n  //  Overwrite/add\n  Object.entries(kv).forEach(([k, v]) => {\n    map[k] = v;\n  });\n  //  Reassemble block\n  const newBlock = Object.entries(map).map(([k, v]) => `${k}=${v}`);\n  return [\n    ...lines.slice(0, start + 1),\n    ...newBlock,\n    ...lines.slice(end)\n  ];\n}\n\nexport function removeEnvKey(lines: string[], key: string) {\n  let start = lines.findIndex(l => l.trim() === ENV_START);\n  let end = lines.findIndex(l => l.trim() === ENV_END);\n  if (start === -1 || end === -1 || end < start) return lines;\n  const block = lines.slice(start + 1, end);\n  const newBlock = block.filter(line => !line.startsWith(key + '='));\n  return [\n    ...lines.slice(0, start + 1),\n    ...newBlock,\n    ...lines.slice(end)\n  ];\n} ", "'use strict'\n\nexports.fromCallback = function (fn) {\n  return Object.defineProperty(function (...args) {\n    if (typeof args[args.length - 1] === 'function') fn.apply(this, args)\n    else {\n      return new Promise((resolve, reject) => {\n        args.push((err, res) => (err != null) ? reject(err) : resolve(res))\n        fn.apply(this, args)\n      })\n    }\n  }, 'name', { value: fn.name })\n}\n\nexports.fromPromise = function (fn) {\n  return Object.defineProperty(function (...args) {\n    const cb = args[args.length - 1]\n    if (typeof cb !== 'function') return fn.apply(this, args)\n    else {\n      args.pop()\n      fn.apply(this, args).then(r => cb(null, r), cb)\n    }\n  }, 'name', { value: fn.name })\n}\n", "var constants = require('constants')\n\nvar origCwd = process.cwd\nvar cwd = null\n\nvar platform = process.env.GRACEFUL_FS_PLATFORM || process.platform\n\nprocess.cwd = function() {\n  if (!cwd)\n    cwd = origCwd.call(process)\n  return cwd\n}\ntry {\n  process.cwd()\n} catch (er) {}\n\n// This check is needed until node.js 12 is required\nif (typeof process.chdir === 'function') {\n  var chdir = process.chdir\n  process.chdir = function (d) {\n    cwd = null\n    chdir.call(process, d)\n  }\n  if (Object.setPrototypeOf) Object.setPrototypeOf(process.chdir, chdir)\n}\n\nmodule.exports = patch\n\nfunction patch (fs) {\n  // (re-)implement some things that are known busted or missing.\n\n  // lchmod, broken prior to 0.6.2\n  // back-port the fix here.\n  if (constants.hasOwnProperty('O_SYMLINK') &&\n      process.version.match(/^v0\\.6\\.[0-2]|^v0\\.5\\./)) {\n    patchLchmod(fs)\n  }\n\n  // lutimes implementation, or no-op\n  if (!fs.lutimes) {\n    patchLutimes(fs)\n  }\n\n  // https://github.com/isaacs/node-graceful-fs/issues/4\n  // Chown should not fail on einval or eperm if non-root.\n  // It should not fail on enosys ever, as this just indicates\n  // that a fs doesn't support the intended operation.\n\n  fs.chown = chownFix(fs.chown)\n  fs.fchown = chownFix(fs.fchown)\n  fs.lchown = chownFix(fs.lchown)\n\n  fs.chmod = chmodFix(fs.chmod)\n  fs.fchmod = chmodFix(fs.fchmod)\n  fs.lchmod = chmodFix(fs.lchmod)\n\n  fs.chownSync = chownFixSync(fs.chownSync)\n  fs.fchownSync = chownFixSync(fs.fchownSync)\n  fs.lchownSync = chownFixSync(fs.lchownSync)\n\n  fs.chmodSync = chmodFixSync(fs.chmodSync)\n  fs.fchmodSync = chmodFixSync(fs.fchmodSync)\n  fs.lchmodSync = chmodFixSync(fs.lchmodSync)\n\n  fs.stat = statFix(fs.stat)\n  fs.fstat = statFix(fs.fstat)\n  fs.lstat = statFix(fs.lstat)\n\n  fs.statSync = statFixSync(fs.statSync)\n  fs.fstatSync = statFixSync(fs.fstatSync)\n  fs.lstatSync = statFixSync(fs.lstatSync)\n\n  // if lchmod/lchown do not exist, then make them no-ops\n  if (fs.chmod && !fs.lchmod) {\n    fs.lchmod = function (path, mode, cb) {\n      if (cb) process.nextTick(cb)\n    }\n    fs.lchmodSync = function () {}\n  }\n  if (fs.chown && !fs.lchown) {\n    fs.lchown = function (path, uid, gid, cb) {\n      if (cb) process.nextTick(cb)\n    }\n    fs.lchownSync = function () {}\n  }\n\n  // on Windows, A/V software can lock the directory, causing this\n  // to fail with an EACCES or EPERM if the directory contains newly\n  // created files.  Try again on failure, for up to 60 seconds.\n\n  // Set the timeout this long because some Windows Anti-Virus, such as Parity\n  // bit9, may lock files for up to a minute, causing npm package install\n  // failures. Also, take care to yield the scheduler. Windows scheduling gives\n  // CPU to a busy looping process, which can cause the program causing the lock\n  // contention to be starved of CPU by node, so the contention doesn't resolve.\n  if (platform === \"win32\") {\n    fs.rename = typeof fs.rename !== 'function' ? fs.rename\n    : (function (fs$rename) {\n      function rename (from, to, cb) {\n        var start = Date.now()\n        var backoff = 0;\n        fs$rename(from, to, function CB (er) {\n          if (er\n              && (er.code === \"EACCES\" || er.code === \"EPERM\" || er.code === \"EBUSY\")\n              && Date.now() - start < 60000) {\n            setTimeout(function() {\n              fs.stat(to, function (stater, st) {\n                if (stater && stater.code === \"ENOENT\")\n                  fs$rename(from, to, CB);\n                else\n                  cb(er)\n              })\n            }, backoff)\n            if (backoff < 100)\n              backoff += 10;\n            return;\n          }\n          if (cb) cb(er)\n        })\n      }\n      if (Object.setPrototypeOf) Object.setPrototypeOf(rename, fs$rename)\n      return rename\n    })(fs.rename)\n  }\n\n  // if read() returns EAGAIN, then just try it again.\n  fs.read = typeof fs.read !== 'function' ? fs.read\n  : (function (fs$read) {\n    function read (fd, buffer, offset, length, position, callback_) {\n      var callback\n      if (callback_ && typeof callback_ === 'function') {\n        var eagCounter = 0\n        callback = function (er, _, __) {\n          if (er && er.code === 'EAGAIN' && eagCounter < 10) {\n            eagCounter ++\n            return fs$read.call(fs, fd, buffer, offset, length, position, callback)\n          }\n          callback_.apply(this, arguments)\n        }\n      }\n      return fs$read.call(fs, fd, buffer, offset, length, position, callback)\n    }\n\n    // This ensures `util.promisify` works as it does for native `fs.read`.\n    if (Object.setPrototypeOf) Object.setPrototypeOf(read, fs$read)\n    return read\n  })(fs.read)\n\n  fs.readSync = typeof fs.readSync !== 'function' ? fs.readSync\n  : (function (fs$readSync) { return function (fd, buffer, offset, length, position) {\n    var eagCounter = 0\n    while (true) {\n      try {\n        return fs$readSync.call(fs, fd, buffer, offset, length, position)\n      } catch (er) {\n        if (er.code === 'EAGAIN' && eagCounter < 10) {\n          eagCounter ++\n          continue\n        }\n        throw er\n      }\n    }\n  }})(fs.readSync)\n\n  function patchLchmod (fs) {\n    fs.lchmod = function (path, mode, callback) {\n      fs.open( path\n             , constants.O_WRONLY | constants.O_SYMLINK\n             , mode\n             , function (err, fd) {\n        if (err) {\n          if (callback) callback(err)\n          return\n        }\n        // prefer to return the chmod error, if one occurs,\n        // but still try to close, and report closing errors if they occur.\n        fs.fchmod(fd, mode, function (err) {\n          fs.close(fd, function(err2) {\n            if (callback) callback(err || err2)\n          })\n        })\n      })\n    }\n\n    fs.lchmodSync = function (path, mode) {\n      var fd = fs.openSync(path, constants.O_WRONLY | constants.O_SYMLINK, mode)\n\n      // prefer to return the chmod error, if one occurs,\n      // but still try to close, and report closing errors if they occur.\n      var threw = true\n      var ret\n      try {\n        ret = fs.fchmodSync(fd, mode)\n        threw = false\n      } finally {\n        if (threw) {\n          try {\n            fs.closeSync(fd)\n          } catch (er) {}\n        } else {\n          fs.closeSync(fd)\n        }\n      }\n      return ret\n    }\n  }\n\n  function patchLutimes (fs) {\n    if (constants.hasOwnProperty(\"O_SYMLINK\") && fs.futimes) {\n      fs.lutimes = function (path, at, mt, cb) {\n        fs.open(path, constants.O_SYMLINK, function (er, fd) {\n          if (er) {\n            if (cb) cb(er)\n            return\n          }\n          fs.futimes(fd, at, mt, function (er) {\n            fs.close(fd, function (er2) {\n              if (cb) cb(er || er2)\n            })\n          })\n        })\n      }\n\n      fs.lutimesSync = function (path, at, mt) {\n        var fd = fs.openSync(path, constants.O_SYMLINK)\n        var ret\n        var threw = true\n        try {\n          ret = fs.futimesSync(fd, at, mt)\n          threw = false\n        } finally {\n          if (threw) {\n            try {\n              fs.closeSync(fd)\n            } catch (er) {}\n          } else {\n            fs.closeSync(fd)\n          }\n        }\n        return ret\n      }\n\n    } else if (fs.futimes) {\n      fs.lutimes = function (_a, _b, _c, cb) { if (cb) process.nextTick(cb) }\n      fs.lutimesSync = function () {}\n    }\n  }\n\n  function chmodFix (orig) {\n    if (!orig) return orig\n    return function (target, mode, cb) {\n      return orig.call(fs, target, mode, function (er) {\n        if (chownErOk(er)) er = null\n        if (cb) cb.apply(this, arguments)\n      })\n    }\n  }\n\n  function chmodFixSync (orig) {\n    if (!orig) return orig\n    return function (target, mode) {\n      try {\n        return orig.call(fs, target, mode)\n      } catch (er) {\n        if (!chownErOk(er)) throw er\n      }\n    }\n  }\n\n\n  function chownFix (orig) {\n    if (!orig) return orig\n    return function (target, uid, gid, cb) {\n      return orig.call(fs, target, uid, gid, function (er) {\n        if (chownErOk(er)) er = null\n        if (cb) cb.apply(this, arguments)\n      })\n    }\n  }\n\n  function chownFixSync (orig) {\n    if (!orig) return orig\n    return function (target, uid, gid) {\n      try {\n        return orig.call(fs, target, uid, gid)\n      } catch (er) {\n        if (!chownErOk(er)) throw er\n      }\n    }\n  }\n\n  function statFix (orig) {\n    if (!orig) return orig\n    // Older versions of Node erroneously returned signed integers for\n    // uid + gid.\n    return function (target, options, cb) {\n      if (typeof options === 'function') {\n        cb = options\n        options = null\n      }\n      function callback (er, stats) {\n        if (stats) {\n          if (stats.uid < 0) stats.uid += 0x100000000\n          if (stats.gid < 0) stats.gid += 0x100000000\n        }\n        if (cb) cb.apply(this, arguments)\n      }\n      return options ? orig.call(fs, target, options, callback)\n        : orig.call(fs, target, callback)\n    }\n  }\n\n  function statFixSync (orig) {\n    if (!orig) return orig\n    // Older versions of Node erroneously returned signed integers for\n    // uid + gid.\n    return function (target, options) {\n      var stats = options ? orig.call(fs, target, options)\n        : orig.call(fs, target)\n      if (stats) {\n        if (stats.uid < 0) stats.uid += 0x100000000\n        if (stats.gid < 0) stats.gid += 0x100000000\n      }\n      return stats;\n    }\n  }\n\n  // ENOSYS means that the fs doesn't support the op. Just ignore\n  // that, because it doesn't matter.\n  //\n  // if there's no getuid, or if getuid() is something other\n  // than 0, and the error is EINVAL or EPERM, then just ignore\n  // it.\n  //\n  // This specific case is a silent failure in cp, install, tar,\n  // and most other unix tools that manage permissions.\n  //\n  // When running as root, or if other types of errors are\n  // encountered, then it's strict.\n  function chownErOk (er) {\n    if (!er)\n      return true\n\n    if (er.code === \"ENOSYS\")\n      return true\n\n    var nonroot = !process.getuid || process.getuid() !== 0\n    if (nonroot) {\n      if (er.code === \"EINVAL\" || er.code === \"EPERM\")\n        return true\n    }\n\n    return false\n  }\n}\n", "var Stream = require('stream').Stream\n\nmodule.exports = legacy\n\nfunction legacy (fs) {\n  return {\n    ReadStream: ReadStream,\n    WriteStream: WriteStream\n  }\n\n  function ReadStream (path, options) {\n    if (!(this instanceof ReadStream)) return new ReadStream(path, options);\n\n    Stream.call(this);\n\n    var self = this;\n\n    this.path = path;\n    this.fd = null;\n    this.readable = true;\n    this.paused = false;\n\n    this.flags = 'r';\n    this.mode = 438; /*=0666*/\n    this.bufferSize = 64 * 1024;\n\n    options = options || {};\n\n    // Mixin options into this\n    var keys = Object.keys(options);\n    for (var index = 0, length = keys.length; index < length; index++) {\n      var key = keys[index];\n      this[key] = options[key];\n    }\n\n    if (this.encoding) this.setEncoding(this.encoding);\n\n    if (this.start !== undefined) {\n      if ('number' !== typeof this.start) {\n        throw TypeError('start must be a Number');\n      }\n      if (this.end === undefined) {\n        this.end = Infinity;\n      } else if ('number' !== typeof this.end) {\n        throw TypeError('end must be a Number');\n      }\n\n      if (this.start > this.end) {\n        throw new Error('start must be <= end');\n      }\n\n      this.pos = this.start;\n    }\n\n    if (this.fd !== null) {\n      process.nextTick(function() {\n        self._read();\n      });\n      return;\n    }\n\n    fs.open(this.path, this.flags, this.mode, function (err, fd) {\n      if (err) {\n        self.emit('error', err);\n        self.readable = false;\n        return;\n      }\n\n      self.fd = fd;\n      self.emit('open', fd);\n      self._read();\n    })\n  }\n\n  function WriteStream (path, options) {\n    if (!(this instanceof WriteStream)) return new WriteStream(path, options);\n\n    Stream.call(this);\n\n    this.path = path;\n    this.fd = null;\n    this.writable = true;\n\n    this.flags = 'w';\n    this.encoding = 'binary';\n    this.mode = 438; /*=0666*/\n    this.bytesWritten = 0;\n\n    options = options || {};\n\n    // Mixin options into this\n    var keys = Object.keys(options);\n    for (var index = 0, length = keys.length; index < length; index++) {\n      var key = keys[index];\n      this[key] = options[key];\n    }\n\n    if (this.start !== undefined) {\n      if ('number' !== typeof this.start) {\n        throw TypeError('start must be a Number');\n      }\n      if (this.start < 0) {\n        throw new Error('start must be >= zero');\n      }\n\n      this.pos = this.start;\n    }\n\n    this.busy = false;\n    this._queue = [];\n\n    if (this.fd === null) {\n      this._open = fs.open;\n      this._queue.push([this._open, this.path, this.flags, this.mode, undefined]);\n      this.flush();\n    }\n  }\n}\n", "'use strict'\n\nmodule.exports = clone\n\nvar getPrototypeOf = Object.getPrototypeOf || function (obj) {\n  return obj.__proto__\n}\n\nfunction clone (obj) {\n  if (obj === null || typeof obj !== 'object')\n    return obj\n\n  if (obj instanceof Object)\n    var copy = { __proto__: getPrototypeOf(obj) }\n  else\n    var copy = Object.create(null)\n\n  Object.getOwnPropertyNames(obj).forEach(function (key) {\n    Object.defineProperty(copy, key, Object.getOwnPropertyDescriptor(obj, key))\n  })\n\n  return copy\n}\n", "var fs = require('fs')\nvar polyfills = require('./polyfills.js')\nvar legacy = require('./legacy-streams.js')\nvar clone = require('./clone.js')\n\nvar util = require('util')\n\n/* istanbul ignore next - node 0.x polyfill */\nvar gracefulQueue\nvar previousSymbol\n\n/* istanbul ignore else - node 0.x polyfill */\nif (typeof Symbol === 'function' && typeof Symbol.for === 'function') {\n  gracefulQueue = Symbol.for('graceful-fs.queue')\n  // This is used in testing by future versions\n  previousSymbol = Symbol.for('graceful-fs.previous')\n} else {\n  gracefulQueue = '___graceful-fs.queue'\n  previousSymbol = '___graceful-fs.previous'\n}\n\nfunction noop () {}\n\nfunction publishQueue(context, queue) {\n  Object.defineProperty(context, gracefulQueue, {\n    get: function() {\n      return queue\n    }\n  })\n}\n\nvar debug = noop\nif (util.debuglog)\n  debug = util.debuglog('gfs4')\nelse if (/\\bgfs4\\b/i.test(process.env.NODE_DEBUG || ''))\n  debug = function() {\n    var m = util.format.apply(util, arguments)\n    m = 'GFS4: ' + m.split(/\\n/).join('\\nGFS4: ')\n    console.error(m)\n  }\n\n// Once time initialization\nif (!fs[gracefulQueue]) {\n  // This queue can be shared by multiple loaded instances\n  var queue = global[gracefulQueue] || []\n  publishQueue(fs, queue)\n\n  // Patch fs.close/closeSync to shared queue version, because we need\n  // to retry() whenever a close happens *anywhere* in the program.\n  // This is essential when multiple graceful-fs instances are\n  // in play at the same time.\n  fs.close = (function (fs$close) {\n    function close (fd, cb) {\n      return fs$close.call(fs, fd, function (err) {\n        // This function uses the graceful-fs shared queue\n        if (!err) {\n          resetQueue()\n        }\n\n        if (typeof cb === 'function')\n          cb.apply(this, arguments)\n      })\n    }\n\n    Object.defineProperty(close, previousSymbol, {\n      value: fs$close\n    })\n    return close\n  })(fs.close)\n\n  fs.closeSync = (function (fs$closeSync) {\n    function closeSync (fd) {\n      // This function uses the graceful-fs shared queue\n      fs$closeSync.apply(fs, arguments)\n      resetQueue()\n    }\n\n    Object.defineProperty(closeSync, previousSymbol, {\n      value: fs$closeSync\n    })\n    return closeSync\n  })(fs.closeSync)\n\n  if (/\\bgfs4\\b/i.test(process.env.NODE_DEBUG || '')) {\n    process.on('exit', function() {\n      debug(fs[gracefulQueue])\n      require('assert').equal(fs[gracefulQueue].length, 0)\n    })\n  }\n}\n\nif (!global[gracefulQueue]) {\n  publishQueue(global, fs[gracefulQueue]);\n}\n\nmodule.exports = patch(clone(fs))\nif (process.env.TEST_GRACEFUL_FS_GLOBAL_PATCH && !fs.__patched) {\n    module.exports = patch(fs)\n    fs.__patched = true;\n}\n\nfunction patch (fs) {\n  // Everything that references the open() function needs to be in here\n  polyfills(fs)\n  fs.gracefulify = patch\n\n  fs.createReadStream = createReadStream\n  fs.createWriteStream = createWriteStream\n  var fs$readFile = fs.readFile\n  fs.readFile = readFile\n  function readFile (path, options, cb) {\n    if (typeof options === 'function')\n      cb = options, options = null\n\n    return go$readFile(path, options, cb)\n\n    function go$readFile (path, options, cb, startTime) {\n      return fs$readFile(path, options, function (err) {\n        if (err && (err.code === 'EMFILE' || err.code === 'ENFILE'))\n          enqueue([go$readFile, [path, options, cb], err, startTime || Date.now(), Date.now()])\n        else {\n          if (typeof cb === 'function')\n            cb.apply(this, arguments)\n        }\n      })\n    }\n  }\n\n  var fs$writeFile = fs.writeFile\n  fs.writeFile = writeFile\n  function writeFile (path, data, options, cb) {\n    if (typeof options === 'function')\n      cb = options, options = null\n\n    return go$writeFile(path, data, options, cb)\n\n    function go$writeFile (path, data, options, cb, startTime) {\n      return fs$writeFile(path, data, options, function (err) {\n        if (err && (err.code === 'EMFILE' || err.code === 'ENFILE'))\n          enqueue([go$writeFile, [path, data, options, cb], err, startTime || Date.now(), Date.now()])\n        else {\n          if (typeof cb === 'function')\n            cb.apply(this, arguments)\n        }\n      })\n    }\n  }\n\n  var fs$appendFile = fs.appendFile\n  if (fs$appendFile)\n    fs.appendFile = appendFile\n  function appendFile (path, data, options, cb) {\n    if (typeof options === 'function')\n      cb = options, options = null\n\n    return go$appendFile(path, data, options, cb)\n\n    function go$appendFile (path, data, options, cb, startTime) {\n      return fs$appendFile(path, data, options, function (err) {\n        if (err && (err.code === 'EMFILE' || err.code === 'ENFILE'))\n          enqueue([go$appendFile, [path, data, options, cb], err, startTime || Date.now(), Date.now()])\n        else {\n          if (typeof cb === 'function')\n            cb.apply(this, arguments)\n        }\n      })\n    }\n  }\n\n  var fs$copyFile = fs.copyFile\n  if (fs$copyFile)\n    fs.copyFile = copyFile\n  function copyFile (src, dest, flags, cb) {\n    if (typeof flags === 'function') {\n      cb = flags\n      flags = 0\n    }\n    return go$copyFile(src, dest, flags, cb)\n\n    function go$copyFile (src, dest, flags, cb, startTime) {\n      return fs$copyFile(src, dest, flags, function (err) {\n        if (err && (err.code === 'EMFILE' || err.code === 'ENFILE'))\n          enqueue([go$copyFile, [src, dest, flags, cb], err, startTime || Date.now(), Date.now()])\n        else {\n          if (typeof cb === 'function')\n            cb.apply(this, arguments)\n        }\n      })\n    }\n  }\n\n  var fs$readdir = fs.readdir\n  fs.readdir = readdir\n  var noReaddirOptionVersions = /^v[0-5]\\./\n  function readdir (path, options, cb) {\n    if (typeof options === 'function')\n      cb = options, options = null\n\n    var go$readdir = noReaddirOptionVersions.test(process.version)\n      ? function go$readdir (path, options, cb, startTime) {\n        return fs$readdir(path, fs$readdirCallback(\n          path, options, cb, startTime\n        ))\n      }\n      : function go$readdir (path, options, cb, startTime) {\n        return fs$readdir(path, options, fs$readdirCallback(\n          path, options, cb, startTime\n        ))\n      }\n\n    return go$readdir(path, options, cb)\n\n    function fs$readdirCallback (path, options, cb, startTime) {\n      return function (err, files) {\n        if (err && (err.code === 'EMFILE' || err.code === 'ENFILE'))\n          enqueue([\n            go$readdir,\n            [path, options, cb],\n            err,\n            startTime || Date.now(),\n            Date.now()\n          ])\n        else {\n          if (files && files.sort)\n            files.sort()\n\n          if (typeof cb === 'function')\n            cb.call(this, err, files)\n        }\n      }\n    }\n  }\n\n  if (process.version.substr(0, 4) === 'v0.8') {\n    var legStreams = legacy(fs)\n    ReadStream = legStreams.ReadStream\n    WriteStream = legStreams.WriteStream\n  }\n\n  var fs$ReadStream = fs.ReadStream\n  if (fs$ReadStream) {\n    ReadStream.prototype = Object.create(fs$ReadStream.prototype)\n    ReadStream.prototype.open = ReadStream$open\n  }\n\n  var fs$WriteStream = fs.WriteStream\n  if (fs$WriteStream) {\n    WriteStream.prototype = Object.create(fs$WriteStream.prototype)\n    WriteStream.prototype.open = WriteStream$open\n  }\n\n  Object.defineProperty(fs, 'ReadStream', {\n    get: function () {\n      return ReadStream\n    },\n    set: function (val) {\n      ReadStream = val\n    },\n    enumerable: true,\n    configurable: true\n  })\n  Object.defineProperty(fs, 'WriteStream', {\n    get: function () {\n      return WriteStream\n    },\n    set: function (val) {\n      WriteStream = val\n    },\n    enumerable: true,\n    configurable: true\n  })\n\n  // legacy names\n  var FileReadStream = ReadStream\n  Object.defineProperty(fs, 'FileReadStream', {\n    get: function () {\n      return FileReadStream\n    },\n    set: function (val) {\n      FileReadStream = val\n    },\n    enumerable: true,\n    configurable: true\n  })\n  var FileWriteStream = WriteStream\n  Object.defineProperty(fs, 'FileWriteStream', {\n    get: function () {\n      return FileWriteStream\n    },\n    set: function (val) {\n      FileWriteStream = val\n    },\n    enumerable: true,\n    configurable: true\n  })\n\n  function ReadStream (path, options) {\n    if (this instanceof ReadStream)\n      return fs$ReadStream.apply(this, arguments), this\n    else\n      return ReadStream.apply(Object.create(ReadStream.prototype), arguments)\n  }\n\n  function ReadStream$open () {\n    var that = this\n    open(that.path, that.flags, that.mode, function (err, fd) {\n      if (err) {\n        if (that.autoClose)\n          that.destroy()\n\n        that.emit('error', err)\n      } else {\n        that.fd = fd\n        that.emit('open', fd)\n        that.read()\n      }\n    })\n  }\n\n  function WriteStream (path, options) {\n    if (this instanceof WriteStream)\n      return fs$WriteStream.apply(this, arguments), this\n    else\n      return WriteStream.apply(Object.create(WriteStream.prototype), arguments)\n  }\n\n  function WriteStream$open () {\n    var that = this\n    open(that.path, that.flags, that.mode, function (err, fd) {\n      if (err) {\n        that.destroy()\n        that.emit('error', err)\n      } else {\n        that.fd = fd\n        that.emit('open', fd)\n      }\n    })\n  }\n\n  function createReadStream (path, options) {\n    return new fs.ReadStream(path, options)\n  }\n\n  function createWriteStream (path, options) {\n    return new fs.WriteStream(path, options)\n  }\n\n  var fs$open = fs.open\n  fs.open = open\n  function open (path, flags, mode, cb) {\n    if (typeof mode === 'function')\n      cb = mode, mode = null\n\n    return go$open(path, flags, mode, cb)\n\n    function go$open (path, flags, mode, cb, startTime) {\n      return fs$open(path, flags, mode, function (err, fd) {\n        if (err && (err.code === 'EMFILE' || err.code === 'ENFILE'))\n          enqueue([go$open, [path, flags, mode, cb], err, startTime || Date.now(), Date.now()])\n        else {\n          if (typeof cb === 'function')\n            cb.apply(this, arguments)\n        }\n      })\n    }\n  }\n\n  return fs\n}\n\nfunction enqueue (elem) {\n  debug('ENQUEUE', elem[0].name, elem[1])\n  fs[gracefulQueue].push(elem)\n  retry()\n}\n\n// keep track of the timeout between retry() calls\nvar retryTimer\n\n// reset the startTime and lastTime to now\n// this resets the start of the 60 second overall timeout as well as the\n// delay between attempts so that we'll retry these jobs sooner\nfunction resetQueue () {\n  var now = Date.now()\n  for (var i = 0; i < fs[gracefulQueue].length; ++i) {\n    // entries that are only a length of 2 are from an older version, don't\n    // bother modifying those since they'll be retried anyway.\n    if (fs[gracefulQueue][i].length > 2) {\n      fs[gracefulQueue][i][3] = now // startTime\n      fs[gracefulQueue][i][4] = now // lastTime\n    }\n  }\n  // call retry to make sure we're actively processing the queue\n  retry()\n}\n\nfunction retry () {\n  // clear the timer and remove it to help prevent unintended concurrency\n  clearTimeout(retryTimer)\n  retryTimer = undefined\n\n  if (fs[gracefulQueue].length === 0)\n    return\n\n  var elem = fs[gracefulQueue].shift()\n  var fn = elem[0]\n  var args = elem[1]\n  // these items may be unset if they were added by an older graceful-fs\n  var err = elem[2]\n  var startTime = elem[3]\n  var lastTime = elem[4]\n\n  // if we don't have a startTime we have no way of knowing if we've waited\n  // long enough, so go ahead and retry this item now\n  if (startTime === undefined) {\n    debug('RETRY', fn.name, args)\n    fn.apply(null, args)\n  } else if (Date.now() - startTime >= 60000) {\n    // it's been more than 60 seconds total, bail now\n    debug('TIMEOUT', fn.name, args)\n    var cb = args.pop()\n    if (typeof cb === 'function')\n      cb.call(null, err)\n  } else {\n    // the amount of time between the last attempt and right now\n    var sinceAttempt = Date.now() - lastTime\n    // the amount of time between when we first tried, and when we last tried\n    // rounded up to at least 1\n    var sinceStart = Math.max(lastTime - startTime, 1)\n    // backoff. wait longer than the total time we've been retrying, but only\n    // up to a maximum of 100ms\n    var desiredDelay = Math.min(sinceStart * 1.2, 100)\n    // it's been long enough since the last retry, do it again\n    if (sinceAttempt >= desiredDelay) {\n      debug('RETRY', fn.name, args)\n      fn.apply(null, args.concat([startTime]))\n    } else {\n      // if we can't do this job yet, push it to the end of the queue\n      // and let the next iteration check again\n      fs[gracefulQueue].push(elem)\n    }\n  }\n\n  // schedule our next run if one isn't already scheduled\n  if (retryTimer === undefined) {\n    retryTimer = setTimeout(retry, 0)\n  }\n}\n", "'use strict'\n// This is adapted from https://github.com/normalize/mz\n// Copyright (c) 2014-2016 <PERSON>g <EMAIL> and Contributors\nconst u = require('universalify').fromCallback\nconst fs = require('graceful-fs')\n\nconst api = [\n  'access',\n  'appendFile',\n  'chmod',\n  'chown',\n  'close',\n  'copyFile',\n  'fchmod',\n  'fchown',\n  'fdatasync',\n  'fstat',\n  'fsync',\n  'ftruncate',\n  'futimes',\n  'lchmod',\n  'lchown',\n  'link',\n  'lstat',\n  'mkdir',\n  'mkdtemp',\n  'open',\n  'opendir',\n  'readdir',\n  'readFile',\n  'readlink',\n  'realpath',\n  'rename',\n  'rm',\n  'rmdir',\n  'stat',\n  'symlink',\n  'truncate',\n  'unlink',\n  'utimes',\n  'writeFile'\n].filter(key => {\n  // Some commands are not available on some systems. Ex:\n  // fs.opendir was added in Node.js v12.12.0\n  // fs.rm was added in Node.js v14.14.0\n  // fs.lchown is not available on at least some Linux\n  return typeof fs[key] === 'function'\n})\n\n// Export all keys:\nObject.keys(fs).forEach(key => {\n  if (key === 'promises') {\n    // fs.promises is a getter property that triggers ExperimentalWarning\n    // Don't re-export it here, the getter is defined in \"lib/index.js\"\n    return\n  }\n  exports[key] = fs[key]\n})\n\n// Universalify async methods:\napi.forEach(method => {\n  exports[method] = u(fs[method])\n})\n\n// We differ from mz/fs in that we still ship the old, broken, fs.exists()\n// since we are a drop-in replacement for the native module\nexports.exists = function (filename, callback) {\n  if (typeof callback === 'function') {\n    return fs.exists(filename, callback)\n  }\n  return new Promise(resolve => {\n    return fs.exists(filename, resolve)\n  })\n}\n\n// fs.read(), fs.write(), & fs.writev() need special treatment due to multiple callback args\n\nexports.read = function (fd, buffer, offset, length, position, callback) {\n  if (typeof callback === 'function') {\n    return fs.read(fd, buffer, offset, length, position, callback)\n  }\n  return new Promise((resolve, reject) => {\n    fs.read(fd, buffer, offset, length, position, (err, bytesRead, buffer) => {\n      if (err) return reject(err)\n      resolve({ bytesRead, buffer })\n    })\n  })\n}\n\n// Function signature can be\n// fs.write(fd, buffer[, offset[, length[, position]]], callback)\n// OR\n// fs.write(fd, string[, position[, encoding]], callback)\n// We need to handle both cases, so we use ...args\nexports.write = function (fd, buffer, ...args) {\n  if (typeof args[args.length - 1] === 'function') {\n    return fs.write(fd, buffer, ...args)\n  }\n\n  return new Promise((resolve, reject) => {\n    fs.write(fd, buffer, ...args, (err, bytesWritten, buffer) => {\n      if (err) return reject(err)\n      resolve({ bytesWritten, buffer })\n    })\n  })\n}\n\n// fs.writev only available in Node v12.9.0+\nif (typeof fs.writev === 'function') {\n  // Function signature is\n  // s.writev(fd, buffers[, position], callback)\n  // We need to handle the optional arg, so we use ...args\n  exports.writev = function (fd, buffers, ...args) {\n    if (typeof args[args.length - 1] === 'function') {\n      return fs.writev(fd, buffers, ...args)\n    }\n\n    return new Promise((resolve, reject) => {\n      fs.writev(fd, buffers, ...args, (err, bytesWritten, buffers) => {\n        if (err) return reject(err)\n        resolve({ bytesWritten, buffers })\n      })\n    })\n  }\n}\n\n// fs.realpath.native only available in Node v9.2+\nif (typeof fs.realpath.native === 'function') {\n  exports.realpath.native = u(fs.realpath.native)\n}\n", "module.exports = r => {\n  const n = process.versions.node.split('.').map(x => parseInt(x, 10))\n  r = r.split('.').map(x => parseInt(x, 10))\n  return n[0] > r[0] || (n[0] === r[0] && (n[1] > r[1] || (n[1] === r[1] && n[2] >= r[2])))\n}\n", "// Adapted from https://github.com/sindresorhus/make-dir\n// Copyright (c) Sindre Sorhus <<EMAIL>> (sindresorhus.com)\n// Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the \"Software\"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:\n// The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n'use strict'\nconst fs = require('../fs')\nconst path = require('path')\nconst atLeastNode = require('at-least-node')\n\nconst useNativeRecursiveOption = atLeastNode('10.12.0')\n\n// https://github.com/nodejs/node/issues/8987\n// https://github.com/libuv/libuv/pull/1088\nconst checkPath = pth => {\n  if (process.platform === 'win32') {\n    const pathHasInvalidWinCharacters = /[<>:\"|?*]/.test(pth.replace(path.parse(pth).root, ''))\n\n    if (pathHasInvalidWinCharacters) {\n      const error = new Error(`Path contains invalid characters: ${pth}`)\n      error.code = 'EINVAL'\n      throw error\n    }\n  }\n}\n\nconst processOptions = options => {\n  const defaults = { mode: 0o777 }\n  if (typeof options === 'number') options = { mode: options }\n  return { ...defaults, ...options }\n}\n\nconst permissionError = pth => {\n  // This replicates the exception of `fs.mkdir` with native the\n  // `recusive` option when run on an invalid drive under Windows.\n  const error = new Error(`operation not permitted, mkdir '${pth}'`)\n  error.code = 'EPERM'\n  error.errno = -4048\n  error.path = pth\n  error.syscall = 'mkdir'\n  return error\n}\n\nmodule.exports.makeDir = async (input, options) => {\n  checkPath(input)\n  options = processOptions(options)\n\n  if (useNativeRecursiveOption) {\n    const pth = path.resolve(input)\n\n    return fs.mkdir(pth, {\n      mode: options.mode,\n      recursive: true\n    })\n  }\n\n  const make = async pth => {\n    try {\n      await fs.mkdir(pth, options.mode)\n    } catch (error) {\n      if (error.code === 'EPERM') {\n        throw error\n      }\n\n      if (error.code === 'ENOENT') {\n        if (path.dirname(pth) === pth) {\n          throw permissionError(pth)\n        }\n\n        if (error.message.includes('null bytes')) {\n          throw error\n        }\n\n        await make(path.dirname(pth))\n        return make(pth)\n      }\n\n      try {\n        const stats = await fs.stat(pth)\n        if (!stats.isDirectory()) {\n          // This error is never exposed to the user\n          // it is caught below, and the original error is thrown\n          throw new Error('The path is not a directory')\n        }\n      } catch {\n        throw error\n      }\n    }\n  }\n\n  return make(path.resolve(input))\n}\n\nmodule.exports.makeDirSync = (input, options) => {\n  checkPath(input)\n  options = processOptions(options)\n\n  if (useNativeRecursiveOption) {\n    const pth = path.resolve(input)\n\n    return fs.mkdirSync(pth, {\n      mode: options.mode,\n      recursive: true\n    })\n  }\n\n  const make = pth => {\n    try {\n      fs.mkdirSync(pth, options.mode)\n    } catch (error) {\n      if (error.code === 'EPERM') {\n        throw error\n      }\n\n      if (error.code === 'ENOENT') {\n        if (path.dirname(pth) === pth) {\n          throw permissionError(pth)\n        }\n\n        if (error.message.includes('null bytes')) {\n          throw error\n        }\n\n        make(path.dirname(pth))\n        return make(pth)\n      }\n\n      try {\n        if (!fs.statSync(pth).isDirectory()) {\n          // This error is never exposed to the user\n          // it is caught below, and the original error is thrown\n          throw new Error('The path is not a directory')\n        }\n      } catch {\n        throw error\n      }\n    }\n  }\n\n  return make(path.resolve(input))\n}\n", "'use strict'\nconst u = require('universalify').fromPromise\nconst { makeDir: _makeDir, makeDirSync } = require('./make-dir')\nconst makeDir = u(_makeDir)\n\nmodule.exports = {\n  mkdirs: makeDir,\n  mkdirsSync: makeDirSync,\n  // alias\n  mkdirp: makeDir,\n  mkdirpSync: makeDirSync,\n  ensureDir: makeDir,\n  ensureDirSync: makeDirSync\n}\n", "'use strict'\n\nconst fs = require('graceful-fs')\n\nfunction utimesMillis (path, atime, mtime, callback) {\n  // if (!HAS_MILLIS_RES) return fs.utimes(path, atime, mtime, callback)\n  fs.open(path, 'r+', (err, fd) => {\n    if (err) return callback(err)\n    fs.futimes(fd, atime, mtime, futimesErr => {\n      fs.close(fd, closeErr => {\n        if (callback) callback(futimesErr || closeErr)\n      })\n    })\n  })\n}\n\nfunction utimesMillisSync (path, atime, mtime) {\n  const fd = fs.openSync(path, 'r+')\n  fs.futimesSync(fd, atime, mtime)\n  return fs.closeSync(fd)\n}\n\nmodule.exports = {\n  utimesMillis,\n  utimesMillisSync\n}\n", "'use strict'\n\nconst fs = require('../fs')\nconst path = require('path')\nconst util = require('util')\nconst atLeastNode = require('at-least-node')\n\nconst nodeSupportsBigInt = atLeastNode('10.5.0')\nconst stat = (file) => nodeSupportsBigInt ? fs.stat(file, { bigint: true }) : fs.stat(file)\nconst statSync = (file) => nodeSupportsBigInt ? fs.statSync(file, { bigint: true }) : fs.statSync(file)\n\nfunction getStats (src, dest) {\n  return Promise.all([\n    stat(src),\n    stat(dest).catch(err => {\n      if (err.code === 'ENOENT') return null\n      throw err\n    })\n  ]).then(([srcStat, destStat]) => ({ srcStat, destStat }))\n}\n\nfunction getStatsSync (src, dest) {\n  let destStat\n  const srcStat = statSync(src)\n  try {\n    destStat = statSync(dest)\n  } catch (err) {\n    if (err.code === 'ENOENT') return { srcStat, destStat: null }\n    throw err\n  }\n  return { srcStat, destStat }\n}\n\nfunction checkPaths (src, dest, funcName, cb) {\n  util.callbackify(getStats)(src, dest, (err, stats) => {\n    if (err) return cb(err)\n    const { srcStat, destStat } = stats\n    if (destStat && areIdentical(srcStat, destStat)) {\n      return cb(new Error('Source and destination must not be the same.'))\n    }\n    if (srcStat.isDirectory() && isSrcSubdir(src, dest)) {\n      return cb(new Error(errMsg(src, dest, funcName)))\n    }\n    return cb(null, { srcStat, destStat })\n  })\n}\n\nfunction checkPathsSync (src, dest, funcName) {\n  const { srcStat, destStat } = getStatsSync(src, dest)\n  if (destStat && areIdentical(srcStat, destStat)) {\n    throw new Error('Source and destination must not be the same.')\n  }\n  if (srcStat.isDirectory() && isSrcSubdir(src, dest)) {\n    throw new Error(errMsg(src, dest, funcName))\n  }\n  return { srcStat, destStat }\n}\n\n// recursively check if dest parent is a subdirectory of src.\n// It works for all file types including symlinks since it\n// checks the src and dest inodes. It starts from the deepest\n// parent and stops once it reaches the src parent or the root path.\nfunction checkParentPaths (src, srcStat, dest, funcName, cb) {\n  const srcParent = path.resolve(path.dirname(src))\n  const destParent = path.resolve(path.dirname(dest))\n  if (destParent === srcParent || destParent === path.parse(destParent).root) return cb()\n  const callback = (err, destStat) => {\n    if (err) {\n      if (err.code === 'ENOENT') return cb()\n      return cb(err)\n    }\n    if (areIdentical(srcStat, destStat)) {\n      return cb(new Error(errMsg(src, dest, funcName)))\n    }\n    return checkParentPaths(src, srcStat, destParent, funcName, cb)\n  }\n  if (nodeSupportsBigInt) fs.stat(destParent, { bigint: true }, callback)\n  else fs.stat(destParent, callback)\n}\n\nfunction checkParentPathsSync (src, srcStat, dest, funcName) {\n  const srcParent = path.resolve(path.dirname(src))\n  const destParent = path.resolve(path.dirname(dest))\n  if (destParent === srcParent || destParent === path.parse(destParent).root) return\n  let destStat\n  try {\n    destStat = statSync(destParent)\n  } catch (err) {\n    if (err.code === 'ENOENT') return\n    throw err\n  }\n  if (areIdentical(srcStat, destStat)) {\n    throw new Error(errMsg(src, dest, funcName))\n  }\n  return checkParentPathsSync(src, srcStat, destParent, funcName)\n}\n\nfunction areIdentical (srcStat, destStat) {\n  if (destStat.ino && destStat.dev && destStat.ino === srcStat.ino && destStat.dev === srcStat.dev) {\n    if (nodeSupportsBigInt || destStat.ino < Number.MAX_SAFE_INTEGER) {\n      // definitive answer\n      return true\n    }\n    // Use additional heuristics if we can't use 'bigint'.\n    // Different 'ino' could be represented the same if they are >= Number.MAX_SAFE_INTEGER\n    // See issue 657\n    if (destStat.size === srcStat.size &&\n        destStat.mode === srcStat.mode &&\n        destStat.nlink === srcStat.nlink &&\n        destStat.atimeMs === srcStat.atimeMs &&\n        destStat.mtimeMs === srcStat.mtimeMs &&\n        destStat.ctimeMs === srcStat.ctimeMs &&\n        destStat.birthtimeMs === srcStat.birthtimeMs) {\n      // heuristic answer\n      return true\n    }\n  }\n  return false\n}\n\n// return true if dest is a subdir of src, otherwise false.\n// It only checks the path strings.\nfunction isSrcSubdir (src, dest) {\n  const srcArr = path.resolve(src).split(path.sep).filter(i => i)\n  const destArr = path.resolve(dest).split(path.sep).filter(i => i)\n  return srcArr.reduce((acc, cur, i) => acc && destArr[i] === cur, true)\n}\n\nfunction errMsg (src, dest, funcName) {\n  return `Cannot ${funcName} '${src}' to a subdirectory of itself, '${dest}'.`\n}\n\nmodule.exports = {\n  checkPaths,\n  checkPathsSync,\n  checkParentPaths,\n  checkParentPathsSync,\n  isSrcSubdir\n}\n", "'use strict'\n\nconst fs = require('graceful-fs')\nconst path = require('path')\nconst mkdirsSync = require('../mkdirs').mkdirsSync\nconst utimesMillisSync = require('../util/utimes').utimesMillisSync\nconst stat = require('../util/stat')\n\nfunction copySync (src, dest, opts) {\n  if (typeof opts === 'function') {\n    opts = { filter: opts }\n  }\n\n  opts = opts || {}\n  opts.clobber = 'clobber' in opts ? !!opts.clobber : true // default to true for now\n  opts.overwrite = 'overwrite' in opts ? !!opts.overwrite : opts.clobber // overwrite falls back to clobber\n\n  // Warn about using preserveTimestamps on 32-bit node\n  if (opts.preserveTimestamps && process.arch === 'ia32') {\n    console.warn(`fs-extra: Using the preserveTimestamps option in 32-bit node is not recommended;\\n\n    see https://github.com/jprichardson/node-fs-extra/issues/269`)\n  }\n\n  const { srcStat, destStat } = stat.checkPathsSync(src, dest, 'copy')\n  stat.checkParentPathsSync(src, srcStat, dest, 'copy')\n  return handleFilterAndCopy(destStat, src, dest, opts)\n}\n\nfunction handleFilterAndCopy (destStat, src, dest, opts) {\n  if (opts.filter && !opts.filter(src, dest)) return\n  const destParent = path.dirname(dest)\n  if (!fs.existsSync(destParent)) mkdirsSync(destParent)\n  return startCopy(destStat, src, dest, opts)\n}\n\nfunction startCopy (destStat, src, dest, opts) {\n  if (opts.filter && !opts.filter(src, dest)) return\n  return getStats(destStat, src, dest, opts)\n}\n\nfunction getStats (destStat, src, dest, opts) {\n  const statSync = opts.dereference ? fs.statSync : fs.lstatSync\n  const srcStat = statSync(src)\n\n  if (srcStat.isDirectory()) return onDir(srcStat, destStat, src, dest, opts)\n  else if (srcStat.isFile() ||\n           srcStat.isCharacterDevice() ||\n           srcStat.isBlockDevice()) return onFile(srcStat, destStat, src, dest, opts)\n  else if (srcStat.isSymbolicLink()) return onLink(destStat, src, dest, opts)\n}\n\nfunction onFile (srcStat, destStat, src, dest, opts) {\n  if (!destStat) return copyFile(srcStat, src, dest, opts)\n  return mayCopyFile(srcStat, src, dest, opts)\n}\n\nfunction mayCopyFile (srcStat, src, dest, opts) {\n  if (opts.overwrite) {\n    fs.unlinkSync(dest)\n    return copyFile(srcStat, src, dest, opts)\n  } else if (opts.errorOnExist) {\n    throw new Error(`'${dest}' already exists`)\n  }\n}\n\nfunction copyFile (srcStat, src, dest, opts) {\n  fs.copyFileSync(src, dest)\n  if (opts.preserveTimestamps) handleTimestamps(srcStat.mode, src, dest)\n  return setDestMode(dest, srcStat.mode)\n}\n\nfunction handleTimestamps (srcMode, src, dest) {\n  // Make sure the file is writable before setting the timestamp\n  // otherwise open fails with EPERM when invoked with 'r+'\n  // (through utimes call)\n  if (fileIsNotWritable(srcMode)) makeFileWritable(dest, srcMode)\n  return setDestTimestamps(src, dest)\n}\n\nfunction fileIsNotWritable (srcMode) {\n  return (srcMode & 0o200) === 0\n}\n\nfunction makeFileWritable (dest, srcMode) {\n  return setDestMode(dest, srcMode | 0o200)\n}\n\nfunction setDestMode (dest, srcMode) {\n  return fs.chmodSync(dest, srcMode)\n}\n\nfunction setDestTimestamps (src, dest) {\n  // The initial srcStat.atime cannot be trusted\n  // because it is modified by the read(2) system call\n  // (See https://nodejs.org/api/fs.html#fs_stat_time_values)\n  const updatedSrcStat = fs.statSync(src)\n  return utimesMillisSync(dest, updatedSrcStat.atime, updatedSrcStat.mtime)\n}\n\nfunction onDir (srcStat, destStat, src, dest, opts) {\n  if (!destStat) return mkDirAndCopy(srcStat.mode, src, dest, opts)\n  if (destStat && !destStat.isDirectory()) {\n    throw new Error(`Cannot overwrite non-directory '${dest}' with directory '${src}'.`)\n  }\n  return copyDir(src, dest, opts)\n}\n\nfunction mkDirAndCopy (srcMode, src, dest, opts) {\n  fs.mkdirSync(dest)\n  copyDir(src, dest, opts)\n  return setDestMode(dest, srcMode)\n}\n\nfunction copyDir (src, dest, opts) {\n  fs.readdirSync(src).forEach(item => copyDirItem(item, src, dest, opts))\n}\n\nfunction copyDirItem (item, src, dest, opts) {\n  const srcItem = path.join(src, item)\n  const destItem = path.join(dest, item)\n  const { destStat } = stat.checkPathsSync(srcItem, destItem, 'copy')\n  return startCopy(destStat, srcItem, destItem, opts)\n}\n\nfunction onLink (destStat, src, dest, opts) {\n  let resolvedSrc = fs.readlinkSync(src)\n  if (opts.dereference) {\n    resolvedSrc = path.resolve(process.cwd(), resolvedSrc)\n  }\n\n  if (!destStat) {\n    return fs.symlinkSync(resolvedSrc, dest)\n  } else {\n    let resolvedDest\n    try {\n      resolvedDest = fs.readlinkSync(dest)\n    } catch (err) {\n      // dest exists and is a regular file or directory,\n      // Windows may throw UNKNOWN error. If dest already exists,\n      // fs throws error anyway, so no need to guard against it here.\n      if (err.code === 'EINVAL' || err.code === 'UNKNOWN') return fs.symlinkSync(resolvedSrc, dest)\n      throw err\n    }\n    if (opts.dereference) {\n      resolvedDest = path.resolve(process.cwd(), resolvedDest)\n    }\n    if (stat.isSrcSubdir(resolvedSrc, resolvedDest)) {\n      throw new Error(`Cannot copy '${resolvedSrc}' to a subdirectory of itself, '${resolvedDest}'.`)\n    }\n\n    // prevent copy if src is a subdir of dest since unlinking\n    // dest in this case would result in removing src contents\n    // and therefore a broken symlink would be created.\n    if (fs.statSync(dest).isDirectory() && stat.isSrcSubdir(resolvedDest, resolvedSrc)) {\n      throw new Error(`Cannot overwrite '${resolvedDest}' with '${resolvedSrc}'.`)\n    }\n    return copyLink(resolvedSrc, dest)\n  }\n}\n\nfunction copyLink (resolvedSrc, dest) {\n  fs.unlinkSync(dest)\n  return fs.symlinkSync(resolvedSrc, dest)\n}\n\nmodule.exports = copySync\n", "'use strict'\n\nmodule.exports = {\n  copySync: require('./copy-sync')\n}\n", "'use strict'\nconst u = require('universalify').fromPromise\nconst fs = require('../fs')\n\nfunction pathExists (path) {\n  return fs.access(path).then(() => true).catch(() => false)\n}\n\nmodule.exports = {\n  pathExists: u(pathExists),\n  pathExistsSync: fs.existsSync\n}\n", "'use strict'\n\nconst fs = require('graceful-fs')\nconst path = require('path')\nconst mkdirs = require('../mkdirs').mkdirs\nconst pathExists = require('../path-exists').pathExists\nconst utimesMillis = require('../util/utimes').utimesMillis\nconst stat = require('../util/stat')\n\nfunction copy (src, dest, opts, cb) {\n  if (typeof opts === 'function' && !cb) {\n    cb = opts\n    opts = {}\n  } else if (typeof opts === 'function') {\n    opts = { filter: opts }\n  }\n\n  cb = cb || function () {}\n  opts = opts || {}\n\n  opts.clobber = 'clobber' in opts ? !!opts.clobber : true // default to true for now\n  opts.overwrite = 'overwrite' in opts ? !!opts.overwrite : opts.clobber // overwrite falls back to clobber\n\n  // Warn about using preserveTimestamps on 32-bit node\n  if (opts.preserveTimestamps && process.arch === 'ia32') {\n    console.warn(`fs-extra: Using the preserveTimestamps option in 32-bit node is not recommended;\\n\n    see https://github.com/jprichardson/node-fs-extra/issues/269`)\n  }\n\n  stat.checkPaths(src, dest, 'copy', (err, stats) => {\n    if (err) return cb(err)\n    const { srcStat, destStat } = stats\n    stat.checkParentPaths(src, srcStat, dest, 'copy', err => {\n      if (err) return cb(err)\n      if (opts.filter) return handleFilter(checkParentDir, destStat, src, dest, opts, cb)\n      return checkParentDir(destStat, src, dest, opts, cb)\n    })\n  })\n}\n\nfunction checkParentDir (destStat, src, dest, opts, cb) {\n  const destParent = path.dirname(dest)\n  pathExists(destParent, (err, dirExists) => {\n    if (err) return cb(err)\n    if (dirExists) return startCopy(destStat, src, dest, opts, cb)\n    mkdirs(destParent, err => {\n      if (err) return cb(err)\n      return startCopy(destStat, src, dest, opts, cb)\n    })\n  })\n}\n\nfunction handleFilter (onInclude, destStat, src, dest, opts, cb) {\n  Promise.resolve(opts.filter(src, dest)).then(include => {\n    if (include) return onInclude(destStat, src, dest, opts, cb)\n    return cb()\n  }, error => cb(error))\n}\n\nfunction startCopy (destStat, src, dest, opts, cb) {\n  if (opts.filter) return handleFilter(getStats, destStat, src, dest, opts, cb)\n  return getStats(destStat, src, dest, opts, cb)\n}\n\nfunction getStats (destStat, src, dest, opts, cb) {\n  const stat = opts.dereference ? fs.stat : fs.lstat\n  stat(src, (err, srcStat) => {\n    if (err) return cb(err)\n\n    if (srcStat.isDirectory()) return onDir(srcStat, destStat, src, dest, opts, cb)\n    else if (srcStat.isFile() ||\n             srcStat.isCharacterDevice() ||\n             srcStat.isBlockDevice()) return onFile(srcStat, destStat, src, dest, opts, cb)\n    else if (srcStat.isSymbolicLink()) return onLink(destStat, src, dest, opts, cb)\n  })\n}\n\nfunction onFile (srcStat, destStat, src, dest, opts, cb) {\n  if (!destStat) return copyFile(srcStat, src, dest, opts, cb)\n  return mayCopyFile(srcStat, src, dest, opts, cb)\n}\n\nfunction mayCopyFile (srcStat, src, dest, opts, cb) {\n  if (opts.overwrite) {\n    fs.unlink(dest, err => {\n      if (err) return cb(err)\n      return copyFile(srcStat, src, dest, opts, cb)\n    })\n  } else if (opts.errorOnExist) {\n    return cb(new Error(`'${dest}' already exists`))\n  } else return cb()\n}\n\nfunction copyFile (srcStat, src, dest, opts, cb) {\n  fs.copyFile(src, dest, err => {\n    if (err) return cb(err)\n    if (opts.preserveTimestamps) return handleTimestampsAndMode(srcStat.mode, src, dest, cb)\n    return setDestMode(dest, srcStat.mode, cb)\n  })\n}\n\nfunction handleTimestampsAndMode (srcMode, src, dest, cb) {\n  // Make sure the file is writable before setting the timestamp\n  // otherwise open fails with EPERM when invoked with 'r+'\n  // (through utimes call)\n  if (fileIsNotWritable(srcMode)) {\n    return makeFileWritable(dest, srcMode, err => {\n      if (err) return cb(err)\n      return setDestTimestampsAndMode(srcMode, src, dest, cb)\n    })\n  }\n  return setDestTimestampsAndMode(srcMode, src, dest, cb)\n}\n\nfunction fileIsNotWritable (srcMode) {\n  return (srcMode & 0o200) === 0\n}\n\nfunction makeFileWritable (dest, srcMode, cb) {\n  return setDestMode(dest, srcMode | 0o200, cb)\n}\n\nfunction setDestTimestampsAndMode (srcMode, src, dest, cb) {\n  setDestTimestamps(src, dest, err => {\n    if (err) return cb(err)\n    return setDestMode(dest, srcMode, cb)\n  })\n}\n\nfunction setDestMode (dest, srcMode, cb) {\n  return fs.chmod(dest, srcMode, cb)\n}\n\nfunction setDestTimestamps (src, dest, cb) {\n  // The initial srcStat.atime cannot be trusted\n  // because it is modified by the read(2) system call\n  // (See https://nodejs.org/api/fs.html#fs_stat_time_values)\n  fs.stat(src, (err, updatedSrcStat) => {\n    if (err) return cb(err)\n    return utimesMillis(dest, updatedSrcStat.atime, updatedSrcStat.mtime, cb)\n  })\n}\n\nfunction onDir (srcStat, destStat, src, dest, opts, cb) {\n  if (!destStat) return mkDirAndCopy(srcStat.mode, src, dest, opts, cb)\n  if (destStat && !destStat.isDirectory()) {\n    return cb(new Error(`Cannot overwrite non-directory '${dest}' with directory '${src}'.`))\n  }\n  return copyDir(src, dest, opts, cb)\n}\n\nfunction mkDirAndCopy (srcMode, src, dest, opts, cb) {\n  fs.mkdir(dest, err => {\n    if (err) return cb(err)\n    copyDir(src, dest, opts, err => {\n      if (err) return cb(err)\n      return setDestMode(dest, srcMode, cb)\n    })\n  })\n}\n\nfunction copyDir (src, dest, opts, cb) {\n  fs.readdir(src, (err, items) => {\n    if (err) return cb(err)\n    return copyDirItems(items, src, dest, opts, cb)\n  })\n}\n\nfunction copyDirItems (items, src, dest, opts, cb) {\n  const item = items.pop()\n  if (!item) return cb()\n  return copyDirItem(items, item, src, dest, opts, cb)\n}\n\nfunction copyDirItem (items, item, src, dest, opts, cb) {\n  const srcItem = path.join(src, item)\n  const destItem = path.join(dest, item)\n  stat.checkPaths(srcItem, destItem, 'copy', (err, stats) => {\n    if (err) return cb(err)\n    const { destStat } = stats\n    startCopy(destStat, srcItem, destItem, opts, err => {\n      if (err) return cb(err)\n      return copyDirItems(items, src, dest, opts, cb)\n    })\n  })\n}\n\nfunction onLink (destStat, src, dest, opts, cb) {\n  fs.readlink(src, (err, resolvedSrc) => {\n    if (err) return cb(err)\n    if (opts.dereference) {\n      resolvedSrc = path.resolve(process.cwd(), resolvedSrc)\n    }\n\n    if (!destStat) {\n      return fs.symlink(resolvedSrc, dest, cb)\n    } else {\n      fs.readlink(dest, (err, resolvedDest) => {\n        if (err) {\n          // dest exists and is a regular file or directory,\n          // Windows may throw UNKNOWN error. If dest already exists,\n          // fs throws error anyway, so no need to guard against it here.\n          if (err.code === 'EINVAL' || err.code === 'UNKNOWN') return fs.symlink(resolvedSrc, dest, cb)\n          return cb(err)\n        }\n        if (opts.dereference) {\n          resolvedDest = path.resolve(process.cwd(), resolvedDest)\n        }\n        if (stat.isSrcSubdir(resolvedSrc, resolvedDest)) {\n          return cb(new Error(`Cannot copy '${resolvedSrc}' to a subdirectory of itself, '${resolvedDest}'.`))\n        }\n\n        // do not copy if src is a subdir of dest since unlinking\n        // dest in this case would result in removing src contents\n        // and therefore a broken symlink would be created.\n        if (destStat.isDirectory() && stat.isSrcSubdir(resolvedDest, resolvedSrc)) {\n          return cb(new Error(`Cannot overwrite '${resolvedDest}' with '${resolvedSrc}'.`))\n        }\n        return copyLink(resolvedSrc, dest, cb)\n      })\n    }\n  })\n}\n\nfunction copyLink (resolvedSrc, dest, cb) {\n  fs.unlink(dest, err => {\n    if (err) return cb(err)\n    return fs.symlink(resolvedSrc, dest, cb)\n  })\n}\n\nmodule.exports = copy\n", "'use strict'\n\nconst u = require('universalify').fromCallback\nmodule.exports = {\n  copy: u(require('./copy'))\n}\n", "'use strict'\n\nconst fs = require('graceful-fs')\nconst path = require('path')\nconst assert = require('assert')\n\nconst isWindows = (process.platform === 'win32')\n\nfunction defaults (options) {\n  const methods = [\n    'unlink',\n    'chmod',\n    'stat',\n    'lstat',\n    'rmdir',\n    'readdir'\n  ]\n  methods.forEach(m => {\n    options[m] = options[m] || fs[m]\n    m = m + 'Sync'\n    options[m] = options[m] || fs[m]\n  })\n\n  options.maxBusyTries = options.maxBusyTries || 3\n}\n\nfunction rimraf (p, options, cb) {\n  let busyTries = 0\n\n  if (typeof options === 'function') {\n    cb = options\n    options = {}\n  }\n\n  assert(p, 'rimraf: missing path')\n  assert.strictEqual(typeof p, 'string', 'rimraf: path should be a string')\n  assert.strictEqual(typeof cb, 'function', 'rimraf: callback function required')\n  assert(options, 'rimraf: invalid options argument provided')\n  assert.strictEqual(typeof options, 'object', 'rimraf: options should be object')\n\n  defaults(options)\n\n  rimraf_(p, options, function CB (er) {\n    if (er) {\n      if ((er.code === 'EBUSY' || er.code === 'ENOTEMPTY' || er.code === 'EPERM') &&\n          busyTries < options.maxBusyTries) {\n        busyTries++\n        const time = busyTries * 100\n        // try again, with the same exact callback as this one.\n        return setTimeout(() => rimraf_(p, options, CB), time)\n      }\n\n      // already gone\n      if (er.code === 'ENOENT') er = null\n    }\n\n    cb(er)\n  })\n}\n\n// Two possible strategies.\n// 1. Assume it's a file.  unlink it, then do the dir stuff on EPERM or EISDIR\n// 2. Assume it's a directory.  readdir, then do the file stuff on ENOTDIR\n//\n// Both result in an extra syscall when you guess wrong.  However, there\n// are likely far more normal files in the world than directories.  This\n// is based on the assumption that a the average number of files per\n// directory is >= 1.\n//\n// If anyone ever complains about this, then I guess the strategy could\n// be made configurable somehow.  But until then, YAGNI.\nfunction rimraf_ (p, options, cb) {\n  assert(p)\n  assert(options)\n  assert(typeof cb === 'function')\n\n  // sunos lets the root user unlink directories, which is... weird.\n  // so we have to lstat here and make sure it's not a dir.\n  options.lstat(p, (er, st) => {\n    if (er && er.code === 'ENOENT') {\n      return cb(null)\n    }\n\n    // Windows can EPERM on stat.  Life is suffering.\n    if (er && er.code === 'EPERM' && isWindows) {\n      return fixWinEPERM(p, options, er, cb)\n    }\n\n    if (st && st.isDirectory()) {\n      return rmdir(p, options, er, cb)\n    }\n\n    options.unlink(p, er => {\n      if (er) {\n        if (er.code === 'ENOENT') {\n          return cb(null)\n        }\n        if (er.code === 'EPERM') {\n          return (isWindows)\n            ? fixWinEPERM(p, options, er, cb)\n            : rmdir(p, options, er, cb)\n        }\n        if (er.code === 'EISDIR') {\n          return rmdir(p, options, er, cb)\n        }\n      }\n      return cb(er)\n    })\n  })\n}\n\nfunction fixWinEPERM (p, options, er, cb) {\n  assert(p)\n  assert(options)\n  assert(typeof cb === 'function')\n\n  options.chmod(p, 0o666, er2 => {\n    if (er2) {\n      cb(er2.code === 'ENOENT' ? null : er)\n    } else {\n      options.stat(p, (er3, stats) => {\n        if (er3) {\n          cb(er3.code === 'ENOENT' ? null : er)\n        } else if (stats.isDirectory()) {\n          rmdir(p, options, er, cb)\n        } else {\n          options.unlink(p, cb)\n        }\n      })\n    }\n  })\n}\n\nfunction fixWinEPERMSync (p, options, er) {\n  let stats\n\n  assert(p)\n  assert(options)\n\n  try {\n    options.chmodSync(p, 0o666)\n  } catch (er2) {\n    if (er2.code === 'ENOENT') {\n      return\n    } else {\n      throw er\n    }\n  }\n\n  try {\n    stats = options.statSync(p)\n  } catch (er3) {\n    if (er3.code === 'ENOENT') {\n      return\n    } else {\n      throw er\n    }\n  }\n\n  if (stats.isDirectory()) {\n    rmdirSync(p, options, er)\n  } else {\n    options.unlinkSync(p)\n  }\n}\n\nfunction rmdir (p, options, originalEr, cb) {\n  assert(p)\n  assert(options)\n  assert(typeof cb === 'function')\n\n  // try to rmdir first, and only readdir on ENOTEMPTY or EEXIST (SunOS)\n  // if we guessed wrong, and it's not a directory, then\n  // raise the original error.\n  options.rmdir(p, er => {\n    if (er && (er.code === 'ENOTEMPTY' || er.code === 'EEXIST' || er.code === 'EPERM')) {\n      rmkids(p, options, cb)\n    } else if (er && er.code === 'ENOTDIR') {\n      cb(originalEr)\n    } else {\n      cb(er)\n    }\n  })\n}\n\nfunction rmkids (p, options, cb) {\n  assert(p)\n  assert(options)\n  assert(typeof cb === 'function')\n\n  options.readdir(p, (er, files) => {\n    if (er) return cb(er)\n\n    let n = files.length\n    let errState\n\n    if (n === 0) return options.rmdir(p, cb)\n\n    files.forEach(f => {\n      rimraf(path.join(p, f), options, er => {\n        if (errState) {\n          return\n        }\n        if (er) return cb(errState = er)\n        if (--n === 0) {\n          options.rmdir(p, cb)\n        }\n      })\n    })\n  })\n}\n\n// this looks simpler, and is strictly *faster*, but will\n// tie up the JavaScript thread and fail on excessively\n// deep directory trees.\nfunction rimrafSync (p, options) {\n  let st\n\n  options = options || {}\n  defaults(options)\n\n  assert(p, 'rimraf: missing path')\n  assert.strictEqual(typeof p, 'string', 'rimraf: path should be a string')\n  assert(options, 'rimraf: missing options')\n  assert.strictEqual(typeof options, 'object', 'rimraf: options should be object')\n\n  try {\n    st = options.lstatSync(p)\n  } catch (er) {\n    if (er.code === 'ENOENT') {\n      return\n    }\n\n    // Windows can EPERM on stat.  Life is suffering.\n    if (er.code === 'EPERM' && isWindows) {\n      fixWinEPERMSync(p, options, er)\n    }\n  }\n\n  try {\n    // sunos lets the root user unlink directories, which is... weird.\n    if (st && st.isDirectory()) {\n      rmdirSync(p, options, null)\n    } else {\n      options.unlinkSync(p)\n    }\n  } catch (er) {\n    if (er.code === 'ENOENT') {\n      return\n    } else if (er.code === 'EPERM') {\n      return isWindows ? fixWinEPERMSync(p, options, er) : rmdirSync(p, options, er)\n    } else if (er.code !== 'EISDIR') {\n      throw er\n    }\n    rmdirSync(p, options, er)\n  }\n}\n\nfunction rmdirSync (p, options, originalEr) {\n  assert(p)\n  assert(options)\n\n  try {\n    options.rmdirSync(p)\n  } catch (er) {\n    if (er.code === 'ENOTDIR') {\n      throw originalEr\n    } else if (er.code === 'ENOTEMPTY' || er.code === 'EEXIST' || er.code === 'EPERM') {\n      rmkidsSync(p, options)\n    } else if (er.code !== 'ENOENT') {\n      throw er\n    }\n  }\n}\n\nfunction rmkidsSync (p, options) {\n  assert(p)\n  assert(options)\n  options.readdirSync(p).forEach(f => rimrafSync(path.join(p, f), options))\n\n  if (isWindows) {\n    // We only end up here once we got ENOTEMPTY at least once, and\n    // at this point, we are guaranteed to have removed all the kids.\n    // So, we know that it won't be ENOENT or ENOTDIR or anything else.\n    // try really hard to delete stuff on windows, because it has a\n    // PROFOUNDLY annoying habit of not closing handles promptly when\n    // files are deleted, resulting in spurious ENOTEMPTY errors.\n    const startTime = Date.now()\n    do {\n      try {\n        const ret = options.rmdirSync(p, options)\n        return ret\n      } catch {}\n    } while (Date.now() - startTime < 500) // give up after 500ms\n  } else {\n    const ret = options.rmdirSync(p, options)\n    return ret\n  }\n}\n\nmodule.exports = rimraf\nrimraf.sync = rimrafSync\n", "'use strict'\n\nconst u = require('universalify').fromCallback\nconst rimraf = require('./rimraf')\n\nmodule.exports = {\n  remove: u(rimraf),\n  removeSync: rimraf.sync\n}\n", "'use strict'\n\nconst u = require('universalify').fromCallback\nconst fs = require('graceful-fs')\nconst path = require('path')\nconst mkdir = require('../mkdirs')\nconst remove = require('../remove')\n\nconst emptyDir = u(function emptyDir (dir, callback) {\n  callback = callback || function () {}\n  fs.readdir(dir, (err, items) => {\n    if (err) return mkdir.mkdirs(dir, callback)\n\n    items = items.map(item => path.join(dir, item))\n\n    deleteItem()\n\n    function deleteItem () {\n      const item = items.pop()\n      if (!item) return callback()\n      remove.remove(item, err => {\n        if (err) return callback(err)\n        deleteItem()\n      })\n    }\n  })\n})\n\nfunction emptyDirSync (dir) {\n  let items\n  try {\n    items = fs.readdirSync(dir)\n  } catch {\n    return mkdir.mkdirsSync(dir)\n  }\n\n  items.forEach(item => {\n    item = path.join(dir, item)\n    remove.removeSync(item)\n  })\n}\n\nmodule.exports = {\n  emptyDirSync,\n  emptydirSync: emptyDirSync,\n  emptyDir,\n  emptydir: emptyDir\n}\n", "'use strict'\n\nconst u = require('universalify').fromCallback\nconst path = require('path')\nconst fs = require('graceful-fs')\nconst mkdir = require('../mkdirs')\n\nfunction createFile (file, callback) {\n  function makeFile () {\n    fs.writeFile(file, '', err => {\n      if (err) return callback(err)\n      callback()\n    })\n  }\n\n  fs.stat(file, (err, stats) => { // eslint-disable-line handle-callback-err\n    if (!err && stats.isFile()) return callback()\n    const dir = path.dirname(file)\n    fs.stat(dir, (err, stats) => {\n      if (err) {\n        // if the directory doesn't exist, make it\n        if (err.code === 'ENOENT') {\n          return mkdir.mkdirs(dir, err => {\n            if (err) return callback(err)\n            makeFile()\n          })\n        }\n        return callback(err)\n      }\n\n      if (stats.isDirectory()) makeFile()\n      else {\n        // parent is not a directory\n        // This is just to cause an internal ENOTDIR error to be thrown\n        fs.readdir(dir, err => {\n          if (err) return callback(err)\n        })\n      }\n    })\n  })\n}\n\nfunction createFileSync (file) {\n  let stats\n  try {\n    stats = fs.statSync(file)\n  } catch {}\n  if (stats && stats.isFile()) return\n\n  const dir = path.dirname(file)\n  try {\n    if (!fs.statSync(dir).isDirectory()) {\n      // parent is not a directory\n      // This is just to cause an internal ENOTDIR error to be thrown\n      fs.readdirSync(dir)\n    }\n  } catch (err) {\n    // If the stat call above failed because the directory doesn't exist, create it\n    if (err && err.code === 'ENOENT') mkdir.mkdirsSync(dir)\n    else throw err\n  }\n\n  fs.writeFileSync(file, '')\n}\n\nmodule.exports = {\n  createFile: u(createFile),\n  createFileSync\n}\n", "'use strict'\n\nconst u = require('universalify').fromCallback\nconst path = require('path')\nconst fs = require('graceful-fs')\nconst mkdir = require('../mkdirs')\nconst pathExists = require('../path-exists').pathExists\n\nfunction createLink (srcpath, dstpath, callback) {\n  function makeLink (srcpath, dstpath) {\n    fs.link(srcpath, dstpath, err => {\n      if (err) return callback(err)\n      callback(null)\n    })\n  }\n\n  pathExists(dstpath, (err, destinationExists) => {\n    if (err) return callback(err)\n    if (destinationExists) return callback(null)\n    fs.lstat(srcpath, (err) => {\n      if (err) {\n        err.message = err.message.replace('lstat', 'ensureLink')\n        return callback(err)\n      }\n\n      const dir = path.dirname(dstpath)\n      pathExists(dir, (err, dirExists) => {\n        if (err) return callback(err)\n        if (dirExists) return makeLink(srcpath, dstpath)\n        mkdir.mkdirs(dir, err => {\n          if (err) return callback(err)\n          makeLink(srcpath, dstpath)\n        })\n      })\n    })\n  })\n}\n\nfunction createLinkSync (srcpath, dstpath) {\n  const destinationExists = fs.existsSync(dstpath)\n  if (destinationExists) return undefined\n\n  try {\n    fs.lstatSync(srcpath)\n  } catch (err) {\n    err.message = err.message.replace('lstat', 'ensureLink')\n    throw err\n  }\n\n  const dir = path.dirname(dstpath)\n  const dirExists = fs.existsSync(dir)\n  if (dirExists) return fs.linkSync(srcpath, dstpath)\n  mkdir.mkdirsSync(dir)\n\n  return fs.linkSync(srcpath, dstpath)\n}\n\nmodule.exports = {\n  createLink: u(createLink),\n  createLinkSync\n}\n", "'use strict'\n\nconst path = require('path')\nconst fs = require('graceful-fs')\nconst pathExists = require('../path-exists').pathExists\n\n/**\n * Function that returns two types of paths, one relative to symlink, and one\n * relative to the current working directory. Checks if path is absolute or\n * relative. If the path is relative, this function checks if the path is\n * relative to symlink or relative to current working directory. This is an\n * initiative to find a smarter `srcpath` to supply when building symlinks.\n * This allows you to determine which path to use out of one of three possible\n * types of source paths. The first is an absolute path. This is detected by\n * `path.isAbsolute()`. When an absolute path is provided, it is checked to\n * see if it exists. If it does it's used, if not an error is returned\n * (callback)/ thrown (sync). The other two options for `srcpath` are a\n * relative url. By default Node's `fs.symlink` works by creating a symlink\n * using `dstpath` and expects the `srcpath` to be relative to the newly\n * created symlink. If you provide a `srcpath` that does not exist on the file\n * system it results in a broken symlink. To minimize this, the function\n * checks to see if the 'relative to symlink' source file exists, and if it\n * does it will use it. If it does not, it checks if there's a file that\n * exists that is relative to the current working directory, if does its used.\n * This preserves the expectations of the original fs.symlink spec and adds\n * the ability to pass in `relative to current working direcotry` paths.\n */\n\nfunction symlinkPaths (srcpath, dstpath, callback) {\n  if (path.isAbsolute(srcpath)) {\n    return fs.lstat(srcpath, (err) => {\n      if (err) {\n        err.message = err.message.replace('lstat', 'ensureSymlink')\n        return callback(err)\n      }\n      return callback(null, {\n        toCwd: srcpath,\n        toDst: srcpath\n      })\n    })\n  } else {\n    const dstdir = path.dirname(dstpath)\n    const relativeToDst = path.join(dstdir, srcpath)\n    return pathExists(relativeToDst, (err, exists) => {\n      if (err) return callback(err)\n      if (exists) {\n        return callback(null, {\n          toCwd: relativeToDst,\n          toDst: srcpath\n        })\n      } else {\n        return fs.lstat(srcpath, (err) => {\n          if (err) {\n            err.message = err.message.replace('lstat', 'ensureSymlink')\n            return callback(err)\n          }\n          return callback(null, {\n            toCwd: srcpath,\n            toDst: path.relative(dstdir, srcpath)\n          })\n        })\n      }\n    })\n  }\n}\n\nfunction symlinkPathsSync (srcpath, dstpath) {\n  let exists\n  if (path.isAbsolute(srcpath)) {\n    exists = fs.existsSync(srcpath)\n    if (!exists) throw new Error('absolute srcpath does not exist')\n    return {\n      toCwd: srcpath,\n      toDst: srcpath\n    }\n  } else {\n    const dstdir = path.dirname(dstpath)\n    const relativeToDst = path.join(dstdir, srcpath)\n    exists = fs.existsSync(relativeToDst)\n    if (exists) {\n      return {\n        toCwd: relativeToDst,\n        toDst: srcpath\n      }\n    } else {\n      exists = fs.existsSync(srcpath)\n      if (!exists) throw new Error('relative srcpath does not exist')\n      return {\n        toCwd: srcpath,\n        toDst: path.relative(dstdir, srcpath)\n      }\n    }\n  }\n}\n\nmodule.exports = {\n  symlinkPaths,\n  symlinkPathsSync\n}\n", "'use strict'\n\nconst fs = require('graceful-fs')\n\nfunction symlinkType (srcpath, type, callback) {\n  callback = (typeof type === 'function') ? type : callback\n  type = (typeof type === 'function') ? false : type\n  if (type) return callback(null, type)\n  fs.lstat(srcpath, (err, stats) => {\n    if (err) return callback(null, 'file')\n    type = (stats && stats.isDirectory()) ? 'dir' : 'file'\n    callback(null, type)\n  })\n}\n\nfunction symlinkTypeSync (srcpath, type) {\n  let stats\n\n  if (type) return type\n  try {\n    stats = fs.lstatSync(srcpath)\n  } catch {\n    return 'file'\n  }\n  return (stats && stats.isDirectory()) ? 'dir' : 'file'\n}\n\nmodule.exports = {\n  symlinkType,\n  symlinkTypeSync\n}\n", "'use strict'\n\nconst u = require('universalify').fromCallback\nconst path = require('path')\nconst fs = require('graceful-fs')\nconst _mkdirs = require('../mkdirs')\nconst mkdirs = _mkdirs.mkdirs\nconst mkdirsSync = _mkdirs.mkdirsSync\n\nconst _symlinkPaths = require('./symlink-paths')\nconst symlinkPaths = _symlinkPaths.symlinkPaths\nconst symlinkPathsSync = _symlinkPaths.symlinkPathsSync\n\nconst _symlinkType = require('./symlink-type')\nconst symlinkType = _symlinkType.symlinkType\nconst symlinkTypeSync = _symlinkType.symlinkTypeSync\n\nconst pathExists = require('../path-exists').pathExists\n\nfunction createSymlink (srcpath, dstpath, type, callback) {\n  callback = (typeof type === 'function') ? type : callback\n  type = (typeof type === 'function') ? false : type\n\n  pathExists(dstpath, (err, destinationExists) => {\n    if (err) return callback(err)\n    if (destinationExists) return callback(null)\n    symlinkPaths(srcpath, dstpath, (err, relative) => {\n      if (err) return callback(err)\n      srcpath = relative.toDst\n      symlinkType(relative.toCwd, type, (err, type) => {\n        if (err) return callback(err)\n        const dir = path.dirname(dstpath)\n        pathExists(dir, (err, dirExists) => {\n          if (err) return callback(err)\n          if (dirExists) return fs.symlink(srcpath, dstpath, type, callback)\n          mkdirs(dir, err => {\n            if (err) return callback(err)\n            fs.symlink(srcpath, dstpath, type, callback)\n          })\n        })\n      })\n    })\n  })\n}\n\nfunction createSymlinkSync (srcpath, dstpath, type) {\n  const destinationExists = fs.existsSync(dstpath)\n  if (destinationExists) return undefined\n\n  const relative = symlinkPathsSync(srcpath, dstpath)\n  srcpath = relative.toDst\n  type = symlinkTypeSync(relative.toCwd, type)\n  const dir = path.dirname(dstpath)\n  const exists = fs.existsSync(dir)\n  if (exists) return fs.symlinkSync(srcpath, dstpath, type)\n  mkdirsSync(dir)\n  return fs.symlinkSync(srcpath, dstpath, type)\n}\n\nmodule.exports = {\n  createSymlink: u(createSymlink),\n  createSymlinkSync\n}\n", "'use strict'\n\nconst file = require('./file')\nconst link = require('./link')\nconst symlink = require('./symlink')\n\nmodule.exports = {\n  // file\n  createFile: file.createFile,\n  createFileSync: file.createFileSync,\n  ensureFile: file.createFile,\n  ensureFileSync: file.createFileSync,\n  // link\n  createLink: link.createLink,\n  createLinkSync: link.createLinkSync,\n  ensureLink: link.createLink,\n  ensureLinkSync: link.createLinkSync,\n  // symlink\n  createSymlink: symlink.createSymlink,\n  createSymlinkSync: symlink.createSymlinkSync,\n  ensureSymlink: symlink.createSymlink,\n  ensureSymlinkSync: symlink.createSymlinkSync\n}\n", "function stringify (obj, { EOL = '\\n', finalEOL = true, replacer = null, spaces } = {}) {\n  const EOF = finalEOL ? EOL : ''\n  const str = JSON.stringify(obj, replacer, spaces)\n\n  return str.replace(/\\n/g, EOL) + EOF\n}\n\nfunction stripBom (content) {\n  // we do this because JSON.parse would convert it to a utf8 string if encoding wasn't specified\n  if (Buffer.isBuffer(content)) content = content.toString('utf8')\n  return content.replace(/^\\uFEFF/, '')\n}\n\nmodule.exports = { stringify, stripBom }\n", "let _fs\ntry {\n  _fs = require('graceful-fs')\n} catch (_) {\n  _fs = require('fs')\n}\nconst universalify = require('universalify')\nconst { stringify, stripBom } = require('./utils')\n\nasync function _readFile (file, options = {}) {\n  if (typeof options === 'string') {\n    options = { encoding: options }\n  }\n\n  const fs = options.fs || _fs\n\n  const shouldThrow = 'throws' in options ? options.throws : true\n\n  let data = await universalify.fromCallback(fs.readFile)(file, options)\n\n  data = stripBom(data)\n\n  let obj\n  try {\n    obj = JSON.parse(data, options ? options.reviver : null)\n  } catch (err) {\n    if (shouldThrow) {\n      err.message = `${file}: ${err.message}`\n      throw err\n    } else {\n      return null\n    }\n  }\n\n  return obj\n}\n\nconst readFile = universalify.fromPromise(_readFile)\n\nfunction readFileSync (file, options = {}) {\n  if (typeof options === 'string') {\n    options = { encoding: options }\n  }\n\n  const fs = options.fs || _fs\n\n  const shouldThrow = 'throws' in options ? options.throws : true\n\n  try {\n    let content = fs.readFileSync(file, options)\n    content = stripBom(content)\n    return JSON.parse(content, options.reviver)\n  } catch (err) {\n    if (shouldThrow) {\n      err.message = `${file}: ${err.message}`\n      throw err\n    } else {\n      return null\n    }\n  }\n}\n\nasync function _writeFile (file, obj, options = {}) {\n  const fs = options.fs || _fs\n\n  const str = stringify(obj, options)\n\n  await universalify.fromCallback(fs.writeFile)(file, str, options)\n}\n\nconst writeFile = universalify.fromPromise(_writeFile)\n\nfunction writeFileSync (file, obj, options = {}) {\n  const fs = options.fs || _fs\n\n  const str = stringify(obj, options)\n  // not sure if fs.writeFileSync returns anything, but just in case\n  return fs.writeFileSync(file, str, options)\n}\n\nconst jsonfile = {\n  readFile,\n  readFileSync,\n  writeFile,\n  writeFileSync\n}\n\nmodule.exports = jsonfile\n", "'use strict'\n\nconst jsonFile = require('jsonfile')\n\nmodule.exports = {\n  // jsonfile exports\n  readJson: jsonFile.readFile,\n  readJsonSync: jsonFile.readFileSync,\n  writeJson: jsonFile.writeFile,\n  writeJsonSync: jsonFile.writeFileSync\n}\n", "'use strict'\n\nconst u = require('universalify').fromCallback\nconst fs = require('graceful-fs')\nconst path = require('path')\nconst mkdir = require('../mkdirs')\nconst pathExists = require('../path-exists').pathExists\n\nfunction outputFile (file, data, encoding, callback) {\n  if (typeof encoding === 'function') {\n    callback = encoding\n    encoding = 'utf8'\n  }\n\n  const dir = path.dirname(file)\n  pathExists(dir, (err, itDoes) => {\n    if (err) return callback(err)\n    if (itDoes) return fs.writeFile(file, data, encoding, callback)\n\n    mkdir.mkdirs(dir, err => {\n      if (err) return callback(err)\n\n      fs.writeFile(file, data, encoding, callback)\n    })\n  })\n}\n\nfunction outputFileSync (file, ...args) {\n  const dir = path.dirname(file)\n  if (fs.existsSync(dir)) {\n    return fs.writeFileSync(file, ...args)\n  }\n  mkdir.mkdirsSync(dir)\n  fs.writeFileSync(file, ...args)\n}\n\nmodule.exports = {\n  outputFile: u(outputFile),\n  outputFileSync\n}\n", "'use strict'\n\nconst { stringify } = require('jsonfile/utils')\nconst { outputFile } = require('../output')\n\nasync function outputJson (file, data, options = {}) {\n  const str = stringify(data, options)\n\n  await outputFile(file, str, options)\n}\n\nmodule.exports = outputJson\n", "'use strict'\n\nconst { stringify } = require('jsonfile/utils')\nconst { outputFileSync } = require('../output')\n\nfunction outputJsonSync (file, data, options) {\n  const str = stringify(data, options)\n\n  outputFileSync(file, str, options)\n}\n\nmodule.exports = outputJsonSync\n", "'use strict'\n\nconst u = require('universalify').fromPromise\nconst jsonFile = require('./jsonfile')\n\njsonFile.outputJson = u(require('./output-json'))\njsonFile.outputJsonSync = require('./output-json-sync')\n// aliases\njsonFile.outputJSON = jsonFile.outputJson\njsonFile.outputJSONSync = jsonFile.outputJsonSync\njsonFile.writeJSON = jsonFile.writeJson\njsonFile.writeJSONSync = jsonFile.writeJsonSync\njsonFile.readJSON = jsonFile.readJson\njsonFile.readJSONSync = jsonFile.readJsonSync\n\nmodule.exports = jsonFile\n", "'use strict'\n\nconst fs = require('graceful-fs')\nconst path = require('path')\nconst copySync = require('../copy-sync').copySync\nconst removeSync = require('../remove').removeSync\nconst mkdirpSync = require('../mkdirs').mkdirpSync\nconst stat = require('../util/stat')\n\nfunction moveSync (src, dest, opts) {\n  opts = opts || {}\n  const overwrite = opts.overwrite || opts.clobber || false\n\n  const { srcStat } = stat.checkPathsSync(src, dest, 'move')\n  stat.checkParentPathsSync(src, srcStat, dest, 'move')\n  mkdirpSync(path.dirname(dest))\n  return doRename(src, dest, overwrite)\n}\n\nfunction doRename (src, dest, overwrite) {\n  if (overwrite) {\n    removeSync(dest)\n    return rename(src, dest, overwrite)\n  }\n  if (fs.existsSync(dest)) throw new Error('dest already exists.')\n  return rename(src, dest, overwrite)\n}\n\nfunction rename (src, dest, overwrite) {\n  try {\n    fs.renameSync(src, dest)\n  } catch (err) {\n    if (err.code !== 'EXDEV') throw err\n    return moveAcrossDevice(src, dest, overwrite)\n  }\n}\n\nfunction moveAcrossDevice (src, dest, overwrite) {\n  const opts = {\n    overwrite,\n    errorOnExist: true\n  }\n  copySync(src, dest, opts)\n  return removeSync(src)\n}\n\nmodule.exports = moveSync\n", "'use strict'\n\nmodule.exports = {\n  moveSync: require('./move-sync')\n}\n", "'use strict'\n\nconst fs = require('graceful-fs')\nconst path = require('path')\nconst copy = require('../copy').copy\nconst remove = require('../remove').remove\nconst mkdirp = require('../mkdirs').mkdirp\nconst pathExists = require('../path-exists').pathExists\nconst stat = require('../util/stat')\n\nfunction move (src, dest, opts, cb) {\n  if (typeof opts === 'function') {\n    cb = opts\n    opts = {}\n  }\n\n  const overwrite = opts.overwrite || opts.clobber || false\n\n  stat.checkPaths(src, dest, 'move', (err, stats) => {\n    if (err) return cb(err)\n    const { srcStat } = stats\n    stat.checkParentPaths(src, srcStat, dest, 'move', err => {\n      if (err) return cb(err)\n      mkdirp(path.dirname(dest), err => {\n        if (err) return cb(err)\n        return doRename(src, dest, overwrite, cb)\n      })\n    })\n  })\n}\n\nfunction doRename (src, dest, overwrite, cb) {\n  if (overwrite) {\n    return remove(dest, err => {\n      if (err) return cb(err)\n      return rename(src, dest, overwrite, cb)\n    })\n  }\n  pathExists(dest, (err, destExists) => {\n    if (err) return cb(err)\n    if (destExists) return cb(new Error('dest already exists.'))\n    return rename(src, dest, overwrite, cb)\n  })\n}\n\nfunction rename (src, dest, overwrite, cb) {\n  fs.rename(src, dest, err => {\n    if (!err) return cb()\n    if (err.code !== 'EXDEV') return cb(err)\n    return moveAcrossDevice(src, dest, overwrite, cb)\n  })\n}\n\nfunction moveAcrossDevice (src, dest, overwrite, cb) {\n  const opts = {\n    overwrite,\n    errorOnExist: true\n  }\n  copy(src, dest, opts, err => {\n    if (err) return cb(err)\n    return remove(src, cb)\n  })\n}\n\nmodule.exports = move\n", "'use strict'\n\nconst u = require('universalify').fromCallback\nmodule.exports = {\n  move: u(require('./move'))\n}\n", "'use strict'\n\nmodule.exports = {\n  // Export promiseified graceful-fs:\n  ...require('./fs'),\n  // Export extra methods:\n  ...require('./copy-sync'),\n  ...require('./copy'),\n  ...require('./empty'),\n  ...require('./ensure'),\n  ...require('./json'),\n  ...require('./mkdirs'),\n  ...require('./move-sync'),\n  ...require('./move'),\n  ...require('./output'),\n  ...require('./path-exists'),\n  ...require('./remove')\n}\n\n// Export fs.promises as a getter property so that we don't trigger\n// ExperimentalWarning before fs.promises is actually accessed.\nconst fs = require('fs')\nif (Object.getOwnPropertyDescriptor(fs, 'promises')) {\n  Object.defineProperty(module.exports, 'promises', {\n    get () { return fs.promises }\n  })\n}\n", "import fs from 'fs-extra'\nimport path from 'path'\n\n\nexport async function copyBrowserData(browserName: string, browserPath: string, electronUserDataPath: string) {\n\tconst subdirs = ['Local Storage', 'IndexedDB']\n\tconst cookieFile = 'Cookies'\n\n\tfor (const dir of subdirs) {\n\t\tconst src = path.join(browserPath, dir)\n\t\tconst dest = path.join(electronUserDataPath, browserName, dir)\n\t\tif (fs.existsSync(src)) {\n\t\t\tawait fs.copy(src, dest, { overwrite: true })\n\t\t\tconsole.log(`[${browserName}] copy ${dir} success`)\n\t\t}\n\t}\n\n\t// copy Cookies file\n\tconst cookieSrc = path.join(browserPath, cookieFile)\n\tconst cookieDest = path.join(electronUserDataPath, browserName, cookieFile)\n\tif (fs.existsSync(cookieSrc)) {\n\t\tawait fs.copy(cookieSrc, cookieDest, { overwrite: true })\n\t\tconsole.log(`[${browserName}] copy Cookies success`)\n\t}\n}\n\n", "import { app, BrowserWindow, shell, ipc<PERSON>ain, Menu, dialog, nativeTheme, protocol, session } from 'electron'\nimport { fileURLToPath } from 'node:url'\nimport path from 'node:path'\nimport os, { homedir } from 'node:os'\nimport log from 'electron-log'\nimport { update, registerUpdateIpcHandlers } from './update'\nimport { checkToolInstalled, installDependencies, startBackend } from './init'\nimport { WebViewManager } from './webview'\nimport { FileReader } from './fileReader'\nimport { ChildProcessWithoutNullStreams } from 'node:child_process'\nimport fs, { existsSync, readFileSync } from 'node:fs'\nimport fsp from 'fs/promises'\nimport { addMcp, removeMcp, updateMcp, readMcpConfig } from './utils/mcpConfig'\nimport { getEnvPath, updateEnvBlock, removeEnvKey } from './utils/envUtil'\nimport { copyBrowserData } from './copy'\nimport { findAvailablePort } from './init'\nimport kill from 'tree-kill';\n\n\nconst userData = app.getPath('userData');\nconst versionFile = path.join(userData, 'version.txt');\n\n// ==================== constants ====================\nconst __dirname = path.dirname(fileURLToPath(import.meta.url));\nconst MAIN_DIST = path.join(__dirname, '../..');\nconst RENDERER_DIST = path.join(MAIN_DIST, 'dist');\nconst VITE_DEV_SERVER_URL = process.env.VITE_DEV_SERVER_URL;\nconst VITE_PUBLIC = VITE_DEV_SERVER_URL\n  ? path.join(MAIN_DIST, 'public')\n  : RENDERER_DIST;\n\n// ==================== global variables ====================\nlet win: BrowserWindow | null = null;\nlet webViewManager: WebViewManager | null = null;\nlet fileReader: FileReader | null = null;\nlet python_process: ChildProcessWithoutNullStreams | null = null;\nlet backendPort: number = 5001;\nlet browser_port = 9222;\n\n// ==================== path config ====================\nconst preload = path.join(__dirname, '../preload/index.mjs');\nconst indexHtml = path.join(RENDERER_DIST, 'index.html');\nconst logPath = log.transports.file.getFile().path;\n\n// set remote debugging port\nfindAvailablePort(browser_port).then(port => {\n  browser_port = port;\n  app.commandLine.appendSwitch('remote-debugging-port', port + '');\n});\n\n// read last run version and install dependencies on update\nasync function checkAndInstallDepsOnUpdate(): Promise<boolean> {\n  const currentVersion = app.getVersion();\n  return new Promise(async (resolve, reject) => {\n    try {\n      log.info(' start check version', { currentVersion });\n\n      // Check if version file exists\n      const versionExists = fs.existsSync(versionFile);\n      let savedVersion = '';\n\n      if (versionExists) {\n        savedVersion = fs.readFileSync(versionFile, 'utf-8').trim();\n        log.info(' read saved version', { savedVersion });\n      } else {\n        log.info(' version file not exist, will create new file');\n      }\n\n      // if version file not exist or version not match, reinstall dependencies\n      if (!versionExists || savedVersion !== currentVersion) {\n        log.info(' version changed, prepare to reinstall uv dependencies...', {\n          currentVersion,\n          savedVersion: versionExists ? savedVersion : 'none',\n          reason: !versionExists ? 'version file not exist' : 'version not match'\n        });\n\n        // notify frontend to update\n        if (win && !win.isDestroyed()) {\n          win.webContents.send('update-notification', {\n            type: 'version-update',\n            currentVersion,\n            previousVersion: versionExists ? savedVersion : 'none',\n            reason: !versionExists ? 'version file not exist' : 'version not match'\n          });\n        }\n\n        // update version file\n        fs.writeFileSync(versionFile, currentVersion);\n        log.info(' version file updated', { currentVersion });\n\n        // install dependencies\n        const result = await installDependencies();\n        if (!result) {\n          log.error(' install dependencies failed');\n          resolve(false);\n          return \n        }\n        resolve(true);\n        log.info(' install dependencies complete');\n        return \n      } else {\n        log.info(' version not changed, skip install dependencies', { currentVersion });\n        resolve(true);\n        return \n      }\n    } catch (error) {\n      log.error(' check version and install dependencies error:', error);\n      resolve(false);\n      return \n    }\n  })\n}\n\n\n\n// ==================== app config ====================\nprocess.env.APP_ROOT = MAIN_DIST;\nprocess.env.VITE_PUBLIC = VITE_PUBLIC;\n\n// disable system theme\nnativeTheme.themeSource = 'light';\n\n// set log level\nlog.transports.console.level = 'info';\nlog.transports.file.level = 'info';\n\n// disable GPU Acceleration for Windows 7\nif (os.release().startsWith('6.1')) app.disableHardwareAcceleration()\n\n// Set application name for Windows 10+ notifications\nif (process.platform === 'win32') app.setAppUserModelId(app.getName())\n\nif (!app.requestSingleInstanceLock()) {\n  app.quit()\n  process.exit(0)\n}\n\n// ==================== protocol config ====================\nconst setupProtocolHandlers = () => {\n  if (process.env.NODE_ENV === 'development') {\n    const isDefault = app.isDefaultProtocolClient('eigent', process.execPath, [path.resolve(process.argv[1])]);\n    if (!isDefault) {\n      app.setAsDefaultProtocolClient('eigent', process.execPath, [path.resolve(process.argv[1])]);\n    }\n  } else {\n    app.setAsDefaultProtocolClient('eigent');\n  }\n};\n\n// ==================== protocol url handle ====================\nfunction handleProtocolUrl(url: string) {\n  log.info('enter handleProtocolUrl', url);\n  const urlObj = new URL(url);\n  const code = urlObj.searchParams.get('code');\n  const share_token = urlObj.searchParams.get('share_token');\n\n  log.info('urlObj', urlObj);\n  log.info('code', code);\n  log.info('share_token', share_token);\n\n  if (win && !win.isDestroyed()) {\n    log.info('urlObj.pathname', urlObj.pathname);\n\n    if (urlObj.pathname === '/oauth') {\n      log.info('oauth');\n      const provider = urlObj.searchParams.get('provider');\n      const code = urlObj.searchParams.get('code');\n      log.info(\"protocol oauth\", provider, code);\n      win.webContents.send('oauth-authorized', { provider, code });\n      return;\n    }\n\n    if (code) {\n      log.error('protocol code:', code);\n      win.webContents.send('auth-code-received', code);\n    }\n\n    if (share_token) {\n      win.webContents.send('auth-share-token-received', share_token);\n    }\n  } else {\n    log.error('window not available');\n  }\n}\n\n// ==================== single instance lock ====================\nconst setupSingleInstanceLock = () => {\n  const gotLock = app.requestSingleInstanceLock();\n  if (!gotLock) {\n    log.info(\"no-lock\");\n    app.quit();\n  } else {\n    app.on('second-instance', (event, argv) => {\n      log.info(\"second-instance\", argv);\n      const url = argv.find(arg => arg.startsWith('eigent://'));\n      if (url) handleProtocolUrl(url);\n      if (win) win.show();\n    });\n\n    app.on('open-url', (event, url) => {\n      log.info(\"open-url\");\n      event.preventDefault();\n      handleProtocolUrl(url);\n    });\n  }\n};\n\n// ==================== initialize config ====================\nconst initializeApp = () => {\n  setupProtocolHandlers();\n  setupSingleInstanceLock();\n\n};\n\n/**\n * Registers all IPC handlers once when the app starts\n * This prevents \"Attempted to register a second handler\" errors\n * when windows are reopened\n */\n// get backup log path\nconst getBackupLogPath = () => {\n  const userDataPath = app.getPath('userData')\n  return path.join(userDataPath, 'logs', 'main.log')\n}\n// constants define\nconst BROWSER_PATHS = {\n  win32: {\n    chrome: 'C:\\\\Program Files\\\\Google\\\\Chrome\\\\Application\\\\chrome.exe',\n    edge: 'C:\\\\Program Files (x86)\\\\Microsoft\\\\Edge\\\\Application\\\\msedge.exe',\n    firefox: 'C:\\\\Program Files\\\\Mozilla Firefox\\\\firefox.exe',\n    qq: 'C:\\\\Program Files\\\\Tencent\\\\QQBrowser\\\\QQBrowser.exe',\n    '360': path.join(homedir(), 'AppData\\\\Local\\\\360Chrome\\\\Chrome\\\\Application\\\\360chrome.exe'),\n    arc: path.join(homedir(), 'AppData\\\\Local\\\\Arc\\\\User Data\\\\Arc.exe'),\n    dia: path.join(homedir(), 'AppData\\\\Local\\\\Dia\\\\Application\\\\dia.exe'),\n    fellou: path.join(homedir(), 'AppData\\\\Local\\\\Fellou\\\\Application\\\\fellou.exe'),\n  },\n  darwin: {\n    chrome: '/Applications/Google Chrome.app',\n    edge: '/Applications/Microsoft Edge.app',\n    firefox: '/Applications/Firefox.app',\n    safari: '/Applications/Safari.app',\n    arc: '/Applications/Arc.app',\n    dia: '/Applications/Dia.app',\n    fellou: '/Applications/Fellou.app',\n  },\n} as const;\n\n// tool function\nconst getSystemLanguage = async () => {\n  const locale = app.getLocale();\n  return locale === 'zh-CN' ? 'zh-cn' : 'en';\n};\n\nconst checkManagerInstance = (manager: any, name: string) => {\n  if (!manager) {\n    throw new Error(`${name} not initialized`);\n  }\n  return manager;\n};\n\nconst handleDependencyInstallation = async () => {\n  try {\n    log.info(' start install dependencies...');\n\n    const isSuccess = await installDependencies();\n    if (!isSuccess) {\n      log.error(' install dependencies failed');\n      return { success: false, error: 'install dependencies failed' };\n    }\n\n    log.info(' install dependencies success, check tool installed status...');\n    const isToolInstalled = await checkToolInstalled();\n\n    if (isToolInstalled && !python_process) {\n      log.info(' tool installed, start backend service...');\n      python_process = await startBackend((port) => {\n        backendPort = port;\n        log.info(' backend service start success', { port });\n      });\n\n      // notify frontend to install success\n      if (win && !win.isDestroyed()) {\n        win.webContents.send('install-dependencies-complete', { success: true, code: 0 });\n      }\n\n      python_process?.on('exit', (code, signal) => {\n        log.info(' python process exit', { code, signal });\n      });\n    } else if (!isToolInstalled) {\n      log.warn(' tool not installed, skip backend start');\n    } else {\n      log.info(' backend process already exist, skip start');\n    }\n\n    log.info(' install dependencies complete');\n    return { success: true };\n  } catch (error: any) {\n    log.error(' install dependencies error:', error);\n    if (win && !win.isDestroyed()) {\n      win.webContents.send('install-dependencies-complete', { success: false, code: 2 });\n    }\n    return { success: false, error: error.message };\n  }\n};\n\nfunction registerIpcHandlers() {\n  // ==================== basic info handler ====================\n  ipcMain.handle('get-browser-port', () => browser_port);\n  ipcMain.handle('get-app-version', () => app.getVersion());\n  ipcMain.handle('get-backend-port', () => backendPort);\n  ipcMain.handle('get-system-language', getSystemLanguage);\n  ipcMain.handle('is-fullscreen', () => win?.isFullScreen() || false);\n  ipcMain.handle('get-home-dir', () => {\n    const platform = process.platform;\n    return platform === 'win32' ? process.env.USERPROFILE : process.env.HOME;\n  });\n\n  // ==================== command execution handler ====================\n  ipcMain.handle('execute-command', async (event, command: string, email: string) => {\n    log.info(\"execute-command\", command);\n    const tempEmail = email.split(\"@\")[0].replace(/[\\\\/*?:\"<>|\\s]/g, \"_\").replace(\".\", \"_\")\n    const MCP_CONFIG_DIR = path.join(os.homedir(), '.eigent');\n    const MCP_REMOTE_CONFIG_DIR = path.join(MCP_CONFIG_DIR, tempEmail);\n    if (!fs.existsSync(MCP_REMOTE_CONFIG_DIR)) {\n      fs.mkdirSync(MCP_REMOTE_CONFIG_DIR, { recursive: true });\n    }\n\n    try {\n      const { spawn } = await import('child_process');\n\n      // add --host parameter\n      const commandWithHost = `${command} --debug --host \"dev.eigent.ai/api/oauth/notion/callback?code=1\"`;\n      // const commandWithHost = `${command}`;\n\n      log.info(' start execute command:', commandWithHost);\n\n      // parse command and arguments\n      const [cmd, ...args] = commandWithHost.split(' ');\n\n      return new Promise((resolve) => {\n        const child = spawn(cmd, args, {\n          cwd: process.cwd(),\n          env: { ...process.env, MCP_REMOTE_CONFIG_DIR },\n          stdio: ['pipe', 'pipe', 'pipe']\n        });\n\n        let stdout = '';\n        let stderr = '';\n\n        // realtime listen standard output\n        child.stdout.on('data', (data) => {\n          const output = data.toString();\n          stdout += output;\n          log.info('Real-time output:', output.trim());\n        });\n\n        // realtime listen error output\n        child.stderr.on('data', (data) => {\n          const output = data.toString();\n          stderr += output;\n          if (output.includes('OAuth callback server running at')) {\n            const url = output.split('OAuth callback server running at')[1].trim();\n            log.info(' detect OAuth callback URL:', url);\n\n            // notify frontend to callback URL\n            if (win && !win.isDestroyed()) {\n              const match = url.match(/^https?:\\/\\/[^:\\n]+:\\d+/);\n              const cleanedUrl = match ? match[0] : null;\n              log.info('cleanedUrl', cleanedUrl);\n              win.webContents.send('oauth-callback-url', {\n                url: cleanedUrl,\n                provider: 'notion' // TODO: can be set dynamically according to actual situation\n              });\n\n            }\n          }\n          if (output.includes('Press Ctrl+C to exit')) {\n            child.kill();\n          }\n          log.info(' realtime error output:', output.trim());\n        });\n\n        // listen process exit\n        child.on('close', (code) => {\n          log.info(` command execute complete, exit code: ${code}`);\n          resolve({ success: code === 0, stdout, stderr });\n        });\n\n        // listen process error\n        child.on('error', (error) => {\n          log.error(' command execute error:', error);\n          resolve({ success: false, error: error.message });\n        });\n      });\n    } catch (error: any) {\n      log.error(' command execute failed:', error);\n      return { success: false, error: error.message };\n    }\n  });\n\n  // ==================== log export handler ====================\n  ipcMain.handle('export-log', async () => {\n    try {\n      let targetLogPath = logPath;\n      if (!fs.existsSync(targetLogPath)) {\n        const backupPath = getBackupLogPath();\n        if (fs.existsSync(backupPath)) {\n          targetLogPath = backupPath;\n        } else {\n          return { success: false, error: 'no log file' };\n        }\n      }\n\n      await fsp.access(targetLogPath, fs.constants.R_OK);\n      const stats = await fsp.stat(targetLogPath);\n      if (stats.size === 0) {\n        return { success: true, data: 'log file is empty' };\n      }\n\n      const logContent = await fsp.readFile(targetLogPath, 'utf-8');\n\n      // get app version and system version\n      const appVersion = app.getVersion();\n      const platform = process.platform;\n      const arch = process.arch;\n      const systemVersion = `${platform}-${arch}`;\n      const defaultFileName = `eigent-${appVersion}-${systemVersion}.log`;\n\n      // show save dialog\n      const { canceled, filePath } = await dialog.showSaveDialog({\n        title: 'save log file',\n        defaultPath: defaultFileName,\n        filters: [{ name: 'log file', extensions: ['log', 'txt'] }]\n      });\n\n      if (canceled || !filePath) {\n        return { success: false, error: '' };\n      }\n\n      await fsp.writeFile(filePath, logContent, 'utf-8');\n      return { success: true, savedPath: filePath };\n    } catch (error: any) {\n      return { success: false, error: error.message };\n    }\n  });\n\n  // ==================== MCP manage handler ====================\n  ipcMain.handle('mcp-install', async (event, name, mcp) => {\n    addMcp(name, mcp);\n    return { success: true };\n  });\n\n  ipcMain.handle('mcp-remove', async (event, name) => {\n    removeMcp(name);\n    return { success: true };\n  });\n\n  ipcMain.handle('mcp-update', async (event, name, mcp) => {\n    updateMcp(name, mcp);\n    return { success: true };\n  });\n\n  ipcMain.handle('mcp-list', async () => {\n    return readMcpConfig();\n  });\n\n  // ==================== browser related handler ====================\n  // TODO:next version implement\n  ipcMain.handle('check-install-browser', async () => {\n    try {\n      const platform = process.platform;\n      const results: Record<string, boolean> = {};\n      const paths = BROWSER_PATHS[platform as keyof typeof BROWSER_PATHS];\n\n      if (!paths) {\n        log.warn(`not support current platform: ${platform}`);\n        return {};\n      }\n\n      for (const [browser, execPath] of Object.entries(paths)) {\n        results[browser] = existsSync(execPath);\n      }\n\n      return results;\n    } catch (error: any) {\n      log.error('Failed to check browser installation:', error);\n      return {};\n    }\n  });\n\n  ipcMain.handle('start-browser-import', async (event, args) => {\n    const isWin = process.platform === 'win32';\n    const localAppData = process.env.LOCALAPPDATA || '';\n    const appData = process.env.APPDATA || '';\n    const home = os.homedir();\n\n    const candidates: Record<string, string> = {\n      chrome: isWin\n        ? `${localAppData}\\\\Google\\\\Chrome\\\\User Data\\\\Default`\n        : `${home}/Library/Application Support/Google/Chrome/Default`,\n      edge: isWin\n        ? `${localAppData}\\\\Microsoft\\\\Edge\\\\User Data\\\\Default`\n        : `${home}/Library/Application Support/Microsoft Edge/Default`,\n      firefox: isWin\n        ? `${appData}\\\\Mozilla\\\\Firefox\\\\Profiles`\n        : `${home}/Library/Application Support/Firefox/Profiles`,\n      qq: `${localAppData}\\\\Tencent\\\\QQBrowser\\\\User Data\\\\Default`,\n      '360': `${localAppData}\\\\360Chrome\\\\Chrome\\\\User Data\\\\Default`,\n      arc: isWin\n        ? `${localAppData}\\\\Arc\\\\User Data\\\\Default`\n        : `${home}/Library/Application Support/Arc/Default`,\n      dia: `${localAppData}\\\\Dia\\\\User Data\\\\Default`,\n      fellou: `${localAppData}\\\\Fellou\\\\User Data\\\\Default`,\n      safari: `${home}/Library/Safari`,\n    };\n\n    // filter unchecked browser\n    Object.keys(candidates).forEach((key) => {\n      const browser = args.find((item: any) => item.browserId === key);\n      if (!browser || !browser.checked) {\n        delete candidates[key];\n      }\n    });\n\n    const result: Record<string, string | null> = {};\n    for (const [name, p] of Object.entries(candidates)) {\n      result[name] = fs.existsSync(p) ? p : null;\n    }\n\n    const electronUserDataPath = app.getPath('userData');\n\n    for (const [browserName, browserPath] of Object.entries(result)) {\n      if (!browserPath) continue;\n      await copyBrowserData(browserName, browserPath, electronUserDataPath);\n    }\n\n    return { success: true };\n  });\n\n  // ==================== window control handler ====================\n  ipcMain.on('window-close', () => win?.close());\n  ipcMain.on('window-minimize', () => win?.minimize());\n  ipcMain.on('window-toggle-maximize', () => {\n    if (win?.isMaximized()) {\n      win?.unmaximize();\n    } else {\n      win?.maximize();\n    }\n  });\n\n  // ==================== file operation handler ====================\n  ipcMain.handle('select-file', async (event, options = {}) => {\n    const result = await dialog.showOpenDialog(win!, {\n      properties: ['openFile', 'multiSelections'],\n      ...options\n    });\n\n    if (!result.canceled && result.filePaths.length > 0) {\n      const files = result.filePaths.map(filePath => ({\n        filePath,\n        fileName: filePath.split(/[/\\\\]/).pop() || ''\n      }));\n\n      return {\n        success: true,\n        files,\n        fileCount: files.length\n      };\n    }\n\n    return {\n      success: false,\n      canceled: result.canceled\n    };\n  });\n\n  ipcMain.handle(\"reveal-in-folder\", async (event, filePath: string) => {\n    try {\n      shell.showItemInFolder(filePath);\n    } catch (e) {\n      log.error(\"reveal in folder failed\", e);\n    }\n  });\n\n  // ==================== read file handler ====================\n  ipcMain.handle('read-file', async (event, filePath: string) => {\n    try {\n      log.info('Reading file:', filePath);\n\n      // Check if file exists\n      if (!fs.existsSync(filePath)) {\n        log.error('File does not exist:', filePath);\n        return { success: false, error: 'File does not exist' };\n      }\n\n      // Read file content\n      const fileContent = await fsp.readFile(filePath);\n      log.info('File read successfully:', filePath);\n\n      return {\n        success: true,\n        data: fileContent,\n        size: fileContent.length\n      };\n    } catch (error: any) {\n      log.error('Failed to read file:', filePath, error);\n      return {\n        success: false,\n        error: error.message || 'Failed to read file'\n      };\n    }\n  });\n\n  // ==================== delete folder handler ====================\n  ipcMain.handle('delete-folder', async (event, email: string) => {\n    const tempEmail = email.split(\"@\")[0].replace(/[\\\\/*?:\"<>|\\s]/g, \"_\").replace(\".\", \"_\")\n    const MCP_CONFIG_DIR = path.join(os.homedir(), '.eigent');\n    const MCP_REMOTE_CONFIG_DIR = path.join(MCP_CONFIG_DIR, tempEmail);\n    try {\n      log.info('Deleting folder:', MCP_REMOTE_CONFIG_DIR);\n\n      // Check if folder exists\n      if (!fs.existsSync(MCP_REMOTE_CONFIG_DIR)) {\n        log.error('Folder does not exist:', MCP_REMOTE_CONFIG_DIR);\n        return { success: false, error: 'Folder does not exist' };\n      }\n\n      // Check if it's actually a directory\n      const stats = await fsp.stat(MCP_REMOTE_CONFIG_DIR);\n      if (!stats.isDirectory()) {\n        log.error('Path is not a directory:', MCP_REMOTE_CONFIG_DIR);\n        return { success: false, error: 'Path is not a directory' };\n      }\n\n      // Delete folder recursively\n      await fsp.rm(MCP_REMOTE_CONFIG_DIR, { recursive: true, force: true });\n      log.info('Folder deleted successfully:', MCP_REMOTE_CONFIG_DIR);\n\n      return {\n        success: true,\n        message: 'Folder deleted successfully'\n      };\n    } catch (error: any) {\n      log.error('Failed to delete folder:', MCP_REMOTE_CONFIG_DIR, error);\n      return {\n        success: false,\n        error: error.message || 'Failed to delete folder'\n      };\n    }\n  });\n\n  // ==================== get MCP config path handler ====================\n  ipcMain.handle('get-mcp-config-path', async (event, email: string) => {\n    try {\n      const tempEmail = email.split(\"@\")[0].replace(/[\\\\/*?:\"<>|\\s]/g, \"_\").replace(\".\", \"_\");\n      const MCP_CONFIG_DIR = path.join(os.homedir(), '.eigent');\n      const MCP_REMOTE_CONFIG_DIR = path.join(MCP_CONFIG_DIR, tempEmail);\n      \n      log.info('Getting MCP config path for email:', email);\n      log.info('MCP config path:', MCP_REMOTE_CONFIG_DIR);\n      \n      return {\n        success: true,\n        path: MCP_REMOTE_CONFIG_DIR,\n        tempEmail: tempEmail,\n        baseDir: MCP_CONFIG_DIR\n      };\n    } catch (error: any) {\n      log.error('Failed to get MCP config path:', error);\n      return {\n        success: false,\n        error: error.message || 'Failed to get MCP config path'\n      };\n    }\n  });\n\n  // ==================== env handler ====================\n  ipcMain.handle('get-env-path', async (_event, email) => {\n    return getEnvPath(email);\n  });\n\n  ipcMain.handle('env-write', async (_event, email, { key, value }) => {\n    const ENV_PATH = getEnvPath(email);\n    let content = '';\n    try {\n      content = fs.existsSync(ENV_PATH) ? fs.readFileSync(ENV_PATH, 'utf-8') : '';\n    } catch (error) {\n      log.error(\"env-write error:\", error);\n    }\n    let lines = content.split(/\\r?\\n/);\n    lines = updateEnvBlock(lines, { [key]: value });\n    fs.writeFileSync(ENV_PATH, lines.join('\\n'), 'utf-8');\n    return { success: true };\n  });\n\n  ipcMain.handle('env-remove', async (_event, email, key) => {\n    log.info(\"env-remove\", key);\n    const ENV_PATH = getEnvPath(email);\n    let content = '';\n    try {\n      content = fs.existsSync(ENV_PATH) ? fs.readFileSync(ENV_PATH, 'utf-8') : '';\n    } catch (error) {\n      log.error(\"env-remove error:\", error);\n    }\n    let lines = content.split(/\\r?\\n/);\n    lines = removeEnvKey(lines, key);\n    fs.writeFileSync(ENV_PATH, lines.join('\\n'), 'utf-8');\n    log.info(\"env-remove success\", ENV_PATH);\n    return { success: true };\n  });\n\n  // ==================== new window handler ====================\n  ipcMain.handle('open-win', (_, arg) => {\n    const childWindow = new BrowserWindow({\n      webPreferences: {\n        preload,\n        nodeIntegration: true,\n        contextIsolation: false,\n      },\n    });\n\n    if (VITE_DEV_SERVER_URL) {\n      childWindow.loadURL(`${VITE_DEV_SERVER_URL}#${arg}`);\n    } else {\n      childWindow.loadFile(indexHtml, { hash: arg });\n    }\n  });\n\n  // ==================== FileReader handler ====================\n  ipcMain.handle('open-file', async (_, type: string, filePath: string, isShowSourceCode: boolean) => {\n    const manager = checkManagerInstance(fileReader, 'FileReader');\n    return manager.openFile(type, filePath, isShowSourceCode);\n  });\n\n  ipcMain.handle('get-file-list', async (_, email: string, taskId: string) => {\n    const manager = checkManagerInstance(fileReader, 'FileReader');\n    return manager.getFileList(email, taskId);\n  });\n\n  // ==================== WebView handler ====================\n  const webviewHandlers = [\n    { name: 'capture-webview', method: 'captureWebview' },\n    { name: 'create-webview', method: 'createWebview' },\n    { name: 'hide-webview', method: 'hideWebview' },\n    { name: 'show-webview', method: 'showWebview' },\n    { name: 'change-view-size', method: 'changeViewSize' },\n    { name: 'hide-all-webview', method: 'hideAllWebview' },\n    { name: 'get-active-webview', method: 'getActiveWebview' },\n    { name: 'set-size', method: 'setSize' },\n    { name: 'get-show-webview', method: 'getShowWebview' },\n    { name: 'webview-destroy', method: 'destroyWebview' },\n  ];\n\n  webviewHandlers.forEach(({ name, method }) => {\n    ipcMain.handle(name, async (_, ...args) => {\n      const manager = checkManagerInstance(webViewManager, 'WebViewManager');\n      return manager[method as keyof typeof manager](...args);\n    });\n  });\n\n  // ==================== dependency install handler ====================\n  ipcMain.handle('install-dependencies', handleDependencyInstallation);\n  ipcMain.handle('frontend-ready', handleDependencyInstallation);\n\n  ipcMain.handle('check-tool-installed', async () => {\n    try {\n      const isInstalled = await checkToolInstalled();\n      return { success: true, isInstalled };\n    } catch (error) {\n      return { success: false, error: (error as Error).message };\n    }\n  });\n\n  // ==================== register update related handler ====================\n  registerUpdateIpcHandlers();\n}\n\n// ==================== window create ====================\nasync function createWindow() {\n  const isMac = process.platform === 'darwin';\n\n  win = new BrowserWindow({\n    title: 'Eigent',\n    width: 1200,\n    height: 800,\n    frame: false,\n    transparent: true,\n    vibrancy: 'sidebar',\n    visualEffectState: 'active',\n    backgroundColor: '#00000000',\n    titleBarStyle: isMac ? 'hidden' : undefined,\n    trafficLightPosition: isMac ? { x: 10, y: 10 } : undefined,\n    icon: path.join(VITE_PUBLIC, 'favicon.ico'),\n    roundedCorners: true,\n    webPreferences: {\n      webSecurity: false,\n      preload,\n      nodeIntegration: true,\n      contextIsolation: true,\n      webviewTag: true,\n      spellcheck: false,\n    },\n  });\n\n  // ==================== initialize manager ====================\n  fileReader = new FileReader(win);\n  webViewManager = new WebViewManager(win);\n\n  // create multiple webviews\n  for (let i = 1; i <= 8; i++) {\n    webViewManager.createWebview(i === 1 ? undefined : i.toString());\n  }\n\n  // ==================== load content ====================\n  if (VITE_DEV_SERVER_URL) {\n    win.loadURL(VITE_DEV_SERVER_URL);\n    win.webContents.openDevTools();\n  } else {\n    win.loadFile(indexHtml);\n  }\n\n  // ==================== set event listeners ====================\n  setupWindowEventListeners();\n  setupDevToolsShortcuts();\n  setupExternalLinkHandling();\n\n  // ==================== auto update ====================\n  update(win);\n\n  // ==================== check tool installed ====================\n  let res = await checkAndInstallDepsOnUpdate();\n  if (!res) {\n    log.info('checkAndInstallDepsOnUpdate,install dependencies failed');\n    win.webContents.send('install-dependencies-complete', { success: false, code: 2 });\n    return;\n  }\n  await checkAndStartBackend();\n}\n\n// ==================== window event listeners ====================\nconst setupWindowEventListeners = () => {\n  if (!win) return;\n\n  // close default menu\n  Menu.setApplicationMenu(null);\n};\n\n// ==================== devtools shortcuts ====================\nconst setupDevToolsShortcuts = () => {\n  if (!win) return;\n\n  const toggleDevTools = () => win?.webContents.toggleDevTools();\n\n  win.webContents.on('before-input-event', (event, input) => {\n    // F12 key\n    if (input.key === 'F12' && input.type === 'keyDown') {\n      toggleDevTools();\n    }\n\n    // Ctrl+Shift+I (Windows/Linux) or Cmd+Shift+I (Mac)\n    if (input.control && input.shift && input.key.toLowerCase() === 'i' && input.type === 'keyDown') {\n      toggleDevTools();\n    }\n\n    // Mac Cmd+Shift+I\n    if (input.meta && input.shift && input.key.toLowerCase() === 'i' && input.type === 'keyDown') {\n      toggleDevTools();\n    }\n  });\n};\n\n// ==================== external link handle ====================\nconst setupExternalLinkHandling = () => {\n  if (!win) return;\n\n  // handle new window open\n  win.webContents.setWindowOpenHandler(({ url }) => {\n    if (url.startsWith('https:') || url.startsWith('http:')) {\n      shell.openExternal(url);\n    }\n    return { action: 'deny' };\n  });\n\n  // handle navigation\n  win.webContents.on('will-navigate', (event, url) => {\n    event.preventDefault();\n    shell.openExternal(url);\n  });\n};\n\n// ==================== check and start backend ====================\nconst checkAndStartBackend = async () => {\n  log.info('Checking and starting backend service...');\n\n  const isToolInstalled = await checkToolInstalled();\n  if (isToolInstalled) {\n    log.info('Tools installed, starting backend service...');\n\n    // Notify frontend of successful installation\n    if (win && !win.isDestroyed()) {\n      win.webContents.send('install-dependencies-complete', { success: true, code: 0 });\n    }\n\n    python_process = await startBackend((port) => {\n      backendPort = port;\n      log.info('Backend service started successfully', { port });\n    });\n\n    python_process?.on('exit', (code, signal) => {\n      log.info('Python process exited', { code, signal });\n    });\n  } else {\n    log.warn('Tools not installed, unable to start backend service');\n  }\n};\n\n// ==================== process cleanup ====================\nconst cleanupPythonProcess = () => {\n  try {\n    if (python_process?.pid) {\n      log.info('Cleaning up Python process', { pid: python_process.pid });\n      kill(python_process.pid, 'SIGINT', (err) => {\n        if (err) {\n          log.error('Failed to clean up process tree:', err);\n        } else {\n          log.info('Successfully cleaned up Python process tree');\n        }\n      });\n    } else {\n      log.info('No Python process to clean up');\n    }\n  } catch (error) {\n    log.error('Error occurred while cleaning up process:', error);\n  }\n};\n\n// ==================== app event handle ====================\napp.whenReady().then(() => {\n\n  // ==================== download handle ====================\n  session.defaultSession.on('will-download', (event, item, webContents) => {\n    item.once('done', (event, state) => {\n      shell.showItemInFolder(item.getURL().replace('localfile://', ''));\n    });\n  });\n\n  // ==================== protocol handle ====================\n  protocol.handle('localfile', async (request) => {\n    const url = decodeURIComponent(request.url.replace('localfile://', ''));\n    const filePath = path.normalize(url);\n\n    try {\n      const data = await fsp.readFile(filePath);\n\n      // set correct Content-Type according to file extension\n      const ext = path.extname(filePath).toLowerCase();\n      let contentType = 'application/octet-stream';\n\n      switch (ext) {\n        case '.pdf':\n          contentType = 'application/pdf';\n          break;\n        case '.html':\n        case '.htm':\n          contentType = 'text/html';\n          break;\n      }\n\n      return new Response(data, {\n        headers: {\n          'Content-Type': contentType,\n        },\n      });\n    } catch (err) {\n      return new Response('Not Found', { status: 404 });\n    }\n  });\n\n  // ==================== initialize app ====================\n  initializeApp();\n  registerIpcHandlers();\n  createWindow();\n});\n\n// ==================== window close event ====================\napp.on('window-all-closed', () => {\n  log.info('window-all-closed');\n  webViewManager = null;\n  win = null;\n  if (process.platform !== 'darwin') {\n    app.quit();\n  }\n});\n\n// ==================== app activate event ====================\napp.on('activate', () => {\n  const allWindows = BrowserWindow.getAllWindows();\n  log.info('activate', allWindows.length);\n\n  if (allWindows.length) {\n    allWindows[0].focus();\n  } else {\n    cleanupPythonProcess();\n    createWindow();\n  }\n});\n\n// ==================== app exit event ====================\napp.on('before-quit', () => {\n  log.info('before-quit');\n  log.info('quit python_process.pid: ' + python_process?.pid);\n  if (win) {\n    win.destroy();\n  }\n  cleanupPythonProcess();\n});\n\n"], "names": ["win", "path", "fs", "mainWindow", "window", "url", "_a", "newId", "file", "universalify", "polyfills", "patch", "rename", "err", "err2", "er", "er2", "require$$0", "legacy", "self", "clone", "copy", "require$$1", "require$$2", "require$$3", "util", "queue", "global", "readFile", "options", "cb", "writeFile", "data", "copyFile", "src", "dest", "flags", "go$readdir", "mode", "u", "buffer", "buffers", "atLeastNode", "defaults", "makeDir", "mkdirs", "utimes<PERSON><PERSON><PERSON>", "utimesMillisSync", "stat", "getStats", "mkdirsSync", "require$$4", "copySync", "startCopy", "statSync", "onDir", "onFile", "onLink", "mayCopyFile", "setDestMode", "fileIsNotWritable", "makeFileWritable", "setDestTimestamps", "mkDirAndCopy", "copyDir", "copyDirItem", "copyLink", "pathExists", "require$$5", "<PERSON><PERSON><PERSON>", "remove", "mkdir", "emptyDir", "stats", "srcpath", "dstpath", "link", "symlinkPaths", "symlinkPathsSync", "symlinkType", "symlinkTypeSync", "require$$6", "type", "symlink", "stringify", "stripBom", "jsonfile", "jsonFile", "outputFile", "outputFileSync", "moveSync", "<PERSON><PERSON><PERSON><PERSON>", "moveAcrossDevice", "move", "require$$7", "require$$8", "require$$9", "require$$10", "require$$11", "require$$12", "os", "code", "platform", "MCP_CONFIG_DIR", "spawn", "output", "event"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAQA,MAAM,EAAE,YAAA,IAAgB,cAAc,YAAY,GAAG,EAAE,kBAAkB;AAElE,SAAS,OAAOA,MAA6B;;AAGlD,cAAY,eAAe;AAC3B,cAAY,sBAAsB;AAClC,cAAY,iBAAiB;AAE7B,cAAY,uBAAuB;AAGnC,cAAY,GAAG,uBAAuB,WAAY;AAAA,EAAE,CAAC;AAErD,cAAY,GAAG,oBAAoB,CAAC,QAAoB;AACtD,QAAIA,QAAO,CAACA,KAAI,eAAe;AAC7B,MAAAA,KAAI,YAAY,KAAK,wBAAwB,EAAE,QAAQ,MAAM,SAAS,IAAI,WAAA,GAAc,YAAY,2BAAK,SAAS;AAAA,IACpH;AAAA,EACF,CAAC;AAED,cAAY,GAAG,wBAAwB,CAAC,QAAoB;AAC1D,QAAIA,QAAO,CAACA,KAAI,eAAe;AAC7B,MAAAA,KAAI,YAAY,KAAK,wBAAwB,EAAE,QAAQ,OAAO,SAAS,IAAI,WAAA,GAAc,YAAY,2BAAK,SAAS;AAAA,IACrH;AAAA,EACF,CAAC;AACD,UAAQ,IAAI,oBAAoB,YAAY,eAAe,OAAO;AAClE,UAAQ,IAAI,wBAAuB,iBAAY,wBAAZ,oCAAmC;AACtE,UAAQ,IAAI,wCAAwC,IAAI,QAAQ,UAAU,CAAC;AAC3E,MAAI,IAAI,YAAY;AAClB,gBAAY,yBAAA;AAAA,EACd;AAEA,MAAI,CAAC,IAAI,YAAY;AACnB,UAAM,YAAY;AAClB,UAAM,OAAO;AAAA,MACX,UAAU;AAAA,MACV,KAAK;AAAA,IAAA;AAGP,YAAQ,IAAI,qBAAqB,SAAS;AAC1C,gBAAY,WAAW,IAAI;AAC3B,gBAAY,gBAAA;AAAA,EACd;AAEF;AAMO,SAAS,4BAA4B;AAE1C,UAAQ,OAAO,gBAAgB,YAAY;AAGzC,QAAI;AACF,aAAO,MAAM,YAAY,yBAAA;AAAA,IAC3B,SAAS,OAAO;AACd,aAAO,EAAE,SAAS,iBAAiB,MAAA;AAAA,IACrC;AAAA,EACF,CAAC;AAGD,UAAQ,OAAO,kBAAkB,CAAC,UAAuC;AACvE;AAAA,MACE,CAAC,OAAO,iBAAiB;AACvB,YAAI,OAAO;AAET,cAAI,CAAC,MAAM,OAAO,eAAe;AAC/B,kBAAM,OAAO,KAAK,gBAAgB,EAAE,SAAS,MAAM,SAAS,OAAO;AAAA,UACrE;AAAA,QACF,OAAO;AAEL,cAAI,CAAC,MAAM,OAAO,eAAe;AAC/B,kBAAM,OAAO,KAAK,qBAAqB,YAAY;AAAA,UACrD;AAAA,QACF;AAAA,MACF;AAAA,MACA,MAAM;AAEJ,YAAI,CAAC,MAAM,OAAO,eAAe;AAC/B,gBAAM,OAAO,KAAK,mBAAmB;AAAA,QACvC;AAAA,MACF;AAAA,IAAA;AAAA,EAEJ,CAAC;AAGD,UAAQ,OAAO,oBAAoB,MAAM;AACvC,gBAAY,eAAe,OAAO,IAAI;AAAA,EACxC,CAAC;AACH;AAEA,SAAS,cACP,UACA,UACA;AACA,cAAY,GAAG,qBAAqB,CAAC,SAAuB,SAAS,MAAM,IAAI,CAAC;AAChF,cAAY,GAAG,SAAS,CAAC,UAAiB,SAAS,OAAO,IAAI,CAAC;AAC/D,cAAY,GAAG,qBAAqB,QAAQ;AAC5C,cAAY,eAAA;AACd;ACrGO,SAAS,kBAAkB;AAChC,SAAOC,OAAK,KAAK,IAAI,WAAA,GAAc,WAAW;AAChD;AAEO,SAAS,iBAAiB;AAC/B,MAAI,IAAI,YAAY;AAElB,WAAOA,OAAK,KAAK,QAAQ,eAAe,SAAS;AAAA,EACnD,OAAO;AAEL,WAAOA,OAAK,KAAK,IAAI,WAAA,GAAc,SAAS;AAAA,EAC9C;AACF;AAEO,SAAS,iBAAiB,YAAsC;AACrE,SAAO,IAAI,QAAiB,CAAC,SAAS,WAAW;AAC/C,UAAM,oBAAoBA,OAAK,KAAK,gBAAA,GAAmB,WAAW,UAAU;AAC5E,QAAI,KAAK,sBAAsB,iBAAiB,EAAE;AAElD,UAAM,cAAc,MAAM,QAAQ,UAAU,CAAC,iBAAiB,GAAG;AAAA,MAC/D,KAAK,EAAE,GAAG,QAAA,KAAa,sBAAsB,IAAA;AAAA,IAAI,CAClD;AAED,gBAAY,OAAO,GAAG,QAAQ,CAAC,SAAS;AACtC,UAAI,KAAK,kBAAkB,IAAI,EAAE;AAAA,IACnC,CAAC;AAED,gBAAY,OAAO,GAAG,QAAQ,CAAC,SAAS;AACtC,UAAI,MAAM,iBAAiB,IAAI,EAAE;AAAA,IACnC,CAAC;AAED,gBAAY,GAAG,SAAS,CAAC,SAAS;AAChC,UAAI,SAAS,GAAG;AACd,YAAI,KAAK,+BAA+B;AACxC,gBAAQ,IAAI;AAAA,MACd,OAAO;AACL,YAAI,MAAM,2BAA2B,IAAI,EAAE;AAC3C,eAAO,KAAK;AAAA,MACd;AAAA,IACF,CAAC;AAAA,EACH,CAAC;AACH;AAEA,eAAsB,cAAc,MAA+B;AACjE,MAAI,QAAQ,aAAa,SAAS;AAChC,WAAO,GAAG,IAAI;AAAA,EAChB;AACA,SAAO;AACT;AAEA,eAAsB,cAAc,MAAgC;AAClE,MAAI,CAAC,MAAM;AACT,WAAOA,OAAK,KAAK,GAAG,QAAA,GAAW,WAAW,KAAK;AAAA,EACjD;AACA,QAAM,aAAa,MAAM,cAAc,IAAI;AAC3C,QAAM,cAAcA,OAAK,KAAK,GAAG,QAAA,GAAW,WAAW,KAAK;AAC5D,QAAM,oBAAoB,MAAMC,KAAG,WAAW,WAAW;AAEzD,SAAO,oBAAoBD,OAAK,KAAK,aAAa,UAAU,IAAI;AAClE;AAEA,eAAsB,eAAe,MAAgC;AACnE,QAAM,MAAM,MAAM,cAAc,IAAI;AAEpC,SAAO,MAAMC,KAAG,WAAW,GAAG;AAChC;AChEA,SAAS,gBAAsC;AAC3C,QAAM,UAAU,cAAc,cAAA;AAC9B,SAAO,QAAQ,SAAS,IAAI,QAAQ,CAAC,IAAI;AAC7C;AAGA,eAAsB,qBAAqB;AACvC,SAAO,IAAI,QAAQ,OAAO,SAAS,WAAW;AAC1C,QAAI,CAAE,MAAM,eAAe,IAAI,GAAI;AAC/B,cAAQ,KAAK;AAAA,IACjB;AAEA,QAAI,CAAE,MAAM,eAAe,KAAK,GAAI;AAChC,cAAQ,KAAK;AAAA,IACjB;AAEA,YAAQ,IAAI;AAAA,EAChB,CAAC;AAEL;AAKA,eAAsB,qBAAqB;AACvC,SAAO,IAAI,QAAQ,OAAO,SAAS,WAAW;AAC1C,QAAI,iBAAiB;AACrB,YAAQ,IAAI,iEAAiE;AAC7E,QAAI,CAAE,MAAM,eAAe,IAAI,GAAI;AAC/B,cAAQ,IAAI,kBAAkB;AAC9B,YAAM,iBAAiB,eAAe;AACtC,YAAM,eAAe,MAAM,eAAe,IAAI;AAC9C,YAAM,aAAa,cAAA;AACnB,UAAI,cAAc,CAAC,WAAW,eAAe;AACzC,YAAI,cAAc;AAEd,qBAAW,YAAY,KAAK,4BAA4B,EAAE,MAAM,UAAU,MAAM,IAAI;AAAA,QACxF,OAAO;AACH,2BAAiB;AACjB,qBAAW,YAAY,KAAK,iCAAiC,EAAE,SAAS,OAAO,MAAM,GAAG,OAAO,2BAA2B,CAAC,GAAA,CAAI;AAAA,QACnI;AAAA,MACJ;AAAA,IACJ;AAEA,QAAI,CAAE,MAAM,eAAe,KAAK,GAAI;AAChC,cAAQ,IAAI,mBAAmB;AAC/B,YAAM,iBAAiB,gBAAgB;AACvC,YAAM,gBAAgB,MAAM,eAAe,KAAK;AAChD,YAAM,aAAa,cAAA;AACnB,UAAI,cAAc,CAAC,WAAW,eAAe;AACzC,YAAI,eAAe;AACf,qBAAW,YAAY,KAAK,4BAA4B,EAAE,MAAM,UAAU,MAAM,IAAI;AAAA,QACxF,OAAO;AACH,2BAAiB;AACjB,qBAAW,YAAY,KAAK,iCAAiC,EAAE,SAAS,OAAO,MAAM,GAAG,OAAO,2BAA2B,CAAC,GAAA,CAAI;AAAA,QACnI;AAAA,MACJ;AAAA,IACJ;AACA,YAAQ,cAAc;AAAA,EAC1B,CAAC;AAEL;AAEA,eAAsB,sBAAsB;AACxC,SAAO,IAAI,QAAiB,OAAO,SAAS,WAAW;AACnD,YAAQ,IAAI,4BAA4B;AAGxC,UAAM,aAAa,cAAA;AACnB,QAAI,cAAc,CAAC,WAAW,eAAe;AACzC,iBAAW,YAAY,KAAK,4BAA4B;AAAA,IAC5D;AAEA,UAAM,sBAAsB,MAAM,mBAAA;AAClC,QAAI,CAAC,qBAAqB;AACtB,cAAQ,KAAK;AACb;AAAA,IACJ;AACA,UAAM,UAAU,MAAM,cAAc,IAAI;AACxC,UAAM,cAAc,eAAA;AAGpB,QAAI,CAACA,KAAG,WAAW,WAAW,GAAG;AAC7BA,WAAG,UAAU,aAAa,EAAE,WAAW,MAAM;AAAA,IACjD;AAGA,UAAM,qBAAqBD,OAAK,KAAK,aAAa,oBAAoB;AACtEC,SAAG,cAAc,oBAAoB,EAAE;AACvC,UAAM,QAAQ,CAAC,mBAAmB,0CAA0C;AAC5E,aAAS,oBAAoB;AACzB,YAAM,WAAW,KAAK,eAAA,EAAiB,kBAAkB;AACzD,aAAO,aAAa;AAAA,IACxB;AACA,YAAQ,IAAI,qBAAqB,mBAAmB;AACpD,UAAM,eAAe,MAAM,SAAS,CAAC,QAAQ,YAAY,GAAI,kBAAA,IAAsB,QAAQ,CAAA,CAAG,GAAG,EAAE,KAAK,aAAa;AACrH,iBAAa,OAAO,GAAG,QAAQ,CAAC,SAAS;AACrC,UAAI,KAAK,kBAAkB,IAAI,EAAE;AAEjC,YAAMC,cAAa,cAAA;AACnB,UAAIA,eAAc,CAACA,YAAW,eAAe;AACzCA,oBAAW,YAAY,KAAK,4BAA4B,EAAE,MAAM,UAAU,MAAM,KAAK,SAAA,GAAY;AAAA,MACrG;AAAA,IACJ,CAAC;AAED,iBAAa,OAAO,GAAG,QAAQ,CAAC,SAAS;AACrC,UAAI,MAAM,oBAAoB,IAAI,EAAE;AAEpC,YAAMA,cAAa,cAAA;AACnB,UAAIA,eAAc,CAACA,YAAW,eAAe;AACzCA,oBAAW,YAAY,KAAK,4BAA4B,EAAE,MAAM,UAAU,MAAM,KAAK,SAAA,GAAY;AAAA,MACrG;AAAA,IACJ,CAAC;AAED,iBAAa,GAAG,SAAS,OAAO,SAAS;AAErC,UAAID,KAAG,WAAW,kBAAkB,GAAG;AACnCA,aAAG,WAAW,kBAAkB;AAAA,MACpC;AAEA,UAAI,SAAS,GAAG;AACZ,YAAI,KAAK,+BAA+B;AAGxC,cAAM,oBAAoBD,OAAK,KAAK,aAAa,mBAAmB;AACpEC,aAAG,cAAc,mBAAmB,EAAE;AACtC,gBAAQ,IAAI,0BAA0B;AAGtC,cAAM,SAAS,CAAC,OAAO,QAAQ,OAAO,GAAG,EAAE,KAAK,aAAa;AAC7D,gBAAQ,IAAI;AAAA,MAEhB,OAAO;AACH,YAAI,MAAM,2BAA2B,IAAI,EAAE;AAE3C,cAAMC,cAAa,cAAA;AACnB,YAAIA,eAAc,CAACA,YAAW,eAAe;AACzCA,sBAAW,YAAY,KAAK,iCAAiC,EAAE,SAAS,OAAO,MAAM,OAAO,2BAA2B,IAAI,GAAA,CAAI;AAC/H,kBAAQ,KAAK;AAAA,QACjB;AAAA,MACJ;AAAA,IACJ,CAAC;AAAA,EACL,CAAC;AACL;AAGA,eAAsB,aAAa,SAAgD;AAC/E,UAAQ,IAAI,eAAe;AAC3B,QAAM,UAAU,MAAM,cAAc,IAAI;AACxC,QAAM,cAAc,eAAA;AACpB,QAAM,OAAO,MAAM,kBAAkB,IAAI;AACzC,MAAI,SAAS;AACT,YAAQ,IAAI;AAAA,EAChB;AAEA,QAAM,MAAM;AAAA,IACR,GAAG,QAAA;AAAA,IACH,YAAY;AAAA,IACZ,kBAAkB;AAAA,EAAA;AAEtB,SAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACpC,UAAM,eAAe;AAAA,MACjB;AAAA,MACA,CAAC,OAAO,WAAW,YAAY,UAAU,KAAK,SAAA,GAAY,UAAU,SAAS;AAAA,MAC7E,EAAE,KAAK,aAAa,KAAU,UAAU,MAAA;AAAA,IAAM;AAIlD,QAAI,UAAU;AAGd,iBAAa,OAAO,GAAG,QAAQ,CAAC,SAAS;AACrC,UAAI,KAAK,mBAAmB,IAAI,EAAE;AAElC,UAAI,CAAC,WAAW,KAAK,WAAW,SAAS,oBAAoB,GAAG;AAC5D,kBAAU;AACV,gBAAQ,YAAY;AAAA,MACxB;AAAA,IACJ,CAAC;AAED,iBAAa,OAAO,GAAG,QAAQ,CAAC,SAAS;AACrC,UAAI,MAAM,0BAA0B,IAAI,EAAE;AAC1C,UAAI,CAAC,WAAW,KAAK,WAAW,SAAS,oBAAoB,GAAG;AAC5D,kBAAU;AACV,gBAAQ,YAAY;AAAA,MACxB;AAAA,IACJ,CAAC;AAED,iBAAa,GAAG,SAAS,CAAC,SAAS;AAC/B,UAAI,CAAC,SAAS;AACV,eAAO,IAAI,MAAM,4BAA4B,IAAI,EAAE,CAAC;AAAA,MACxD;AAAA,IACJ,CAAC;AAAA,EACL,CAAC;AAwBL;AAEA,SAAS,mBAAmB,MAAgC;AACxD,SAAO,IAAI,QAAQ,CAAC,YAAY;AAC5B,UAAM,SAAS,IAAI,aAAA;AAEnB,WAAO,KAAK,SAAS,CAAC,QAAa;AAC/B,UAAI,IAAI,SAAS,cAAc;AAC3B,gBAAQ,KAAK;AAAA,MACjB,OAAO;AACH,gBAAQ,KAAK;AAAA,MACjB;AAAA,IACJ,CAAC;AAED,WAAO,KAAK,aAAa,MAAM;AAC3B,aAAO,MAAM,MAAM;AACf,gBAAQ,IAAI,YAAY,IAAI;AAC5B,gBAAQ,IAAI;AAAA,MAChB,CAAC;AAAA,IACL,CAAC;AAGD,WAAO,OAAO,EAAE,MAAM,MAAM,aAAa,WAAW,MAAM;AAAA,EAC9D,CAAC;AACL;AAEA,eAAsB,kBAAkB,WAAmB,cAAc,IAAqB;AAC1F,MAAI,OAAO;AACX,WAAS,IAAI,GAAG,IAAI,aAAa,KAAK;AAClC,UAAM,YAAY,MAAM,mBAAmB,IAAI;AAC/C,QAAI,WAAW;AACX,aAAO;AAAA,IACX;AACA;AAAA,EACJ;AACA,QAAM,IAAI,MAAM,yBAAyB;AAC7C;ACpPO,MAAM,eAAe;AAAA,EAI1B,YAAYC,SAAuB;AAH3B,wDAAe,IAAA;AACf,+BAA4B;AAC5B,gCAAa,EAAE,GAAG,GAAG,GAAG,GAAG,OAAO,GAAG,QAAQ,EAAA;AAEnD,SAAK,MAAMA;AAAA,EACb;AAAA;AAAA;AAAA,EAKA,MAAa,eAAe,WAAmB;AAC7C,UAAM,cAAc,KAAK,SAAS,IAAI,SAAS;AAC/C,QAAI,CAAC,YAAa,QAAO;AAEzB,UAAM,QAAQ,MAAM,YAAY,KAAK,YAAY,YAAA;AACjD,UAAM,aAAa,MAAM,OAAO,EAAE;AAClC,WAAO,4BAA4B,WAAW,SAAS,QAAQ;AAAA,EACjE;AAAA,EAEO,QAAQ,MAAY;AACzB,SAAK,OAAO;AACZ,SAAK,SAAS,QAAQ,CAAC,YAAY;AACjC,UAAI,QAAQ,YAAY,QAAQ,QAAQ;AACtC,aAAK,eAAe,QAAQ,IAAI,IAAI;AAAA,MACtC;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EAEO,mBAAmB;AACxB,UAAM,iBAAiB,MAAM,KAAK,KAAK,SAAS,OAAA,CAAQ,EAAE,OAAO,CAAA,YAAW,QAAQ,QAAQ;AAE5F,WAAO,eAAe,IAAI,CAAA,YAAW,QAAQ,EAAE;AAAA,EACjD;AAAA,EAIA,MAAa,cAAc,KAAa,KAAK,MAAc,qBAAqB;;AAC9E,QAAI;AAEF,UAAI,KAAK,SAAS,IAAI,EAAE,GAAG;AACzB,eAAO,EAAE,SAAS,OAAO,OAAO,mBAAmB,EAAE,kBAAA;AAAA,MACvD;AACA,YAAM,OAAO,IAAI,gBAAgB;AAAA,QAC/B,gBAAgB;AAAA,UACd,iBAAiB;AAAA,UACjB,kBAAkB;AAAA,QAAA;AAAA,MACpB,CACD;AACD,WAAK,YAAY,GAAG,mBAAmB,MAAM;AAC3C,aAAK,YAAY,kBAAkB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,SAMlC;AAAA,MACH,CAAC;AAGD,WAAK,YAAY,aAAa;AAC9B,UAAI,QAAQ,OAAO,EAAE;AACrB,WAAK,UAAU,EAAE,GAAG,QAAQ,QAAQ,KAAK,GAAG,QAAQ,QAAQ,KAAK,OAAO,KAAK,QAAQ,KAAK;AAC1F,WAAK,gBAAgB,EAAE;AAEvB,YAAM,KAAK,YAAY,QAAQ,GAAG;AAElC,YAAM,cAA2B;AAAA,QAC/B;AAAA,QACA;AAAA,QACA,YAAY;AAAA,QACZ,YAAY;AAAA,QACZ,UAAU;AAAA,QACV,QAAQ;AAAA,MAAA;AAOV,WAAK,YAAY,GAAG,wBAAwB,CAAC,OAAOC,SAAQ;;AAC1D,YAAI,YAAY,YAAY,YAAY,UAAUA,SAAQ,uBAAuBA,SAAQ,eAAe;AACtG,kBAAQ,IAAI,wBAAwB,IAAIA,IAAG;AAC3C,WAAAC,MAAA,KAAK,QAAL,gBAAAA,IAAU,YAAY,KAAK,eAAeD;AAC1C;AAAA,QACF;AAAA,MACF,CAAC;AAED,WAAK,YAAY,GAAG,gBAAgB,CAAC,OAAO,kBAAkB;;AAE5D,oBAAY,aAAa;AACzB,YAAI,kBAAkB,YAAY,YAAY;AAC5C,sBAAY,WAAW;AAAA,QACzB;AACA,gBAAQ,IAAI,WAAW,EAAE,kBAAkB,aAAa,EAAE;AAC1D,YAAI,YAAY,YAAY,YAAY,UAAU,kBAAkB,uBAAuB,kBAAkB,eAAe;AAC1H,kBAAQ,IAAI,gBAAgB,IAAI,GAAG;AACnC,WAAAC,MAAA,KAAK,QAAL,gBAAAA,IAAU,YAAY,KAAK,eAAe;AAC1C;AAAA,QACF;AACA,oBAAY,KAAK,UAAU,EAAE,GAAG,OAAO,GAAG,OAAO,OAAO,MAAM,QAAQ,KAAA,CAAM;AAC5E,cAAM,aAAa,KAAK,iBAAA,EAAmB;AAC3C,cAAM,UAAU,MAAM,KAAK,KAAK,SAAS,OAAA,CAAQ,EAAE;AACnD,YAAI,UAAU,cAAc,GAAG;AAC7B,gBAAMC,SAAQ,MAAM,KAAK,KAAK,SAAS,KAAA,CAAM,EAAE,SAAS;AACxD,eAAK,cAAcA,OAAM,SAAA,GAAY,mBAAmB;AACxD,eAAK,eAAeA,SAAQ,GAAG,SAAA,GAAY,mBAAmB;AAC9D,eAAK,eAAeA,SAAQ,GAAG,SAAA,GAAY,mBAAmB;AAAA,QAChE;AAOA,YAAI,KAAK,OAAO,CAAC,KAAK,IAAI,eAAe;AACvC,eAAK,IAAI,YAAY,KAAK,qBAAqB,IAAI,aAAa;AAAA,QAClE;AAAA,MACF,CAAC;AAGD,WAAK,YAAY,qBAAqB,CAAC,EAAE,KAAAF,WAAU;AACjD,aAAK,YAAY,QAAQA,IAAG;AAE5B,eAAO,EAAE,QAAQ,OAAA;AAAA,MACnB,CAAC;AAED,WAAK,SAAS,IAAI,IAAI,WAAW;AAEjC,iBAAK,QAAL,mBAAU,YAAY,aAAa;AACnC,aAAO,EAAE,SAAS,MAAM,IAAI,QAAQ,KAAA;AAAA,IACtC,SAAS,OAAY;AACnB,cAAQ,MAAM,mCAAmC,EAAE,KAAK,KAAK;AAC7D,aAAO,EAAE,SAAS,OAAO,OAAO,MAAM,QAAA;AAAA,IACxC;AAAA,EACF;AAAA,EAGO,eAAe,IAAY,MAAY;AAC5C,QAAI;AACF,YAAM,cAAc,KAAK,SAAS,IAAI,EAAE;AACxC,UAAI,CAAC,aAAa;AAChB,eAAO,EAAE,SAAS,OAAO,OAAO,mBAAmB,EAAE,aAAA;AAAA,MACvD;AAEA,YAAM,EAAE,GAAG,GAAG,OAAO,WAAW;AAChC,UAAI,YAAY,YAAY,YAAY,QAAQ;AAC9C,oBAAY,KAAK,UAAU,EAAE,GAAG,GAAG,OAAO,KAAK,IAAI,OAAO,GAAG,GAAG,QAAQ,KAAK,IAAI,QAAQ,GAAG,GAAG;AAAA,MACjG,OAAO;AACL,YAAI,QAAQ,OAAO,EAAE;AACrB,oBAAY,KAAK,UAAU,EAAE,GAAG,QAAQ,QAAQ,KAAK,GAAG,QAAQ,QAAQ,KAAK,OAAO,KAAK,IAAI,OAAO,GAAG,GAAG,QAAQ,KAAK,IAAI,QAAQ,GAAG,EAAA,CAAG;AAAA,MAC3I;AAEA,aAAO,EAAE,SAAS,KAAA;AAAA,IACpB,SAAS,OAAY;AACnB,cAAQ,MAAM,kCAAkC,KAAK;AACrD,aAAO,EAAE,SAAS,OAAO,OAAO,MAAM,QAAA;AAAA,IACxC;AAAA,EACF;AAAA,EAGO,YAAY,IAAY;AAC7B,UAAM,cAAc,KAAK,SAAS,IAAI,EAAE;AACxC,QAAI,CAAC,aAAa;AAChB,aAAO,EAAE,SAAS,OAAO,OAAO,mBAAmB,EAAE,aAAA;AAAA,IACvD;AACA,QAAI,QAAQ,OAAO,EAAE;AACrB,gBAAY,KAAK,UAAU,EAAE,GAAG,QAAQ,QAAQ,KAAK,GAAG,QAAQ,QAAQ,KAAK,OAAO,KAAK,QAAQ,KAAK;AACtG,gBAAY,SAAS;AAErB,WAAO,EAAE,SAAS,KAAA;AAAA,EACpB;AAAA,EACO,iBAAiB;AACtB,SAAK,SAAS,QAAQ,CAAA,YAAW;AAC/B,UAAI,QAAQ,OAAO,QAAQ,EAAE;AAC7B,cAAQ,KAAK,UAAU,EAAE,GAAG,QAAQ,QAAQ,KAAK,GAAG,QAAQ,QAAQ,KAAK,OAAO,KAAK,QAAQ,KAAK;AAClG,cAAQ,SAAS;AAAA,IACnB,CAAC;AAAA,EACH;AAAA,EAEO,YAAY,IAAY;;AAC7B,UAAM,cAAc,KAAK,SAAS,IAAI,EAAE;AACxC,QAAI,CAAC,aAAa;AAChB,aAAO,EAAE,SAAS,OAAO,OAAO,mBAAmB,EAAE,aAAA;AAAA,IACvD;AACA,UAAM,aAAa,YAAY,KAAK,YAAY,OAAA;AAChD,eAAK,QAAL,mBAAU,YAAY,KAAK,eAAe;AAC1C,gBAAY,SAAS;AACrB,SAAK,eAAe,IAAI,KAAK,IAAI;AACjC,YAAQ,IAAI,eAAe,IAAI,KAAK,IAAI;AACxC,QAAI,KAAK,OAAO,CAAC,KAAK,IAAI,eAAe;AACvC,WAAK,IAAI,YAAY,KAAK,gBAAgB,EAAE;AAAA,IAC9C;AAEA,WAAO,EAAE,SAAS,KAAA;AAAA,EACpB;AAAA,EACO,iBAAiB;AACtB,WAAO,KAAK,MAAM,KAAK,UAAU,MAAM,KAAK,KAAK,SAAS,OAAA,CAAQ,EAAE,OAAO,CAAA,YAAW,QAAQ,MAAM,EAAE,IAAI,CAAA,YAAW,QAAQ,EAAE,CAAC,CAAC;AAAA,EACnI;AAAA,EAEO,eAAe,IAAY;;AAChC,QAAI;AACF,YAAM,cAAc,KAAK,SAAS,IAAI,EAAE;AACxC,UAAI,CAAC,aAAa;AAChB,eAAO,EAAE,SAAS,OAAO,OAAO,mBAAmB,EAAE,aAAA;AAAA,MACvD;AAGA,WAAI,UAAK,QAAL,mBAAU,aAAa;AACzB,aAAK,IAAI,YAAY,gBAAgB,YAAY,IAAI;AAAA,MACvD;AAGA,kBAAY,KAAK,YAAY,MAAA;AAG7B,WAAK,SAAS,OAAO,EAAE;AAEvB,cAAQ,IAAI,WAAW,EAAE,yBAAyB;AAClD,aAAO,EAAE,SAAS,KAAA;AAAA,IACpB,SAAS,OAAY;AACnB,cAAQ,MAAM,6BAA6B,EAAE,KAAK,KAAK;AACvD,aAAO,EAAE,SAAS,OAAO,OAAO,MAAM,QAAA;AAAA,IACxC;AAAA,EACF;AAAA,EAEO,UAAU;AAAA,EAEjB;AACF;AC9OO,MAAM,WAAW;AAAA,EAEvB,YAAYD,SAAuB;AAD3B,+BAA4B;AAEnC,SAAK,MAAMA;AAAA,EACZ;AAAA;AAAA;AAAA,EAKA,MAAc,UAAU,UAAmC;AAC1D,QAAI;AACH,YAAM,SAAS,MAAM,QAAQ,cAAc,EAAE,MAAM,UAAU;AAC7D,aAAO,OAAO;AAAA,IACf,SAAS,OAAO;AACf,cAAQ,MAAM,uBAAuB,KAAK;AAC1C,YAAM;AAAA,IACP;AAAA,EACD;AAAA,EAEA,MAAc,SAAS,UAAmC;AACzD,QAAI;AACH,YAAM,SAAS,MAAM,QAAQ,cAAc,EAAE,MAAM,UAAU;AAC7D,aAAO,OAAO;AAAA,IACf,SAAS,OAAO;AACf,cAAQ,MAAM,sBAAsB,KAAK;AACzC,YAAM;AAAA,IACP;AAAA,EACD;AAAA,EAEA,MAAc,UAAU,UAAmC;;AAC1D,QAAI;AACH,YAAM,YAAY,MAAM,SAAS,KAAK,KAAK,QAAQ;AACnD,YAAM,aAAa,UAAU,MAAM,OAAO,CAAC,MAAW,EAAE,KAAK,MAAM,8BAA8B,CAAC;AAElG,UAAI,OAAO;AAEX,eAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK;AAC3C,cAAMI,QAAO,WAAW,CAAC;AACzB,cAAM,gBAAgB,MAAMA,MAAK,OAAA;AACjC,cAAM,UAAU,cAAc,SAAS,OAAO;AAC9C,cAAM,SAAS,MAAM,mBAAmB,OAAO;AAE/C,gBAAQ,aAAa,IAAI,CAAC;AAE1B,cAAM,QAAQ,OAAO,OAAO,EAAE,QAAQ,EAAE,CAAC,EAAE,UAAU,EAAE,CAAC,EAAE,MAAM,KAAK,CAAA;AAErE,mBAAW,YAAY,OAAO;AAC7B,gBAAM,UAAQ,gDAAW,gBAAX,mBAAyB,OAAzB,mBAA8B,WAAU,CAAA;AACtD,qBAAW,QAAQ,OAAO;AACzB,kBAAM,QAAO,6BAAO,WAAU,CAAA;AAC9B,uBAAW,OAAO,MAAM;AACvB,oBAAM,QAAO,gCAAM,WAAN,mBAAe;AAC5B,kBAAI,MAAM;AACT,wBAAQ,OAAO,IAAI;AAAA,cACpB;AAAA,YACD;AAAA,UACD;AAAA,QACD;AAEA,gBAAQ;AAAA,MACT;AAEA,cAAQ;AACR,aAAO;AAAA,IACR,SAAS,OAAO;AACf,cAAQ,MAAM,2BAA2B,KAAK;AAC9C,YAAM;AAAA,IACP;AAAA,EACD;AAAA,EAEA,MAAc,SAAS,UAAmC;AACzD,QAAI;AACH,YAAM,cAAcN,KAAG,aAAa,UAAU,OAAO;AACrD,YAAM,SAAS,KAAK,MAAM,aAAa;AAAA,QACtC,QAAQ;AAAA,QACR,gBAAgB;AAAA,QAChB,WAAW;AAAA,MAAA,CACX;AAGD,UAAI,OAAO,QAAQ,OAAO,KAAK,SAAS,GAAG;AAC1C,cAAM,UAAU,OAAO,KAAK,OAAO,KAAK,CAAC,CAAa;AACtD,YAAI,OAAO;AAGX,gBAAQ;AACR,gBAAQ,QAAQ,CAAA,WAAU;AACzB,kBAAQ,uEAAuE,MAAM;AAAA,QACtF,CAAC;AACD,gBAAQ;AAGR,gBAAQ;AACR,eAAO,KAAK,QAAQ,CAAC,QAAa;AACjC,kBAAQ;AACR,kBAAQ,QAAQ,CAAA,WAAU;AACzB,oBAAQ,qDAAqD,IAAI,MAAM,KAAK,EAAE;AAAA,UAC/E,CAAC;AACD,kBAAQ;AAAA,QACT,CAAC;AACD,gBAAQ;AAER,eAAO;AAAA,MACR;AACA,aAAO;AAAA,IACR,SAAS,OAAO;AACf,cAAQ,MAAM,sBAAsB,KAAK;AACzC,YAAM;AAAA,IACP;AAAA,EACD;AAAA,EAEO,SAAS,MAAc,UAAkB,kBAA2B;AAC1E,WAAO,IAAI,QAAQ,OAAO,SAAS,WAAW;AAC7C,UAAI;AACH,YAAI,SAAS,MAAM;AAClB,gBAAM,UAAUA,KAAG,aAAa,UAAU,OAAO;AACjD,kBAAQ,OAAO;AAAA,QAChB,WAAW,oBAAoB,SAAS,QAAQ;AAC/C,gBAAM,UAAUA,KAAG,aAAa,UAAU,OAAO;AACjD,kBAAQ,OAAO;AAAA,QAChB,WAAW,CAAC,OAAO,MAAM,EAAE,SAAS,IAAI,GAAG;AAC1C,kBAAQ,QAAQ;AAAA,QACjB,WAAW,SAAS,OAAO;AAC1B,cAAI;AACH,kBAAM,cAAc,MAAM,KAAK,SAAS,QAAQ;AAChD,oBAAQ,WAAW;AAAA,UACpB,SAAS,OAAO;AACf,oBAAQ,KAAK,wCAAwC,KAAK;AAC1D,kBAAM,UAAUA,KAAG,aAAa,UAAU,OAAO;AACjD,oBAAQ,OAAO;AAAA,UAChB;AAAA,QACD,WAAW,SAAS,QAAQ;AAC3B,cAAI;AACH,kBAAM,cAAc,MAAM,KAAK,UAAU,QAAQ;AACjD,oBAAQ,WAAW;AAAA,UACpB,SAAS,OAAO;AACf,oBAAQ,KAAK,yCAAyC,KAAK;AAC3D,kBAAM,UAAUA,KAAG,aAAa,UAAU,OAAO;AACjD,oBAAQ,OAAO;AAAA,UAChB;AAAA,QACD,WAAW,SAAS,OAAO;AAC1B,cAAI;AACH,kBAAM,cAAc,MAAM,KAAK,SAAS,QAAQ;AAChD,oBAAQ,WAAW;AAAA,UACpB,SAAS,OAAO;AACf,oBAAQ,KAAK,wCAAwC,KAAK;AAC1D,kBAAM,UAAUA,KAAG,aAAa,UAAU,OAAO;AACjD,oBAAQ,OAAO;AAAA,UAChB;AAAA,QACD,WAAW,SAAS,QAAQ;AAC3B,cAAI;AACH,kBAAM,cAAc,MAAM,KAAK,UAAU,QAAQ;AACjD,oBAAQ,WAAW;AAAA,UACpB,SAAS,OAAO;AACf,oBAAQ,KAAK,kDAAkD,KAAK;AACpE,kBAAM,UAAUA,KAAG,aAAa,UAAU,QAAQ;AAClD,oBAAQ,QAAQ,OAAO,QAAQ;AAAA,UAChC;AAAA,QACD,OAAO;AACN,gBAAM,UAAUA,KAAG,aAAa,UAAU,OAAO;AACjD,kBAAQ,OAAO;AAAA,QAChB;AAAA,MACD,SAAS,OAAO;AACf,eAAO,KAAK;AAAA,MACb;AAAA,IACD,CAAC;AAAA,EACF;AAAA,EAEO,YAAY,OAAe,QAA4B;AAE7D,UAAM,YAAY,MAAM,MAAM,GAAG,EAAE,CAAC,EAAE,QAAQ,mBAAmB,GAAG,EAAE,QAAQ,cAAc,EAAE;AAE9F,UAAM,WAAW,IAAI,QAAQ,MAAM;AACnC,UAAM,UAAUD,OAAK,KAAK,UAAU,UAAU,WAAW,QAAQ,MAAM,EAAE;AAEzE,QAAI;AACH,UAAI,CAACC,KAAG,WAAW,OAAO,GAAG;AAC5B,eAAO,CAAA;AAAA,MACR;AACA,YAAM,QAAQA,KAAG,YAAY,OAAO;AAEpC,aAAO,MAAM,OAAO,CAAAM,UAAM,CAACA,MAAK,WAAW,GAAG,CAAC,EAAE,IAAI,CAAAA,UAAQ;;AAC5D,eAAO;AAAA,UACN,MAAMP,OAAK,KAAK,SAASO,KAAI;AAAA,UAC7B,MAAMA;AAAA,UACN,QAAM,KAAAA,MAAK,MAAM,GAAG,EAAE,IAAA,MAAhB,mBAAuB,kBAAiB;AAAA,QAAA;AAAA,MAEhD,CAAC;AAAA,IACF,SAAS,KAAK;AACb,cAAQ,MAAM,qBAAqB,GAAG;AACtC,aAAO,CAAA;AAAA,IACR;AAAA,EACD;AACD;ACtMA,MAAM,iBAAiBP,OAAK,KAAK,GAAG,QAAA,GAAW,SAAS;AACxD,MAAM,kBAAkBA,OAAK,KAAK,gBAAgB,UAAU;AAsB5D,SAAS,mBAA+B;AACtC,SAAO,EAAE,YAAY,GAAC;AACxB;AAEO,SAAS,gBAA4B;AAC1C,MAAI;AACF,QAAI,CAACC,KAAG,WAAW,eAAe,GAAG;AAEnC,qBAAe,kBAAkB;AACjC,aAAO,iBAAA;AAAA,IACT;AACA,UAAM,OAAOA,KAAG,aAAa,iBAAiB,OAAO;AACrD,UAAM,SAAS,KAAK,MAAM,IAAI;AAE9B,QAAI,CAAC,OAAO,cAAc,OAAO,OAAO,eAAe,UAAU;AAC/D,aAAO,iBAAA;AAAA,IACT;AACA,WAAO;AAAA,EACT,SAAS,GAAG;AACV,WAAO,iBAAA;AAAA,EACT;AACF;AAEO,SAAS,eAAe,QAA0B;AACvD,MAAI,CAACA,KAAG,WAAW,cAAc,GAAG;AAClCA,SAAG,UAAU,gBAAgB,EAAE,WAAW,MAAM;AAAA,EAClD;AACAA,OAAG,cAAc,iBAAiB,KAAK,UAAU,QAAQ,MAAM,CAAC,GAAG,OAAO;AAC5E;AAEO,SAAS,OAAO,MAAc,KAA4B;AAC/D,QAAM,SAAS,cAAA;AACf,MAAI,CAAC,OAAO,WAAW,IAAI,GAAG;AAC5B,WAAO,WAAW,IAAI,IAAI;AAC1B,mBAAe,MAAM;AAAA,EACvB;AACF;AAEO,SAAS,UAAU,MAAoB;AAC5C,QAAM,SAAS,cAAA;AACf,UAAQ,IAAI,aAAa,IAAI;AAC7B,MAAI,OAAO,WAAW,IAAI,GAAG;AAC3B,WAAO,OAAO,WAAW,IAAI;AAC7B,mBAAe,MAAM;AAAA,EACvB;AACF;AAEO,SAAS,UAAU,MAAc,KAA4B;AAClE,QAAM,SAAS,cAAA;AACf,SAAO,WAAW,IAAI,IAAI;AAC1B,iBAAe,MAAM;AACvB;AC1EO,MAAM,YAAY;AAClB,MAAM,UAAU;AAEhB,SAAS,WAAW,OAAe;AACxC,QAAM,YAAY,MAAM,MAAM,GAAG,EAAE,CAAC,EAAE,QAAQ,mBAAmB,GAAG,EAAE,QAAQ,KAAK,GAAG;AACtF,QAAM,UAAUD,OAAK,KAAK,GAAG,WAAW,WAAW,UAAU,SAAS;AACtE,QAAM,aAAaA,OAAK,KAAK,QAAQ,eAAe,WAAW,MAAM;AACrE,MAAI,CAACC,KAAG,WAAW,OAAO,KAAKA,KAAG,WAAW,UAAU,GAAG;AACxDA,SAAG,aAAa,YAAY,OAAO;AACnCA,SAAG,UAAU,SAAS,GAAK;AAAA,EAC7B;AAEA,SAAO;AACT;AAWO,SAAS,eAAe,OAAiB,IAA4B;AAE1E,MAAI,QAAQ,MAAM,UAAU,OAAK,EAAE,KAAA,MAAW,SAAS;AACvD,MAAI,MAAM,MAAM,UAAU,OAAK,EAAE,KAAA,MAAW,OAAO;AACnD,MAAI,UAAU,MAAM,QAAQ,MAAM,MAAM,OAAO;AAE7C,UAAM,KAAK,SAAS;AACpB,WAAO,QAAQ,EAAE,EAAE,QAAQ,CAAC,CAAC,GAAG,CAAC,MAAM;AACrC,YAAM,KAAK,GAAG,CAAC,IAAI,CAAC,EAAE;AAAA,IACxB,CAAC;AACD,UAAM,KAAK,OAAO;AAClB,WAAO;AAAA,EACT;AAEA,QAAM,QAAQ,MAAM,MAAM,QAAQ,GAAG,GAAG;AACxC,QAAM,MAA8B,CAAA;AACpC,QAAM,QAAQ,CAAA,SAAQ;AACpB,UAAM,IAAI,KAAK,MAAM,qBAAqB;AAC1C,QAAI,EAAG,KAAI,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;AAAA,EACxB,CAAC;AAED,SAAO,QAAQ,EAAE,EAAE,QAAQ,CAAC,CAAC,GAAG,CAAC,MAAM;AACrC,QAAI,CAAC,IAAI;AAAA,EACX,CAAC;AAED,QAAM,WAAW,OAAO,QAAQ,GAAG,EAAE,IAAI,CAAC,CAAC,GAAG,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,EAAE;AAChE,SAAO;AAAA,IACL,GAAG,MAAM,MAAM,GAAG,QAAQ,CAAC;AAAA,IAC3B,GAAG;AAAA,IACH,GAAG,MAAM,MAAM,GAAG;AAAA,EAAA;AAEtB;AAEO,SAAS,aAAa,OAAiB,KAAa;AACzD,MAAI,QAAQ,MAAM,UAAU,OAAK,EAAE,KAAA,MAAW,SAAS;AACvD,MAAI,MAAM,MAAM,UAAU,OAAK,EAAE,KAAA,MAAW,OAAO;AACnD,MAAI,UAAU,MAAM,QAAQ,MAAM,MAAM,MAAO,QAAO;AACtD,QAAM,QAAQ,MAAM,MAAM,QAAQ,GAAG,GAAG;AACxC,QAAM,WAAW,MAAM,OAAO,CAAA,SAAQ,CAAC,KAAK,WAAW,MAAM,GAAG,CAAC;AACjE,SAAO;AAAA,IACL,GAAG,MAAM,MAAM,GAAG,QAAQ,CAAC;AAAA,IAC3B,GAAG;AAAA,IACH,GAAG,MAAM,MAAM,GAAG;AAAA,EAAA;AAEtB;;;;;;;;ACtEAO,eAAA,eAAuB,SAAU,IAAI;AACnC,SAAO,OAAO,eAAe,YAAa,MAAM;AAC9C,QAAI,OAAO,KAAK,KAAK,SAAS,CAAC,MAAM,WAAY,IAAG,MAAM,MAAM,IAAI;AAAA,SAC/D;AACH,aAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,aAAK,KAAK,CAAC,KAAK,QAAS,OAAO,OAAQ,OAAO,GAAG,IAAI,QAAQ,GAAG,CAAC;AAClE,WAAG,MAAM,MAAM,IAAI;AAAA,MAC3B,CAAO;AAAA,IACP;AAAA,EACA,GAAK,QAAQ,EAAE,OAAO,GAAG,KAAI,CAAE;AAC/B;AAEAA,eAAA,cAAsB,SAAU,IAAI;AAClC,SAAO,OAAO,eAAe,YAAa,MAAM;AAC9C,UAAM,KAAK,KAAK,KAAK,SAAS,CAAC;AAC/B,QAAI,OAAO,OAAO,WAAY,QAAO,GAAG,MAAM,MAAM,IAAI;AAAA,SACnD;AACH,WAAK,IAAG;AACR,SAAG,MAAM,MAAM,IAAI,EAAE,KAAK,OAAK,GAAG,MAAM,CAAC,GAAG,EAAE;AAAA,IACpD;AAAA,EACA,GAAK,QAAQ,EAAE,OAAO,GAAG,KAAI,CAAE;AAC/B;ACvBA,IAAI,YAAY;AAEhB,IAAI,UAAU,QAAQ;AACtB,IAAI,MAAM;AAEV,IAAI,WAAW,QAAA,IAAY,wBAAwB,QAAQ;AAE3D,QAAQ,MAAM,WAAW;AACvB,MAAI,CAAC;AACH,UAAM,QAAQ,KAAK,OAAO;AAC5B,SAAO;AACT;AACA,IAAI;AACF,UAAQ,IAAA;AACV,SAAS,IAAI;AAAC;AAGd,IAAI,OAAO,QAAQ,UAAU,YAAY;AACvC,MAAI,QAAQ,QAAQ;AACpB,UAAQ,QAAQ,SAAU,GAAG;AAC3B,UAAM;AACN,UAAM,KAAK,SAAS,CAAC;AAAA,EAAA;AAEvB,MAAI,OAAO,eAAgB,QAAO,eAAe,QAAQ,OAAO,KAAK;AACvE;IAEAC,cAAiBC;AAEjB,SAASA,QAAOT,KAAI;AAKlB,MAAI,UAAU,eAAe,WAAW,KACpC,QAAQ,QAAQ,MAAM,wBAAwB,GAAG;AACnD,gBAAYA,GAAE;AAAA,EAAA;AAIhB,MAAI,CAACA,IAAG,SAAS;AACf,iBAAaA,GAAE;AAAA,EAAA;AAQjB,EAAAA,IAAG,QAAQ,SAASA,IAAG,KAAK;AAC5B,EAAAA,IAAG,SAAS,SAASA,IAAG,MAAM;AAC9B,EAAAA,IAAG,SAAS,SAASA,IAAG,MAAM;AAE9B,EAAAA,IAAG,QAAQ,SAASA,IAAG,KAAK;AAC5B,EAAAA,IAAG,SAAS,SAASA,IAAG,MAAM;AAC9B,EAAAA,IAAG,SAAS,SAASA,IAAG,MAAM;AAE9B,EAAAA,IAAG,YAAY,aAAaA,IAAG,SAAS;AACxC,EAAAA,IAAG,aAAa,aAAaA,IAAG,UAAU;AAC1C,EAAAA,IAAG,aAAa,aAAaA,IAAG,UAAU;AAE1C,EAAAA,IAAG,YAAY,aAAaA,IAAG,SAAS;AACxC,EAAAA,IAAG,aAAa,aAAaA,IAAG,UAAU;AAC1C,EAAAA,IAAG,aAAa,aAAaA,IAAG,UAAU;AAE1C,EAAAA,IAAG,OAAO,QAAQA,IAAG,IAAI;AACzB,EAAAA,IAAG,QAAQ,QAAQA,IAAG,KAAK;AAC3B,EAAAA,IAAG,QAAQ,QAAQA,IAAG,KAAK;AAE3B,EAAAA,IAAG,WAAW,YAAYA,IAAG,QAAQ;AACrC,EAAAA,IAAG,YAAY,YAAYA,IAAG,SAAS;AACvC,EAAAA,IAAG,YAAY,YAAYA,IAAG,SAAS;AAGvC,MAAIA,IAAG,SAAS,CAACA,IAAG,QAAQ;AAC1B,IAAAA,IAAG,SAAS,SAAUD,OAAM,MAAM,IAAI;AACpC,UAAI,GAAI,SAAQ,SAAS,EAAE;AAAA,IAAA;AAE7B,IAAAC,IAAG,aAAa,WAAY;AAAA,IAAA;AAAA,EAAC;AAE/B,MAAIA,IAAG,SAAS,CAACA,IAAG,QAAQ;AAC1B,IAAAA,IAAG,SAAS,SAAUD,OAAM,KAAK,KAAK,IAAI;AACxC,UAAI,GAAI,SAAQ,SAAS,EAAE;AAAA,IAAA;AAE7B,IAAAC,IAAG,aAAa,WAAY;AAAA,IAAA;AAAA,EAAC;AAY/B,MAAI,aAAa,SAAS;AACxB,IAAAA,IAAG,SAAS,OAAOA,IAAG,WAAW,aAAaA,IAAG,SAC9C,SAAU,WAAW;AACtB,eAASU,QAAQ,MAAM,IAAI,IAAI;AAC7B,YAAI,QAAQ,KAAK,IAAA;AACjB,YAAI,UAAU;AACd,kBAAU,MAAM,IAAI,SAAS,GAAI,IAAI;AACnC,cAAI,OACI,GAAG,SAAS,YAAY,GAAG,SAAS,WAAW,GAAG,SAAS,YAC5D,KAAK,IAAA,IAAQ,QAAQ,KAAO;AACjC,uBAAW,WAAW;AACpB,cAAAV,IAAG,KAAK,IAAI,SAAU,QAAQ,IAAI;AAChC,oBAAI,UAAU,OAAO,SAAS;AAC5B,4BAAU,MAAM,IAAI,EAAE;AAAA;AAEtB,qBAAG,EAAE;AAAA,cAAA,CACR;AAAA,YAAA,GACA,OAAO;AACV,gBAAI,UAAU;AACZ,yBAAW;AACb;AAAA,UAAA;AAEF,cAAI,OAAO,EAAE;AAAA,QAAA,CACd;AAAA,MAAA;AAEH,UAAI,OAAO,eAAgB,QAAO,eAAeU,SAAQ,SAAS;AAClE,aAAOA;AAAA,IAAA,EACNV,IAAG,MAAM;AAAA,EAAA;AAId,EAAAA,IAAG,OAAO,OAAOA,IAAG,SAAS,aAAaA,IAAG,OAC1C,SAAU,SAAS;AACpB,aAAS,KAAM,IAAI,QAAQ,QAAQ,QAAQ,UAAU,WAAW;AAC9D,UAAI;AACJ,UAAI,aAAa,OAAO,cAAc,YAAY;AAChD,YAAI,aAAa;AACjB,mBAAW,SAAU,IAAI,GAAG,IAAI;AAC9B,cAAI,MAAM,GAAG,SAAS,YAAY,aAAa,IAAI;AACjD;AACA,mBAAO,QAAQ,KAAKA,KAAI,IAAI,QAAQ,QAAQ,QAAQ,UAAU,QAAQ;AAAA,UAAA;AAExE,oBAAU,MAAM,MAAM,SAAS;AAAA,QAAA;AAAA,MACjC;AAEF,aAAO,QAAQ,KAAKA,KAAI,IAAI,QAAQ,QAAQ,QAAQ,UAAU,QAAQ;AAAA,IAAA;AAIxE,QAAI,OAAO,eAAgB,QAAO,eAAe,MAAM,OAAO;AAC9D,WAAO;AAAA,EAAA,EACNA,IAAG,IAAI;AAEV,EAAAA,IAAG,WAAW,OAAOA,IAAG,aAAa,aAAaA,IAAG,oCACxC,aAAa;AAAE,WAAO,SAAU,IAAI,QAAQ,QAAQ,QAAQ,UAAU;AACjF,UAAI,aAAa;AACjB,aAAO,MAAM;AACX,YAAI;AACF,iBAAO,YAAY,KAAKA,KAAI,IAAI,QAAQ,QAAQ,QAAQ,QAAQ;AAAA,QAAA,SACzD,IAAI;AACX,cAAI,GAAG,SAAS,YAAY,aAAa,IAAI;AAC3C;AACA;AAAA,UAAA;AAEF,gBAAM;AAAA,QAAA;AAAA,MACR;AAAA,IACF;AAAA,EACF,EAAIA,IAAG,QAAQ;AAEf,WAAS,YAAaA,MAAI;AACxBA,IAAAA,KAAG,SAAS,SAAUD,OAAM,MAAM,UAAU;AAC1CC,MAAAA,KAAG;AAAA,QAAMD;AAAA,QACA,UAAU,WAAW,UAAU;AAAA,QAC/B;AAAA,QACA,SAAU,KAAK,IAAI;AAC1B,cAAI,KAAK;AACP,gBAAI,mBAAmB,GAAG;AAC1B;AAAA,UAAA;AAIFC,UAAAA,KAAG,OAAO,IAAI,MAAM,SAAUW,MAAK;AACjCX,YAAAA,KAAG,MAAM,IAAI,SAASY,OAAM;AAC1B,kBAAI,SAAU,UAASD,QAAOC,KAAI;AAAA,YAAA,CACnC;AAAA,UAAA,CACF;AAAA,QAAA;AAAA,MACH;AAAA,IAAC;AAGHZ,IAAAA,KAAG,aAAa,SAAUD,OAAM,MAAM;AACpC,UAAI,KAAKC,KAAG,SAASD,OAAM,UAAU,WAAW,UAAU,WAAW,IAAI;AAIzE,UAAI,QAAQ;AACZ,UAAI;AACJ,UAAI;AACF,cAAMC,KAAG,WAAW,IAAI,IAAI;AAC5B,gBAAQ;AAAA,MAAA,UACV;AACE,YAAI,OAAO;AACT,cAAI;AACFA,YAAAA,KAAG,UAAU,EAAE;AAAA,UAAA,SACR,IAAI;AAAA,UAAA;AAAA,QAAC,OACT;AACLA,UAAAA,KAAG,UAAU,EAAE;AAAA,QAAA;AAAA,MACjB;AAEF,aAAO;AAAA,IAAA;AAAA,EACT;AAGF,WAAS,aAAcA,MAAI;AACzB,QAAI,UAAU,eAAe,WAAW,KAAKA,KAAG,SAAS;AACvDA,MAAAA,KAAG,UAAU,SAAUD,OAAM,IAAI,IAAI,IAAI;AACvCC,QAAAA,KAAG,KAAKD,OAAM,UAAU,WAAW,SAAU,IAAI,IAAI;AACnD,cAAI,IAAI;AACN,gBAAI,OAAO,EAAE;AACb;AAAA,UAAA;AAEFC,UAAAA,KAAG,QAAQ,IAAI,IAAI,IAAI,SAAUa,KAAI;AACnCb,YAAAA,KAAG,MAAM,IAAI,SAAUc,MAAK;AAC1B,kBAAI,GAAI,IAAGD,OAAMC,IAAG;AAAA,YAAA,CACrB;AAAA,UAAA,CACF;AAAA,QAAA,CACF;AAAA,MAAA;AAGHd,MAAAA,KAAG,cAAc,SAAUD,OAAM,IAAI,IAAI;AACvC,YAAI,KAAKC,KAAG,SAASD,OAAM,UAAU,SAAS;AAC9C,YAAI;AACJ,YAAI,QAAQ;AACZ,YAAI;AACF,gBAAMC,KAAG,YAAY,IAAI,IAAI,EAAE;AAC/B,kBAAQ;AAAA,QAAA,UACV;AACE,cAAI,OAAO;AACT,gBAAI;AACFA,cAAAA,KAAG,UAAU,EAAE;AAAA,YAAA,SACR,IAAI;AAAA,YAAA;AAAA,UAAC,OACT;AACLA,YAAAA,KAAG,UAAU,EAAE;AAAA,UAAA;AAAA,QACjB;AAEF,eAAO;AAAA,MAAA;AAAA,IACT,WAESA,KAAG,SAAS;AACrBA,MAAAA,KAAG,UAAU,SAAU,IAAI,IAAI,IAAI,IAAI;AAAE,YAAI,GAAI,SAAQ,SAAS,EAAE;AAAA,MAAA;AACpEA,MAAAA,KAAG,cAAc,WAAY;AAAA,MAAA;AAAA,IAAC;AAAA,EAChC;AAGF,WAAS,SAAU,MAAM;AACvB,QAAI,CAAC,KAAM,QAAO;AAClB,WAAO,SAAU,QAAQ,MAAM,IAAI;AACjC,aAAO,KAAK,KAAKA,KAAI,QAAQ,MAAM,SAAU,IAAI;AAC/C,YAAI,UAAU,EAAE,EAAG,MAAK;AACxB,YAAI,GAAI,IAAG,MAAM,MAAM,SAAS;AAAA,MAAA,CACjC;AAAA,IAAA;AAAA,EACH;AAGF,WAAS,aAAc,MAAM;AAC3B,QAAI,CAAC,KAAM,QAAO;AAClB,WAAO,SAAU,QAAQ,MAAM;AAC7B,UAAI;AACF,eAAO,KAAK,KAAKA,KAAI,QAAQ,IAAI;AAAA,MAAA,SAC1B,IAAI;AACX,YAAI,CAAC,UAAU,EAAE,EAAG,OAAM;AAAA,MAAA;AAAA,IAC5B;AAAA,EACF;AAIF,WAAS,SAAU,MAAM;AACvB,QAAI,CAAC,KAAM,QAAO;AAClB,WAAO,SAAU,QAAQ,KAAK,KAAK,IAAI;AACrC,aAAO,KAAK,KAAKA,KAAI,QAAQ,KAAK,KAAK,SAAU,IAAI;AACnD,YAAI,UAAU,EAAE,EAAG,MAAK;AACxB,YAAI,GAAI,IAAG,MAAM,MAAM,SAAS;AAAA,MAAA,CACjC;AAAA,IAAA;AAAA,EACH;AAGF,WAAS,aAAc,MAAM;AAC3B,QAAI,CAAC,KAAM,QAAO;AAClB,WAAO,SAAU,QAAQ,KAAK,KAAK;AACjC,UAAI;AACF,eAAO,KAAK,KAAKA,KAAI,QAAQ,KAAK,GAAG;AAAA,MAAA,SAC9B,IAAI;AACX,YAAI,CAAC,UAAU,EAAE,EAAG,OAAM;AAAA,MAAA;AAAA,IAC5B;AAAA,EACF;AAGF,WAAS,QAAS,MAAM;AACtB,QAAI,CAAC,KAAM,QAAO;AAGlB,WAAO,SAAU,QAAQ,SAAS,IAAI;AACpC,UAAI,OAAO,YAAY,YAAY;AACjC,aAAK;AACL,kBAAU;AAAA,MAAA;AAEZ,eAAS,SAAU,IAAI,OAAO;AAC5B,YAAI,OAAO;AACT,cAAI,MAAM,MAAM,EAAG,OAAM,OAAO;AAChC,cAAI,MAAM,MAAM,EAAG,OAAM,OAAO;AAAA,QAAA;AAElC,YAAI,GAAI,IAAG,MAAM,MAAM,SAAS;AAAA,MAAA;AAElC,aAAO,UAAU,KAAK,KAAKA,KAAI,QAAQ,SAAS,QAAQ,IACpD,KAAK,KAAKA,KAAI,QAAQ,QAAQ;AAAA,IAAA;AAAA,EACpC;AAGF,WAAS,YAAa,MAAM;AAC1B,QAAI,CAAC,KAAM,QAAO;AAGlB,WAAO,SAAU,QAAQ,SAAS;AAChC,UAAI,QAAQ,UAAU,KAAK,KAAKA,KAAI,QAAQ,OAAO,IAC/C,KAAK,KAAKA,KAAI,MAAM;AACxB,UAAI,OAAO;AACT,YAAI,MAAM,MAAM,EAAG,OAAM,OAAO;AAChC,YAAI,MAAM,MAAM,EAAG,OAAM,OAAO;AAAA,MAAA;AAElC,aAAO;AAAA,IAAA;AAAA,EACT;AAeF,WAAS,UAAW,IAAI;AACtB,QAAI,CAAC;AACH,aAAO;AAET,QAAI,GAAG,SAAS;AACd,aAAO;AAET,QAAI,UAAU,CAAC,QAAQ,UAAU,QAAQ,aAAa;AACtD,QAAI,SAAS;AACX,UAAI,GAAG,SAAS,YAAY,GAAG,SAAS;AACtC,eAAO;AAAA,IAAA;AAGX,WAAO;AAAA,EAAA;AAEX;AClWA,IAAI,SAASe,aAAkB;AAE/B,IAAA,gBAAiBC;AAEjB,SAASA,SAAQhB,KAAI;AACnB,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACJ;AAEE,WAAS,WAAYD,OAAM,SAAS;AAClC,QAAI,EAAE,gBAAgB,YAAa,QAAO,IAAI,WAAWA,OAAM,OAAO;AAEtE,WAAO,KAAK,IAAI;AAEhB,QAAIkB,QAAO;AAEX,SAAK,OAAOlB;AACZ,SAAK,KAAK;AACV,SAAK,WAAW;AAChB,SAAK,SAAS;AAEd,SAAK,QAAQ;AACb,SAAK,OAAO;AACZ,SAAK,aAAa,KAAK;AAEvB,cAAU,WAAW,CAAA;AAGrB,QAAI,OAAO,OAAO,KAAK,OAAO;AAC9B,aAAS,QAAQ,GAAG,SAAS,KAAK,QAAQ,QAAQ,QAAQ,SAAS;AACjE,UAAI,MAAM,KAAK,KAAK;AACpB,WAAK,GAAG,IAAI,QAAQ,GAAG;AAAA,IAC7B;AAEI,QAAI,KAAK,SAAU,MAAK,YAAY,KAAK,QAAQ;AAEjD,QAAI,KAAK,UAAU,QAAW;AAC5B,UAAI,aAAa,OAAO,KAAK,OAAO;AAClC,cAAM,UAAU,wBAAwB;AAAA,MAChD;AACM,UAAI,KAAK,QAAQ,QAAW;AAC1B,aAAK,MAAM;AAAA,MACnB,WAAiB,aAAa,OAAO,KAAK,KAAK;AACvC,cAAM,UAAU,sBAAsB;AAAA,MAC9C;AAEM,UAAI,KAAK,QAAQ,KAAK,KAAK;AACzB,cAAM,IAAI,MAAM,sBAAsB;AAAA,MAC9C;AAEM,WAAK,MAAM,KAAK;AAAA,IACtB;AAEI,QAAI,KAAK,OAAO,MAAM;AACpB,cAAQ,SAAS,WAAW;AAC1B,QAAAkB,MAAK,MAAK;AAAA,MAClB,CAAO;AACD;AAAA,IACN;AAEI,IAAAjB,IAAG,KAAK,KAAK,MAAM,KAAK,OAAO,KAAK,MAAM,SAAU,KAAK,IAAI;AAC3D,UAAI,KAAK;AACP,QAAAiB,MAAK,KAAK,SAAS,GAAG;AACtB,QAAAA,MAAK,WAAW;AAChB;AAAA,MACR;AAEM,MAAAA,MAAK,KAAK;AACV,MAAAA,MAAK,KAAK,QAAQ,EAAE;AACpB,MAAAA,MAAK,MAAK;AAAA,IAChB,CAAK;AAAA,EACL;AAEE,WAAS,YAAalB,OAAM,SAAS;AACnC,QAAI,EAAE,gBAAgB,aAAc,QAAO,IAAI,YAAYA,OAAM,OAAO;AAExE,WAAO,KAAK,IAAI;AAEhB,SAAK,OAAOA;AACZ,SAAK,KAAK;AACV,SAAK,WAAW;AAEhB,SAAK,QAAQ;AACb,SAAK,WAAW;AAChB,SAAK,OAAO;AACZ,SAAK,eAAe;AAEpB,cAAU,WAAW,CAAA;AAGrB,QAAI,OAAO,OAAO,KAAK,OAAO;AAC9B,aAAS,QAAQ,GAAG,SAAS,KAAK,QAAQ,QAAQ,QAAQ,SAAS;AACjE,UAAI,MAAM,KAAK,KAAK;AACpB,WAAK,GAAG,IAAI,QAAQ,GAAG;AAAA,IAC7B;AAEI,QAAI,KAAK,UAAU,QAAW;AAC5B,UAAI,aAAa,OAAO,KAAK,OAAO;AAClC,cAAM,UAAU,wBAAwB;AAAA,MAChD;AACM,UAAI,KAAK,QAAQ,GAAG;AAClB,cAAM,IAAI,MAAM,uBAAuB;AAAA,MAC/C;AAEM,WAAK,MAAM,KAAK;AAAA,IACtB;AAEI,SAAK,OAAO;AACZ,SAAK,SAAS,CAAA;AAEd,QAAI,KAAK,OAAO,MAAM;AACpB,WAAK,QAAQC,IAAG;AAChB,WAAK,OAAO,KAAK,CAAC,KAAK,OAAO,KAAK,MAAM,KAAK,OAAO,KAAK,MAAM,MAAS,CAAC;AAC1E,WAAK,MAAK;AAAA,IAChB;AAAA,EACA;AACA;ACnHA,IAAA,UAAiBkB;AAEjB,IAAI,iBAAiB,OAAO,kBAAkB,SAAU,KAAK;AAC3D,SAAO,IAAI;AACb;AAEA,SAASA,QAAO,KAAK;AACnB,MAAI,QAAQ,QAAQ,OAAO,QAAQ;AACjC,WAAO;AAET,MAAI,eAAe;AACjB,QAAIC,QAAO,EAAE,WAAW,eAAe,GAAG,EAAC;AAAA;AAE3C,QAAIA,QAAO,uBAAO,OAAO,IAAI;AAE/B,SAAO,oBAAoB,GAAG,EAAE,QAAQ,SAAU,KAAK;AACrD,WAAO,eAAeA,OAAM,KAAK,OAAO,yBAAyB,KAAK,GAAG,CAAC;AAAA,EAC9E,CAAG;AAED,SAAOA;AACT;ACtBA,IAAInB,OAAKe;AACT,IAAI,YAAYK;AAChB,IAAI,SAASC;AACb,IAAI,QAAQC;AAEZ,IAAIC,SAAO;AAGX,IAAI;AACJ,IAAI;AAGJ,IAAI,OAAO,WAAW,cAAc,OAAO,OAAO,QAAQ,YAAY;AACpE,kBAAgB,OAAO,IAAI,mBAAmB;AAE9C,mBAAiB,OAAO,IAAI,sBAAsB;AACpD,OAAO;AACL,kBAAgB;AAChB,mBAAiB;AACnB;AAEA,SAAS,OAAQ;AAAC;AAElB,SAAS,aAAa,SAASC,QAAO;AACpC,SAAO,eAAe,SAAS,eAAe;AAAA,IAC5C,KAAK,WAAW;AACd,aAAOA;AAAAA,IAAA;AAAA,EACT,CACD;AACH;AAEA,IAAI,QAAQ;AACZ,IAAID,OAAK;AACP,UAAQA,OAAK,SAAS,MAAM;AAAA,SACrB,YAAY,KAAK,QAAA,IAAY,cAAc,EAAE;AACpD,UAAQ,WAAW;AACjB,QAAI,IAAIA,OAAK,OAAO,MAAMA,QAAM,SAAS;AACzC,QAAI,WAAW,EAAE,MAAM,IAAI,EAAE,KAAK,UAAU;AAC5C,YAAQ,MAAM,CAAC;AAAA,EAAA;AAInB,IAAI,CAACvB,KAAG,aAAa,GAAG;AAEtB,MAAI,QAAQyB,eAAO,aAAa,KAAK,CAAA;AACrC,eAAazB,MAAI,KAAK;AAMtBA,OAAG,QAAS,SAAU,UAAU;AAC9B,aAAS,MAAO,IAAI,IAAI;AACtB,aAAO,SAAS,KAAKA,MAAI,IAAI,SAAU,KAAK;AAE1C,YAAI,CAAC,KAAK;AACR,qBAAA;AAAA,QAAW;AAGb,YAAI,OAAO,OAAO;AAChB,aAAG,MAAM,MAAM,SAAS;AAAA,MAAA,CAC3B;AAAA,IAAA;AAGH,WAAO,eAAe,OAAO,gBAAgB;AAAA,MAC3C,OAAO;AAAA,IAAA,CACR;AACD,WAAO;AAAA,EAAA,EACNA,KAAG,KAAK;AAEXA,OAAG,YAAa,SAAU,cAAc;AACtC,aAAS,UAAW,IAAI;AAEtB,mBAAa,MAAMA,MAAI,SAAS;AAChC,iBAAA;AAAA,IAAW;AAGb,WAAO,eAAe,WAAW,gBAAgB;AAAA,MAC/C,OAAO;AAAA,IAAA,CACR;AACD,WAAO;AAAA,EAAA,EACNA,KAAG,SAAS;AAEf,MAAI,YAAY,KAAK,QAAA,IAAY,cAAc,EAAE,GAAG;AAClD,YAAQ,GAAG,QAAQ,WAAW;AAC5B,YAAMA,KAAG,aAAa,CAAC;AACvB,iBAAkB,MAAMA,KAAG,aAAa,EAAE,QAAQ,CAAC;AAAA,IAAA,CACpD;AAAA,EAAA;AAEL;AAEA,IAAI,CAACyB,eAAO,aAAa,GAAG;AAC1B,eAAaA,gBAAQzB,KAAG,aAAa,CAAC;AACxC;IAEA,aAAiB,MAAM,MAAMA,IAAE,CAAC;AAChC,IAAI,QAAA,IAAY,iCAAiC,CAACA,KAAG,WAAW;AAC5D,eAAiB,MAAMA,IAAE;AACzBA,OAAG,YAAY;AACnB;AAEA,SAAS,MAAOA,KAAI;AAElB,YAAUA,GAAE;AACZA,MAAG,cAAc;AAEjBA,MAAG,mBAAmB;AACtBA,MAAG,oBAAoB;AACvB,MAAI,cAAcA,IAAG;AACrBA,MAAG,WAAW0B;AACd,WAASA,UAAU3B,OAAM,SAAS,IAAI;AACpC,QAAI,OAAO,YAAY;AACrB,WAAK,SAAS,UAAU;AAE1B,WAAO,YAAYA,OAAM,SAAS,EAAE;AAEpC,aAAS,YAAaA,QAAM4B,UAASC,KAAI,WAAW;AAClD,aAAO,YAAY7B,QAAM4B,UAAS,SAAU,KAAK;AAC/C,YAAI,QAAQ,IAAI,SAAS,YAAY,IAAI,SAAS;AAChD,kBAAQ,CAAC,aAAa,CAAC5B,QAAM4B,UAASC,GAAE,GAAG,KAAK,aAAa,KAAK,IAAA,GAAO,KAAK,IAAA,CAAK,CAAC;AAAA,aACjF;AACH,cAAI,OAAOA,QAAO;AAChBA,gBAAG,MAAM,MAAM,SAAS;AAAA,QAAA;AAAA,MAC5B,CACD;AAAA,IAAA;AAAA,EACH;AAGF,MAAI,eAAe5B,IAAG;AACtBA,MAAG,YAAY6B;AACf,WAASA,WAAW9B,OAAM,MAAM,SAAS,IAAI;AAC3C,QAAI,OAAO,YAAY;AACrB,WAAK,SAAS,UAAU;AAE1B,WAAO,aAAaA,OAAM,MAAM,SAAS,EAAE;AAE3C,aAAS,aAAcA,QAAM+B,OAAMH,UAASC,KAAI,WAAW;AACzD,aAAO,aAAa7B,QAAM+B,OAAMH,UAAS,SAAU,KAAK;AACtD,YAAI,QAAQ,IAAI,SAAS,YAAY,IAAI,SAAS;AAChD,kBAAQ,CAAC,cAAc,CAAC5B,QAAM+B,OAAMH,UAASC,GAAE,GAAG,KAAK,aAAa,KAAK,IAAA,GAAO,KAAK,IAAA,CAAK,CAAC;AAAA,aACxF;AACH,cAAI,OAAOA,QAAO;AAChBA,gBAAG,MAAM,MAAM,SAAS;AAAA,QAAA;AAAA,MAC5B,CACD;AAAA,IAAA;AAAA,EACH;AAGF,MAAI,gBAAgB5B,IAAG;AACvB,MAAI;AACFA,QAAG,aAAa;AAClB,WAAS,WAAYD,OAAM,MAAM,SAAS,IAAI;AAC5C,QAAI,OAAO,YAAY;AACrB,WAAK,SAAS,UAAU;AAE1B,WAAO,cAAcA,OAAM,MAAM,SAAS,EAAE;AAE5C,aAAS,cAAeA,QAAM+B,OAAMH,UAASC,KAAI,WAAW;AAC1D,aAAO,cAAc7B,QAAM+B,OAAMH,UAAS,SAAU,KAAK;AACvD,YAAI,QAAQ,IAAI,SAAS,YAAY,IAAI,SAAS;AAChD,kBAAQ,CAAC,eAAe,CAAC5B,QAAM+B,OAAMH,UAASC,GAAE,GAAG,KAAK,aAAa,KAAK,IAAA,GAAO,KAAK,IAAA,CAAK,CAAC;AAAA,aACzF;AACH,cAAI,OAAOA,QAAO;AAChBA,gBAAG,MAAM,MAAM,SAAS;AAAA,QAAA;AAAA,MAC5B,CACD;AAAA,IAAA;AAAA,EACH;AAGF,MAAI,cAAc5B,IAAG;AACrB,MAAI;AACFA,QAAG,WAAW+B;AAChB,WAASA,UAAU,KAAK,MAAM,OAAO,IAAI;AACvC,QAAI,OAAO,UAAU,YAAY;AAC/B,WAAK;AACL,cAAQ;AAAA,IAAA;AAEV,WAAO,YAAY,KAAK,MAAM,OAAO,EAAE;AAEvC,aAAS,YAAaC,MAAKC,OAAMC,QAAON,KAAI,WAAW;AACrD,aAAO,YAAYI,MAAKC,OAAMC,QAAO,SAAU,KAAK;AAClD,YAAI,QAAQ,IAAI,SAAS,YAAY,IAAI,SAAS;AAChD,kBAAQ,CAAC,aAAa,CAACF,MAAKC,OAAMC,QAAON,GAAE,GAAG,KAAK,aAAa,KAAK,IAAA,GAAO,KAAK,IAAA,CAAK,CAAC;AAAA,aACpF;AACH,cAAI,OAAOA,QAAO;AAChBA,gBAAG,MAAM,MAAM,SAAS;AAAA,QAAA;AAAA,MAC5B,CACD;AAAA,IAAA;AAAA,EACH;AAGF,MAAI,aAAa5B,IAAG;AACpBA,MAAG,UAAU;AACb,MAAI,0BAA0B;AAC9B,WAAS,QAASD,OAAM,SAAS,IAAI;AACnC,QAAI,OAAO,YAAY;AACrB,WAAK,SAAS,UAAU;AAE1B,QAAI,aAAa,wBAAwB,KAAK,QAAQ,OAAO,IACzD,SAASoC,YAAYpC,QAAM4B,UAASC,KAAI,WAAW;AACnD,aAAO,WAAW7B,QAAM;AAAA,QACtBA;AAAAA,QAAM4B;AAAAA,QAASC;AAAAA,QAAI;AAAA,MAAA,CACpB;AAAA,IAAA,IAED,SAASO,YAAYpC,QAAM4B,UAASC,KAAI,WAAW;AACnD,aAAO,WAAW7B,QAAM4B,UAAS;AAAA,QAC/B5B;AAAAA,QAAM4B;AAAAA,QAASC;AAAAA,QAAI;AAAA,MAAA,CACpB;AAAA,IAAA;AAGL,WAAO,WAAW7B,OAAM,SAAS,EAAE;AAEnC,aAAS,mBAAoBA,QAAM4B,UAASC,KAAI,WAAW;AACzD,aAAO,SAAU,KAAK,OAAO;AAC3B,YAAI,QAAQ,IAAI,SAAS,YAAY,IAAI,SAAS;AAChD,kBAAQ;AAAA,YACN;AAAA,YACA,CAAC7B,QAAM4B,UAASC,GAAE;AAAA,YAClB;AAAA,YACA,aAAa,KAAK,IAAA;AAAA,YAClB,KAAK,IAAA;AAAA,UAAI,CACV;AAAA,aACE;AACH,cAAI,SAAS,MAAM;AACjB,kBAAM,KAAA;AAER,cAAI,OAAOA,QAAO;AAChBA,gBAAG,KAAK,MAAM,KAAK,KAAK;AAAA,QAAA;AAAA,MAC5B;AAAA,IACF;AAAA,EACF;AAGF,MAAI,QAAQ,QAAQ,OAAO,GAAG,CAAC,MAAM,QAAQ;AAC3C,QAAI,aAAa,OAAO5B,GAAE;AAC1B,iBAAa,WAAW;AACxB,kBAAc,WAAW;AAAA,EAAA;AAG3B,MAAI,gBAAgBA,IAAG;AACvB,MAAI,eAAe;AACjB,eAAW,YAAY,OAAO,OAAO,cAAc,SAAS;AAC5D,eAAW,UAAU,OAAO;AAAA,EAAA;AAG9B,MAAI,iBAAiBA,IAAG;AACxB,MAAI,gBAAgB;AAClB,gBAAY,YAAY,OAAO,OAAO,eAAe,SAAS;AAC9D,gBAAY,UAAU,OAAO;AAAA,EAAA;AAG/B,SAAO,eAAeA,KAAI,cAAc;AAAA,IACtC,KAAK,WAAY;AACf,aAAO;AAAA,IAAA;AAAA,IAET,KAAK,SAAU,KAAK;AAClB,mBAAa;AAAA,IAAA;AAAA,IAEf,YAAY;AAAA,IACZ,cAAc;AAAA,EAAA,CACf;AACD,SAAO,eAAeA,KAAI,eAAe;AAAA,IACvC,KAAK,WAAY;AACf,aAAO;AAAA,IAAA;AAAA,IAET,KAAK,SAAU,KAAK;AAClB,oBAAc;AAAA,IAAA;AAAA,IAEhB,YAAY;AAAA,IACZ,cAAc;AAAA,EAAA,CACf;AAGD,MAAI,iBAAiB;AACrB,SAAO,eAAeA,KAAI,kBAAkB;AAAA,IAC1C,KAAK,WAAY;AACf,aAAO;AAAA,IAAA;AAAA,IAET,KAAK,SAAU,KAAK;AAClB,uBAAiB;AAAA,IAAA;AAAA,IAEnB,YAAY;AAAA,IACZ,cAAc;AAAA,EAAA,CACf;AACD,MAAI,kBAAkB;AACtB,SAAO,eAAeA,KAAI,mBAAmB;AAAA,IAC3C,KAAK,WAAY;AACf,aAAO;AAAA,IAAA;AAAA,IAET,KAAK,SAAU,KAAK;AAClB,wBAAkB;AAAA,IAAA;AAAA,IAEpB,YAAY;AAAA,IACZ,cAAc;AAAA,EAAA,CACf;AAED,WAAS,WAAYD,OAAM,SAAS;AAClC,QAAI,gBAAgB;AAClB,aAAO,cAAc,MAAM,MAAM,SAAS,GAAG;AAAA;AAE7C,aAAO,WAAW,MAAM,OAAO,OAAO,WAAW,SAAS,GAAG,SAAS;AAAA,EAAA;AAG1E,WAAS,kBAAmB;AAC1B,QAAI,OAAO;AACX,SAAK,KAAK,MAAM,KAAK,OAAO,KAAK,MAAM,SAAU,KAAK,IAAI;AACxD,UAAI,KAAK;AACP,YAAI,KAAK;AACP,eAAK,QAAA;AAEP,aAAK,KAAK,SAAS,GAAG;AAAA,MAAA,OACjB;AACL,aAAK,KAAK;AACV,aAAK,KAAK,QAAQ,EAAE;AACpB,aAAK,KAAA;AAAA,MAAK;AAAA,IACZ,CACD;AAAA,EAAA;AAGH,WAAS,YAAaA,OAAM,SAAS;AACnC,QAAI,gBAAgB;AAClB,aAAO,eAAe,MAAM,MAAM,SAAS,GAAG;AAAA;AAE9C,aAAO,YAAY,MAAM,OAAO,OAAO,YAAY,SAAS,GAAG,SAAS;AAAA,EAAA;AAG5E,WAAS,mBAAoB;AAC3B,QAAI,OAAO;AACX,SAAK,KAAK,MAAM,KAAK,OAAO,KAAK,MAAM,SAAU,KAAK,IAAI;AACxD,UAAI,KAAK;AACP,aAAK,QAAA;AACL,aAAK,KAAK,SAAS,GAAG;AAAA,MAAA,OACjB;AACL,aAAK,KAAK;AACV,aAAK,KAAK,QAAQ,EAAE;AAAA,MAAA;AAAA,IACtB,CACD;AAAA,EAAA;AAGH,WAAS,iBAAkBA,OAAM,SAAS;AACxC,WAAO,IAAIC,IAAG,WAAWD,OAAM,OAAO;AAAA,EAAA;AAGxC,WAAS,kBAAmBA,OAAM,SAAS;AACzC,WAAO,IAAIC,IAAG,YAAYD,OAAM,OAAO;AAAA,EAAA;AAGzC,MAAI,UAAUC,IAAG;AACjBA,MAAG,OAAO;AACV,WAAS,KAAMD,OAAM,OAAO,MAAM,IAAI;AACpC,QAAI,OAAO,SAAS;AAClB,WAAK,MAAM,OAAO;AAEpB,WAAO,QAAQA,OAAM,OAAO,MAAM,EAAE;AAEpC,aAAS,QAASA,QAAMmC,QAAOE,OAAMR,KAAI,WAAW;AAClD,aAAO,QAAQ7B,QAAMmC,QAAOE,OAAM,SAAU,KAAK,IAAI;AACnD,YAAI,QAAQ,IAAI,SAAS,YAAY,IAAI,SAAS;AAChD,kBAAQ,CAAC,SAAS,CAACrC,QAAMmC,QAAOE,OAAMR,GAAE,GAAG,KAAK,aAAa,KAAK,IAAA,GAAO,KAAK,IAAA,CAAK,CAAC;AAAA,aACjF;AACH,cAAI,OAAOA,QAAO;AAChBA,gBAAG,MAAM,MAAM,SAAS;AAAA,QAAA;AAAA,MAC5B,CACD;AAAA,IAAA;AAAA,EACH;AAGF,SAAO5B;AACT;AAEA,SAAS,QAAS,MAAM;AACtB,QAAM,WAAW,KAAK,CAAC,EAAE,MAAM,KAAK,CAAC,CAAC;AACtCA,OAAG,aAAa,EAAE,KAAK,IAAI;AAC3B,QAAA;AACF;AAGA,IAAI;AAKJ,SAAS,aAAc;AACrB,MAAI,MAAM,KAAK,IAAA;AACf,WAAS,IAAI,GAAG,IAAIA,KAAG,aAAa,EAAE,QAAQ,EAAE,GAAG;AAGjD,QAAIA,KAAG,aAAa,EAAE,CAAC,EAAE,SAAS,GAAG;AACnCA,WAAG,aAAa,EAAE,CAAC,EAAE,CAAC,IAAI;AAC1BA,WAAG,aAAa,EAAE,CAAC,EAAE,CAAC,IAAI;AAAA,IAAA;AAAA,EAC5B;AAGF,QAAA;AACF;AAEA,SAAS,QAAS;AAEhB,eAAa,UAAU;AACvB,eAAa;AAEb,MAAIA,KAAG,aAAa,EAAE,WAAW;AAC/B;AAEF,MAAI,OAAOA,KAAG,aAAa,EAAE,MAAA;AAC7B,MAAI,KAAK,KAAK,CAAC;AACf,MAAI,OAAO,KAAK,CAAC;AAEjB,MAAI,MAAM,KAAK,CAAC;AAChB,MAAI,YAAY,KAAK,CAAC;AACtB,MAAI,WAAW,KAAK,CAAC;AAIrB,MAAI,cAAc,QAAW;AAC3B,UAAM,SAAS,GAAG,MAAM,IAAI;AAC5B,OAAG,MAAM,MAAM,IAAI;AAAA,EAAA,WACV,KAAK,IAAA,IAAQ,aAAa,KAAO;AAE1C,UAAM,WAAW,GAAG,MAAM,IAAI;AAC9B,QAAI,KAAK,KAAK,IAAA;AACd,QAAI,OAAO,OAAO;AAChB,SAAG,KAAK,MAAM,GAAG;AAAA,EAAA,OACd;AAEL,QAAI,eAAe,KAAK,IAAA,IAAQ;AAGhC,QAAI,aAAa,KAAK,IAAI,WAAW,WAAW,CAAC;AAGjD,QAAI,eAAe,KAAK,IAAI,aAAa,KAAK,GAAG;AAEjD,QAAI,gBAAgB,cAAc;AAChC,YAAM,SAAS,GAAG,MAAM,IAAI;AAC5B,SAAG,MAAM,MAAM,KAAK,OAAO,CAAC,SAAS,CAAC,CAAC;AAAA,IAAA,OAClC;AAGLA,WAAG,aAAa,EAAE,KAAK,IAAI;AAAA,IAAA;AAAA,EAC7B;AAIF,MAAI,eAAe,QAAW;AAC5B,iBAAa,WAAW,OAAO,CAAC;AAAA,EAAA;AAEpC;AAAA;AC5bA,QAAMqC,KAAItB,eAAwB;AAClC,QAAMf,MAAKoB;AAEX,QAAM,MAAM;AAAA,IACV;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,EAAE,OAAO,SAAO;AAKd,WAAO,OAAOpB,IAAG,GAAG,MAAM;AAAA,EAC5B,CAAC;AAGD,SAAO,KAAKA,GAAE,EAAE,QAAQ,SAAO;AAC7B,QAAI,QAAQ,YAAY;AAGtB;AAAA,IACJ;AACE,YAAQ,GAAG,IAAIA,IAAG,GAAG;AAAA,EACvB,CAAC;AAGD,MAAI,QAAQ,YAAU;AACpB,YAAQ,MAAM,IAAIqC,GAAErC,IAAG,MAAM,CAAC;AAAA,EAChC,CAAC;AAID,UAAA,SAAiB,SAAU,UAAU,UAAU;AAC7C,QAAI,OAAO,aAAa,YAAY;AAClC,aAAOA,IAAG,OAAO,UAAU,QAAQ;AAAA,IACvC;AACE,WAAO,IAAI,QAAQ,aAAW;AAC5B,aAAOA,IAAG,OAAO,UAAU,OAAO;AAAA,IACtC,CAAG;AAAA,EACH;AAIA,UAAA,OAAe,SAAU,IAAI,QAAQ,QAAQ,QAAQ,UAAU,UAAU;AACvE,QAAI,OAAO,aAAa,YAAY;AAClC,aAAOA,IAAG,KAAK,IAAI,QAAQ,QAAQ,QAAQ,UAAU,QAAQ;AAAA,IACjE;AACE,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,MAAAA,IAAG,KAAK,IAAI,QAAQ,QAAQ,QAAQ,UAAU,CAAC,KAAK,WAAWsC,YAAW;AACxE,YAAI,IAAK,QAAO,OAAO,GAAG;AAC1B,gBAAQ,EAAE,WAAW,QAAAA,QAAM,CAAE;AAAA,MACnC,CAAK;AAAA,IACL,CAAG;AAAA,EACH;AAOA,UAAA,QAAgB,SAAU,IAAI,WAAW,MAAM;AAC7C,QAAI,OAAO,KAAK,KAAK,SAAS,CAAC,MAAM,YAAY;AAC/C,aAAOtC,IAAG,MAAM,IAAI,QAAQ,GAAG,IAAI;AAAA,IACvC;AAEE,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,MAAAA,IAAG,MAAM,IAAI,QAAQ,GAAG,MAAM,CAAC,KAAK,cAAcsC,YAAW;AAC3D,YAAI,IAAK,QAAO,OAAO,GAAG;AAC1B,gBAAQ,EAAE,cAAc,QAAAA,QAAM,CAAE;AAAA,MACtC,CAAK;AAAA,IACL,CAAG;AAAA,EACH;AAGA,MAAI,OAAOtC,IAAG,WAAW,YAAY;AAInC,YAAA,SAAiB,SAAU,IAAI,YAAY,MAAM;AAC/C,UAAI,OAAO,KAAK,KAAK,SAAS,CAAC,MAAM,YAAY;AAC/C,eAAOA,IAAG,OAAO,IAAI,SAAS,GAAG,IAAI;AAAA,MAC3C;AAEI,aAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,QAAAA,IAAG,OAAO,IAAI,SAAS,GAAG,MAAM,CAAC,KAAK,cAAcuC,aAAY;AAC9D,cAAI,IAAK,QAAO,OAAO,GAAG;AAC1B,kBAAQ,EAAE,cAAc,SAAAA,SAAO,CAAE;AAAA,QACzC,CAAO;AAAA,MACP,CAAK;AAAA,IACL;AAAA,EACA;AAGA,MAAI,OAAOvC,IAAG,SAAS,WAAW,YAAY;AAC5C,YAAQ,SAAS,SAASqC,GAAErC,IAAG,SAAS,MAAM;AAAA,EAChD;;;ICjIAwC,gBAAiB,OAAK;AACpB,QAAM,IAAI,QAAQ,SAAS,KAAK,MAAM,GAAG,EAAE,IAAI,OAAK,SAAS,GAAG,EAAE,CAAC;AACnE,MAAI,EAAE,MAAM,GAAG,EAAE,IAAI,OAAK,SAAS,GAAG,EAAE,CAAC;AACzC,SAAO,EAAE,CAAC,IAAI,EAAE,CAAC,KAAM,EAAE,CAAC,MAAM,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,KAAM,EAAE,CAAC,MAAM,EAAE,CAAC,KAAK,EAAE,CAAC,KAAK,EAAE,CAAC;AACvF;ACEA,MAAMxC,OAAKe;AACX,MAAMhB,SAAOqB;AACb,MAAMoB,gBAAcnB;AAEpB,MAAM,2BAA2BmB,cAAY,SAAS;AAItD,MAAM,YAAY,SAAO;AACvB,MAAI,QAAQ,aAAa,SAAS;AAChC,UAAM,8BAA8B,YAAY,KAAK,IAAI,QAAQzC,OAAK,MAAM,GAAG,EAAE,MAAM,EAAE,CAAC;AAE1F,QAAI,6BAA6B;AAC/B,YAAM,QAAQ,IAAI,MAAM,qCAAqC,GAAG,EAAE;AAClE,YAAM,OAAO;AACb,YAAM;AAAA,IACZ;AAAA,EACA;AACA;AAEA,MAAM,iBAAiB,aAAW;AAChC,QAAM0C,YAAW,EAAE,MAAM,IAAK;AAC9B,MAAI,OAAO,YAAY,SAAU,WAAU,EAAE,MAAM,QAAO;AAC1D,SAAO,EAAE,GAAGA,WAAU,GAAG,QAAO;AAClC;AAEA,MAAM,kBAAkB,SAAO;AAG7B,QAAM,QAAQ,IAAI,MAAM,mCAAmC,GAAG,GAAG;AACjE,QAAM,OAAO;AACb,QAAM,QAAQ;AACd,QAAM,OAAO;AACb,QAAM,UAAU;AAChB,SAAO;AACT;AAEAC,UAAA,UAAyB,OAAO,OAAO,YAAY;AACjD,YAAU,KAAK;AACf,YAAU,eAAe,OAAO;AAEhC,MAAI,0BAA0B;AAC5B,UAAM,MAAM3C,OAAK,QAAQ,KAAK;AAE9B,WAAOC,KAAG,MAAM,KAAK;AAAA,MACnB,MAAM,QAAQ;AAAA,MACd,WAAW;AAAA,IACjB,CAAK;AAAA,EACL;AAEE,QAAM,OAAO,OAAM,QAAO;AACxB,QAAI;AACF,YAAMA,KAAG,MAAM,KAAK,QAAQ,IAAI;AAAA,IACtC,SAAa,OAAO;AACd,UAAI,MAAM,SAAS,SAAS;AAC1B,cAAM;AAAA,MACd;AAEM,UAAI,MAAM,SAAS,UAAU;AAC3B,YAAID,OAAK,QAAQ,GAAG,MAAM,KAAK;AAC7B,gBAAM,gBAAgB,GAAG;AAAA,QACnC;AAEQ,YAAI,MAAM,QAAQ,SAAS,YAAY,GAAG;AACxC,gBAAM;AAAA,QAChB;AAEQ,cAAM,KAAKA,OAAK,QAAQ,GAAG,CAAC;AAC5B,eAAO,KAAK,GAAG;AAAA,MACvB;AAEM,UAAI;AACF,cAAM,QAAQ,MAAMC,KAAG,KAAK,GAAG;AAC/B,YAAI,CAAC,MAAM,eAAe;AAGxB,gBAAM,IAAI,MAAM,6BAA6B;AAAA,QACvD;AAAA,MACA,QAAc;AACN,cAAM;AAAA,MACd;AAAA,IACA;AAAA,EACA;AAEE,SAAO,KAAKD,OAAK,QAAQ,KAAK,CAAC;AACjC;AAEA2C,UAAA,cAA6B,CAAC,OAAO,YAAY;AAC/C,YAAU,KAAK;AACf,YAAU,eAAe,OAAO;AAEhC,MAAI,0BAA0B;AAC5B,UAAM,MAAM3C,OAAK,QAAQ,KAAK;AAE9B,WAAOC,KAAG,UAAU,KAAK;AAAA,MACvB,MAAM,QAAQ;AAAA,MACd,WAAW;AAAA,IACjB,CAAK;AAAA,EACL;AAEE,QAAM,OAAO,SAAO;AAClB,QAAI;AACFA,WAAG,UAAU,KAAK,QAAQ,IAAI;AAAA,IACpC,SAAa,OAAO;AACd,UAAI,MAAM,SAAS,SAAS;AAC1B,cAAM;AAAA,MACd;AAEM,UAAI,MAAM,SAAS,UAAU;AAC3B,YAAID,OAAK,QAAQ,GAAG,MAAM,KAAK;AAC7B,gBAAM,gBAAgB,GAAG;AAAA,QACnC;AAEQ,YAAI,MAAM,QAAQ,SAAS,YAAY,GAAG;AACxC,gBAAM;AAAA,QAChB;AAEQ,aAAKA,OAAK,QAAQ,GAAG,CAAC;AACtB,eAAO,KAAK,GAAG;AAAA,MACvB;AAEM,UAAI;AACF,YAAI,CAACC,KAAG,SAAS,GAAG,EAAE,YAAW,GAAI;AAGnC,gBAAM,IAAI,MAAM,6BAA6B;AAAA,QACvD;AAAA,MACA,QAAc;AACN,cAAM;AAAA,MACd;AAAA,IACA;AAAA,EACA;AAEE,SAAO,KAAKD,OAAK,QAAQ,KAAK,CAAC;AACjC;AC3IA,MAAMsC,MAAItB,eAAwB;AAClC,MAAM,EAAE,SAAS,UAAU,gBAAgBK;AAC3C,MAAM,UAAUiB,IAAE,QAAQ;AAE1B,IAAAM,WAAiB;AAAA,EACf,QAAQ;AAAA,EACR,YAAY;AAAA;AAAA,EAEZ,QAAQ;AAAA,EACR,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,eAAe;AACjB;ACXA,MAAM3C,OAAKe;AAEX,SAAS6B,eAAc7C,OAAM,OAAO,OAAO,UAAU;AAEnDC,OAAG,KAAKD,OAAM,MAAM,CAAC,KAAK,OAAO;AAC/B,QAAI,IAAK,QAAO,SAAS,GAAG;AAC5BC,SAAG,QAAQ,IAAI,OAAO,OAAO,gBAAc;AACzCA,WAAG,MAAM,IAAI,cAAY;AACvB,YAAI,SAAU,UAAS,cAAc,QAAQ;AAAA,MACrD,CAAO;AAAA,IACP,CAAK;AAAA,EACL,CAAG;AACH;AAEA,SAAS6C,mBAAkB9C,OAAM,OAAO,OAAO;AAC7C,QAAM,KAAKC,KAAG,SAASD,OAAM,IAAI;AACjCC,OAAG,YAAY,IAAI,OAAO,KAAK;AAC/B,SAAOA,KAAG,UAAU,EAAE;AACxB;AAEA,IAAA,SAAiB;AAAA,EACjB,cAAE4C;AAAAA,EACF,kBAAEC;AACF;ACvBA,MAAM7C,OAAKe;AACX,MAAMhB,SAAOqB;AACb,MAAM,OAAOC;AACb,MAAM,cAAcC;AAEpB,MAAM,qBAAqB,YAAY,QAAQ;AAC/C,MAAMwB,SAAO,CAACxC,UAAS,qBAAqBN,KAAG,KAAKM,OAAM,EAAE,QAAQ,KAAI,CAAE,IAAIN,KAAG,KAAKM,KAAI;AAC1F,MAAM,WAAW,CAACA,UAAS,qBAAqBN,KAAG,SAASM,OAAM,EAAE,QAAQ,KAAI,CAAE,IAAIN,KAAG,SAASM,KAAI;AAEtG,SAASyC,WAAU,KAAK,MAAM;AAC5B,SAAO,QAAQ,IAAI;AAAA,IACjBD,OAAK,GAAG;AAAA,IACRA,OAAK,IAAI,EAAE,MAAM,SAAO;AACtB,UAAI,IAAI,SAAS,SAAU,QAAO;AAClC,YAAM;AAAA,IACZ,CAAK;AAAA,EACL,CAAG,EAAE,KAAK,CAAC,CAAC,SAAS,QAAQ,OAAO,EAAE,SAAS,WAAW;AAC1D;AAEA,SAAS,aAAc,KAAK,MAAM;AAChC,MAAI;AACJ,QAAM,UAAU,SAAS,GAAG;AAC5B,MAAI;AACF,eAAW,SAAS,IAAI;AAAA,EAC5B,SAAW,KAAK;AACZ,QAAI,IAAI,SAAS,SAAU,QAAO,EAAE,SAAS,UAAU,KAAI;AAC3D,UAAM;AAAA,EACV;AACE,SAAO,EAAE,SAAS,SAAQ;AAC5B;AAEA,SAAS,WAAY,KAAK,MAAM,UAAU,IAAI;AAC5C,OAAK,YAAYC,UAAQ,EAAE,KAAK,MAAM,CAAC,KAAK,UAAU;AACpD,QAAI,IAAK,QAAO,GAAG,GAAG;AACtB,UAAM,EAAE,SAAS,aAAa;AAC9B,QAAI,YAAY,aAAa,SAAS,QAAQ,GAAG;AAC/C,aAAO,GAAG,IAAI,MAAM,8CAA8C,CAAC;AAAA,IACzE;AACI,QAAI,QAAQ,YAAW,KAAM,YAAY,KAAK,IAAI,GAAG;AACnD,aAAO,GAAG,IAAI,MAAM,OAAO,KAAK,MAAM,QAAQ,CAAC,CAAC;AAAA,IACtD;AACI,WAAO,GAAG,MAAM,EAAE,SAAS,SAAQ,CAAE;AAAA,EACzC,CAAG;AACH;AAEA,SAAS,eAAgB,KAAK,MAAM,UAAU;AAC5C,QAAM,EAAE,SAAS,SAAQ,IAAK,aAAa,KAAK,IAAI;AACpD,MAAI,YAAY,aAAa,SAAS,QAAQ,GAAG;AAC/C,UAAM,IAAI,MAAM,8CAA8C;AAAA,EAClE;AACE,MAAI,QAAQ,YAAW,KAAM,YAAY,KAAK,IAAI,GAAG;AACnD,UAAM,IAAI,MAAM,OAAO,KAAK,MAAM,QAAQ,CAAC;AAAA,EAC/C;AACE,SAAO,EAAE,SAAS,SAAQ;AAC5B;AAMA,SAAS,iBAAkB,KAAK,SAAS,MAAM,UAAU,IAAI;AAC3D,QAAM,YAAYhD,OAAK,QAAQA,OAAK,QAAQ,GAAG,CAAC;AAChD,QAAM,aAAaA,OAAK,QAAQA,OAAK,QAAQ,IAAI,CAAC;AAClD,MAAI,eAAe,aAAa,eAAeA,OAAK,MAAM,UAAU,EAAE,KAAM,QAAO,GAAE;AACrF,QAAM,WAAW,CAAC,KAAK,aAAa;AAClC,QAAI,KAAK;AACP,UAAI,IAAI,SAAS,SAAU,QAAO,GAAE;AACpC,aAAO,GAAG,GAAG;AAAA,IACnB;AACI,QAAI,aAAa,SAAS,QAAQ,GAAG;AACnC,aAAO,GAAG,IAAI,MAAM,OAAO,KAAK,MAAM,QAAQ,CAAC,CAAC;AAAA,IACtD;AACI,WAAO,iBAAiB,KAAK,SAAS,YAAY,UAAU,EAAE;AAAA,EAClE;AACE,MAAI,mBAAoBC,MAAG,KAAK,YAAY,EAAE,QAAQ,KAAI,GAAI,QAAQ;AAAA,MACjEA,MAAG,KAAK,YAAY,QAAQ;AACnC;AAEA,SAAS,qBAAsB,KAAK,SAAS,MAAM,UAAU;AAC3D,QAAM,YAAYD,OAAK,QAAQA,OAAK,QAAQ,GAAG,CAAC;AAChD,QAAM,aAAaA,OAAK,QAAQA,OAAK,QAAQ,IAAI,CAAC;AAClD,MAAI,eAAe,aAAa,eAAeA,OAAK,MAAM,UAAU,EAAE,KAAM;AAC5E,MAAI;AACJ,MAAI;AACF,eAAW,SAAS,UAAU;AAAA,EAClC,SAAW,KAAK;AACZ,QAAI,IAAI,SAAS,SAAU;AAC3B,UAAM;AAAA,EACV;AACE,MAAI,aAAa,SAAS,QAAQ,GAAG;AACnC,UAAM,IAAI,MAAM,OAAO,KAAK,MAAM,QAAQ,CAAC;AAAA,EAC/C;AACE,SAAO,qBAAqB,KAAK,SAAS,YAAY,QAAQ;AAChE;AAEA,SAAS,aAAc,SAAS,UAAU;AACxC,MAAI,SAAS,OAAO,SAAS,OAAO,SAAS,QAAQ,QAAQ,OAAO,SAAS,QAAQ,QAAQ,KAAK;AAChG,QAAI,sBAAsB,SAAS,MAAM,OAAO,kBAAkB;AAEhE,aAAO;AAAA,IACb;AAII,QAAI,SAAS,SAAS,QAAQ,QAC1B,SAAS,SAAS,QAAQ,QAC1B,SAAS,UAAU,QAAQ,SAC3B,SAAS,YAAY,QAAQ,WAC7B,SAAS,YAAY,QAAQ,WAC7B,SAAS,YAAY,QAAQ,WAC7B,SAAS,gBAAgB,QAAQ,aAAa;AAEhD,aAAO;AAAA,IACb;AAAA,EACA;AACE,SAAO;AACT;AAIA,SAAS,YAAa,KAAK,MAAM;AAC/B,QAAM,SAASA,OAAK,QAAQ,GAAG,EAAE,MAAMA,OAAK,GAAG,EAAE,OAAO,OAAK,CAAC;AAC9D,QAAM,UAAUA,OAAK,QAAQ,IAAI,EAAE,MAAMA,OAAK,GAAG,EAAE,OAAO,OAAK,CAAC;AAChE,SAAO,OAAO,OAAO,CAAC,KAAK,KAAK,MAAM,OAAO,QAAQ,CAAC,MAAM,KAAK,IAAI;AACvE;AAEA,SAAS,OAAQ,KAAK,MAAM,UAAU;AACpC,SAAO,UAAU,QAAQ,KAAK,GAAG,mCAAmC,IAAI;AAC1E;AAEA,IAAA,SAAiB;AAAA,EACf;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;ACxIA,MAAMC,OAAKe;AACX,MAAMhB,SAAOqB;AACb,MAAM4B,eAAa3B,SAAqB;AACxC,MAAM,mBAAmBC,OAA0B;AACnD,MAAMwB,SAAOG;AAEb,SAASC,WAAU,KAAK,MAAM,MAAM;AAClC,MAAI,OAAO,SAAS,YAAY;AAC9B,WAAO,EAAE,QAAQ,KAAI;AAAA,EACzB;AAEE,SAAO,QAAQ,CAAA;AACf,OAAK,UAAU,aAAa,OAAO,CAAC,CAAC,KAAK,UAAU;AACpD,OAAK,YAAY,eAAe,OAAO,CAAC,CAAC,KAAK,YAAY,KAAK;AAG/D,MAAI,KAAK,sBAAsB,QAAQ,SAAS,QAAQ;AACtD,YAAQ,KAAK;AAAA;AAAA,iEACgD;AAAA,EACjE;AAEE,QAAM,EAAE,SAAS,SAAQ,IAAKJ,OAAK,eAAe,KAAK,MAAM,MAAM;AACnEA,SAAK,qBAAqB,KAAK,SAAS,MAAM,MAAM;AACpD,SAAO,oBAAoB,UAAU,KAAK,MAAM,IAAI;AACtD;AAEA,SAAS,oBAAqB,UAAU,KAAK,MAAM,MAAM;AACvD,MAAI,KAAK,UAAU,CAAC,KAAK,OAAO,KAAK,IAAI,EAAG;AAC5C,QAAM,aAAa/C,OAAK,QAAQ,IAAI;AACpC,MAAI,CAACC,KAAG,WAAW,UAAU,EAAGgD,cAAW,UAAU;AACrD,SAAOG,YAAU,UAAU,KAAK,MAAM,IAAI;AAC5C;AAEA,SAASA,YAAW,UAAU,KAAK,MAAM,MAAM;AAC7C,MAAI,KAAK,UAAU,CAAC,KAAK,OAAO,KAAK,IAAI,EAAG;AAC5C,SAAOJ,WAAS,UAAU,KAAK,MAAM,IAAI;AAC3C;AAEA,SAASA,WAAU,UAAU,KAAK,MAAM,MAAM;AAC5C,QAAMK,YAAW,KAAK,cAAcpD,KAAG,WAAWA,KAAG;AACrD,QAAM,UAAUoD,UAAS,GAAG;AAE5B,MAAI,QAAQ,YAAW,EAAI,QAAOC,QAAM,SAAS,UAAU,KAAK,MAAM,IAAI;AAAA,WACjE,QAAQ,OAAM,KACd,QAAQ,kBAAiB,KACzB,QAAQ,cAAa,EAAI,QAAOC,SAAO,SAAS,UAAU,KAAK,MAAM,IAAI;AAAA,WACzE,QAAQ,iBAAkB,QAAOC,SAAO,UAAU,KAAK,MAAM,IAAI;AAC5E;AAEA,SAASD,SAAQ,SAAS,UAAU,KAAK,MAAM,MAAM;AACnD,MAAI,CAAC,SAAU,QAAOvB,WAAS,SAAS,KAAK,MAAM,IAAI;AACvD,SAAOyB,cAAY,SAAS,KAAK,MAAM,IAAI;AAC7C;AAEA,SAASA,cAAa,SAAS,KAAK,MAAM,MAAM;AAC9C,MAAI,KAAK,WAAW;AAClBxD,SAAG,WAAW,IAAI;AAClB,WAAO+B,WAAS,SAAS,KAAK,MAAM,IAAI;AAAA,EAC5C,WAAa,KAAK,cAAc;AAC5B,UAAM,IAAI,MAAM,IAAI,IAAI,kBAAkB;AAAA,EAC9C;AACA;AAEA,SAASA,WAAU,SAAS,KAAK,MAAM,MAAM;AAC3C/B,OAAG,aAAa,KAAK,IAAI;AACzB,MAAI,KAAK,mBAAoB,kBAAiB,QAAQ,MAAM,KAAK,IAAI;AACrE,SAAOyD,cAAY,MAAM,QAAQ,IAAI;AACvC;AAEA,SAAS,iBAAkB,SAAS,KAAK,MAAM;AAI7C,MAAIC,oBAAkB,OAAO,EAAGC,oBAAiB,MAAM,OAAO;AAC9D,SAAOC,oBAAkB,KAAK,IAAI;AACpC;AAEA,SAASF,oBAAmB,SAAS;AACnC,UAAQ,UAAU,SAAW;AAC/B;AAEA,SAASC,mBAAkB,MAAM,SAAS;AACxC,SAAOF,cAAY,MAAM,UAAU,GAAK;AAC1C;AAEA,SAASA,cAAa,MAAM,SAAS;AACnC,SAAOzD,KAAG,UAAU,MAAM,OAAO;AACnC;AAEA,SAAS4D,oBAAmB,KAAK,MAAM;AAIrC,QAAM,iBAAiB5D,KAAG,SAAS,GAAG;AACtC,SAAO,iBAAiB,MAAM,eAAe,OAAO,eAAe,KAAK;AAC1E;AAEA,SAASqD,QAAO,SAAS,UAAU,KAAK,MAAM,MAAM;AAClD,MAAI,CAAC,SAAU,QAAOQ,eAAa,QAAQ,MAAM,KAAK,MAAM,IAAI;AAChE,MAAI,YAAY,CAAC,SAAS,eAAe;AACvC,UAAM,IAAI,MAAM,mCAAmC,IAAI,qBAAqB,GAAG,IAAI;AAAA,EACvF;AACE,SAAOC,UAAQ,KAAK,MAAM,IAAI;AAChC;AAEA,SAASD,eAAc,SAAS,KAAK,MAAM,MAAM;AAC/C7D,OAAG,UAAU,IAAI;AACjB8D,YAAQ,KAAK,MAAM,IAAI;AACvB,SAAOL,cAAY,MAAM,OAAO;AAClC;AAEA,SAASK,UAAS,KAAK,MAAM,MAAM;AACjC9D,OAAG,YAAY,GAAG,EAAE,QAAQ,UAAQ+D,cAAY,MAAM,KAAK,MAAM,IAAI,CAAC;AACxE;AAEA,SAASA,cAAa,MAAM,KAAK,MAAM,MAAM;AAC3C,QAAM,UAAUhE,OAAK,KAAK,KAAK,IAAI;AACnC,QAAM,WAAWA,OAAK,KAAK,MAAM,IAAI;AACrC,QAAM,EAAE,SAAQ,IAAK+C,OAAK,eAAe,SAAS,UAAU,MAAM;AAClE,SAAOK,YAAU,UAAU,SAAS,UAAU,IAAI;AACpD;AAEA,SAASI,SAAQ,UAAU,KAAK,MAAM,MAAM;AAC1C,MAAI,cAAcvD,KAAG,aAAa,GAAG;AACrC,MAAI,KAAK,aAAa;AACpB,kBAAcD,OAAK,QAAQ,QAAQ,IAAG,GAAI,WAAW;AAAA,EACzD;AAEE,MAAI,CAAC,UAAU;AACb,WAAOC,KAAG,YAAY,aAAa,IAAI;AAAA,EAC3C,OAAS;AACL,QAAI;AACJ,QAAI;AACF,qBAAeA,KAAG,aAAa,IAAI;AAAA,IACzC,SAAa,KAAK;AAIZ,UAAI,IAAI,SAAS,YAAY,IAAI,SAAS,UAAW,QAAOA,KAAG,YAAY,aAAa,IAAI;AAC5F,YAAM;AAAA,IACZ;AACI,QAAI,KAAK,aAAa;AACpB,qBAAeD,OAAK,QAAQ,QAAQ,IAAG,GAAI,YAAY;AAAA,IAC7D;AACI,QAAI+C,OAAK,YAAY,aAAa,YAAY,GAAG;AAC/C,YAAM,IAAI,MAAM,gBAAgB,WAAW,mCAAmC,YAAY,IAAI;AAAA,IACpG;AAKI,QAAI9C,KAAG,SAAS,IAAI,EAAE,iBAAiB8C,OAAK,YAAY,cAAc,WAAW,GAAG;AAClF,YAAM,IAAI,MAAM,qBAAqB,YAAY,WAAW,WAAW,IAAI;AAAA,IACjF;AACI,WAAOkB,WAAS,aAAa,IAAI;AAAA,EACrC;AACA;AAEA,SAASA,WAAU,aAAa,MAAM;AACpChE,OAAG,WAAW,IAAI;AAClB,SAAOA,KAAG,YAAY,aAAa,IAAI;AACzC;AAEA,IAAA,aAAiBkD;ACnKjB,IAAAA,aAAiB;AAAA,EACf,UAAUnC;AACZ;ACHA,MAAMsB,MAAItB,eAAwB;AAClC,MAAMf,OAAKoB;AAEX,SAAS6C,aAAYlE,OAAM;AACzB,SAAOC,KAAG,OAAOD,KAAI,EAAE,KAAK,MAAM,IAAI,EAAE,MAAM,MAAM,KAAK;AAC3D;AAEA,IAAA,eAAiB;AAAA,EACf,YAAYsC,IAAE4B,YAAU;AAAA,EACxB,gBAAgBjE,KAAG;AACrB;ACTA,MAAMA,OAAKe;AACX,MAAMhB,SAAOqB;AACb,MAAMuB,WAAStB,SAAqB;AACpC,MAAM4C,eAAa3C,aAA0B;AAC7C,MAAM,eAAe2B,OAA0B;AAC/C,MAAMH,SAAOoB;AAEb,SAAS/C,OAAM,KAAK,MAAM,MAAM,IAAI;AAClC,MAAI,OAAO,SAAS,cAAc,CAAC,IAAI;AACrC,SAAK;AACL,WAAO,CAAA;AAAA,EACX,WAAa,OAAO,SAAS,YAAY;AACrC,WAAO,EAAE,QAAQ,KAAI;AAAA,EACzB;AAEE,OAAK,MAAM,WAAY;AAAA,EAAA;AACvB,SAAO,QAAQ,CAAA;AAEf,OAAK,UAAU,aAAa,OAAO,CAAC,CAAC,KAAK,UAAU;AACpD,OAAK,YAAY,eAAe,OAAO,CAAC,CAAC,KAAK,YAAY,KAAK;AAG/D,MAAI,KAAK,sBAAsB,QAAQ,SAAS,QAAQ;AACtD,YAAQ,KAAK;AAAA;AAAA,iEACgD;AAAA,EACjE;AAEE2B,SAAK,WAAW,KAAK,MAAM,QAAQ,CAAC,KAAK,UAAU;AACjD,QAAI,IAAK,QAAO,GAAG,GAAG;AACtB,UAAM,EAAE,SAAS,aAAa;AAC9BA,WAAK,iBAAiB,KAAK,SAAS,MAAM,QAAQ,CAAAnC,SAAO;AACvD,UAAIA,KAAK,QAAO,GAAGA,IAAG;AACtB,UAAI,KAAK,OAAQ,QAAO,aAAa,gBAAgB,UAAU,KAAK,MAAM,MAAM,EAAE;AAClF,aAAO,eAAe,UAAU,KAAK,MAAM,MAAM,EAAE;AAAA,IACzD,CAAK;AAAA,EACL,CAAG;AACH;AAEA,SAAS,eAAgB,UAAU,KAAK,MAAM,MAAM,IAAI;AACtD,QAAM,aAAaZ,OAAK,QAAQ,IAAI;AACpCkE,eAAW,YAAY,CAAC,KAAK,cAAc;AACzC,QAAI,IAAK,QAAO,GAAG,GAAG;AACtB,QAAI,UAAW,QAAO,UAAU,UAAU,KAAK,MAAM,MAAM,EAAE;AAC7DtB,aAAO,YAAY,CAAAhC,SAAO;AACxB,UAAIA,KAAK,QAAO,GAAGA,IAAG;AACtB,aAAO,UAAU,UAAU,KAAK,MAAM,MAAM,EAAE;AAAA,IACpD,CAAK;AAAA,EACL,CAAG;AACH;AAEA,SAAS,aAAc,WAAW,UAAU,KAAK,MAAM,MAAM,IAAI;AAC/D,UAAQ,QAAQ,KAAK,OAAO,KAAK,IAAI,CAAC,EAAE,KAAK,aAAW;AACtD,QAAI,QAAS,QAAO,UAAU,UAAU,KAAK,MAAM,MAAM,EAAE;AAC3D,WAAO,GAAE;AAAA,EACb,GAAK,WAAS,GAAG,KAAK,CAAC;AACvB;AAEA,SAAS,UAAW,UAAU,KAAK,MAAM,MAAM,IAAI;AACjD,MAAI,KAAK,OAAQ,QAAO,aAAa,UAAU,UAAU,KAAK,MAAM,MAAM,EAAE;AAC5E,SAAO,SAAS,UAAU,KAAK,MAAM,MAAM,EAAE;AAC/C;AAEA,SAAS,SAAU,UAAU,KAAK,MAAM,MAAM,IAAI;AAChD,QAAMmC,QAAO,KAAK,cAAc9C,KAAG,OAAOA,KAAG;AAC7C,EAAA8C,MAAK,KAAK,CAAC,KAAK,YAAY;AAC1B,QAAI,IAAK,QAAO,GAAG,GAAG;AAEtB,QAAI,QAAQ,YAAW,EAAI,QAAO,MAAM,SAAS,UAAU,KAAK,MAAM,MAAM,EAAE;AAAA,aACrE,QAAQ,OAAM,KACd,QAAQ,kBAAiB,KACzB,QAAQ,gBAAiB,QAAO,OAAO,SAAS,UAAU,KAAK,MAAM,MAAM,EAAE;AAAA,aAC7E,QAAQ,eAAc,EAAI,QAAO,OAAO,UAAU,KAAK,MAAM,MAAM,EAAE;AAAA,EAClF,CAAG;AACH;AAEA,SAAS,OAAQ,SAAS,UAAU,KAAK,MAAM,MAAM,IAAI;AACvD,MAAI,CAAC,SAAU,QAAO,SAAS,SAAS,KAAK,MAAM,MAAM,EAAE;AAC3D,SAAO,YAAY,SAAS,KAAK,MAAM,MAAM,EAAE;AACjD;AAEA,SAAS,YAAa,SAAS,KAAK,MAAM,MAAM,IAAI;AAClD,MAAI,KAAK,WAAW;AAClB9C,SAAG,OAAO,MAAM,SAAO;AACrB,UAAI,IAAK,QAAO,GAAG,GAAG;AACtB,aAAO,SAAS,SAAS,KAAK,MAAM,MAAM,EAAE;AAAA,IAClD,CAAK;AAAA,EACL,WAAa,KAAK,cAAc;AAC5B,WAAO,GAAG,IAAI,MAAM,IAAI,IAAI,kBAAkB,CAAC;AAAA,EACnD,MAAS,QAAO,GAAE;AAClB;AAEA,SAAS,SAAU,SAAS,KAAK,MAAM,MAAM,IAAI;AAC/CA,OAAG,SAAS,KAAK,MAAM,SAAO;AAC5B,QAAI,IAAK,QAAO,GAAG,GAAG;AACtB,QAAI,KAAK,mBAAoB,QAAO,wBAAwB,QAAQ,MAAM,KAAK,MAAM,EAAE;AACvF,WAAO,YAAY,MAAM,QAAQ,MAAM,EAAE;AAAA,EAC7C,CAAG;AACH;AAEA,SAAS,wBAAyB,SAAS,KAAK,MAAM,IAAI;AAIxD,MAAI,kBAAkB,OAAO,GAAG;AAC9B,WAAO,iBAAiB,MAAM,SAAS,SAAO;AAC5C,UAAI,IAAK,QAAO,GAAG,GAAG;AACtB,aAAO,yBAAyB,SAAS,KAAK,MAAM,EAAE;AAAA,IAC5D,CAAK;AAAA,EACL;AACE,SAAO,yBAAyB,SAAS,KAAK,MAAM,EAAE;AACxD;AAEA,SAAS,kBAAmB,SAAS;AACnC,UAAQ,UAAU,SAAW;AAC/B;AAEA,SAAS,iBAAkB,MAAM,SAAS,IAAI;AAC5C,SAAO,YAAY,MAAM,UAAU,KAAO,EAAE;AAC9C;AAEA,SAAS,yBAA0B,SAAS,KAAK,MAAM,IAAI;AACzD,oBAAkB,KAAK,MAAM,SAAO;AAClC,QAAI,IAAK,QAAO,GAAG,GAAG;AACtB,WAAO,YAAY,MAAM,SAAS,EAAE;AAAA,EACxC,CAAG;AACH;AAEA,SAAS,YAAa,MAAM,SAAS,IAAI;AACvC,SAAOA,KAAG,MAAM,MAAM,SAAS,EAAE;AACnC;AAEA,SAAS,kBAAmB,KAAK,MAAM,IAAI;AAIzCA,OAAG,KAAK,KAAK,CAAC,KAAK,mBAAmB;AACpC,QAAI,IAAK,QAAO,GAAG,GAAG;AACtB,WAAO,aAAa,MAAM,eAAe,OAAO,eAAe,OAAO,EAAE;AAAA,EAC5E,CAAG;AACH;AAEA,SAAS,MAAO,SAAS,UAAU,KAAK,MAAM,MAAM,IAAI;AACtD,MAAI,CAAC,SAAU,QAAO,aAAa,QAAQ,MAAM,KAAK,MAAM,MAAM,EAAE;AACpE,MAAI,YAAY,CAAC,SAAS,eAAe;AACvC,WAAO,GAAG,IAAI,MAAM,mCAAmC,IAAI,qBAAqB,GAAG,IAAI,CAAC;AAAA,EAC5F;AACE,SAAO,QAAQ,KAAK,MAAM,MAAM,EAAE;AACpC;AAEA,SAAS,aAAc,SAAS,KAAK,MAAM,MAAM,IAAI;AACnDA,OAAG,MAAM,MAAM,SAAO;AACpB,QAAI,IAAK,QAAO,GAAG,GAAG;AACtB,YAAQ,KAAK,MAAM,MAAM,CAAAW,SAAO;AAC9B,UAAIA,KAAK,QAAO,GAAGA,IAAG;AACtB,aAAO,YAAY,MAAM,SAAS,EAAE;AAAA,IAC1C,CAAK;AAAA,EACL,CAAG;AACH;AAEA,SAAS,QAAS,KAAK,MAAM,MAAM,IAAI;AACrCX,OAAG,QAAQ,KAAK,CAAC,KAAK,UAAU;AAC9B,QAAI,IAAK,QAAO,GAAG,GAAG;AACtB,WAAO,aAAa,OAAO,KAAK,MAAM,MAAM,EAAE;AAAA,EAClD,CAAG;AACH;AAEA,SAAS,aAAc,OAAO,KAAK,MAAM,MAAM,IAAI;AACjD,QAAM,OAAO,MAAM,IAAG;AACtB,MAAI,CAAC,KAAM,QAAO,GAAE;AACpB,SAAO,YAAY,OAAO,MAAM,KAAK,MAAM,MAAM,EAAE;AACrD;AAEA,SAAS,YAAa,OAAO,MAAM,KAAK,MAAM,MAAM,IAAI;AACtD,QAAM,UAAUD,OAAK,KAAK,KAAK,IAAI;AACnC,QAAM,WAAWA,OAAK,KAAK,MAAM,IAAI;AACrC+C,SAAK,WAAW,SAAS,UAAU,QAAQ,CAAC,KAAK,UAAU;AACzD,QAAI,IAAK,QAAO,GAAG,GAAG;AACtB,UAAM,EAAE,SAAQ,IAAK;AACrB,cAAU,UAAU,SAAS,UAAU,MAAM,CAAAnC,SAAO;AAClD,UAAIA,KAAK,QAAO,GAAGA,IAAG;AACtB,aAAO,aAAa,OAAO,KAAK,MAAM,MAAM,EAAE;AAAA,IACpD,CAAK;AAAA,EACL,CAAG;AACH;AAEA,SAAS,OAAQ,UAAU,KAAK,MAAM,MAAM,IAAI;AAC9CX,OAAG,SAAS,KAAK,CAAC,KAAK,gBAAgB;AACrC,QAAI,IAAK,QAAO,GAAG,GAAG;AACtB,QAAI,KAAK,aAAa;AACpB,oBAAcD,OAAK,QAAQ,QAAQ,IAAG,GAAI,WAAW;AAAA,IAC3D;AAEI,QAAI,CAAC,UAAU;AACb,aAAOC,KAAG,QAAQ,aAAa,MAAM,EAAE;AAAA,IAC7C,OAAW;AACLA,WAAG,SAAS,MAAM,CAACW,MAAK,iBAAiB;AACvC,YAAIA,MAAK;AAIP,cAAIA,KAAI,SAAS,YAAYA,KAAI,SAAS,UAAW,QAAOX,KAAG,QAAQ,aAAa,MAAM,EAAE;AAC5F,iBAAO,GAAGW,IAAG;AAAA,QACvB;AACQ,YAAI,KAAK,aAAa;AACpB,yBAAeZ,OAAK,QAAQ,QAAQ,IAAG,GAAI,YAAY;AAAA,QACjE;AACQ,YAAI+C,OAAK,YAAY,aAAa,YAAY,GAAG;AAC/C,iBAAO,GAAG,IAAI,MAAM,gBAAgB,WAAW,mCAAmC,YAAY,IAAI,CAAC;AAAA,QAC7G;AAKQ,YAAI,SAAS,iBAAiBA,OAAK,YAAY,cAAc,WAAW,GAAG;AACzE,iBAAO,GAAG,IAAI,MAAM,qBAAqB,YAAY,WAAW,WAAW,IAAI,CAAC;AAAA,QAC1F;AACQ,eAAO,SAAS,aAAa,MAAM,EAAE;AAAA,MAC7C,CAAO;AAAA,IACP;AAAA,EACA,CAAG;AACH;AAEA,SAAS,SAAU,aAAa,MAAM,IAAI;AACxC9C,OAAG,OAAO,MAAM,SAAO;AACrB,QAAI,IAAK,QAAO,GAAG,GAAG;AACtB,WAAOA,KAAG,QAAQ,aAAa,MAAM,EAAE;AAAA,EAC3C,CAAG;AACH;AAEA,IAAA,SAAiBmB;ACrOjB,MAAMkB,MAAItB,eAAwB;AAClC,IAAAI,SAAiB;AAAA,EACf,MAAMkB,IAAEjB,MAAiB;AAC3B;ACHA,MAAMpB,OAAKe;AACX,MAAMhB,SAAOqB;AACb,MAAM,SAASC;AAEf,MAAM,YAAa,QAAQ,aAAa;AAExC,SAAS,SAAU,SAAS;AAC1B,QAAM,UAAU;AAAA,IACd;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AACE,UAAQ,QAAQ,OAAK;AACnB,YAAQ,CAAC,IAAI,QAAQ,CAAC,KAAKrB,KAAG,CAAC;AAC/B,QAAI,IAAI;AACR,YAAQ,CAAC,IAAI,QAAQ,CAAC,KAAKA,KAAG,CAAC;AAAA,EACnC,CAAG;AAED,UAAQ,eAAe,QAAQ,gBAAgB;AACjD;AAEA,SAASmE,SAAQ,GAAG,SAAS,IAAI;AAC/B,MAAI,YAAY;AAEhB,MAAI,OAAO,YAAY,YAAY;AACjC,SAAK;AACL,cAAU,CAAA;AAAA,EACd;AAEE,SAAO,GAAG,sBAAsB;AAChC,SAAO,YAAY,OAAO,GAAG,UAAU,iCAAiC;AACxE,SAAO,YAAY,OAAO,IAAI,YAAY,oCAAoC;AAC9E,SAAO,SAAS,2CAA2C;AAC3D,SAAO,YAAY,OAAO,SAAS,UAAU,kCAAkC;AAE/E,WAAS,OAAO;AAEhB,UAAQ,GAAG,SAAS,SAAS,GAAI,IAAI;AACnC,QAAI,IAAI;AACN,WAAK,GAAG,SAAS,WAAW,GAAG,SAAS,eAAe,GAAG,SAAS,YAC/D,YAAY,QAAQ,cAAc;AACpC;AACA,cAAM,OAAO,YAAY;AAEzB,eAAO,WAAW,MAAM,QAAQ,GAAG,SAAS,EAAE,GAAG,IAAI;AAAA,MAC7D;AAGM,UAAI,GAAG,SAAS,SAAU,MAAK;AAAA,IACrC;AAEI,OAAG,EAAE;AAAA,EACT,CAAG;AACH;AAaA,SAAS,QAAS,GAAG,SAAS,IAAI;AAChC,SAAO,CAAC;AACR,SAAO,OAAO;AACd,SAAO,OAAO,OAAO,UAAU;AAI/B,UAAQ,MAAM,GAAG,CAAC,IAAI,OAAO;AAC3B,QAAI,MAAM,GAAG,SAAS,UAAU;AAC9B,aAAO,GAAG,IAAI;AAAA,IACpB;AAGI,QAAI,MAAM,GAAG,SAAS,WAAW,WAAW;AAC1C,aAAO,YAAY,GAAG,SAAS,IAAI,EAAE;AAAA,IAC3C;AAEI,QAAI,MAAM,GAAG,eAAe;AAC1B,aAAO,MAAM,GAAG,SAAS,IAAI,EAAE;AAAA,IACrC;AAEI,YAAQ,OAAO,GAAG,CAAAtD,QAAM;AACtB,UAAIA,KAAI;AACN,YAAIA,IAAG,SAAS,UAAU;AACxB,iBAAO,GAAG,IAAI;AAAA,QACxB;AACQ,YAAIA,IAAG,SAAS,SAAS;AACvB,iBAAQ,YACJ,YAAY,GAAG,SAASA,KAAI,EAAE,IAC9B,MAAM,GAAG,SAASA,KAAI,EAAE;AAAA,QACtC;AACQ,YAAIA,IAAG,SAAS,UAAU;AACxB,iBAAO,MAAM,GAAG,SAASA,KAAI,EAAE;AAAA,QACzC;AAAA,MACA;AACM,aAAO,GAAGA,GAAE;AAAA,IAClB,CAAK;AAAA,EACL,CAAG;AACH;AAEA,SAAS,YAAa,GAAG,SAAS,IAAI,IAAI;AACxC,SAAO,CAAC;AACR,SAAO,OAAO;AACd,SAAO,OAAO,OAAO,UAAU;AAE/B,UAAQ,MAAM,GAAG,KAAO,SAAO;AAC7B,QAAI,KAAK;AACP,SAAG,IAAI,SAAS,WAAW,OAAO,EAAE;AAAA,IAC1C,OAAW;AACL,cAAQ,KAAK,GAAG,CAAC,KAAK,UAAU;AAC9B,YAAI,KAAK;AACP,aAAG,IAAI,SAAS,WAAW,OAAO,EAAE;AAAA,QAC9C,WAAmB,MAAM,eAAe;AAC9B,gBAAM,GAAG,SAAS,IAAI,EAAE;AAAA,QAClC,OAAe;AACL,kBAAQ,OAAO,GAAG,EAAE;AAAA,QAC9B;AAAA,MACA,CAAO;AAAA,IACP;AAAA,EACA,CAAG;AACH;AAEA,SAAS,gBAAiB,GAAG,SAAS,IAAI;AACxC,MAAI;AAEJ,SAAO,CAAC;AACR,SAAO,OAAO;AAEd,MAAI;AACF,YAAQ,UAAU,GAAG,GAAK;AAAA,EAC9B,SAAW,KAAK;AACZ,QAAI,IAAI,SAAS,UAAU;AACzB;AAAA,IACN,OAAW;AACL,YAAM;AAAA,IACZ;AAAA,EACA;AAEE,MAAI;AACF,YAAQ,QAAQ,SAAS,CAAC;AAAA,EAC9B,SAAW,KAAK;AACZ,QAAI,IAAI,SAAS,UAAU;AACzB;AAAA,IACN,OAAW;AACL,YAAM;AAAA,IACZ;AAAA,EACA;AAEE,MAAI,MAAM,eAAe;AACvB,cAAU,GAAG,SAAS,EAAE;AAAA,EAC5B,OAAS;AACL,YAAQ,WAAW,CAAC;AAAA,EACxB;AACA;AAEA,SAAS,MAAO,GAAG,SAAS,YAAY,IAAI;AAC1C,SAAO,CAAC;AACR,SAAO,OAAO;AACd,SAAO,OAAO,OAAO,UAAU;AAK/B,UAAQ,MAAM,GAAG,QAAM;AACrB,QAAI,OAAO,GAAG,SAAS,eAAe,GAAG,SAAS,YAAY,GAAG,SAAS,UAAU;AAClF,aAAO,GAAG,SAAS,EAAE;AAAA,IAC3B,WAAe,MAAM,GAAG,SAAS,WAAW;AACtC,SAAG,UAAU;AAAA,IACnB,OAAW;AACL,SAAG,EAAE;AAAA,IACX;AAAA,EACA,CAAG;AACH;AAEA,SAAS,OAAQ,GAAG,SAAS,IAAI;AAC/B,SAAO,CAAC;AACR,SAAO,OAAO;AACd,SAAO,OAAO,OAAO,UAAU;AAE/B,UAAQ,QAAQ,GAAG,CAAC,IAAI,UAAU;AAChC,QAAI,GAAI,QAAO,GAAG,EAAE;AAEpB,QAAI,IAAI,MAAM;AACd,QAAI;AAEJ,QAAI,MAAM,EAAG,QAAO,QAAQ,MAAM,GAAG,EAAE;AAEvC,UAAM,QAAQ,OAAK;AACjBsD,eAAOpE,OAAK,KAAK,GAAG,CAAC,GAAG,SAAS,CAAAc,QAAM;AACrC,YAAI,UAAU;AACZ;AAAA,QACV;AACQ,YAAIA,IAAI,QAAO,GAAG,WAAWA,GAAE;AAC/B,YAAI,EAAE,MAAM,GAAG;AACb,kBAAQ,MAAM,GAAG,EAAE;AAAA,QAC7B;AAAA,MACA,CAAO;AAAA,IACP,CAAK;AAAA,EACL,CAAG;AACH;AAKA,SAAS,WAAY,GAAG,SAAS;AAC/B,MAAI;AAEJ,YAAU,WAAW,CAAA;AACrB,WAAS,OAAO;AAEhB,SAAO,GAAG,sBAAsB;AAChC,SAAO,YAAY,OAAO,GAAG,UAAU,iCAAiC;AACxE,SAAO,SAAS,yBAAyB;AACzC,SAAO,YAAY,OAAO,SAAS,UAAU,kCAAkC;AAE/E,MAAI;AACF,SAAK,QAAQ,UAAU,CAAC;AAAA,EAC5B,SAAW,IAAI;AACX,QAAI,GAAG,SAAS,UAAU;AACxB;AAAA,IACN;AAGI,QAAI,GAAG,SAAS,WAAW,WAAW;AACpC,sBAAgB,GAAG,SAAS,EAAE;AAAA,IACpC;AAAA,EACA;AAEE,MAAI;AAEF,QAAI,MAAM,GAAG,eAAe;AAC1B,gBAAU,GAAG,SAAS,IAAI;AAAA,IAChC,OAAW;AACL,cAAQ,WAAW,CAAC;AAAA,IAC1B;AAAA,EACA,SAAW,IAAI;AACX,QAAI,GAAG,SAAS,UAAU;AACxB;AAAA,IACN,WAAe,GAAG,SAAS,SAAS;AAC9B,aAAO,YAAY,gBAAgB,GAAG,SAAS,EAAE,IAAI,UAAU,GAAG,SAAS,EAAE;AAAA,IACnF,WAAe,GAAG,SAAS,UAAU;AAC/B,YAAM;AAAA,IACZ;AACI,cAAU,GAAG,SAAS,EAAE;AAAA,EAC5B;AACA;AAEA,SAAS,UAAW,GAAG,SAAS,YAAY;AAC1C,SAAO,CAAC;AACR,SAAO,OAAO;AAEd,MAAI;AACF,YAAQ,UAAU,CAAC;AAAA,EACvB,SAAW,IAAI;AACX,QAAI,GAAG,SAAS,WAAW;AACzB,YAAM;AAAA,IACZ,WAAe,GAAG,SAAS,eAAe,GAAG,SAAS,YAAY,GAAG,SAAS,SAAS;AACjF,iBAAW,GAAG,OAAO;AAAA,IAC3B,WAAe,GAAG,SAAS,UAAU;AAC/B,YAAM;AAAA,IACZ;AAAA,EACA;AACA;AAEA,SAAS,WAAY,GAAG,SAAS;AAC/B,SAAO,CAAC;AACR,SAAO,OAAO;AACd,UAAQ,YAAY,CAAC,EAAE,QAAQ,OAAK,WAAWd,OAAK,KAAK,GAAG,CAAC,GAAG,OAAO,CAAC;AAExE,MAAI,WAAW;AAOb,UAAM,YAAY,KAAK,IAAG;AAC1B,OAAG;AACD,UAAI;AACF,cAAM,MAAM,QAAQ,UAAU,GAAG,OAAO;AACxC,eAAO;AAAA,MACf,QAAc;AAAA,MAAA;AAAA,IACd,SAAa,KAAK,QAAQ,YAAY;AAAA,EACtC,OAAS;AACL,UAAM,MAAM,QAAQ,UAAU,GAAG,OAAO;AACxC,WAAO;AAAA,EACX;AACA;AAEA,IAAA,WAAiBoE;AACjBA,SAAO,OAAO;AC3Sd,MAAM9B,MAAItB,eAAwB;AAClC,MAAM,SAASK;AAEf,IAAAgD,WAAiB;AAAA,EACf,QAAQ/B,IAAE,MAAM;AAAA,EAChB,YAAY,OAAO;AACrB;ACNA,MAAMA,MAAItB,eAAwB;AAClC,MAAMf,OAAKoB;AACX,MAAMrB,SAAOsB;AACb,MAAMgD,UAAQ/C;AACd,MAAM8C,WAASnB;AAEf,MAAM,WAAWZ,IAAE,SAASiC,UAAU,KAAK,UAAU;AACnD,aAAW,YAAY,WAAY;AAAA,EAAA;AACnCtE,OAAG,QAAQ,KAAK,CAAC,KAAK,UAAU;AAC9B,QAAI,IAAK,QAAOqE,QAAM,OAAO,KAAK,QAAQ;AAE1C,YAAQ,MAAM,IAAI,UAAQtE,OAAK,KAAK,KAAK,IAAI,CAAC;AAE9C,eAAU;AAEV,aAAS,aAAc;AACrB,YAAM,OAAO,MAAM,IAAG;AACtB,UAAI,CAAC,KAAM,QAAO,SAAQ;AAC1BqE,eAAO,OAAO,MAAM,CAAAzD,SAAO;AACzB,YAAIA,KAAK,QAAO,SAASA,IAAG;AAC5B,mBAAU;AAAA,MAClB,CAAO;AAAA,IACP;AAAA,EACA,CAAG;AACH,CAAC;AAED,SAAS,aAAc,KAAK;AAC1B,MAAI;AACJ,MAAI;AACF,YAAQX,KAAG,YAAY,GAAG;AAAA,EAC9B,QAAU;AACN,WAAOqE,QAAM,WAAW,GAAG;AAAA,EAC/B;AAEE,QAAM,QAAQ,UAAQ;AACpB,WAAOtE,OAAK,KAAK,KAAK,IAAI;AAC1BqE,aAAO,WAAW,IAAI;AAAA,EAC1B,CAAG;AACH;AAEA,IAAA,QAAiB;AAAA,EACf;AAAA,EACA,cAAc;AAAA,EACd;AAAA,EACA,UAAU;AACZ;AC7CA,MAAM/B,MAAItB,eAAwB;AAClC,MAAMhB,SAAOqB;AACb,MAAMpB,OAAKqB;AACX,MAAMgD,UAAQ/C;AAEd,SAAS,WAAYhB,OAAM,UAAU;AACnC,WAAS,WAAY;AACnBN,SAAG,UAAUM,OAAM,IAAI,SAAO;AAC5B,UAAI,IAAK,QAAO,SAAS,GAAG;AAC5B,eAAQ;AAAA,IACd,CAAK;AAAA,EACL;AAEEN,OAAG,KAAKM,OAAM,CAAC,KAAK,UAAU;AAC5B,QAAI,CAAC,OAAO,MAAM,OAAM,EAAI,QAAO,SAAQ;AAC3C,UAAM,MAAMP,OAAK,QAAQO,KAAI;AAC7BN,SAAG,KAAK,KAAK,CAACW,MAAK4D,WAAU;AAC3B,UAAI5D,MAAK;AAEP,YAAIA,KAAI,SAAS,UAAU;AACzB,iBAAO0D,QAAM,OAAO,KAAK,CAAA1D,SAAO;AAC9B,gBAAIA,KAAK,QAAO,SAASA,IAAG;AAC5B,qBAAQ;AAAA,UACpB,CAAW;AAAA,QACX;AACQ,eAAO,SAASA,IAAG;AAAA,MAC3B;AAEM,UAAI4D,OAAM,YAAW,EAAI,UAAQ;AAAA,WAC5B;AAGHvE,aAAG,QAAQ,KAAK,CAAAW,SAAO;AACrB,cAAIA,KAAK,QAAO,SAASA,IAAG;AAAA,QACtC,CAAS;AAAA,MACT;AAAA,IACA,CAAK;AAAA,EACL,CAAG;AACH;AAEA,SAAS,eAAgBL,OAAM;AAC7B,MAAI;AACJ,MAAI;AACF,YAAQN,KAAG,SAASM,KAAI;AAAA,EAC5B,QAAU;AAAA,EAAA;AACR,MAAI,SAAS,MAAM,SAAU;AAE7B,QAAM,MAAMP,OAAK,QAAQO,KAAI;AAC7B,MAAI;AACF,QAAI,CAACN,KAAG,SAAS,GAAG,EAAE,YAAW,GAAI;AAGnCA,WAAG,YAAY,GAAG;AAAA,IACxB;AAAA,EACA,SAAW,KAAK;AAEZ,QAAI,OAAO,IAAI,SAAS,SAAUqE,SAAM,WAAW,GAAG;AAAA,QACjD,OAAM;AAAA,EACf;AAEErE,OAAG,cAAcM,OAAM,EAAE;AAC3B;AAEA,IAAAA,SAAiB;AAAA,EACf,YAAY+B,IAAE,UAAU;AAAA,EACxB;AACF;AClEA,MAAMA,MAAItB,eAAwB;AAClC,MAAMhB,SAAOqB;AACb,MAAMpB,OAAKqB;AACX,MAAMgD,UAAQ/C;AACd,MAAM2C,eAAahB,aAA0B;AAE7C,SAAS,WAAY,SAAS,SAAS,UAAU;AAC/C,WAAS,SAAUuB,UAASC,UAAS;AACnCzE,SAAG,KAAKwE,UAASC,UAAS,SAAO;AAC/B,UAAI,IAAK,QAAO,SAAS,GAAG;AAC5B,eAAS,IAAI;AAAA,IACnB,CAAK;AAAA,EACL;AAEER,eAAW,SAAS,CAAC,KAAK,sBAAsB;AAC9C,QAAI,IAAK,QAAO,SAAS,GAAG;AAC5B,QAAI,kBAAmB,QAAO,SAAS,IAAI;AAC3CjE,SAAG,MAAM,SAAS,CAACW,SAAQ;AACzB,UAAIA,MAAK;AACP,QAAAA,KAAI,UAAUA,KAAI,QAAQ,QAAQ,SAAS,YAAY;AACvD,eAAO,SAASA,IAAG;AAAA,MAC3B;AAEM,YAAM,MAAMZ,OAAK,QAAQ,OAAO;AAChCkE,mBAAW,KAAK,CAACtD,MAAK,cAAc;AAClC,YAAIA,KAAK,QAAO,SAASA,IAAG;AAC5B,YAAI,UAAW,QAAO,SAAS,SAAS,OAAO;AAC/C0D,gBAAM,OAAO,KAAK,CAAA1D,SAAO;AACvB,cAAIA,KAAK,QAAO,SAASA,IAAG;AAC5B,mBAAS,SAAS,OAAO;AAAA,QACnC,CAAS;AAAA,MACT,CAAO;AAAA,IACP,CAAK;AAAA,EACL,CAAG;AACH;AAEA,SAAS,eAAgB,SAAS,SAAS;AACzC,QAAM,oBAAoBX,KAAG,WAAW,OAAO;AAC/C,MAAI,kBAAmB,QAAO;AAE9B,MAAI;AACFA,SAAG,UAAU,OAAO;AAAA,EACxB,SAAW,KAAK;AACZ,QAAI,UAAU,IAAI,QAAQ,QAAQ,SAAS,YAAY;AACvD,UAAM;AAAA,EACV;AAEE,QAAM,MAAMD,OAAK,QAAQ,OAAO;AAChC,QAAM,YAAYC,KAAG,WAAW,GAAG;AACnC,MAAI,UAAW,QAAOA,KAAG,SAAS,SAAS,OAAO;AAClDqE,UAAM,WAAW,GAAG;AAEpB,SAAOrE,KAAG,SAAS,SAAS,OAAO;AACrC;AAEA,IAAA0E,SAAiB;AAAA,EACf,YAAYrC,IAAE,UAAU;AAAA,EACxB;AACF;AC1DA,MAAMtC,SAAOgB;AACb,MAAMf,OAAKoB;AACX,MAAM6C,eAAa5C,aAA0B;AAwB7C,SAASsD,eAAc,SAAS,SAAS,UAAU;AACjD,MAAI5E,OAAK,WAAW,OAAO,GAAG;AAC5B,WAAOC,KAAG,MAAM,SAAS,CAAC,QAAQ;AAChC,UAAI,KAAK;AACP,YAAI,UAAU,IAAI,QAAQ,QAAQ,SAAS,eAAe;AAC1D,eAAO,SAAS,GAAG;AAAA,MAC3B;AACM,aAAO,SAAS,MAAM;AAAA,QACpB,OAAO;AAAA,QACP,OAAO;AAAA,MACf,CAAO;AAAA,IACP,CAAK;AAAA,EACL,OAAS;AACL,UAAM,SAASD,OAAK,QAAQ,OAAO;AACnC,UAAM,gBAAgBA,OAAK,KAAK,QAAQ,OAAO;AAC/C,WAAOkE,aAAW,eAAe,CAAC,KAAK,WAAW;AAChD,UAAI,IAAK,QAAO,SAAS,GAAG;AAC5B,UAAI,QAAQ;AACV,eAAO,SAAS,MAAM;AAAA,UACpB,OAAO;AAAA,UACP,OAAO;AAAA,QACjB,CAAS;AAAA,MACT,OAAa;AACL,eAAOjE,KAAG,MAAM,SAAS,CAACW,SAAQ;AAChC,cAAIA,MAAK;AACP,YAAAA,KAAI,UAAUA,KAAI,QAAQ,QAAQ,SAAS,eAAe;AAC1D,mBAAO,SAASA,IAAG;AAAA,UAC/B;AACU,iBAAO,SAAS,MAAM;AAAA,YACpB,OAAO;AAAA,YACP,OAAOZ,OAAK,SAAS,QAAQ,OAAO;AAAA,UAChD,CAAW;AAAA,QACX,CAAS;AAAA,MACT;AAAA,IACA,CAAK;AAAA,EACL;AACA;AAEA,SAAS6E,mBAAkB,SAAS,SAAS;AAC3C,MAAI;AACJ,MAAI7E,OAAK,WAAW,OAAO,GAAG;AAC5B,aAASC,KAAG,WAAW,OAAO;AAC9B,QAAI,CAAC,OAAQ,OAAM,IAAI,MAAM,iCAAiC;AAC9D,WAAO;AAAA,MACL,OAAO;AAAA,MACP,OAAO;AAAA,IACb;AAAA,EACA,OAAS;AACL,UAAM,SAASD,OAAK,QAAQ,OAAO;AACnC,UAAM,gBAAgBA,OAAK,KAAK,QAAQ,OAAO;AAC/C,aAASC,KAAG,WAAW,aAAa;AACpC,QAAI,QAAQ;AACV,aAAO;AAAA,QACL,OAAO;AAAA,QACP,OAAO;AAAA,MACf;AAAA,IACA,OAAW;AACL,eAASA,KAAG,WAAW,OAAO;AAC9B,UAAI,CAAC,OAAQ,OAAM,IAAI,MAAM,iCAAiC;AAC9D,aAAO;AAAA,QACL,OAAO;AAAA,QACP,OAAOD,OAAK,SAAS,QAAQ,OAAO;AAAA,MAC5C;AAAA,IACA;AAAA,EACA;AACA;AAEA,IAAA,iBAAiB;AAAA,EACjB,cAAE4E;AAAAA,EACF,kBAAEC;AACF;AChGA,MAAM5E,OAAKe;AAEX,SAAS8D,cAAa,SAAS,MAAM,UAAU;AAC7C,aAAY,OAAO,SAAS,aAAc,OAAO;AACjD,SAAQ,OAAO,SAAS,aAAc,QAAQ;AAC9C,MAAI,KAAM,QAAO,SAAS,MAAM,IAAI;AACpC7E,OAAG,MAAM,SAAS,CAAC,KAAK,UAAU;AAChC,QAAI,IAAK,QAAO,SAAS,MAAM,MAAM;AACrC,WAAQ,SAAS,MAAM,YAAW,IAAM,QAAQ;AAChD,aAAS,MAAM,IAAI;AAAA,EACvB,CAAG;AACH;AAEA,SAAS8E,kBAAiB,SAAS,MAAM;AACvC,MAAI;AAEJ,MAAI,KAAM,QAAO;AACjB,MAAI;AACF,YAAQ9E,KAAG,UAAU,OAAO;AAAA,EAChC,QAAU;AACN,WAAO;AAAA,EACX;AACE,SAAQ,SAAS,MAAM,YAAW,IAAM,QAAQ;AAClD;AAEA,IAAA,gBAAiB;AAAA,EACjB,aAAE6E;AAAAA,EACF,iBAAEC;AACF;AC5BA,MAAMzC,MAAItB,eAAwB;AAClC,MAAMhB,SAAOqB;AACb,MAAMpB,OAAKqB;AACX,MAAM,UAAUC;AAChB,MAAM,SAAS,QAAQ;AACvB,MAAM,aAAa,QAAQ;AAE3B,MAAM,gBAAgB2B;AACtB,MAAM,eAAe,cAAc;AACnC,MAAM,mBAAmB,cAAc;AAEvC,MAAM,eAAeiB;AACrB,MAAM,cAAc,aAAa;AACjC,MAAM,kBAAkB,aAAa;AAErC,MAAMD,eAAac,aAA0B;AAE7C,SAAS,cAAe,SAAS,SAAS,MAAM,UAAU;AACxD,aAAY,OAAO,SAAS,aAAc,OAAO;AACjD,SAAQ,OAAO,SAAS,aAAc,QAAQ;AAE9Cd,eAAW,SAAS,CAAC,KAAK,sBAAsB;AAC9C,QAAI,IAAK,QAAO,SAAS,GAAG;AAC5B,QAAI,kBAAmB,QAAO,SAAS,IAAI;AAC3C,iBAAa,SAAS,SAAS,CAACtD,MAAK,aAAa;AAChD,UAAIA,KAAK,QAAO,SAASA,IAAG;AAC5B,gBAAU,SAAS;AACnB,kBAAY,SAAS,OAAO,MAAM,CAACA,MAAKqE,UAAS;AAC/C,YAAIrE,KAAK,QAAO,SAASA,IAAG;AAC5B,cAAM,MAAMZ,OAAK,QAAQ,OAAO;AAChCkE,qBAAW,KAAK,CAACtD,MAAK,cAAc;AAClC,cAAIA,KAAK,QAAO,SAASA,IAAG;AAC5B,cAAI,UAAW,QAAOX,KAAG,QAAQ,SAAS,SAASgF,OAAM,QAAQ;AACjE,iBAAO,KAAK,CAAArE,SAAO;AACjB,gBAAIA,KAAK,QAAO,SAASA,IAAG;AAC5BX,iBAAG,QAAQ,SAAS,SAASgF,OAAM,QAAQ;AAAA,UACvD,CAAW;AAAA,QACX,CAAS;AAAA,MACT,CAAO;AAAA,IACP,CAAK;AAAA,EACL,CAAG;AACH;AAEA,SAAS,kBAAmB,SAAS,SAAS,MAAM;AAClD,QAAM,oBAAoBhF,KAAG,WAAW,OAAO;AAC/C,MAAI,kBAAmB,QAAO;AAE9B,QAAM,WAAW,iBAAiB,SAAS,OAAO;AAClD,YAAU,SAAS;AACnB,SAAO,gBAAgB,SAAS,OAAO,IAAI;AAC3C,QAAM,MAAMD,OAAK,QAAQ,OAAO;AAChC,QAAM,SAASC,KAAG,WAAW,GAAG;AAChC,MAAI,OAAQ,QAAOA,KAAG,YAAY,SAAS,SAAS,IAAI;AACxD,aAAW,GAAG;AACd,SAAOA,KAAG,YAAY,SAAS,SAAS,IAAI;AAC9C;AAEA,IAAAiF,YAAiB;AAAA,EACf,eAAe5C,IAAE,aAAa;AAAA,EAC9B;AACF;AC5DA,MAAM,OAAOtB;AACb,MAAM,OAAOK;AACb,MAAM,UAAUC;AAEhB,IAAA,SAAiB;AAAA;AAAA,EAEf,YAAY,KAAK;AAAA,EACjB,gBAAgB,KAAK;AAAA,EACrB,YAAY,KAAK;AAAA,EACjB,gBAAgB,KAAK;AAAA;AAAA,EAErB,YAAY,KAAK;AAAA,EACjB,gBAAgB,KAAK;AAAA,EACrB,YAAY,KAAK;AAAA,EACjB,gBAAgB,KAAK;AAAA;AAAA,EAErB,eAAe,QAAQ;AAAA,EACvB,mBAAmB,QAAQ;AAAA,EAC3B,eAAe,QAAQ;AAAA,EACvB,mBAAmB,QAAQ;AAC7B;ACtBA,SAAS6D,YAAW,KAAK,EAAE,MAAM,MAAM,WAAW,MAAM,WAAW,MAAM,OAAM,IAAK,CAAA,GAAI;AACtF,QAAM,MAAM,WAAW,MAAM;AAC7B,QAAM,MAAM,KAAK,UAAU,KAAK,UAAU,MAAM;AAEhD,SAAO,IAAI,QAAQ,OAAO,GAAG,IAAI;AACnC;AAEA,SAASC,WAAU,SAAS;AAE1B,MAAI,OAAO,SAAS,OAAO,EAAG,WAAU,QAAQ,SAAS,MAAM;AAC/D,SAAO,QAAQ,QAAQ,WAAW,EAAE;AACtC;AAEA,IAAA,QAAiB,EAAA,WAAED,uBAAWC,WAAQ;ACbtC,IAAI;AACJ,IAAI;AACF,QAAMpE;AACR,SAAS,GAAG;AACV,QAAMK;AACR;AACA,MAAM,eAAeC;AACrB,MAAM,EAAA,WAAE6D,aAAW,aAAa5D;AAEhC,eAAe,UAAWhB,OAAM,UAAU,IAAI;AAC5C,MAAI,OAAO,YAAY,UAAU;AAC/B,cAAU,EAAE,UAAU,QAAO;AAAA,EACjC;AAEE,QAAMN,MAAK,QAAQ,MAAM;AAEzB,QAAM,cAAc,YAAY,UAAU,QAAQ,SAAS;AAE3D,MAAI,OAAO,MAAM,aAAa,aAAaA,IAAG,QAAQ,EAAEM,OAAM,OAAO;AAErE,SAAO,SAAS,IAAI;AAEpB,MAAI;AACJ,MAAI;AACF,UAAM,KAAK,MAAM,MAAM,UAAU,QAAQ,UAAU,IAAI;AAAA,EAC3D,SAAW,KAAK;AACZ,QAAI,aAAa;AACf,UAAI,UAAU,GAAGA,KAAI,KAAK,IAAI,OAAO;AACrC,YAAM;AAAA,IACZ,OAAW;AACL,aAAO;AAAA,IACb;AAAA,EACA;AAEE,SAAO;AACT;AAEA,MAAM,WAAW,aAAa,YAAY,SAAS;AAEnD,SAAS,aAAcA,OAAM,UAAU,IAAI;AACzC,MAAI,OAAO,YAAY,UAAU;AAC/B,cAAU,EAAE,UAAU,QAAO;AAAA,EACjC;AAEE,QAAMN,MAAK,QAAQ,MAAM;AAEzB,QAAM,cAAc,YAAY,UAAU,QAAQ,SAAS;AAE3D,MAAI;AACF,QAAI,UAAUA,IAAG,aAAaM,OAAM,OAAO;AAC3C,cAAU,SAAS,OAAO;AAC1B,WAAO,KAAK,MAAM,SAAS,QAAQ,OAAO;AAAA,EAC9C,SAAW,KAAK;AACZ,QAAI,aAAa;AACf,UAAI,UAAU,GAAGA,KAAI,KAAK,IAAI,OAAO;AACrC,YAAM;AAAA,IACZ,OAAW;AACL,aAAO;AAAA,IACb;AAAA,EACA;AACA;AAEA,eAAe,WAAYA,OAAM,KAAK,UAAU,CAAA,GAAI;AAClD,QAAMN,MAAK,QAAQ,MAAM;AAEzB,QAAM,MAAMkF,YAAU,KAAK,OAAO;AAElC,QAAM,aAAa,aAAalF,IAAG,SAAS,EAAEM,OAAM,KAAK,OAAO;AAClE;AAEA,MAAM,YAAY,aAAa,YAAY,UAAU;AAErD,SAAS,cAAeA,OAAM,KAAK,UAAU,CAAA,GAAI;AAC/C,QAAMN,MAAK,QAAQ,MAAM;AAEzB,QAAM,MAAMkF,YAAU,KAAK,OAAO;AAElC,SAAOlF,IAAG,cAAcM,OAAM,KAAK,OAAO;AAC5C;AAEA,MAAM8E,aAAW;AAAA,EACf;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAEA,IAAA,aAAiBA;ACrFjB,MAAMC,aAAWtE;AAEjB,IAAA,WAAiB;AAAA;AAAA,EAEf,UAAUsE,WAAS;AAAA,EACnB,cAAcA,WAAS;AAAA,EACvB,WAAWA,WAAS;AAAA,EACpB,eAAeA,WAAS;AAC1B;ACRA,MAAMhD,MAAItB,eAAwB;AAClC,MAAMf,OAAKoB;AACX,MAAMrB,SAAOsB;AACb,MAAM,QAAQC;AACd,MAAM2C,eAAahB,aAA0B;AAE7C,SAASqC,aAAYhF,OAAM,MAAM,UAAU,UAAU;AACnD,MAAI,OAAO,aAAa,YAAY;AAClC,eAAW;AACX,eAAW;AAAA,EACf;AAEE,QAAM,MAAMP,OAAK,QAAQO,KAAI;AAC7B2D,eAAW,KAAK,CAAC,KAAK,WAAW;AAC/B,QAAI,IAAK,QAAO,SAAS,GAAG;AAC5B,QAAI,OAAQ,QAAOjE,KAAG,UAAUM,OAAM,MAAM,UAAU,QAAQ;AAE9D,UAAM,OAAO,KAAK,CAAAK,SAAO;AACvB,UAAIA,KAAK,QAAO,SAASA,IAAG;AAE5BX,WAAG,UAAUM,OAAM,MAAM,UAAU,QAAQ;AAAA,IACjD,CAAK;AAAA,EACL,CAAG;AACH;AAEA,SAASiF,iBAAgBjF,UAAS,MAAM;AACtC,QAAM,MAAMP,OAAK,QAAQO,KAAI;AAC7B,MAAIN,KAAG,WAAW,GAAG,GAAG;AACtB,WAAOA,KAAG,cAAcM,OAAM,GAAG,IAAI;AAAA,EACzC;AACE,QAAM,WAAW,GAAG;AACpBN,OAAG,cAAcM,OAAM,GAAG,IAAI;AAChC;AAEA,IAAA,SAAiB;AAAA,EACf,YAAY+B,IAAEiD,YAAU;AAAA,EAC1B,gBAAEC;AACF;ACrCA,MAAM,EAAA,WAAEL,YAAS,IAAKnE;AACtB,MAAM,EAAE,WAAU,IAAKK;AAEvB,eAAe,WAAYd,OAAM,MAAM,UAAU,CAAA,GAAI;AACnD,QAAM,MAAM4E,YAAU,MAAM,OAAO;AAEnC,QAAM,WAAW5E,OAAM,KAAK,OAAO;AACrC;AAEA,IAAA,eAAiB;ACTjB,MAAM,EAAE,UAAS,IAAKS;AACtB,MAAM,EAAE,eAAc,IAAKK;AAE3B,SAAS,eAAgBd,OAAM,MAAM,SAAS;AAC5C,QAAM,MAAM,UAAU,MAAM,OAAO;AAEnC,iBAAeA,OAAM,KAAK,OAAO;AACnC;AAEA,IAAA,mBAAiB;ACTjB,MAAM+B,MAAItB,eAAwB;AAClC,MAAM,WAAWK;AAEjB,SAAS,aAAaiB,IAAEhB,YAAwB;AAChD,SAAS,iBAAiBC;AAE1B,SAAS,aAAa,SAAS;AAC/B,SAAS,iBAAiB,SAAS;AACnC,SAAS,YAAY,SAAS;AAC9B,SAAS,gBAAgB,SAAS;AAClC,SAAS,WAAW,SAAS;AAC7B,SAAS,eAAe,SAAS;AAEjC,IAAA,OAAiB;ACbjB,MAAMtB,OAAKe;AACX,MAAMhB,SAAOqB;AACb,MAAM,WAAWC,WAAwB;AACzC,MAAM,aAAaC,SAAqB;AACxC,MAAM,aAAa2B,SAAqB;AACxC,MAAMH,SAAOoB;AAEb,SAASsB,WAAU,KAAK,MAAM,MAAM;AAClC,SAAO,QAAQ,CAAA;AACf,QAAM,YAAY,KAAK,aAAa,KAAK,WAAW;AAEpD,QAAM,EAAE,QAAO,IAAK1C,OAAK,eAAe,KAAK,MAAM,MAAM;AACzDA,SAAK,qBAAqB,KAAK,SAAS,MAAM,MAAM;AACpD,aAAW/C,OAAK,QAAQ,IAAI,CAAC;AAC7B,SAAO0F,WAAS,KAAK,MAAM,SAAS;AACtC;AAEA,SAASA,WAAU,KAAK,MAAM,WAAW;AACvC,MAAI,WAAW;AACb,eAAW,IAAI;AACf,WAAO/E,SAAO,KAAK,MAAM,SAAS;AAAA,EACtC;AACE,MAAIV,KAAG,WAAW,IAAI,EAAG,OAAM,IAAI,MAAM,sBAAsB;AAC/D,SAAOU,SAAO,KAAK,MAAM,SAAS;AACpC;AAEA,SAASA,SAAQ,KAAK,MAAM,WAAW;AACrC,MAAI;AACFV,SAAG,WAAW,KAAK,IAAI;AAAA,EAC3B,SAAW,KAAK;AACZ,QAAI,IAAI,SAAS,QAAS,OAAM;AAChC,WAAO0F,mBAAiB,KAAK,MAAM,SAAS;AAAA,EAChD;AACA;AAEA,SAASA,mBAAkB,KAAK,MAAM,WAAW;AAC/C,QAAM,OAAO;AAAA,IACX;AAAA,IACA,cAAc;AAAA,EAClB;AACE,WAAS,KAAK,MAAM,IAAI;AACxB,SAAO,WAAW,GAAG;AACvB;AAEA,IAAA,aAAiBF;AC5CjB,IAAA,WAAiB;AAAA,EACf,UAAUzE;AACZ;ACFA,MAAMf,OAAKe;AACX,MAAM,OAAOK;AACb,MAAM,OAAOC,OAAmB;AAChC,MAAM,SAASC,SAAqB;AACpC,MAAM,SAAS2B,SAAqB;AACpC,MAAM,aAAaiB,aAA0B;AAC7C,MAAM,OAAOa;AAEb,SAASY,OAAM,KAAK,MAAM,MAAM,IAAI;AAClC,MAAI,OAAO,SAAS,YAAY;AAC9B,SAAK;AACL,WAAO,CAAA;AAAA,EACX;AAEE,QAAM,YAAY,KAAK,aAAa,KAAK,WAAW;AAEpD,OAAK,WAAW,KAAK,MAAM,QAAQ,CAAC,KAAK,UAAU;AACjD,QAAI,IAAK,QAAO,GAAG,GAAG;AACtB,UAAM,EAAE,QAAO,IAAK;AACpB,SAAK,iBAAiB,KAAK,SAAS,MAAM,QAAQ,CAAAhF,SAAO;AACvD,UAAIA,KAAK,QAAO,GAAGA,IAAG;AACtB,aAAO,KAAK,QAAQ,IAAI,GAAG,CAAAA,SAAO;AAChC,YAAIA,KAAK,QAAO,GAAGA,IAAG;AACtB,eAAO,SAAS,KAAK,MAAM,WAAW,EAAE;AAAA,MAChD,CAAO;AAAA,IACP,CAAK;AAAA,EACL,CAAG;AACH;AAEA,SAAS,SAAU,KAAK,MAAM,WAAW,IAAI;AAC3C,MAAI,WAAW;AACb,WAAO,OAAO,MAAM,SAAO;AACzB,UAAI,IAAK,QAAO,GAAG,GAAG;AACtB,aAAO,OAAO,KAAK,MAAM,WAAW,EAAE;AAAA,IAC5C,CAAK;AAAA,EACL;AACE,aAAW,MAAM,CAAC,KAAK,eAAe;AACpC,QAAI,IAAK,QAAO,GAAG,GAAG;AACtB,QAAI,WAAY,QAAO,GAAG,IAAI,MAAM,sBAAsB,CAAC;AAC3D,WAAO,OAAO,KAAK,MAAM,WAAW,EAAE;AAAA,EAC1C,CAAG;AACH;AAEA,SAAS,OAAQ,KAAK,MAAM,WAAW,IAAI;AACzCX,OAAG,OAAO,KAAK,MAAM,SAAO;AAC1B,QAAI,CAAC,IAAK,QAAO,GAAE;AACnB,QAAI,IAAI,SAAS,QAAS,QAAO,GAAG,GAAG;AACvC,WAAO,iBAAiB,KAAK,MAAM,WAAW,EAAE;AAAA,EACpD,CAAG;AACH;AAEA,SAAS,iBAAkB,KAAK,MAAM,WAAW,IAAI;AACnD,QAAM,OAAO;AAAA,IACX;AAAA,IACA,cAAc;AAAA,EAClB;AACE,OAAK,KAAK,MAAM,MAAM,SAAO;AAC3B,QAAI,IAAK,QAAO,GAAG,GAAG;AACtB,WAAO,OAAO,KAAK,EAAE;AAAA,EACzB,CAAG;AACH;AAEA,IAAA,SAAiB2F;AC9DjB,MAAM,IAAI5E,eAAwB;AAClC,IAAA,OAAiB;AAAA,EACf,MAAM,EAAEK,MAAiB;AAC3B;AAAA;ACHA,SAAA,UAAiB;AAAA;AAAA,IAEf,GAAGL;AAAAA;AAAAA,IAEH,GAAGK;AAAAA,IACH,GAAGC;AAAAA,IACH,GAAGC;AAAAA,IACH,GAAG2B;AAAAA,IACH,GAAGiB;AAAAA,IACH,GAAGa;AAAAA,IACH,GAAGa;AAAAA,IACH,GAAGC;AAAAA,IACH,GAAGC;AAAAA,IACH,GAAGC;AAAAA,IACH,GAAGC;AAAAA;AAKL,QAAMhG,MAAKiG;AACX,MAAI,OAAO,yBAAyBjG,KAAI,UAAU,GAAG;AACnD,WAAO,eAAe,OAAO,SAAS,YAAY;AAAA,MAChD,MAAO;AAAE,eAAOA,IAAG;AAAA,MAAQ;AAAA,IAC/B,CAAG;AAAA,EACH;;;;ACtBA,eAAsB,gBAAgB,aAAqB,aAAqB,sBAA8B;AAC7G,QAAM,UAAU,CAAC,iBAAiB,WAAW;AAC7C,QAAM,aAAa;AAEnB,aAAW,OAAO,SAAS;AAC1B,UAAM,MAAMD,OAAK,KAAK,aAAa,GAAG;AACtC,UAAM,OAAOA,OAAK,KAAK,sBAAsB,aAAa,GAAG;AAC7D,QAAI,GAAG,WAAW,GAAG,GAAG;AACvB,YAAM,GAAG,KAAK,KAAK,MAAM,EAAE,WAAW,MAAM;AAC5C,cAAQ,IAAI,IAAI,WAAW,UAAU,GAAG,UAAU;AAAA,IACnD;AAAA,EACD;AAGA,QAAM,YAAYA,OAAK,KAAK,aAAa,UAAU;AACnD,QAAM,aAAaA,OAAK,KAAK,sBAAsB,aAAa,UAAU;AAC1E,MAAI,GAAG,WAAW,SAAS,GAAG;AAC7B,UAAM,GAAG,KAAK,WAAW,YAAY,EAAE,WAAW,MAAM;AACxD,YAAQ,IAAI,IAAI,WAAW,wBAAwB;AAAA,EACpD;AACD;ACLA,MAAM,WAAW,IAAI,QAAQ,UAAU;AACvC,MAAM,cAAcA,OAAK,KAAK,UAAU,aAAa;AAGrD,MAAM,YAAYA,OAAK,QAAQ,cAAc,YAAY,GAAG,CAAC;AAC7D,MAAM,YAAYA,OAAK,KAAK,WAAW,OAAO;AAC9C,MAAM,gBAAgBA,OAAK,KAAK,WAAW,MAAM;AACjD,MAAM,sBAAsB,QAAA,IAAY;AACxC,MAAM,cAAc,sBAChBA,OAAK,KAAK,WAAW,QAAQ,IAC7B;AAGJ,IAAI,MAA4B;AAChC,IAAI,iBAAwC;AAC5C,IAAI,aAAgC;AACpC,IAAI,iBAAwD;AAC5D,IAAI,cAAsB;AAC1B,IAAI,eAAe;AAGnB,MAAM,UAAUA,OAAK,KAAK,WAAW,sBAAsB;AAC3D,MAAM,YAAYA,OAAK,KAAK,eAAe,YAAY;AACvD,MAAM,UAAU,IAAI,WAAW,KAAK,UAAU;AAG9C,kBAAkB,YAAY,EAAE,KAAK,CAAA,SAAQ;AAC3C,iBAAe;AACf,MAAI,YAAY,aAAa,yBAAyB,OAAO,EAAE;AACjE,CAAC;AAGD,eAAe,8BAAgD;AAC7D,QAAM,iBAAiB,IAAI,WAAA;AAC3B,SAAO,IAAI,QAAQ,OAAO,SAAS,WAAW;AAC5C,QAAI;AACF,UAAI,KAAK,wBAAwB,EAAE,eAAA,CAAgB;AAGnD,YAAM,gBAAgBC,KAAG,WAAW,WAAW;AAC/C,UAAI,eAAe;AAEnB,UAAI,eAAe;AACjB,uBAAeA,KAAG,aAAa,aAAa,OAAO,EAAE,KAAA;AACrD,YAAI,KAAK,uBAAuB,EAAE,aAAA,CAAc;AAAA,MAClD,OAAO;AACL,YAAI,KAAK,+CAA+C;AAAA,MAC1D;AAGA,UAAI,CAAC,iBAAiB,iBAAiB,gBAAgB;AACrD,YAAI,KAAK,6DAA6D;AAAA,UACpE;AAAA,UACA,cAAc,gBAAgB,eAAe;AAAA,UAC7C,QAAQ,CAAC,gBAAgB,2BAA2B;AAAA,QAAA,CACrD;AAGD,YAAI,OAAO,CAAC,IAAI,eAAe;AAC7B,cAAI,YAAY,KAAK,uBAAuB;AAAA,YAC1C,MAAM;AAAA,YACN;AAAA,YACA,iBAAiB,gBAAgB,eAAe;AAAA,YAChD,QAAQ,CAAC,gBAAgB,2BAA2B;AAAA,UAAA,CACrD;AAAA,QACH;AAGAA,aAAG,cAAc,aAAa,cAAc;AAC5C,YAAI,KAAK,yBAAyB,EAAE,eAAA,CAAgB;AAGpD,cAAM,SAAS,MAAM,oBAAA;AACrB,YAAI,CAAC,QAAQ;AACX,cAAI,MAAM,8BAA8B;AACxC,kBAAQ,KAAK;AACb;AAAA,QACF;AACA,gBAAQ,IAAI;AACZ,YAAI,KAAK,gCAAgC;AACzC;AAAA,MACF,OAAO;AACL,YAAI,KAAK,mDAAmD,EAAE,eAAA,CAAgB;AAC9E,gBAAQ,IAAI;AACZ;AAAA,MACF;AAAA,IACF,SAAS,OAAO;AACd,UAAI,MAAM,kDAAkD,KAAK;AACjE,cAAQ,KAAK;AACb;AAAA,IACF;AAAA,EACF,CAAC;AACH;AAKA,QAAA,IAAY,WAAW;AACvB,QAAA,IAAY,cAAc;AAG1B,YAAY,cAAc;AAG1B,IAAI,WAAW,QAAQ,QAAQ;AAC/B,IAAI,WAAW,KAAK,QAAQ;AAG5B,IAAIkG,KAAG,QAAA,EAAU,WAAW,KAAK,OAAO,4BAAA;AAGxC,IAAI,QAAQ,aAAa,aAAa,kBAAkB,IAAI,SAAS;AAErE,IAAI,CAAC,IAAI,6BAA6B;AACpC,MAAI,KAAA;AACJ,UAAQ,KAAK,CAAC;AAChB;AAGA,MAAM,wBAAwB,MAAM;AAClC,MAAI,QAAA,IAAY,aAAa,eAAe;AAC1C,UAAM,YAAY,IAAI,wBAAwB,UAAU,QAAQ,UAAU,CAACnG,OAAK,QAAQ,QAAQ,KAAK,CAAC,CAAC,CAAC,CAAC;AACzG,QAAI,CAAC,WAAW;AACd,UAAI,2BAA2B,UAAU,QAAQ,UAAU,CAACA,OAAK,QAAQ,QAAQ,KAAK,CAAC,CAAC,CAAC,CAAC;AAAA,IAC5F;AAAA,EACF,OAAO;AACL,QAAI,2BAA2B,QAAQ;AAAA,EACzC;AACF;AAGA,SAAS,kBAAkB,KAAa;AACtC,MAAI,KAAK,2BAA2B,GAAG;AACvC,QAAM,SAAS,IAAI,IAAI,GAAG;AAC1B,QAAM,OAAO,OAAO,aAAa,IAAI,MAAM;AAC3C,QAAM,cAAc,OAAO,aAAa,IAAI,aAAa;AAEzD,MAAI,KAAK,UAAU,MAAM;AACzB,MAAI,KAAK,QAAQ,IAAI;AACrB,MAAI,KAAK,eAAe,WAAW;AAEnC,MAAI,OAAO,CAAC,IAAI,eAAe;AAC7B,QAAI,KAAK,mBAAmB,OAAO,QAAQ;AAE3C,QAAI,OAAO,aAAa,UAAU;AAChC,UAAI,KAAK,OAAO;AAChB,YAAM,WAAW,OAAO,aAAa,IAAI,UAAU;AACnD,YAAMoG,QAAO,OAAO,aAAa,IAAI,MAAM;AAC3C,UAAI,KAAK,kBAAkB,UAAUA,KAAI;AACzC,UAAI,YAAY,KAAK,oBAAoB,EAAE,UAAU,MAAAA,OAAM;AAC3D;AAAA,IACF;AAEA,QAAI,MAAM;AACR,UAAI,MAAM,kBAAkB,IAAI;AAChC,UAAI,YAAY,KAAK,sBAAsB,IAAI;AAAA,IACjD;AAEA,QAAI,aAAa;AACf,UAAI,YAAY,KAAK,6BAA6B,WAAW;AAAA,IAC/D;AAAA,EACF,OAAO;AACL,QAAI,MAAM,sBAAsB;AAAA,EAClC;AACF;AAGA,MAAM,0BAA0B,MAAM;AACpC,QAAM,UAAU,IAAI,0BAAA;AACpB,MAAI,CAAC,SAAS;AACZ,QAAI,KAAK,SAAS;AAClB,QAAI,KAAA;AAAA,EACN,OAAO;AACL,QAAI,GAAG,mBAAmB,CAAC,OAAO,SAAS;AACzC,UAAI,KAAK,mBAAmB,IAAI;AAChC,YAAM,MAAM,KAAK,KAAK,SAAO,IAAI,WAAW,WAAW,CAAC;AACxD,UAAI,uBAAuB,GAAG;AAC9B,UAAI,SAAS,KAAA;AAAA,IACf,CAAC;AAED,QAAI,GAAG,YAAY,CAAC,OAAO,QAAQ;AACjC,UAAI,KAAK,UAAU;AACnB,YAAM,eAAA;AACN,wBAAkB,GAAG;AAAA,IACvB,CAAC;AAAA,EACH;AACF;AAGA,MAAM,gBAAgB,MAAM;AAC1B,wBAAA;AACA,0BAAA;AAEF;AAQA,MAAM,mBAAmB,MAAM;AAC7B,QAAM,eAAe,IAAI,QAAQ,UAAU;AAC3C,SAAOpG,OAAK,KAAK,cAAc,QAAQ,UAAU;AACnD;AAEA,MAAM,gBAAgB;AAAA,EACpB,OAAO;AAAA,IACL,QAAQ;AAAA,IACR,MAAM;AAAA,IACN,SAAS;AAAA,IACT,IAAI;AAAA,IACJ,OAAOA,OAAK,KAAK,QAAA,GAAW,+DAA+D;AAAA,IAC3F,KAAKA,OAAK,KAAK,QAAA,GAAW,yCAAyC;AAAA,IACnE,KAAKA,OAAK,KAAK,QAAA,GAAW,2CAA2C;AAAA,IACrE,QAAQA,OAAK,KAAK,QAAA,GAAW,iDAAiD;AAAA,EAAA;AAAA,EAEhF,QAAQ;AAAA,IACN,QAAQ;AAAA,IACR,MAAM;AAAA,IACN,SAAS;AAAA,IACT,QAAQ;AAAA,IACR,KAAK;AAAA,IACL,KAAK;AAAA,IACL,QAAQ;AAAA,EAAA;AAEZ;AAGA,MAAM,oBAAoB,YAAY;AACpC,QAAM,SAAS,IAAI,UAAA;AACnB,SAAO,WAAW,UAAU,UAAU;AACxC;AAEA,MAAM,uBAAuB,CAAC,SAAc,SAAiB;AAC3D,MAAI,CAAC,SAAS;AACZ,UAAM,IAAI,MAAM,GAAG,IAAI,kBAAkB;AAAA,EAC3C;AACA,SAAO;AACT;AAEA,MAAM,+BAA+B,YAAY;AAC/C,MAAI;AACF,QAAI,KAAK,gCAAgC;AAEzC,UAAM,YAAY,MAAM,oBAAA;AACxB,QAAI,CAAC,WAAW;AACd,UAAI,MAAM,8BAA8B;AACxC,aAAO,EAAE,SAAS,OAAO,OAAO,8BAAA;AAAA,IAClC;AAEA,QAAI,KAAK,+DAA+D;AACxE,UAAM,kBAAkB,MAAM,mBAAA;AAE9B,QAAI,mBAAmB,CAAC,gBAAgB;AACtC,UAAI,KAAK,2CAA2C;AACpD,uBAAiB,MAAM,aAAa,CAAC,SAAS;AAC5C,sBAAc;AACd,YAAI,KAAK,kCAAkC,EAAE,KAAA,CAAM;AAAA,MACrD,CAAC;AAGD,UAAI,OAAO,CAAC,IAAI,eAAe;AAC7B,YAAI,YAAY,KAAK,iCAAiC,EAAE,SAAS,MAAM,MAAM,GAAG;AAAA,MAClF;AAEA,uDAAgB,GAAG,QAAQ,CAAC,MAAM,WAAW;AAC3C,YAAI,KAAK,wBAAwB,EAAE,MAAM,QAAQ;AAAA,MACnD;AAAA,IACF,WAAW,CAAC,iBAAiB;AAC3B,UAAI,KAAK,yCAAyC;AAAA,IACpD,OAAO;AACL,UAAI,KAAK,4CAA4C;AAAA,IACvD;AAEA,QAAI,KAAK,gCAAgC;AACzC,WAAO,EAAE,SAAS,KAAA;AAAA,EACpB,SAAS,OAAY;AACnB,QAAI,MAAM,gCAAgC,KAAK;AAC/C,QAAI,OAAO,CAAC,IAAI,eAAe;AAC7B,UAAI,YAAY,KAAK,iCAAiC,EAAE,SAAS,OAAO,MAAM,GAAG;AAAA,IACnF;AACA,WAAO,EAAE,SAAS,OAAO,OAAO,MAAM,QAAA;AAAA,EACxC;AACF;AAEA,SAAS,sBAAsB;AAE7B,UAAQ,OAAO,oBAAoB,MAAM,YAAY;AACrD,UAAQ,OAAO,mBAAmB,MAAM,IAAI,YAAY;AACxD,UAAQ,OAAO,oBAAoB,MAAM,WAAW;AACpD,UAAQ,OAAO,uBAAuB,iBAAiB;AACvD,UAAQ,OAAO,iBAAiB,OAAM,2BAAK,mBAAkB,KAAK;AAClE,UAAQ,OAAO,gBAAgB,MAAM;AACnC,UAAMqG,YAAW,QAAQ;AACzB,WAAOA,cAAa,UAAU,QAAA,IAAY,cAAc,QAAA,IAAY;AAAA,EACtE,CAAC;AAGD,UAAQ,OAAO,mBAAmB,OAAO,OAAO,SAAiB,UAAkB;AACjF,QAAI,KAAK,mBAAmB,OAAO;AACnC,UAAM,YAAY,MAAM,MAAM,GAAG,EAAE,CAAC,EAAE,QAAQ,mBAAmB,GAAG,EAAE,QAAQ,KAAK,GAAG;AACtF,UAAMC,kBAAiBtG,OAAK,KAAKmG,KAAG,QAAA,GAAW,SAAS;AACxD,UAAM,wBAAwBnG,OAAK,KAAKsG,iBAAgB,SAAS;AACjE,QAAI,CAACrG,KAAG,WAAW,qBAAqB,GAAG;AACzCA,WAAG,UAAU,uBAAuB,EAAE,WAAW,MAAM;AAAA,IACzD;AAEA,QAAI;AACF,YAAM,EAAE,OAAAsG,OAAA,IAAU,MAAM,OAAO,eAAe;AAG9C,YAAM,kBAAkB,GAAG,OAAO;AAGlC,UAAI,KAAK,2BAA2B,eAAe;AAGnD,YAAM,CAAC,KAAK,GAAG,IAAI,IAAI,gBAAgB,MAAM,GAAG;AAEhD,aAAO,IAAI,QAAQ,CAAC,YAAY;AAC9B,cAAM,QAAQA,OAAM,KAAK,MAAM;AAAA,UAC7B,KAAK,QAAQ,IAAA;AAAA,UACb,KAAK,EAAE,GAAG,QAAA,KAAa,sBAAA;AAAA,UACvB,OAAO,CAAC,QAAQ,QAAQ,MAAM;AAAA,QAAA,CAC/B;AAED,YAAI,SAAS;AACb,YAAI,SAAS;AAGb,cAAM,OAAO,GAAG,QAAQ,CAAC,SAAS;AAChC,gBAAMC,UAAS,KAAK,SAAA;AACpB,oBAAUA;AACV,cAAI,KAAK,qBAAqBA,QAAO,KAAA,CAAM;AAAA,QAC7C,CAAC;AAGD,cAAM,OAAO,GAAG,QAAQ,CAAC,SAAS;AAChC,gBAAMA,UAAS,KAAK,SAAA;AACpB,oBAAUA;AACV,cAAIA,QAAO,SAAS,kCAAkC,GAAG;AACvD,kBAAM,MAAMA,QAAO,MAAM,kCAAkC,EAAE,CAAC,EAAE,KAAA;AAChE,gBAAI,KAAK,+BAA+B,GAAG;AAG3C,gBAAI,OAAO,CAAC,IAAI,eAAe;AAC7B,oBAAM,QAAQ,IAAI,MAAM,yBAAyB;AACjD,oBAAM,aAAa,QAAQ,MAAM,CAAC,IAAI;AACtC,kBAAI,KAAK,cAAc,UAAU;AACjC,kBAAI,YAAY,KAAK,sBAAsB;AAAA,gBACzC,KAAK;AAAA,gBACL,UAAU;AAAA;AAAA,cAAA,CACX;AAAA,YAEH;AAAA,UACF;AACA,cAAIA,QAAO,SAAS,sBAAsB,GAAG;AAC3C,kBAAM,KAAA;AAAA,UACR;AACA,cAAI,KAAK,2BAA2BA,QAAO,KAAA,CAAM;AAAA,QACnD,CAAC;AAGD,cAAM,GAAG,SAAS,CAAC,SAAS;AAC1B,cAAI,KAAK,yCAAyC,IAAI,EAAE;AACxD,kBAAQ,EAAE,SAAS,SAAS,GAAG,QAAQ,QAAQ;AAAA,QACjD,CAAC;AAGD,cAAM,GAAG,SAAS,CAAC,UAAU;AAC3B,cAAI,MAAM,2BAA2B,KAAK;AAC1C,kBAAQ,EAAE,SAAS,OAAO,OAAO,MAAM,SAAS;AAAA,QAClD,CAAC;AAAA,MACH,CAAC;AAAA,IACH,SAAS,OAAY;AACnB,UAAI,MAAM,4BAA4B,KAAK;AAC3C,aAAO,EAAE,SAAS,OAAO,OAAO,MAAM,QAAA;AAAA,IACxC;AAAA,EACF,CAAC;AAGD,UAAQ,OAAO,cAAc,YAAY;AACvC,QAAI;AACF,UAAI,gBAAgB;AACpB,UAAI,CAACvG,KAAG,WAAW,aAAa,GAAG;AACjC,cAAM,aAAa,iBAAA;AACnB,YAAIA,KAAG,WAAW,UAAU,GAAG;AAC7B,0BAAgB;AAAA,QAClB,OAAO;AACL,iBAAO,EAAE,SAAS,OAAO,OAAO,cAAA;AAAA,QAClC;AAAA,MACF;AAEA,YAAM,IAAI,OAAO,eAAeA,KAAG,UAAU,IAAI;AACjD,YAAM,QAAQ,MAAM,IAAI,KAAK,aAAa;AAC1C,UAAI,MAAM,SAAS,GAAG;AACpB,eAAO,EAAE,SAAS,MAAM,MAAM,oBAAA;AAAA,MAChC;AAEA,YAAM,aAAa,MAAM,IAAI,SAAS,eAAe,OAAO;AAG5D,YAAM,aAAa,IAAI,WAAA;AACvB,YAAMoG,YAAW,QAAQ;AACzB,YAAM,OAAO,QAAQ;AACrB,YAAM,gBAAgB,GAAGA,SAAQ,IAAI,IAAI;AACzC,YAAM,kBAAkB,UAAU,UAAU,IAAI,aAAa;AAG7D,YAAM,EAAE,UAAU,SAAA,IAAa,MAAM,OAAO,eAAe;AAAA,QACzD,OAAO;AAAA,QACP,aAAa;AAAA,QACb,SAAS,CAAC,EAAE,MAAM,YAAY,YAAY,CAAC,OAAO,KAAK,EAAA,CAAG;AAAA,MAAA,CAC3D;AAED,UAAI,YAAY,CAAC,UAAU;AACzB,eAAO,EAAE,SAAS,OAAO,OAAO,GAAA;AAAA,MAClC;AAEA,YAAM,IAAI,UAAU,UAAU,YAAY,OAAO;AACjD,aAAO,EAAE,SAAS,MAAM,WAAW,SAAA;AAAA,IACrC,SAAS,OAAY;AACnB,aAAO,EAAE,SAAS,OAAO,OAAO,MAAM,QAAA;AAAA,IACxC;AAAA,EACF,CAAC;AAGD,UAAQ,OAAO,eAAe,OAAO,OAAO,MAAM,QAAQ;AACxD,WAAO,MAAM,GAAG;AAChB,WAAO,EAAE,SAAS,KAAA;AAAA,EACpB,CAAC;AAED,UAAQ,OAAO,cAAc,OAAO,OAAO,SAAS;AAClD,cAAU,IAAI;AACd,WAAO,EAAE,SAAS,KAAA;AAAA,EACpB,CAAC;AAED,UAAQ,OAAO,cAAc,OAAO,OAAO,MAAM,QAAQ;AACvD,cAAU,MAAM,GAAG;AACnB,WAAO,EAAE,SAAS,KAAA;AAAA,EACpB,CAAC;AAED,UAAQ,OAAO,YAAY,YAAY;AACrC,WAAO,cAAA;AAAA,EACT,CAAC;AAID,UAAQ,OAAO,yBAAyB,YAAY;AAClD,QAAI;AACF,YAAMA,YAAW,QAAQ;AACzB,YAAM,UAAmC,CAAA;AACzC,YAAM,QAAQ,cAAcA,SAAsC;AAElE,UAAI,CAAC,OAAO;AACV,YAAI,KAAK,iCAAiCA,SAAQ,EAAE;AACpD,eAAO,CAAA;AAAA,MACT;AAEA,iBAAW,CAAC,SAAS,QAAQ,KAAK,OAAO,QAAQ,KAAK,GAAG;AACvD,gBAAQ,OAAO,IAAI,WAAW,QAAQ;AAAA,MACxC;AAEA,aAAO;AAAA,IACT,SAAS,OAAY;AACnB,UAAI,MAAM,yCAAyC,KAAK;AACxD,aAAO,CAAA;AAAA,IACT;AAAA,EACF,CAAC;AAED,UAAQ,OAAO,wBAAwB,OAAO,OAAO,SAAS;AAC5D,UAAM,QAAQ,QAAQ,aAAa;AACnC,UAAM,eAAe,YAAY,gBAAgB;AACjD,UAAM,UAAU,YAAY,WAAW;AACvC,UAAM,OAAOF,KAAG,QAAA;AAEhB,UAAM,aAAqC;AAAA,MACzC,QAAQ,QACJ,GAAG,YAAY,yCACf,GAAG,IAAI;AAAA,MACX,MAAM,QACF,GAAG,YAAY,0CACf,GAAG,IAAI;AAAA,MACX,SAAS,QACL,GAAG,OAAO,iCACV,GAAG,IAAI;AAAA,MACX,IAAI,GAAG,YAAY;AAAA,MACnB,OAAO,GAAG,YAAY;AAAA,MACtB,KAAK,QACD,GAAG,YAAY,8BACf,GAAG,IAAI;AAAA,MACX,KAAK,GAAG,YAAY;AAAA,MACpB,QAAQ,GAAG,YAAY;AAAA,MACvB,QAAQ,GAAG,IAAI;AAAA,IAAA;AAIjB,WAAO,KAAK,UAAU,EAAE,QAAQ,CAAC,QAAQ;AACvC,YAAM,UAAU,KAAK,KAAK,CAAC,SAAc,KAAK,cAAc,GAAG;AAC/D,UAAI,CAAC,WAAW,CAAC,QAAQ,SAAS;AAChC,eAAO,WAAW,GAAG;AAAA,MACvB;AAAA,IACF,CAAC;AAED,UAAM,SAAwC,CAAA;AAC9C,eAAW,CAAC,MAAM,CAAC,KAAK,OAAO,QAAQ,UAAU,GAAG;AAClD,aAAO,IAAI,IAAIlG,KAAG,WAAW,CAAC,IAAI,IAAI;AAAA,IACxC;AAEA,UAAM,uBAAuB,IAAI,QAAQ,UAAU;AAEnD,eAAW,CAAC,aAAa,WAAW,KAAK,OAAO,QAAQ,MAAM,GAAG;AAC/D,UAAI,CAAC,YAAa;AAClB,YAAM,gBAAgB,aAAa,aAAa,oBAAoB;AAAA,IACtE;AAEA,WAAO,EAAE,SAAS,KAAA;AAAA,EACpB,CAAC;AAGD,UAAQ,GAAG,gBAAgB,MAAM,2BAAK,OAAO;AAC7C,UAAQ,GAAG,mBAAmB,MAAM,2BAAK,UAAU;AACnD,UAAQ,GAAG,0BAA0B,MAAM;AACzC,QAAI,2BAAK,eAAe;AACtB,iCAAK;AAAA,IACP,OAAO;AACL,iCAAK;AAAA,IACP;AAAA,EACF,CAAC;AAGD,UAAQ,OAAO,eAAe,OAAO,OAAO,UAAU,CAAA,MAAO;AAC3D,UAAM,SAAS,MAAM,OAAO,eAAe,KAAM;AAAA,MAC/C,YAAY,CAAC,YAAY,iBAAiB;AAAA,MAC1C,GAAG;AAAA,IAAA,CACJ;AAED,QAAI,CAAC,OAAO,YAAY,OAAO,UAAU,SAAS,GAAG;AACnD,YAAM,QAAQ,OAAO,UAAU,IAAI,CAAA,cAAa;AAAA,QAC9C;AAAA,QACA,UAAU,SAAS,MAAM,OAAO,EAAE,SAAS;AAAA,MAAA,EAC3C;AAEF,aAAO;AAAA,QACL,SAAS;AAAA,QACT;AAAA,QACA,WAAW,MAAM;AAAA,MAAA;AAAA,IAErB;AAEA,WAAO;AAAA,MACL,SAAS;AAAA,MACT,UAAU,OAAO;AAAA,IAAA;AAAA,EAErB,CAAC;AAED,UAAQ,OAAO,oBAAoB,OAAO,OAAO,aAAqB;AACpE,QAAI;AACF,YAAM,iBAAiB,QAAQ;AAAA,IACjC,SAAS,GAAG;AACV,UAAI,MAAM,2BAA2B,CAAC;AAAA,IACxC;AAAA,EACF,CAAC;AAGD,UAAQ,OAAO,aAAa,OAAO,OAAO,aAAqB;AAC7D,QAAI;AACF,UAAI,KAAK,iBAAiB,QAAQ;AAGlC,UAAI,CAACA,KAAG,WAAW,QAAQ,GAAG;AAC5B,YAAI,MAAM,wBAAwB,QAAQ;AAC1C,eAAO,EAAE,SAAS,OAAO,OAAO,sBAAA;AAAA,MAClC;AAGA,YAAM,cAAc,MAAM,IAAI,SAAS,QAAQ;AAC/C,UAAI,KAAK,2BAA2B,QAAQ;AAE5C,aAAO;AAAA,QACL,SAAS;AAAA,QACT,MAAM;AAAA,QACN,MAAM,YAAY;AAAA,MAAA;AAAA,IAEtB,SAAS,OAAY;AACnB,UAAI,MAAM,wBAAwB,UAAU,KAAK;AACjD,aAAO;AAAA,QACL,SAAS;AAAA,QACT,OAAO,MAAM,WAAW;AAAA,MAAA;AAAA,IAE5B;AAAA,EACF,CAAC;AAGD,UAAQ,OAAO,iBAAiB,OAAO,OAAO,UAAkB;AAC9D,UAAM,YAAY,MAAM,MAAM,GAAG,EAAE,CAAC,EAAE,QAAQ,mBAAmB,GAAG,EAAE,QAAQ,KAAK,GAAG;AACtF,UAAMqG,kBAAiBtG,OAAK,KAAKmG,KAAG,QAAA,GAAW,SAAS;AACxD,UAAM,wBAAwBnG,OAAK,KAAKsG,iBAAgB,SAAS;AACjE,QAAI;AACF,UAAI,KAAK,oBAAoB,qBAAqB;AAGlD,UAAI,CAACrG,KAAG,WAAW,qBAAqB,GAAG;AACzC,YAAI,MAAM,0BAA0B,qBAAqB;AACzD,eAAO,EAAE,SAAS,OAAO,OAAO,wBAAA;AAAA,MAClC;AAGA,YAAM,QAAQ,MAAM,IAAI,KAAK,qBAAqB;AAClD,UAAI,CAAC,MAAM,eAAe;AACxB,YAAI,MAAM,4BAA4B,qBAAqB;AAC3D,eAAO,EAAE,SAAS,OAAO,OAAO,0BAAA;AAAA,MAClC;AAGA,YAAM,IAAI,GAAG,uBAAuB,EAAE,WAAW,MAAM,OAAO,MAAM;AACpE,UAAI,KAAK,gCAAgC,qBAAqB;AAE9D,aAAO;AAAA,QACL,SAAS;AAAA,QACT,SAAS;AAAA,MAAA;AAAA,IAEb,SAAS,OAAY;AACnB,UAAI,MAAM,4BAA4B,uBAAuB,KAAK;AAClE,aAAO;AAAA,QACL,SAAS;AAAA,QACT,OAAO,MAAM,WAAW;AAAA,MAAA;AAAA,IAE5B;AAAA,EACF,CAAC;AAGD,UAAQ,OAAO,uBAAuB,OAAO,OAAO,UAAkB;AACpE,QAAI;AACF,YAAM,YAAY,MAAM,MAAM,GAAG,EAAE,CAAC,EAAE,QAAQ,mBAAmB,GAAG,EAAE,QAAQ,KAAK,GAAG;AACtF,YAAMqG,kBAAiBtG,OAAK,KAAKmG,KAAG,QAAA,GAAW,SAAS;AACxD,YAAM,wBAAwBnG,OAAK,KAAKsG,iBAAgB,SAAS;AAEjE,UAAI,KAAK,sCAAsC,KAAK;AACpD,UAAI,KAAK,oBAAoB,qBAAqB;AAElD,aAAO;AAAA,QACL,SAAS;AAAA,QACT,MAAM;AAAA,QACN;AAAA,QACA,SAASA;AAAA,MAAA;AAAA,IAEb,SAAS,OAAY;AACnB,UAAI,MAAM,kCAAkC,KAAK;AACjD,aAAO;AAAA,QACL,SAAS;AAAA,QACT,OAAO,MAAM,WAAW;AAAA,MAAA;AAAA,IAE5B;AAAA,EACF,CAAC;AAGD,UAAQ,OAAO,gBAAgB,OAAO,QAAQ,UAAU;AACtD,WAAO,WAAW,KAAK;AAAA,EACzB,CAAC;AAED,UAAQ,OAAO,aAAa,OAAO,QAAQ,OAAO,EAAE,KAAK,YAAY;AACnE,UAAM,WAAW,WAAW,KAAK;AACjC,QAAI,UAAU;AACd,QAAI;AACF,gBAAUrG,KAAG,WAAW,QAAQ,IAAIA,KAAG,aAAa,UAAU,OAAO,IAAI;AAAA,IAC3E,SAAS,OAAO;AACd,UAAI,MAAM,oBAAoB,KAAK;AAAA,IACrC;AACA,QAAI,QAAQ,QAAQ,MAAM,OAAO;AACjC,YAAQ,eAAe,OAAO,EAAE,CAAC,GAAG,GAAG,OAAO;AAC9CA,SAAG,cAAc,UAAU,MAAM,KAAK,IAAI,GAAG,OAAO;AACpD,WAAO,EAAE,SAAS,KAAA;AAAA,EACpB,CAAC;AAED,UAAQ,OAAO,cAAc,OAAO,QAAQ,OAAO,QAAQ;AACzD,QAAI,KAAK,cAAc,GAAG;AAC1B,UAAM,WAAW,WAAW,KAAK;AACjC,QAAI,UAAU;AACd,QAAI;AACF,gBAAUA,KAAG,WAAW,QAAQ,IAAIA,KAAG,aAAa,UAAU,OAAO,IAAI;AAAA,IAC3E,SAAS,OAAO;AACd,UAAI,MAAM,qBAAqB,KAAK;AAAA,IACtC;AACA,QAAI,QAAQ,QAAQ,MAAM,OAAO;AACjC,YAAQ,aAAa,OAAO,GAAG;AAC/BA,SAAG,cAAc,UAAU,MAAM,KAAK,IAAI,GAAG,OAAO;AACpD,QAAI,KAAK,sBAAsB,QAAQ;AACvC,WAAO,EAAE,SAAS,KAAA;AAAA,EACpB,CAAC;AAGD,UAAQ,OAAO,YAAY,CAAC,GAAG,QAAQ;AACrC,UAAM,cAAc,IAAI,cAAc;AAAA,MACpC,gBAAgB;AAAA,QACd;AAAA,QACA,iBAAiB;AAAA,QACjB,kBAAkB;AAAA,MAAA;AAAA,IACpB,CACD;AAED,QAAI,qBAAqB;AACvB,kBAAY,QAAQ,GAAG,mBAAmB,IAAI,GAAG,EAAE;AAAA,IACrD,OAAO;AACL,kBAAY,SAAS,WAAW,EAAE,MAAM,KAAK;AAAA,IAC/C;AAAA,EACF,CAAC;AAGD,UAAQ,OAAO,aAAa,OAAO,GAAG,MAAc,UAAkB,qBAA8B;AAClG,UAAM,UAAU,qBAAqB,YAAY,YAAY;AAC7D,WAAO,QAAQ,SAAS,MAAM,UAAU,gBAAgB;AAAA,EAC1D,CAAC;AAED,UAAQ,OAAO,iBAAiB,OAAO,GAAG,OAAe,WAAmB;AAC1E,UAAM,UAAU,qBAAqB,YAAY,YAAY;AAC7D,WAAO,QAAQ,YAAY,OAAO,MAAM;AAAA,EAC1C,CAAC;AAGD,QAAM,kBAAkB;AAAA,IACtB,EAAE,MAAM,mBAAmB,QAAQ,iBAAA;AAAA,IACnC,EAAE,MAAM,kBAAkB,QAAQ,gBAAA;AAAA,IAClC,EAAE,MAAM,gBAAgB,QAAQ,cAAA;AAAA,IAChC,EAAE,MAAM,gBAAgB,QAAQ,cAAA;AAAA,IAChC,EAAE,MAAM,oBAAoB,QAAQ,iBAAA;AAAA,IACpC,EAAE,MAAM,oBAAoB,QAAQ,iBAAA;AAAA,IACpC,EAAE,MAAM,sBAAsB,QAAQ,mBAAA;AAAA,IACtC,EAAE,MAAM,YAAY,QAAQ,UAAA;AAAA,IAC5B,EAAE,MAAM,oBAAoB,QAAQ,iBAAA;AAAA,IACpC,EAAE,MAAM,mBAAmB,QAAQ,iBAAA;AAAA,EAAiB;AAGtD,kBAAgB,QAAQ,CAAC,EAAE,MAAM,aAAa;AAC5C,YAAQ,OAAO,MAAM,OAAO,MAAM,SAAS;AACzC,YAAM,UAAU,qBAAqB,gBAAgB,gBAAgB;AACrE,aAAO,QAAQ,MAA8B,EAAE,GAAG,IAAI;AAAA,IACxD,CAAC;AAAA,EACH,CAAC;AAGD,UAAQ,OAAO,wBAAwB,4BAA4B;AACnE,UAAQ,OAAO,kBAAkB,4BAA4B;AAE7D,UAAQ,OAAO,wBAAwB,YAAY;AACjD,QAAI;AACF,YAAM,cAAc,MAAM,mBAAA;AAC1B,aAAO,EAAE,SAAS,MAAM,YAAA;AAAA,IAC1B,SAAS,OAAO;AACd,aAAO,EAAE,SAAS,OAAO,OAAQ,MAAgB,QAAA;AAAA,IACnD;AAAA,EACF,CAAC;AAGD,4BAAA;AACF;AAGA,eAAe,eAAe;AAC5B,QAAM,QAAQ,QAAQ,aAAa;AAEnC,QAAM,IAAI,cAAc;AAAA,IACtB,OAAO;AAAA,IACP,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,OAAO;AAAA,IACP,aAAa;AAAA,IACb,UAAU;AAAA,IACV,mBAAmB;AAAA,IACnB,iBAAiB;AAAA,IACjB,eAAe,QAAQ,WAAW;AAAA,IAClC,sBAAsB,QAAQ,EAAE,GAAG,IAAI,GAAG,OAAO;AAAA,IACjD,MAAMD,OAAK,KAAK,aAAa,aAAa;AAAA,IAC1C,gBAAgB;AAAA,IAChB,gBAAgB;AAAA,MACd,aAAa;AAAA,MACb;AAAA,MACA,iBAAiB;AAAA,MACjB,kBAAkB;AAAA,MAClB,YAAY;AAAA,MACZ,YAAY;AAAA,IAAA;AAAA,EACd,CACD;AAGD,eAAa,IAAI,WAAW,GAAG;AAC/B,mBAAiB,IAAI,eAAe,GAAG;AAGvC,WAAS,IAAI,GAAG,KAAK,GAAG,KAAK;AAC3B,mBAAe,cAAc,MAAM,IAAI,SAAY,EAAE,UAAU;AAAA,EACjE;AAGA,MAAI,qBAAqB;AACvB,QAAI,QAAQ,mBAAmB;AAC/B,QAAI,YAAY,aAAA;AAAA,EAClB,OAAO;AACL,QAAI,SAAS,SAAS;AAAA,EACxB;AAGA,4BAAA;AACA,yBAAA;AACA,4BAAA;AAGA,SAAO,GAAG;AAGV,MAAI,MAAM,MAAM,4BAAA;AAChB,MAAI,CAAC,KAAK;AACR,QAAI,KAAK,yDAAyD;AAClE,QAAI,YAAY,KAAK,iCAAiC,EAAE,SAAS,OAAO,MAAM,GAAG;AACjF;AAAA,EACF;AACA,QAAM,qBAAA;AACR;AAGA,MAAM,4BAA4B,MAAM;AACtC,MAAI,CAAC,IAAK;AAGV,OAAK,mBAAmB,IAAI;AAC9B;AAGA,MAAM,yBAAyB,MAAM;AACnC,MAAI,CAAC,IAAK;AAEV,QAAM,iBAAiB,MAAM,2BAAK,YAAY;AAE9C,MAAI,YAAY,GAAG,sBAAsB,CAAC,OAAO,UAAU;AAEzD,QAAI,MAAM,QAAQ,SAAS,MAAM,SAAS,WAAW;AACnD,qBAAA;AAAA,IACF;AAGA,QAAI,MAAM,WAAW,MAAM,SAAS,MAAM,IAAI,YAAA,MAAkB,OAAO,MAAM,SAAS,WAAW;AAC/F,qBAAA;AAAA,IACF;AAGA,QAAI,MAAM,QAAQ,MAAM,SAAS,MAAM,IAAI,YAAA,MAAkB,OAAO,MAAM,SAAS,WAAW;AAC5F,qBAAA;AAAA,IACF;AAAA,EACF,CAAC;AACH;AAGA,MAAM,4BAA4B,MAAM;AACtC,MAAI,CAAC,IAAK;AAGV,MAAI,YAAY,qBAAqB,CAAC,EAAE,UAAU;AAChD,QAAI,IAAI,WAAW,QAAQ,KAAK,IAAI,WAAW,OAAO,GAAG;AACvD,YAAM,aAAa,GAAG;AAAA,IACxB;AACA,WAAO,EAAE,QAAQ,OAAA;AAAA,EACnB,CAAC;AAGD,MAAI,YAAY,GAAG,iBAAiB,CAAC,OAAO,QAAQ;AAClD,UAAM,eAAA;AACN,UAAM,aAAa,GAAG;AAAA,EACxB,CAAC;AACH;AAGA,MAAM,uBAAuB,YAAY;AACvC,MAAI,KAAK,0CAA0C;AAEnD,QAAM,kBAAkB,MAAM,mBAAA;AAC9B,MAAI,iBAAiB;AACnB,QAAI,KAAK,8CAA8C;AAGvD,QAAI,OAAO,CAAC,IAAI,eAAe;AAC7B,UAAI,YAAY,KAAK,iCAAiC,EAAE,SAAS,MAAM,MAAM,GAAG;AAAA,IAClF;AAEA,qBAAiB,MAAM,aAAa,CAAC,SAAS;AAC5C,oBAAc;AACd,UAAI,KAAK,wCAAwC,EAAE,KAAA,CAAM;AAAA,IAC3D,CAAC;AAED,qDAAgB,GAAG,QAAQ,CAAC,MAAM,WAAW;AAC3C,UAAI,KAAK,yBAAyB,EAAE,MAAM,QAAQ;AAAA,IACpD;AAAA,EACF,OAAO;AACL,QAAI,KAAK,sDAAsD;AAAA,EACjE;AACF;AAGA,MAAM,uBAAuB,MAAM;AACjC,MAAI;AACF,QAAI,iDAAgB,KAAK;AACvB,UAAI,KAAK,8BAA8B,EAAE,KAAK,eAAe,KAAK;AAClE,WAAK,eAAe,KAAK,UAAU,CAAC,QAAQ;AAC1C,YAAI,KAAK;AACP,cAAI,MAAM,oCAAoC,GAAG;AAAA,QACnD,OAAO;AACL,cAAI,KAAK,6CAA6C;AAAA,QACxD;AAAA,MACF,CAAC;AAAA,IACH,OAAO;AACL,UAAI,KAAK,+BAA+B;AAAA,IAC1C;AAAA,EACF,SAAS,OAAO;AACd,QAAI,MAAM,6CAA6C,KAAK;AAAA,EAC9D;AACF;AAGA,IAAI,UAAA,EAAY,KAAK,MAAM;AAGzB,UAAQ,eAAe,GAAG,iBAAiB,CAAC,OAAO,MAAM,gBAAgB;AACvE,SAAK,KAAK,QAAQ,CAACyG,QAAO,UAAU;AAClC,YAAM,iBAAiB,KAAK,OAAA,EAAS,QAAQ,gBAAgB,EAAE,CAAC;AAAA,IAClE,CAAC;AAAA,EACH,CAAC;AAGD,WAAS,OAAO,aAAa,OAAO,YAAY;AAC9C,UAAM,MAAM,mBAAmB,QAAQ,IAAI,QAAQ,gBAAgB,EAAE,CAAC;AACtE,UAAM,WAAWzG,OAAK,UAAU,GAAG;AAEnC,QAAI;AACF,YAAM,OAAO,MAAM,IAAI,SAAS,QAAQ;AAGxC,YAAM,MAAMA,OAAK,QAAQ,QAAQ,EAAE,YAAA;AACnC,UAAI,cAAc;AAElB,cAAQ,KAAA;AAAA,QACN,KAAK;AACH,wBAAc;AACd;AAAA,QACF,KAAK;AAAA,QACL,KAAK;AACH,wBAAc;AACd;AAAA,MAAA;AAGJ,aAAO,IAAI,SAAS,MAAM;AAAA,QACxB,SAAS;AAAA,UACP,gBAAgB;AAAA,QAAA;AAAA,MAClB,CACD;AAAA,IACH,SAAS,KAAK;AACZ,aAAO,IAAI,SAAS,aAAa,EAAE,QAAQ,KAAK;AAAA,IAClD;AAAA,EACF,CAAC;AAGD,gBAAA;AACA,sBAAA;AACA,eAAA;AACF,CAAC;AAGD,IAAI,GAAG,qBAAqB,MAAM;AAChC,MAAI,KAAK,mBAAmB;AAC5B,mBAAiB;AACjB,QAAM;AACN,MAAI,QAAQ,aAAa,UAAU;AACjC,QAAI,KAAA;AAAA,EACN;AACF,CAAC;AAGD,IAAI,GAAG,YAAY,MAAM;AACvB,QAAM,aAAa,cAAc,cAAA;AACjC,MAAI,KAAK,YAAY,WAAW,MAAM;AAEtC,MAAI,WAAW,QAAQ;AACrB,eAAW,CAAC,EAAE,MAAA;AAAA,EAChB,OAAO;AACL,yBAAA;AACA,iBAAA;AAAA,EACF;AACF,CAAC;AAGD,IAAI,GAAG,eAAe,MAAM;AAC1B,MAAI,KAAK,aAAa;AACtB,MAAI,KAAK,+BAA8B,iDAAgB,IAAG;AAC1D,MAAI,KAAK;AACP,QAAI,QAAA;AAAA,EACN;AACA,uBAAA;AACF,CAAC;", "x_google_ignoreList": [7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43]}