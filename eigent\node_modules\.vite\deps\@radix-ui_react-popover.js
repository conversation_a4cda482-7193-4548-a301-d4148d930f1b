"use client";
import {
  Anchor2,
  Arrow2,
  Close,
  Content2,
  Pop<PERSON>,
  <PERSON><PERSON>Anchor,
  <PERSON>over<PERSON>rrow,
  PopoverClose,
  PopoverContent,
  <PERSON>overPortal,
  Popover<PERSON>rigger,
  Portal,
  Root2,
  Trigger,
  createPopoverScope
} from "./chunk-NQZNQEI5.js";
import "./chunk-DO7FCOXT.js";
import "./chunk-62QBFC2M.js";
import "./chunk-G4AN3473.js";
import "./chunk-INJJPTDC.js";
import "./chunk-VRLMKXGW.js";
import "./chunk-C2D4OKTQ.js";
import "./chunk-IWV5NBJL.js";
import "./chunk-54BE7P2O.js";
import "./chunk-753TZ7EK.js";
import "./chunk-4SYFA6CU.js";
import "./chunk-6ONK65C5.js";
import "./chunk-I3M3GCX4.js";
import "./chunk-MBTB7A2C.js";
import "./chunk-6OBIWUOU.js";
import "./chunk-7UVSMXVG.js";
export {
  Anchor2 as Anchor,
  Arrow2 as Arrow,
  Close,
  Content2 as Content,
  Popover,
  PopoverAnchor,
  PopoverArrow,
  PopoverClose,
  PopoverContent,
  PopoverPortal,
  PopoverTrigger,
  Portal,
  Root2 as Root,
  Trigger,
  createPopoverScope
};
//# sourceMappingURL=@radix-ui_react-popover.js.map
