{"version": 3, "sources": ["../../color-name/index.js", "../../is-arrayish/index.js", "../../simple-swizzle/index.js", "../../color-string/index.js", "../../color-convert/conversions.js", "../../color-convert/route.js", "../../color-convert/index.js", "../../color/index.js"], "sourcesContent": ["'use strict'\r\n\r\nmodule.exports = {\r\n\t\"aliceblue\": [240, 248, 255],\r\n\t\"antiquewhite\": [250, 235, 215],\r\n\t\"aqua\": [0, 255, 255],\r\n\t\"aquamarine\": [127, 255, 212],\r\n\t\"azure\": [240, 255, 255],\r\n\t\"beige\": [245, 245, 220],\r\n\t\"bisque\": [255, 228, 196],\r\n\t\"black\": [0, 0, 0],\r\n\t\"blanchedalmond\": [255, 235, 205],\r\n\t\"blue\": [0, 0, 255],\r\n\t\"blueviolet\": [138, 43, 226],\r\n\t\"brown\": [165, 42, 42],\r\n\t\"burlywood\": [222, 184, 135],\r\n\t\"cadetblue\": [95, 158, 160],\r\n\t\"chartreuse\": [127, 255, 0],\r\n\t\"chocolate\": [210, 105, 30],\r\n\t\"coral\": [255, 127, 80],\r\n\t\"cornflowerblue\": [100, 149, 237],\r\n\t\"cornsilk\": [255, 248, 220],\r\n\t\"crimson\": [220, 20, 60],\r\n\t\"cyan\": [0, 255, 255],\r\n\t\"darkblue\": [0, 0, 139],\r\n\t\"darkcyan\": [0, 139, 139],\r\n\t\"darkgoldenrod\": [184, 134, 11],\r\n\t\"darkgray\": [169, 169, 169],\r\n\t\"darkgreen\": [0, 100, 0],\r\n\t\"darkgrey\": [169, 169, 169],\r\n\t\"darkkhaki\": [189, 183, 107],\r\n\t\"darkmagenta\": [139, 0, 139],\r\n\t\"darkolivegreen\": [85, 107, 47],\r\n\t\"darkorange\": [255, 140, 0],\r\n\t\"darkorchid\": [153, 50, 204],\r\n\t\"darkred\": [139, 0, 0],\r\n\t\"darksalmon\": [233, 150, 122],\r\n\t\"darkseagreen\": [143, 188, 143],\r\n\t\"darkslateblue\": [72, 61, 139],\r\n\t\"darkslategray\": [47, 79, 79],\r\n\t\"darkslategrey\": [47, 79, 79],\r\n\t\"darkturquoise\": [0, 206, 209],\r\n\t\"darkviolet\": [148, 0, 211],\r\n\t\"deeppink\": [255, 20, 147],\r\n\t\"deepskyblue\": [0, 191, 255],\r\n\t\"dimgray\": [105, 105, 105],\r\n\t\"dimgrey\": [105, 105, 105],\r\n\t\"dodgerblue\": [30, 144, 255],\r\n\t\"firebrick\": [178, 34, 34],\r\n\t\"floralwhite\": [255, 250, 240],\r\n\t\"forestgreen\": [34, 139, 34],\r\n\t\"fuchsia\": [255, 0, 255],\r\n\t\"gainsboro\": [220, 220, 220],\r\n\t\"ghostwhite\": [248, 248, 255],\r\n\t\"gold\": [255, 215, 0],\r\n\t\"goldenrod\": [218, 165, 32],\r\n\t\"gray\": [128, 128, 128],\r\n\t\"green\": [0, 128, 0],\r\n\t\"greenyellow\": [173, 255, 47],\r\n\t\"grey\": [128, 128, 128],\r\n\t\"honeydew\": [240, 255, 240],\r\n\t\"hotpink\": [255, 105, 180],\r\n\t\"indianred\": [205, 92, 92],\r\n\t\"indigo\": [75, 0, 130],\r\n\t\"ivory\": [255, 255, 240],\r\n\t\"khaki\": [240, 230, 140],\r\n\t\"lavender\": [230, 230, 250],\r\n\t\"lavenderblush\": [255, 240, 245],\r\n\t\"lawngreen\": [124, 252, 0],\r\n\t\"lemonchiffon\": [255, 250, 205],\r\n\t\"lightblue\": [173, 216, 230],\r\n\t\"lightcoral\": [240, 128, 128],\r\n\t\"lightcyan\": [224, 255, 255],\r\n\t\"lightgoldenrodyellow\": [250, 250, 210],\r\n\t\"lightgray\": [211, 211, 211],\r\n\t\"lightgreen\": [144, 238, 144],\r\n\t\"lightgrey\": [211, 211, 211],\r\n\t\"lightpink\": [255, 182, 193],\r\n\t\"lightsalmon\": [255, 160, 122],\r\n\t\"lightseagreen\": [32, 178, 170],\r\n\t\"lightskyblue\": [135, 206, 250],\r\n\t\"lightslategray\": [119, 136, 153],\r\n\t\"lightslategrey\": [119, 136, 153],\r\n\t\"lightsteelblue\": [176, 196, 222],\r\n\t\"lightyellow\": [255, 255, 224],\r\n\t\"lime\": [0, 255, 0],\r\n\t\"limegreen\": [50, 205, 50],\r\n\t\"linen\": [250, 240, 230],\r\n\t\"magenta\": [255, 0, 255],\r\n\t\"maroon\": [128, 0, 0],\r\n\t\"mediumaquamarine\": [102, 205, 170],\r\n\t\"mediumblue\": [0, 0, 205],\r\n\t\"mediumorchid\": [186, 85, 211],\r\n\t\"mediumpurple\": [147, 112, 219],\r\n\t\"mediumseagreen\": [60, 179, 113],\r\n\t\"mediumslateblue\": [123, 104, 238],\r\n\t\"mediumspringgreen\": [0, 250, 154],\r\n\t\"mediumturquoise\": [72, 209, 204],\r\n\t\"mediumvioletred\": [199, 21, 133],\r\n\t\"midnightblue\": [25, 25, 112],\r\n\t\"mintcream\": [245, 255, 250],\r\n\t\"mistyrose\": [255, 228, 225],\r\n\t\"moccasin\": [255, 228, 181],\r\n\t\"navajowhite\": [255, 222, 173],\r\n\t\"navy\": [0, 0, 128],\r\n\t\"oldlace\": [253, 245, 230],\r\n\t\"olive\": [128, 128, 0],\r\n\t\"olivedrab\": [107, 142, 35],\r\n\t\"orange\": [255, 165, 0],\r\n\t\"orangered\": [255, 69, 0],\r\n\t\"orchid\": [218, 112, 214],\r\n\t\"palegoldenrod\": [238, 232, 170],\r\n\t\"palegreen\": [152, 251, 152],\r\n\t\"paleturquoise\": [175, 238, 238],\r\n\t\"palevioletred\": [219, 112, 147],\r\n\t\"papayawhip\": [255, 239, 213],\r\n\t\"peachpuff\": [255, 218, 185],\r\n\t\"peru\": [205, 133, 63],\r\n\t\"pink\": [255, 192, 203],\r\n\t\"plum\": [221, 160, 221],\r\n\t\"powderblue\": [176, 224, 230],\r\n\t\"purple\": [128, 0, 128],\r\n\t\"rebeccapurple\": [102, 51, 153],\r\n\t\"red\": [255, 0, 0],\r\n\t\"rosybrown\": [188, 143, 143],\r\n\t\"royalblue\": [65, 105, 225],\r\n\t\"saddlebrown\": [139, 69, 19],\r\n\t\"salmon\": [250, 128, 114],\r\n\t\"sandybrown\": [244, 164, 96],\r\n\t\"seagreen\": [46, 139, 87],\r\n\t\"seashell\": [255, 245, 238],\r\n\t\"sienna\": [160, 82, 45],\r\n\t\"silver\": [192, 192, 192],\r\n\t\"skyblue\": [135, 206, 235],\r\n\t\"slateblue\": [106, 90, 205],\r\n\t\"slategray\": [112, 128, 144],\r\n\t\"slategrey\": [112, 128, 144],\r\n\t\"snow\": [255, 250, 250],\r\n\t\"springgreen\": [0, 255, 127],\r\n\t\"steelblue\": [70, 130, 180],\r\n\t\"tan\": [210, 180, 140],\r\n\t\"teal\": [0, 128, 128],\r\n\t\"thistle\": [216, 191, 216],\r\n\t\"tomato\": [255, 99, 71],\r\n\t\"turquoise\": [64, 224, 208],\r\n\t\"violet\": [238, 130, 238],\r\n\t\"wheat\": [245, 222, 179],\r\n\t\"white\": [255, 255, 255],\r\n\t\"whitesmoke\": [245, 245, 245],\r\n\t\"yellow\": [255, 255, 0],\r\n\t\"yellowgreen\": [154, 205, 50]\r\n};\r\n", "module.exports = function isArrayish(obj) {\n\tif (!obj || typeof obj === 'string') {\n\t\treturn false;\n\t}\n\n\treturn obj instanceof Array || Array.isArray(obj) ||\n\t\t(obj.length >= 0 && (obj.splice instanceof Function ||\n\t\t\t(Object.getOwnPropertyDescriptor(obj, (obj.length - 1)) && obj.constructor.name !== 'String')));\n};\n", "'use strict';\n\nvar isArrayish = require('is-arrayish');\n\nvar concat = Array.prototype.concat;\nvar slice = Array.prototype.slice;\n\nvar swizzle = module.exports = function swizzle(args) {\n\tvar results = [];\n\n\tfor (var i = 0, len = args.length; i < len; i++) {\n\t\tvar arg = args[i];\n\n\t\tif (isArrayish(arg)) {\n\t\t\t// http://jsperf.com/javascript-array-concat-vs-push/98\n\t\t\tresults = concat.call(results, slice.call(arg));\n\t\t} else {\n\t\t\tresults.push(arg);\n\t\t}\n\t}\n\n\treturn results;\n};\n\nswizzle.wrap = function (fn) {\n\treturn function () {\n\t\treturn fn(swizzle(arguments));\n\t};\n};\n", "/* MIT license */\nvar colorNames = require('color-name');\nvar swizzle = require('simple-swizzle');\nvar hasOwnProperty = Object.hasOwnProperty;\n\nvar reverseNames = Object.create(null);\n\n// create a list of reverse color names\nfor (var name in colorNames) {\n\tif (hasOwnProperty.call(colorNames, name)) {\n\t\treverseNames[colorNames[name]] = name;\n\t}\n}\n\nvar cs = module.exports = {\n\tto: {},\n\tget: {}\n};\n\ncs.get = function (string) {\n\tvar prefix = string.substring(0, 3).toLowerCase();\n\tvar val;\n\tvar model;\n\tswitch (prefix) {\n\t\tcase 'hsl':\n\t\t\tval = cs.get.hsl(string);\n\t\t\tmodel = 'hsl';\n\t\t\tbreak;\n\t\tcase 'hwb':\n\t\t\tval = cs.get.hwb(string);\n\t\t\tmodel = 'hwb';\n\t\t\tbreak;\n\t\tdefault:\n\t\t\tval = cs.get.rgb(string);\n\t\t\tmodel = 'rgb';\n\t\t\tbreak;\n\t}\n\n\tif (!val) {\n\t\treturn null;\n\t}\n\n\treturn {model: model, value: val};\n};\n\ncs.get.rgb = function (string) {\n\tif (!string) {\n\t\treturn null;\n\t}\n\n\tvar abbr = /^#([a-f0-9]{3,4})$/i;\n\tvar hex = /^#([a-f0-9]{6})([a-f0-9]{2})?$/i;\n\tvar rgba = /^rgba?\\(\\s*([+-]?\\d+)(?=[\\s,])\\s*(?:,\\s*)?([+-]?\\d+)(?=[\\s,])\\s*(?:,\\s*)?([+-]?\\d+)\\s*(?:[,|\\/]\\s*([+-]?[\\d\\.]+)(%?)\\s*)?\\)$/;\n\tvar per = /^rgba?\\(\\s*([+-]?[\\d\\.]+)\\%\\s*,?\\s*([+-]?[\\d\\.]+)\\%\\s*,?\\s*([+-]?[\\d\\.]+)\\%\\s*(?:[,|\\/]\\s*([+-]?[\\d\\.]+)(%?)\\s*)?\\)$/;\n\tvar keyword = /^(\\w+)$/;\n\n\tvar rgb = [0, 0, 0, 1];\n\tvar match;\n\tvar i;\n\tvar hexAlpha;\n\n\tif (match = string.match(hex)) {\n\t\thexAlpha = match[2];\n\t\tmatch = match[1];\n\n\t\tfor (i = 0; i < 3; i++) {\n\t\t\t// https://jsperf.com/slice-vs-substr-vs-substring-methods-long-string/19\n\t\t\tvar i2 = i * 2;\n\t\t\trgb[i] = parseInt(match.slice(i2, i2 + 2), 16);\n\t\t}\n\n\t\tif (hexAlpha) {\n\t\t\trgb[3] = parseInt(hexAlpha, 16) / 255;\n\t\t}\n\t} else if (match = string.match(abbr)) {\n\t\tmatch = match[1];\n\t\thexAlpha = match[3];\n\n\t\tfor (i = 0; i < 3; i++) {\n\t\t\trgb[i] = parseInt(match[i] + match[i], 16);\n\t\t}\n\n\t\tif (hexAlpha) {\n\t\t\trgb[3] = parseInt(hexAlpha + hexAlpha, 16) / 255;\n\t\t}\n\t} else if (match = string.match(rgba)) {\n\t\tfor (i = 0; i < 3; i++) {\n\t\t\trgb[i] = parseInt(match[i + 1], 0);\n\t\t}\n\n\t\tif (match[4]) {\n\t\t\tif (match[5]) {\n\t\t\t\trgb[3] = parseFloat(match[4]) * 0.01;\n\t\t\t} else {\n\t\t\t\trgb[3] = parseFloat(match[4]);\n\t\t\t}\n\t\t}\n\t} else if (match = string.match(per)) {\n\t\tfor (i = 0; i < 3; i++) {\n\t\t\trgb[i] = Math.round(parseFloat(match[i + 1]) * 2.55);\n\t\t}\n\n\t\tif (match[4]) {\n\t\t\tif (match[5]) {\n\t\t\t\trgb[3] = parseFloat(match[4]) * 0.01;\n\t\t\t} else {\n\t\t\t\trgb[3] = parseFloat(match[4]);\n\t\t\t}\n\t\t}\n\t} else if (match = string.match(keyword)) {\n\t\tif (match[1] === 'transparent') {\n\t\t\treturn [0, 0, 0, 0];\n\t\t}\n\n\t\tif (!hasOwnProperty.call(colorNames, match[1])) {\n\t\t\treturn null;\n\t\t}\n\n\t\trgb = colorNames[match[1]];\n\t\trgb[3] = 1;\n\n\t\treturn rgb;\n\t} else {\n\t\treturn null;\n\t}\n\n\tfor (i = 0; i < 3; i++) {\n\t\trgb[i] = clamp(rgb[i], 0, 255);\n\t}\n\trgb[3] = clamp(rgb[3], 0, 1);\n\n\treturn rgb;\n};\n\ncs.get.hsl = function (string) {\n\tif (!string) {\n\t\treturn null;\n\t}\n\n\tvar hsl = /^hsla?\\(\\s*([+-]?(?:\\d{0,3}\\.)?\\d+)(?:deg)?\\s*,?\\s*([+-]?[\\d\\.]+)%\\s*,?\\s*([+-]?[\\d\\.]+)%\\s*(?:[,|\\/]\\s*([+-]?(?=\\.\\d|\\d)(?:0|[1-9]\\d*)?(?:\\.\\d*)?(?:[eE][+-]?\\d+)?)\\s*)?\\)$/;\n\tvar match = string.match(hsl);\n\n\tif (match) {\n\t\tvar alpha = parseFloat(match[4]);\n\t\tvar h = ((parseFloat(match[1]) % 360) + 360) % 360;\n\t\tvar s = clamp(parseFloat(match[2]), 0, 100);\n\t\tvar l = clamp(parseFloat(match[3]), 0, 100);\n\t\tvar a = clamp(isNaN(alpha) ? 1 : alpha, 0, 1);\n\n\t\treturn [h, s, l, a];\n\t}\n\n\treturn null;\n};\n\ncs.get.hwb = function (string) {\n\tif (!string) {\n\t\treturn null;\n\t}\n\n\tvar hwb = /^hwb\\(\\s*([+-]?\\d{0,3}(?:\\.\\d+)?)(?:deg)?\\s*,\\s*([+-]?[\\d\\.]+)%\\s*,\\s*([+-]?[\\d\\.]+)%\\s*(?:,\\s*([+-]?(?=\\.\\d|\\d)(?:0|[1-9]\\d*)?(?:\\.\\d*)?(?:[eE][+-]?\\d+)?)\\s*)?\\)$/;\n\tvar match = string.match(hwb);\n\n\tif (match) {\n\t\tvar alpha = parseFloat(match[4]);\n\t\tvar h = ((parseFloat(match[1]) % 360) + 360) % 360;\n\t\tvar w = clamp(parseFloat(match[2]), 0, 100);\n\t\tvar b = clamp(parseFloat(match[3]), 0, 100);\n\t\tvar a = clamp(isNaN(alpha) ? 1 : alpha, 0, 1);\n\t\treturn [h, w, b, a];\n\t}\n\n\treturn null;\n};\n\ncs.to.hex = function () {\n\tvar rgba = swizzle(arguments);\n\n\treturn (\n\t\t'#' +\n\t\thexDouble(rgba[0]) +\n\t\thexDouble(rgba[1]) +\n\t\thexDouble(rgba[2]) +\n\t\t(rgba[3] < 1\n\t\t\t? (hexDouble(Math.round(rgba[3] * 255)))\n\t\t\t: '')\n\t);\n};\n\ncs.to.rgb = function () {\n\tvar rgba = swizzle(arguments);\n\n\treturn rgba.length < 4 || rgba[3] === 1\n\t\t? 'rgb(' + Math.round(rgba[0]) + ', ' + Math.round(rgba[1]) + ', ' + Math.round(rgba[2]) + ')'\n\t\t: 'rgba(' + Math.round(rgba[0]) + ', ' + Math.round(rgba[1]) + ', ' + Math.round(rgba[2]) + ', ' + rgba[3] + ')';\n};\n\ncs.to.rgb.percent = function () {\n\tvar rgba = swizzle(arguments);\n\n\tvar r = Math.round(rgba[0] / 255 * 100);\n\tvar g = Math.round(rgba[1] / 255 * 100);\n\tvar b = Math.round(rgba[2] / 255 * 100);\n\n\treturn rgba.length < 4 || rgba[3] === 1\n\t\t? 'rgb(' + r + '%, ' + g + '%, ' + b + '%)'\n\t\t: 'rgba(' + r + '%, ' + g + '%, ' + b + '%, ' + rgba[3] + ')';\n};\n\ncs.to.hsl = function () {\n\tvar hsla = swizzle(arguments);\n\treturn hsla.length < 4 || hsla[3] === 1\n\t\t? 'hsl(' + hsla[0] + ', ' + hsla[1] + '%, ' + hsla[2] + '%)'\n\t\t: 'hsla(' + hsla[0] + ', ' + hsla[1] + '%, ' + hsla[2] + '%, ' + hsla[3] + ')';\n};\n\n// hwb is a bit different than rgb(a) & hsl(a) since there is no alpha specific syntax\n// (hwb have alpha optional & 1 is default value)\ncs.to.hwb = function () {\n\tvar hwba = swizzle(arguments);\n\n\tvar a = '';\n\tif (hwba.length >= 4 && hwba[3] !== 1) {\n\t\ta = ', ' + hwba[3];\n\t}\n\n\treturn 'hwb(' + hwba[0] + ', ' + hwba[1] + '%, ' + hwba[2] + '%' + a + ')';\n};\n\ncs.to.keyword = function (rgb) {\n\treturn reverseNames[rgb.slice(0, 3)];\n};\n\n// helpers\nfunction clamp(num, min, max) {\n\treturn Math.min(Math.max(min, num), max);\n}\n\nfunction hexDouble(num) {\n\tvar str = Math.round(num).toString(16).toUpperCase();\n\treturn (str.length < 2) ? '0' + str : str;\n}\n", "/* MIT license */\n/* eslint-disable no-mixed-operators */\nconst cssKeywords = require('color-name');\n\n// NOTE: conversions should only return primitive values (i.e. arrays, or\n//       values that give correct `typeof` results).\n//       do not use box values types (i.e. Number(), String(), etc.)\n\nconst reverseKeywords = {};\nfor (const key of Object.keys(cssKeywords)) {\n\treverseKeywords[cssKeywords[key]] = key;\n}\n\nconst convert = {\n\trgb: {channels: 3, labels: 'rgb'},\n\thsl: {channels: 3, labels: 'hsl'},\n\thsv: {channels: 3, labels: 'hsv'},\n\thwb: {channels: 3, labels: 'hwb'},\n\tcmyk: {channels: 4, labels: 'cmyk'},\n\txyz: {channels: 3, labels: 'xyz'},\n\tlab: {channels: 3, labels: 'lab'},\n\tlch: {channels: 3, labels: 'lch'},\n\thex: {channels: 1, labels: ['hex']},\n\tkeyword: {channels: 1, labels: ['keyword']},\n\tansi16: {channels: 1, labels: ['ansi16']},\n\tansi256: {channels: 1, labels: ['ansi256']},\n\thcg: {channels: 3, labels: ['h', 'c', 'g']},\n\tapple: {channels: 3, labels: ['r16', 'g16', 'b16']},\n\tgray: {channels: 1, labels: ['gray']}\n};\n\nmodule.exports = convert;\n\n// Hide .channels and .labels properties\nfor (const model of Object.keys(convert)) {\n\tif (!('channels' in convert[model])) {\n\t\tthrow new Error('missing channels property: ' + model);\n\t}\n\n\tif (!('labels' in convert[model])) {\n\t\tthrow new Error('missing channel labels property: ' + model);\n\t}\n\n\tif (convert[model].labels.length !== convert[model].channels) {\n\t\tthrow new Error('channel and label counts mismatch: ' + model);\n\t}\n\n\tconst {channels, labels} = convert[model];\n\tdelete convert[model].channels;\n\tdelete convert[model].labels;\n\tObject.defineProperty(convert[model], 'channels', {value: channels});\n\tObject.defineProperty(convert[model], 'labels', {value: labels});\n}\n\nconvert.rgb.hsl = function (rgb) {\n\tconst r = rgb[0] / 255;\n\tconst g = rgb[1] / 255;\n\tconst b = rgb[2] / 255;\n\tconst min = Math.min(r, g, b);\n\tconst max = Math.max(r, g, b);\n\tconst delta = max - min;\n\tlet h;\n\tlet s;\n\n\tif (max === min) {\n\t\th = 0;\n\t} else if (r === max) {\n\t\th = (g - b) / delta;\n\t} else if (g === max) {\n\t\th = 2 + (b - r) / delta;\n\t} else if (b === max) {\n\t\th = 4 + (r - g) / delta;\n\t}\n\n\th = Math.min(h * 60, 360);\n\n\tif (h < 0) {\n\t\th += 360;\n\t}\n\n\tconst l = (min + max) / 2;\n\n\tif (max === min) {\n\t\ts = 0;\n\t} else if (l <= 0.5) {\n\t\ts = delta / (max + min);\n\t} else {\n\t\ts = delta / (2 - max - min);\n\t}\n\n\treturn [h, s * 100, l * 100];\n};\n\nconvert.rgb.hsv = function (rgb) {\n\tlet rdif;\n\tlet gdif;\n\tlet bdif;\n\tlet h;\n\tlet s;\n\n\tconst r = rgb[0] / 255;\n\tconst g = rgb[1] / 255;\n\tconst b = rgb[2] / 255;\n\tconst v = Math.max(r, g, b);\n\tconst diff = v - Math.min(r, g, b);\n\tconst diffc = function (c) {\n\t\treturn (v - c) / 6 / diff + 1 / 2;\n\t};\n\n\tif (diff === 0) {\n\t\th = 0;\n\t\ts = 0;\n\t} else {\n\t\ts = diff / v;\n\t\trdif = diffc(r);\n\t\tgdif = diffc(g);\n\t\tbdif = diffc(b);\n\n\t\tif (r === v) {\n\t\t\th = bdif - gdif;\n\t\t} else if (g === v) {\n\t\t\th = (1 / 3) + rdif - bdif;\n\t\t} else if (b === v) {\n\t\t\th = (2 / 3) + gdif - rdif;\n\t\t}\n\n\t\tif (h < 0) {\n\t\t\th += 1;\n\t\t} else if (h > 1) {\n\t\t\th -= 1;\n\t\t}\n\t}\n\n\treturn [\n\t\th * 360,\n\t\ts * 100,\n\t\tv * 100\n\t];\n};\n\nconvert.rgb.hwb = function (rgb) {\n\tconst r = rgb[0];\n\tconst g = rgb[1];\n\tlet b = rgb[2];\n\tconst h = convert.rgb.hsl(rgb)[0];\n\tconst w = 1 / 255 * Math.min(r, Math.min(g, b));\n\n\tb = 1 - 1 / 255 * Math.max(r, Math.max(g, b));\n\n\treturn [h, w * 100, b * 100];\n};\n\nconvert.rgb.cmyk = function (rgb) {\n\tconst r = rgb[0] / 255;\n\tconst g = rgb[1] / 255;\n\tconst b = rgb[2] / 255;\n\n\tconst k = Math.min(1 - r, 1 - g, 1 - b);\n\tconst c = (1 - r - k) / (1 - k) || 0;\n\tconst m = (1 - g - k) / (1 - k) || 0;\n\tconst y = (1 - b - k) / (1 - k) || 0;\n\n\treturn [c * 100, m * 100, y * 100, k * 100];\n};\n\nfunction comparativeDistance(x, y) {\n\t/*\n\t\tSee https://en.m.wikipedia.org/wiki/Euclidean_distance#Squared_Euclidean_distance\n\t*/\n\treturn (\n\t\t((x[0] - y[0]) ** 2) +\n\t\t((x[1] - y[1]) ** 2) +\n\t\t((x[2] - y[2]) ** 2)\n\t);\n}\n\nconvert.rgb.keyword = function (rgb) {\n\tconst reversed = reverseKeywords[rgb];\n\tif (reversed) {\n\t\treturn reversed;\n\t}\n\n\tlet currentClosestDistance = Infinity;\n\tlet currentClosestKeyword;\n\n\tfor (const keyword of Object.keys(cssKeywords)) {\n\t\tconst value = cssKeywords[keyword];\n\n\t\t// Compute comparative distance\n\t\tconst distance = comparativeDistance(rgb, value);\n\n\t\t// Check if its less, if so set as closest\n\t\tif (distance < currentClosestDistance) {\n\t\t\tcurrentClosestDistance = distance;\n\t\t\tcurrentClosestKeyword = keyword;\n\t\t}\n\t}\n\n\treturn currentClosestKeyword;\n};\n\nconvert.keyword.rgb = function (keyword) {\n\treturn cssKeywords[keyword];\n};\n\nconvert.rgb.xyz = function (rgb) {\n\tlet r = rgb[0] / 255;\n\tlet g = rgb[1] / 255;\n\tlet b = rgb[2] / 255;\n\n\t// Assume sRGB\n\tr = r > 0.04045 ? (((r + 0.055) / 1.055) ** 2.4) : (r / 12.92);\n\tg = g > 0.04045 ? (((g + 0.055) / 1.055) ** 2.4) : (g / 12.92);\n\tb = b > 0.04045 ? (((b + 0.055) / 1.055) ** 2.4) : (b / 12.92);\n\n\tconst x = (r * 0.4124) + (g * 0.3576) + (b * 0.1805);\n\tconst y = (r * 0.2126) + (g * 0.7152) + (b * 0.0722);\n\tconst z = (r * 0.0193) + (g * 0.1192) + (b * 0.9505);\n\n\treturn [x * 100, y * 100, z * 100];\n};\n\nconvert.rgb.lab = function (rgb) {\n\tconst xyz = convert.rgb.xyz(rgb);\n\tlet x = xyz[0];\n\tlet y = xyz[1];\n\tlet z = xyz[2];\n\n\tx /= 95.047;\n\ty /= 100;\n\tz /= 108.883;\n\n\tx = x > 0.008856 ? (x ** (1 / 3)) : (7.787 * x) + (16 / 116);\n\ty = y > 0.008856 ? (y ** (1 / 3)) : (7.787 * y) + (16 / 116);\n\tz = z > 0.008856 ? (z ** (1 / 3)) : (7.787 * z) + (16 / 116);\n\n\tconst l = (116 * y) - 16;\n\tconst a = 500 * (x - y);\n\tconst b = 200 * (y - z);\n\n\treturn [l, a, b];\n};\n\nconvert.hsl.rgb = function (hsl) {\n\tconst h = hsl[0] / 360;\n\tconst s = hsl[1] / 100;\n\tconst l = hsl[2] / 100;\n\tlet t2;\n\tlet t3;\n\tlet val;\n\n\tif (s === 0) {\n\t\tval = l * 255;\n\t\treturn [val, val, val];\n\t}\n\n\tif (l < 0.5) {\n\t\tt2 = l * (1 + s);\n\t} else {\n\t\tt2 = l + s - l * s;\n\t}\n\n\tconst t1 = 2 * l - t2;\n\n\tconst rgb = [0, 0, 0];\n\tfor (let i = 0; i < 3; i++) {\n\t\tt3 = h + 1 / 3 * -(i - 1);\n\t\tif (t3 < 0) {\n\t\t\tt3++;\n\t\t}\n\n\t\tif (t3 > 1) {\n\t\t\tt3--;\n\t\t}\n\n\t\tif (6 * t3 < 1) {\n\t\t\tval = t1 + (t2 - t1) * 6 * t3;\n\t\t} else if (2 * t3 < 1) {\n\t\t\tval = t2;\n\t\t} else if (3 * t3 < 2) {\n\t\t\tval = t1 + (t2 - t1) * (2 / 3 - t3) * 6;\n\t\t} else {\n\t\t\tval = t1;\n\t\t}\n\n\t\trgb[i] = val * 255;\n\t}\n\n\treturn rgb;\n};\n\nconvert.hsl.hsv = function (hsl) {\n\tconst h = hsl[0];\n\tlet s = hsl[1] / 100;\n\tlet l = hsl[2] / 100;\n\tlet smin = s;\n\tconst lmin = Math.max(l, 0.01);\n\n\tl *= 2;\n\ts *= (l <= 1) ? l : 2 - l;\n\tsmin *= lmin <= 1 ? lmin : 2 - lmin;\n\tconst v = (l + s) / 2;\n\tconst sv = l === 0 ? (2 * smin) / (lmin + smin) : (2 * s) / (l + s);\n\n\treturn [h, sv * 100, v * 100];\n};\n\nconvert.hsv.rgb = function (hsv) {\n\tconst h = hsv[0] / 60;\n\tconst s = hsv[1] / 100;\n\tlet v = hsv[2] / 100;\n\tconst hi = Math.floor(h) % 6;\n\n\tconst f = h - Math.floor(h);\n\tconst p = 255 * v * (1 - s);\n\tconst q = 255 * v * (1 - (s * f));\n\tconst t = 255 * v * (1 - (s * (1 - f)));\n\tv *= 255;\n\n\tswitch (hi) {\n\t\tcase 0:\n\t\t\treturn [v, t, p];\n\t\tcase 1:\n\t\t\treturn [q, v, p];\n\t\tcase 2:\n\t\t\treturn [p, v, t];\n\t\tcase 3:\n\t\t\treturn [p, q, v];\n\t\tcase 4:\n\t\t\treturn [t, p, v];\n\t\tcase 5:\n\t\t\treturn [v, p, q];\n\t}\n};\n\nconvert.hsv.hsl = function (hsv) {\n\tconst h = hsv[0];\n\tconst s = hsv[1] / 100;\n\tconst v = hsv[2] / 100;\n\tconst vmin = Math.max(v, 0.01);\n\tlet sl;\n\tlet l;\n\n\tl = (2 - s) * v;\n\tconst lmin = (2 - s) * vmin;\n\tsl = s * vmin;\n\tsl /= (lmin <= 1) ? lmin : 2 - lmin;\n\tsl = sl || 0;\n\tl /= 2;\n\n\treturn [h, sl * 100, l * 100];\n};\n\n// http://dev.w3.org/csswg/css-color/#hwb-to-rgb\nconvert.hwb.rgb = function (hwb) {\n\tconst h = hwb[0] / 360;\n\tlet wh = hwb[1] / 100;\n\tlet bl = hwb[2] / 100;\n\tconst ratio = wh + bl;\n\tlet f;\n\n\t// Wh + bl cant be > 1\n\tif (ratio > 1) {\n\t\twh /= ratio;\n\t\tbl /= ratio;\n\t}\n\n\tconst i = Math.floor(6 * h);\n\tconst v = 1 - bl;\n\tf = 6 * h - i;\n\n\tif ((i & 0x01) !== 0) {\n\t\tf = 1 - f;\n\t}\n\n\tconst n = wh + f * (v - wh); // Linear interpolation\n\n\tlet r;\n\tlet g;\n\tlet b;\n\t/* eslint-disable max-statements-per-line,no-multi-spaces */\n\tswitch (i) {\n\t\tdefault:\n\t\tcase 6:\n\t\tcase 0: r = v;  g = n;  b = wh; break;\n\t\tcase 1: r = n;  g = v;  b = wh; break;\n\t\tcase 2: r = wh; g = v;  b = n; break;\n\t\tcase 3: r = wh; g = n;  b = v; break;\n\t\tcase 4: r = n;  g = wh; b = v; break;\n\t\tcase 5: r = v;  g = wh; b = n; break;\n\t}\n\t/* eslint-enable max-statements-per-line,no-multi-spaces */\n\n\treturn [r * 255, g * 255, b * 255];\n};\n\nconvert.cmyk.rgb = function (cmyk) {\n\tconst c = cmyk[0] / 100;\n\tconst m = cmyk[1] / 100;\n\tconst y = cmyk[2] / 100;\n\tconst k = cmyk[3] / 100;\n\n\tconst r = 1 - Math.min(1, c * (1 - k) + k);\n\tconst g = 1 - Math.min(1, m * (1 - k) + k);\n\tconst b = 1 - Math.min(1, y * (1 - k) + k);\n\n\treturn [r * 255, g * 255, b * 255];\n};\n\nconvert.xyz.rgb = function (xyz) {\n\tconst x = xyz[0] / 100;\n\tconst y = xyz[1] / 100;\n\tconst z = xyz[2] / 100;\n\tlet r;\n\tlet g;\n\tlet b;\n\n\tr = (x * 3.2406) + (y * -1.5372) + (z * -0.4986);\n\tg = (x * -0.9689) + (y * 1.8758) + (z * 0.0415);\n\tb = (x * 0.0557) + (y * -0.2040) + (z * 1.0570);\n\n\t// Assume sRGB\n\tr = r > 0.0031308\n\t\t? ((1.055 * (r ** (1.0 / 2.4))) - 0.055)\n\t\t: r * 12.92;\n\n\tg = g > 0.0031308\n\t\t? ((1.055 * (g ** (1.0 / 2.4))) - 0.055)\n\t\t: g * 12.92;\n\n\tb = b > 0.0031308\n\t\t? ((1.055 * (b ** (1.0 / 2.4))) - 0.055)\n\t\t: b * 12.92;\n\n\tr = Math.min(Math.max(0, r), 1);\n\tg = Math.min(Math.max(0, g), 1);\n\tb = Math.min(Math.max(0, b), 1);\n\n\treturn [r * 255, g * 255, b * 255];\n};\n\nconvert.xyz.lab = function (xyz) {\n\tlet x = xyz[0];\n\tlet y = xyz[1];\n\tlet z = xyz[2];\n\n\tx /= 95.047;\n\ty /= 100;\n\tz /= 108.883;\n\n\tx = x > 0.008856 ? (x ** (1 / 3)) : (7.787 * x) + (16 / 116);\n\ty = y > 0.008856 ? (y ** (1 / 3)) : (7.787 * y) + (16 / 116);\n\tz = z > 0.008856 ? (z ** (1 / 3)) : (7.787 * z) + (16 / 116);\n\n\tconst l = (116 * y) - 16;\n\tconst a = 500 * (x - y);\n\tconst b = 200 * (y - z);\n\n\treturn [l, a, b];\n};\n\nconvert.lab.xyz = function (lab) {\n\tconst l = lab[0];\n\tconst a = lab[1];\n\tconst b = lab[2];\n\tlet x;\n\tlet y;\n\tlet z;\n\n\ty = (l + 16) / 116;\n\tx = a / 500 + y;\n\tz = y - b / 200;\n\n\tconst y2 = y ** 3;\n\tconst x2 = x ** 3;\n\tconst z2 = z ** 3;\n\ty = y2 > 0.008856 ? y2 : (y - 16 / 116) / 7.787;\n\tx = x2 > 0.008856 ? x2 : (x - 16 / 116) / 7.787;\n\tz = z2 > 0.008856 ? z2 : (z - 16 / 116) / 7.787;\n\n\tx *= 95.047;\n\ty *= 100;\n\tz *= 108.883;\n\n\treturn [x, y, z];\n};\n\nconvert.lab.lch = function (lab) {\n\tconst l = lab[0];\n\tconst a = lab[1];\n\tconst b = lab[2];\n\tlet h;\n\n\tconst hr = Math.atan2(b, a);\n\th = hr * 360 / 2 / Math.PI;\n\n\tif (h < 0) {\n\t\th += 360;\n\t}\n\n\tconst c = Math.sqrt(a * a + b * b);\n\n\treturn [l, c, h];\n};\n\nconvert.lch.lab = function (lch) {\n\tconst l = lch[0];\n\tconst c = lch[1];\n\tconst h = lch[2];\n\n\tconst hr = h / 360 * 2 * Math.PI;\n\tconst a = c * Math.cos(hr);\n\tconst b = c * Math.sin(hr);\n\n\treturn [l, a, b];\n};\n\nconvert.rgb.ansi16 = function (args, saturation = null) {\n\tconst [r, g, b] = args;\n\tlet value = saturation === null ? convert.rgb.hsv(args)[2] : saturation; // Hsv -> ansi16 optimization\n\n\tvalue = Math.round(value / 50);\n\n\tif (value === 0) {\n\t\treturn 30;\n\t}\n\n\tlet ansi = 30\n\t\t+ ((Math.round(b / 255) << 2)\n\t\t| (Math.round(g / 255) << 1)\n\t\t| Math.round(r / 255));\n\n\tif (value === 2) {\n\t\tansi += 60;\n\t}\n\n\treturn ansi;\n};\n\nconvert.hsv.ansi16 = function (args) {\n\t// Optimization here; we already know the value and don't need to get\n\t// it converted for us.\n\treturn convert.rgb.ansi16(convert.hsv.rgb(args), args[2]);\n};\n\nconvert.rgb.ansi256 = function (args) {\n\tconst r = args[0];\n\tconst g = args[1];\n\tconst b = args[2];\n\n\t// We use the extended greyscale palette here, with the exception of\n\t// black and white. normal palette only has 4 greyscale shades.\n\tif (r === g && g === b) {\n\t\tif (r < 8) {\n\t\t\treturn 16;\n\t\t}\n\n\t\tif (r > 248) {\n\t\t\treturn 231;\n\t\t}\n\n\t\treturn Math.round(((r - 8) / 247) * 24) + 232;\n\t}\n\n\tconst ansi = 16\n\t\t+ (36 * Math.round(r / 255 * 5))\n\t\t+ (6 * Math.round(g / 255 * 5))\n\t\t+ Math.round(b / 255 * 5);\n\n\treturn ansi;\n};\n\nconvert.ansi16.rgb = function (args) {\n\tlet color = args % 10;\n\n\t// Handle greyscale\n\tif (color === 0 || color === 7) {\n\t\tif (args > 50) {\n\t\t\tcolor += 3.5;\n\t\t}\n\n\t\tcolor = color / 10.5 * 255;\n\n\t\treturn [color, color, color];\n\t}\n\n\tconst mult = (~~(args > 50) + 1) * 0.5;\n\tconst r = ((color & 1) * mult) * 255;\n\tconst g = (((color >> 1) & 1) * mult) * 255;\n\tconst b = (((color >> 2) & 1) * mult) * 255;\n\n\treturn [r, g, b];\n};\n\nconvert.ansi256.rgb = function (args) {\n\t// Handle greyscale\n\tif (args >= 232) {\n\t\tconst c = (args - 232) * 10 + 8;\n\t\treturn [c, c, c];\n\t}\n\n\targs -= 16;\n\n\tlet rem;\n\tconst r = Math.floor(args / 36) / 5 * 255;\n\tconst g = Math.floor((rem = args % 36) / 6) / 5 * 255;\n\tconst b = (rem % 6) / 5 * 255;\n\n\treturn [r, g, b];\n};\n\nconvert.rgb.hex = function (args) {\n\tconst integer = ((Math.round(args[0]) & 0xFF) << 16)\n\t\t+ ((Math.round(args[1]) & 0xFF) << 8)\n\t\t+ (Math.round(args[2]) & 0xFF);\n\n\tconst string = integer.toString(16).toUpperCase();\n\treturn '000000'.substring(string.length) + string;\n};\n\nconvert.hex.rgb = function (args) {\n\tconst match = args.toString(16).match(/[a-f0-9]{6}|[a-f0-9]{3}/i);\n\tif (!match) {\n\t\treturn [0, 0, 0];\n\t}\n\n\tlet colorString = match[0];\n\n\tif (match[0].length === 3) {\n\t\tcolorString = colorString.split('').map(char => {\n\t\t\treturn char + char;\n\t\t}).join('');\n\t}\n\n\tconst integer = parseInt(colorString, 16);\n\tconst r = (integer >> 16) & 0xFF;\n\tconst g = (integer >> 8) & 0xFF;\n\tconst b = integer & 0xFF;\n\n\treturn [r, g, b];\n};\n\nconvert.rgb.hcg = function (rgb) {\n\tconst r = rgb[0] / 255;\n\tconst g = rgb[1] / 255;\n\tconst b = rgb[2] / 255;\n\tconst max = Math.max(Math.max(r, g), b);\n\tconst min = Math.min(Math.min(r, g), b);\n\tconst chroma = (max - min);\n\tlet grayscale;\n\tlet hue;\n\n\tif (chroma < 1) {\n\t\tgrayscale = min / (1 - chroma);\n\t} else {\n\t\tgrayscale = 0;\n\t}\n\n\tif (chroma <= 0) {\n\t\thue = 0;\n\t} else\n\tif (max === r) {\n\t\thue = ((g - b) / chroma) % 6;\n\t} else\n\tif (max === g) {\n\t\thue = 2 + (b - r) / chroma;\n\t} else {\n\t\thue = 4 + (r - g) / chroma;\n\t}\n\n\thue /= 6;\n\thue %= 1;\n\n\treturn [hue * 360, chroma * 100, grayscale * 100];\n};\n\nconvert.hsl.hcg = function (hsl) {\n\tconst s = hsl[1] / 100;\n\tconst l = hsl[2] / 100;\n\n\tconst c = l < 0.5 ? (2.0 * s * l) : (2.0 * s * (1.0 - l));\n\n\tlet f = 0;\n\tif (c < 1.0) {\n\t\tf = (l - 0.5 * c) / (1.0 - c);\n\t}\n\n\treturn [hsl[0], c * 100, f * 100];\n};\n\nconvert.hsv.hcg = function (hsv) {\n\tconst s = hsv[1] / 100;\n\tconst v = hsv[2] / 100;\n\n\tconst c = s * v;\n\tlet f = 0;\n\n\tif (c < 1.0) {\n\t\tf = (v - c) / (1 - c);\n\t}\n\n\treturn [hsv[0], c * 100, f * 100];\n};\n\nconvert.hcg.rgb = function (hcg) {\n\tconst h = hcg[0] / 360;\n\tconst c = hcg[1] / 100;\n\tconst g = hcg[2] / 100;\n\n\tif (c === 0.0) {\n\t\treturn [g * 255, g * 255, g * 255];\n\t}\n\n\tconst pure = [0, 0, 0];\n\tconst hi = (h % 1) * 6;\n\tconst v = hi % 1;\n\tconst w = 1 - v;\n\tlet mg = 0;\n\n\t/* eslint-disable max-statements-per-line */\n\tswitch (Math.floor(hi)) {\n\t\tcase 0:\n\t\t\tpure[0] = 1; pure[1] = v; pure[2] = 0; break;\n\t\tcase 1:\n\t\t\tpure[0] = w; pure[1] = 1; pure[2] = 0; break;\n\t\tcase 2:\n\t\t\tpure[0] = 0; pure[1] = 1; pure[2] = v; break;\n\t\tcase 3:\n\t\t\tpure[0] = 0; pure[1] = w; pure[2] = 1; break;\n\t\tcase 4:\n\t\t\tpure[0] = v; pure[1] = 0; pure[2] = 1; break;\n\t\tdefault:\n\t\t\tpure[0] = 1; pure[1] = 0; pure[2] = w;\n\t}\n\t/* eslint-enable max-statements-per-line */\n\n\tmg = (1.0 - c) * g;\n\n\treturn [\n\t\t(c * pure[0] + mg) * 255,\n\t\t(c * pure[1] + mg) * 255,\n\t\t(c * pure[2] + mg) * 255\n\t];\n};\n\nconvert.hcg.hsv = function (hcg) {\n\tconst c = hcg[1] / 100;\n\tconst g = hcg[2] / 100;\n\n\tconst v = c + g * (1.0 - c);\n\tlet f = 0;\n\n\tif (v > 0.0) {\n\t\tf = c / v;\n\t}\n\n\treturn [hcg[0], f * 100, v * 100];\n};\n\nconvert.hcg.hsl = function (hcg) {\n\tconst c = hcg[1] / 100;\n\tconst g = hcg[2] / 100;\n\n\tconst l = g * (1.0 - c) + 0.5 * c;\n\tlet s = 0;\n\n\tif (l > 0.0 && l < 0.5) {\n\t\ts = c / (2 * l);\n\t} else\n\tif (l >= 0.5 && l < 1.0) {\n\t\ts = c / (2 * (1 - l));\n\t}\n\n\treturn [hcg[0], s * 100, l * 100];\n};\n\nconvert.hcg.hwb = function (hcg) {\n\tconst c = hcg[1] / 100;\n\tconst g = hcg[2] / 100;\n\tconst v = c + g * (1.0 - c);\n\treturn [hcg[0], (v - c) * 100, (1 - v) * 100];\n};\n\nconvert.hwb.hcg = function (hwb) {\n\tconst w = hwb[1] / 100;\n\tconst b = hwb[2] / 100;\n\tconst v = 1 - b;\n\tconst c = v - w;\n\tlet g = 0;\n\n\tif (c < 1) {\n\t\tg = (v - c) / (1 - c);\n\t}\n\n\treturn [hwb[0], c * 100, g * 100];\n};\n\nconvert.apple.rgb = function (apple) {\n\treturn [(apple[0] / 65535) * 255, (apple[1] / 65535) * 255, (apple[2] / 65535) * 255];\n};\n\nconvert.rgb.apple = function (rgb) {\n\treturn [(rgb[0] / 255) * 65535, (rgb[1] / 255) * 65535, (rgb[2] / 255) * 65535];\n};\n\nconvert.gray.rgb = function (args) {\n\treturn [args[0] / 100 * 255, args[0] / 100 * 255, args[0] / 100 * 255];\n};\n\nconvert.gray.hsl = function (args) {\n\treturn [0, 0, args[0]];\n};\n\nconvert.gray.hsv = convert.gray.hsl;\n\nconvert.gray.hwb = function (gray) {\n\treturn [0, 100, gray[0]];\n};\n\nconvert.gray.cmyk = function (gray) {\n\treturn [0, 0, 0, gray[0]];\n};\n\nconvert.gray.lab = function (gray) {\n\treturn [gray[0], 0, 0];\n};\n\nconvert.gray.hex = function (gray) {\n\tconst val = Math.round(gray[0] / 100 * 255) & 0xFF;\n\tconst integer = (val << 16) + (val << 8) + val;\n\n\tconst string = integer.toString(16).toUpperCase();\n\treturn '000000'.substring(string.length) + string;\n};\n\nconvert.rgb.gray = function (rgb) {\n\tconst val = (rgb[0] + rgb[1] + rgb[2]) / 3;\n\treturn [val / 255 * 100];\n};\n", "const conversions = require('./conversions');\n\n/*\n\tThis function routes a model to all other models.\n\n\tall functions that are routed have a property `.conversion` attached\n\tto the returned synthetic function. This property is an array\n\tof strings, each with the steps in between the 'from' and 'to'\n\tcolor models (inclusive).\n\n\tconversions that are not possible simply are not included.\n*/\n\nfunction buildGraph() {\n\tconst graph = {};\n\t// https://jsperf.com/object-keys-vs-for-in-with-closure/3\n\tconst models = Object.keys(conversions);\n\n\tfor (let len = models.length, i = 0; i < len; i++) {\n\t\tgraph[models[i]] = {\n\t\t\t// http://jsperf.com/1-vs-infinity\n\t\t\t// micro-opt, but this is simple.\n\t\t\tdistance: -1,\n\t\t\tparent: null\n\t\t};\n\t}\n\n\treturn graph;\n}\n\n// https://en.wikipedia.org/wiki/Breadth-first_search\nfunction deriveBFS(fromModel) {\n\tconst graph = buildGraph();\n\tconst queue = [fromModel]; // Unshift -> queue -> pop\n\n\tgraph[fromModel].distance = 0;\n\n\twhile (queue.length) {\n\t\tconst current = queue.pop();\n\t\tconst adjacents = Object.keys(conversions[current]);\n\n\t\tfor (let len = adjacents.length, i = 0; i < len; i++) {\n\t\t\tconst adjacent = adjacents[i];\n\t\t\tconst node = graph[adjacent];\n\n\t\t\tif (node.distance === -1) {\n\t\t\t\tnode.distance = graph[current].distance + 1;\n\t\t\t\tnode.parent = current;\n\t\t\t\tqueue.unshift(adjacent);\n\t\t\t}\n\t\t}\n\t}\n\n\treturn graph;\n}\n\nfunction link(from, to) {\n\treturn function (args) {\n\t\treturn to(from(args));\n\t};\n}\n\nfunction wrapConversion(toModel, graph) {\n\tconst path = [graph[toModel].parent, toModel];\n\tlet fn = conversions[graph[toModel].parent][toModel];\n\n\tlet cur = graph[toModel].parent;\n\twhile (graph[cur].parent) {\n\t\tpath.unshift(graph[cur].parent);\n\t\tfn = link(conversions[graph[cur].parent][cur], fn);\n\t\tcur = graph[cur].parent;\n\t}\n\n\tfn.conversion = path;\n\treturn fn;\n}\n\nmodule.exports = function (fromModel) {\n\tconst graph = deriveBFS(fromModel);\n\tconst conversion = {};\n\n\tconst models = Object.keys(graph);\n\tfor (let len = models.length, i = 0; i < len; i++) {\n\t\tconst toModel = models[i];\n\t\tconst node = graph[toModel];\n\n\t\tif (node.parent === null) {\n\t\t\t// No possible conversion, or this node is the source model.\n\t\t\tcontinue;\n\t\t}\n\n\t\tconversion[toModel] = wrapConversion(toModel, graph);\n\t}\n\n\treturn conversion;\n};\n\n", "const conversions = require('./conversions');\nconst route = require('./route');\n\nconst convert = {};\n\nconst models = Object.keys(conversions);\n\nfunction wrapRaw(fn) {\n\tconst wrappedFn = function (...args) {\n\t\tconst arg0 = args[0];\n\t\tif (arg0 === undefined || arg0 === null) {\n\t\t\treturn arg0;\n\t\t}\n\n\t\tif (arg0.length > 1) {\n\t\t\targs = arg0;\n\t\t}\n\n\t\treturn fn(args);\n\t};\n\n\t// Preserve .conversion property if there is one\n\tif ('conversion' in fn) {\n\t\twrappedFn.conversion = fn.conversion;\n\t}\n\n\treturn wrappedFn;\n}\n\nfunction wrapRounded(fn) {\n\tconst wrappedFn = function (...args) {\n\t\tconst arg0 = args[0];\n\n\t\tif (arg0 === undefined || arg0 === null) {\n\t\t\treturn arg0;\n\t\t}\n\n\t\tif (arg0.length > 1) {\n\t\t\targs = arg0;\n\t\t}\n\n\t\tconst result = fn(args);\n\n\t\t// We're assuming the result is an array here.\n\t\t// see notice in conversions.js; don't use box types\n\t\t// in conversion functions.\n\t\tif (typeof result === 'object') {\n\t\t\tfor (let len = result.length, i = 0; i < len; i++) {\n\t\t\t\tresult[i] = Math.round(result[i]);\n\t\t\t}\n\t\t}\n\n\t\treturn result;\n\t};\n\n\t// Preserve .conversion property if there is one\n\tif ('conversion' in fn) {\n\t\twrappedFn.conversion = fn.conversion;\n\t}\n\n\treturn wrappedFn;\n}\n\nmodels.forEach(fromModel => {\n\tconvert[fromModel] = {};\n\n\tObject.defineProperty(convert[fromModel], 'channels', {value: conversions[fromModel].channels});\n\tObject.defineProperty(convert[fromModel], 'labels', {value: conversions[fromModel].labels});\n\n\tconst routes = route(fromModel);\n\tconst routeModels = Object.keys(routes);\n\n\trouteModels.forEach(toModel => {\n\t\tconst fn = routes[toModel];\n\n\t\tconvert[fromModel][toModel] = wrapRounded(fn);\n\t\tconvert[fromModel][toModel].raw = wrapRaw(fn);\n\t});\n});\n\nmodule.exports = convert;\n", "const colorString = require('color-string');\nconst convert = require('color-convert');\n\nconst skippedModels = [\n\t// To be honest, I don't really feel like keyword belongs in color convert, but eh.\n\t'keyword',\n\n\t// Gray conflicts with some method names, and has its own method defined.\n\t'gray',\n\n\t// Shouldn't really be in color-convert either...\n\t'hex',\n];\n\nconst hashedModelKeys = {};\nfor (const model of Object.keys(convert)) {\n\thashedModelKeys[[...convert[model].labels].sort().join('')] = model;\n}\n\nconst limiters = {};\n\nfunction Color(object, model) {\n\tif (!(this instanceof Color)) {\n\t\treturn new Color(object, model);\n\t}\n\n\tif (model && model in skippedModels) {\n\t\tmodel = null;\n\t}\n\n\tif (model && !(model in convert)) {\n\t\tthrow new Error('Unknown model: ' + model);\n\t}\n\n\tlet i;\n\tlet channels;\n\n\tif (object == null) { // eslint-disable-line no-eq-null,eqeqeq\n\t\tthis.model = 'rgb';\n\t\tthis.color = [0, 0, 0];\n\t\tthis.valpha = 1;\n\t} else if (object instanceof Color) {\n\t\tthis.model = object.model;\n\t\tthis.color = [...object.color];\n\t\tthis.valpha = object.valpha;\n\t} else if (typeof object === 'string') {\n\t\tconst result = colorString.get(object);\n\t\tif (result === null) {\n\t\t\tthrow new Error('Unable to parse color from string: ' + object);\n\t\t}\n\n\t\tthis.model = result.model;\n\t\tchannels = convert[this.model].channels;\n\t\tthis.color = result.value.slice(0, channels);\n\t\tthis.valpha = typeof result.value[channels] === 'number' ? result.value[channels] : 1;\n\t} else if (object.length > 0) {\n\t\tthis.model = model || 'rgb';\n\t\tchannels = convert[this.model].channels;\n\t\tconst newArray = Array.prototype.slice.call(object, 0, channels);\n\t\tthis.color = zeroArray(newArray, channels);\n\t\tthis.valpha = typeof object[channels] === 'number' ? object[channels] : 1;\n\t} else if (typeof object === 'number') {\n\t\t// This is always RGB - can be converted later on.\n\t\tthis.model = 'rgb';\n\t\tthis.color = [\n\t\t\t(object >> 16) & 0xFF,\n\t\t\t(object >> 8) & 0xFF,\n\t\t\tobject & 0xFF,\n\t\t];\n\t\tthis.valpha = 1;\n\t} else {\n\t\tthis.valpha = 1;\n\n\t\tconst keys = Object.keys(object);\n\t\tif ('alpha' in object) {\n\t\t\tkeys.splice(keys.indexOf('alpha'), 1);\n\t\t\tthis.valpha = typeof object.alpha === 'number' ? object.alpha : 0;\n\t\t}\n\n\t\tconst hashedKeys = keys.sort().join('');\n\t\tif (!(hashedKeys in hashedModelKeys)) {\n\t\t\tthrow new Error('Unable to parse color from object: ' + JSON.stringify(object));\n\t\t}\n\n\t\tthis.model = hashedModelKeys[hashedKeys];\n\n\t\tconst {labels} = convert[this.model];\n\t\tconst color = [];\n\t\tfor (i = 0; i < labels.length; i++) {\n\t\t\tcolor.push(object[labels[i]]);\n\t\t}\n\n\t\tthis.color = zeroArray(color);\n\t}\n\n\t// Perform limitations (clamping, etc.)\n\tif (limiters[this.model]) {\n\t\tchannels = convert[this.model].channels;\n\t\tfor (i = 0; i < channels; i++) {\n\t\t\tconst limit = limiters[this.model][i];\n\t\t\tif (limit) {\n\t\t\t\tthis.color[i] = limit(this.color[i]);\n\t\t\t}\n\t\t}\n\t}\n\n\tthis.valpha = Math.max(0, Math.min(1, this.valpha));\n\n\tif (Object.freeze) {\n\t\tObject.freeze(this);\n\t}\n}\n\nColor.prototype = {\n\ttoString() {\n\t\treturn this.string();\n\t},\n\n\ttoJSON() {\n\t\treturn this[this.model]();\n\t},\n\n\tstring(places) {\n\t\tlet self = this.model in colorString.to ? this : this.rgb();\n\t\tself = self.round(typeof places === 'number' ? places : 1);\n\t\tconst args = self.valpha === 1 ? self.color : [...self.color, this.valpha];\n\t\treturn colorString.to[self.model](args);\n\t},\n\n\tpercentString(places) {\n\t\tconst self = this.rgb().round(typeof places === 'number' ? places : 1);\n\t\tconst args = self.valpha === 1 ? self.color : [...self.color, this.valpha];\n\t\treturn colorString.to.rgb.percent(args);\n\t},\n\n\tarray() {\n\t\treturn this.valpha === 1 ? [...this.color] : [...this.color, this.valpha];\n\t},\n\n\tobject() {\n\t\tconst result = {};\n\t\tconst {channels} = convert[this.model];\n\t\tconst {labels} = convert[this.model];\n\n\t\tfor (let i = 0; i < channels; i++) {\n\t\t\tresult[labels[i]] = this.color[i];\n\t\t}\n\n\t\tif (this.valpha !== 1) {\n\t\t\tresult.alpha = this.valpha;\n\t\t}\n\n\t\treturn result;\n\t},\n\n\tunitArray() {\n\t\tconst rgb = this.rgb().color;\n\t\trgb[0] /= 255;\n\t\trgb[1] /= 255;\n\t\trgb[2] /= 255;\n\n\t\tif (this.valpha !== 1) {\n\t\t\trgb.push(this.valpha);\n\t\t}\n\n\t\treturn rgb;\n\t},\n\n\tunitObject() {\n\t\tconst rgb = this.rgb().object();\n\t\trgb.r /= 255;\n\t\trgb.g /= 255;\n\t\trgb.b /= 255;\n\n\t\tif (this.valpha !== 1) {\n\t\t\trgb.alpha = this.valpha;\n\t\t}\n\n\t\treturn rgb;\n\t},\n\n\tround(places) {\n\t\tplaces = Math.max(places || 0, 0);\n\t\treturn new Color([...this.color.map(roundToPlace(places)), this.valpha], this.model);\n\t},\n\n\talpha(value) {\n\t\tif (value !== undefined) {\n\t\t\treturn new Color([...this.color, Math.max(0, Math.min(1, value))], this.model);\n\t\t}\n\n\t\treturn this.valpha;\n\t},\n\n\t// Rgb\n\tred: getset('rgb', 0, maxfn(255)),\n\tgreen: getset('rgb', 1, maxfn(255)),\n\tblue: getset('rgb', 2, maxfn(255)),\n\n\thue: getset(['hsl', 'hsv', 'hsl', 'hwb', 'hcg'], 0, value => ((value % 360) + 360) % 360),\n\n\tsaturationl: getset('hsl', 1, maxfn(100)),\n\tlightness: getset('hsl', 2, maxfn(100)),\n\n\tsaturationv: getset('hsv', 1, maxfn(100)),\n\tvalue: getset('hsv', 2, maxfn(100)),\n\n\tchroma: getset('hcg', 1, maxfn(100)),\n\tgray: getset('hcg', 2, maxfn(100)),\n\n\twhite: getset('hwb', 1, maxfn(100)),\n\twblack: getset('hwb', 2, maxfn(100)),\n\n\tcyan: getset('cmyk', 0, maxfn(100)),\n\tmagenta: getset('cmyk', 1, maxfn(100)),\n\tyellow: getset('cmyk', 2, maxfn(100)),\n\tblack: getset('cmyk', 3, maxfn(100)),\n\n\tx: getset('xyz', 0, maxfn(95.047)),\n\ty: getset('xyz', 1, maxfn(100)),\n\tz: getset('xyz', 2, maxfn(108.833)),\n\n\tl: getset('lab', 0, maxfn(100)),\n\ta: getset('lab', 1),\n\tb: getset('lab', 2),\n\n\tkeyword(value) {\n\t\tif (value !== undefined) {\n\t\t\treturn new Color(value);\n\t\t}\n\n\t\treturn convert[this.model].keyword(this.color);\n\t},\n\n\thex(value) {\n\t\tif (value !== undefined) {\n\t\t\treturn new Color(value);\n\t\t}\n\n\t\treturn colorString.to.hex(this.rgb().round().color);\n\t},\n\n\thexa(value) {\n\t\tif (value !== undefined) {\n\t\t\treturn new Color(value);\n\t\t}\n\n\t\tconst rgbArray = this.rgb().round().color;\n\n\t\tlet alphaHex = Math.round(this.valpha * 255).toString(16).toUpperCase();\n\t\tif (alphaHex.length === 1) {\n\t\t\talphaHex = '0' + alphaHex;\n\t\t}\n\n\t\treturn colorString.to.hex(rgbArray) + alphaHex;\n\t},\n\n\trgbNumber() {\n\t\tconst rgb = this.rgb().color;\n\t\treturn ((rgb[0] & 0xFF) << 16) | ((rgb[1] & 0xFF) << 8) | (rgb[2] & 0xFF);\n\t},\n\n\tluminosity() {\n\t\t// http://www.w3.org/TR/WCAG20/#relativeluminancedef\n\t\tconst rgb = this.rgb().color;\n\n\t\tconst lum = [];\n\t\tfor (const [i, element] of rgb.entries()) {\n\t\t\tconst chan = element / 255;\n\t\t\tlum[i] = (chan <= 0.04045) ? chan / 12.92 : ((chan + 0.055) / 1.055) ** 2.4;\n\t\t}\n\n\t\treturn 0.2126 * lum[0] + 0.7152 * lum[1] + 0.0722 * lum[2];\n\t},\n\n\tcontrast(color2) {\n\t\t// http://www.w3.org/TR/WCAG20/#contrast-ratiodef\n\t\tconst lum1 = this.luminosity();\n\t\tconst lum2 = color2.luminosity();\n\n\t\tif (lum1 > lum2) {\n\t\t\treturn (lum1 + 0.05) / (lum2 + 0.05);\n\t\t}\n\n\t\treturn (lum2 + 0.05) / (lum1 + 0.05);\n\t},\n\n\tlevel(color2) {\n\t\t// https://www.w3.org/TR/WCAG/#contrast-enhanced\n\t\tconst contrastRatio = this.contrast(color2);\n\t\tif (contrastRatio >= 7) {\n\t\t\treturn 'AAA';\n\t\t}\n\n\t\treturn (contrastRatio >= 4.5) ? 'AA' : '';\n\t},\n\n\tisDark() {\n\t\t// YIQ equation from http://24ways.org/2010/calculating-color-contrast\n\t\tconst rgb = this.rgb().color;\n\t\tconst yiq = (rgb[0] * 2126 + rgb[1] * 7152 + rgb[2] * 722) / 10000;\n\t\treturn yiq < 128;\n\t},\n\n\tisLight() {\n\t\treturn !this.isDark();\n\t},\n\n\tnegate() {\n\t\tconst rgb = this.rgb();\n\t\tfor (let i = 0; i < 3; i++) {\n\t\t\trgb.color[i] = 255 - rgb.color[i];\n\t\t}\n\n\t\treturn rgb;\n\t},\n\n\tlighten(ratio) {\n\t\tconst hsl = this.hsl();\n\t\thsl.color[2] += hsl.color[2] * ratio;\n\t\treturn hsl;\n\t},\n\n\tdarken(ratio) {\n\t\tconst hsl = this.hsl();\n\t\thsl.color[2] -= hsl.color[2] * ratio;\n\t\treturn hsl;\n\t},\n\n\tsaturate(ratio) {\n\t\tconst hsl = this.hsl();\n\t\thsl.color[1] += hsl.color[1] * ratio;\n\t\treturn hsl;\n\t},\n\n\tdesaturate(ratio) {\n\t\tconst hsl = this.hsl();\n\t\thsl.color[1] -= hsl.color[1] * ratio;\n\t\treturn hsl;\n\t},\n\n\twhiten(ratio) {\n\t\tconst hwb = this.hwb();\n\t\thwb.color[1] += hwb.color[1] * ratio;\n\t\treturn hwb;\n\t},\n\n\tblacken(ratio) {\n\t\tconst hwb = this.hwb();\n\t\thwb.color[2] += hwb.color[2] * ratio;\n\t\treturn hwb;\n\t},\n\n\tgrayscale() {\n\t\t// http://en.wikipedia.org/wiki/Grayscale#Converting_color_to_grayscale\n\t\tconst rgb = this.rgb().color;\n\t\tconst value = rgb[0] * 0.3 + rgb[1] * 0.59 + rgb[2] * 0.11;\n\t\treturn Color.rgb(value, value, value);\n\t},\n\n\tfade(ratio) {\n\t\treturn this.alpha(this.valpha - (this.valpha * ratio));\n\t},\n\n\topaquer(ratio) {\n\t\treturn this.alpha(this.valpha + (this.valpha * ratio));\n\t},\n\n\trotate(degrees) {\n\t\tconst hsl = this.hsl();\n\t\tlet hue = hsl.color[0];\n\t\thue = (hue + degrees) % 360;\n\t\thue = hue < 0 ? 360 + hue : hue;\n\t\thsl.color[0] = hue;\n\t\treturn hsl;\n\t},\n\n\tmix(mixinColor, weight) {\n\t\t// Ported from sass implementation in C\n\t\t// https://github.com/sass/libsass/blob/0e6b4a2850092356aa3ece07c6b249f0221caced/functions.cpp#L209\n\t\tif (!mixinColor || !mixinColor.rgb) {\n\t\t\tthrow new Error('Argument to \"mix\" was not a Color instance, but rather an instance of ' + typeof mixinColor);\n\t\t}\n\n\t\tconst color1 = mixinColor.rgb();\n\t\tconst color2 = this.rgb();\n\t\tconst p = weight === undefined ? 0.5 : weight;\n\n\t\tconst w = 2 * p - 1;\n\t\tconst a = color1.alpha() - color2.alpha();\n\n\t\tconst w1 = (((w * a === -1) ? w : (w + a) / (1 + w * a)) + 1) / 2;\n\t\tconst w2 = 1 - w1;\n\n\t\treturn Color.rgb(\n\t\t\tw1 * color1.red() + w2 * color2.red(),\n\t\t\tw1 * color1.green() + w2 * color2.green(),\n\t\t\tw1 * color1.blue() + w2 * color2.blue(),\n\t\t\tcolor1.alpha() * p + color2.alpha() * (1 - p));\n\t},\n};\n\n// Model conversion methods and static constructors\nfor (const model of Object.keys(convert)) {\n\tif (skippedModels.includes(model)) {\n\t\tcontinue;\n\t}\n\n\tconst {channels} = convert[model];\n\n\t// Conversion methods\n\tColor.prototype[model] = function (...args) {\n\t\tif (this.model === model) {\n\t\t\treturn new Color(this);\n\t\t}\n\n\t\tif (args.length > 0) {\n\t\t\treturn new Color(args, model);\n\t\t}\n\n\t\treturn new Color([...assertArray(convert[this.model][model].raw(this.color)), this.valpha], model);\n\t};\n\n\t// 'static' construction methods\n\tColor[model] = function (...args) {\n\t\tlet color = args[0];\n\t\tif (typeof color === 'number') {\n\t\t\tcolor = zeroArray(args, channels);\n\t\t}\n\n\t\treturn new Color(color, model);\n\t};\n}\n\nfunction roundTo(number, places) {\n\treturn Number(number.toFixed(places));\n}\n\nfunction roundToPlace(places) {\n\treturn function (number) {\n\t\treturn roundTo(number, places);\n\t};\n}\n\nfunction getset(model, channel, modifier) {\n\tmodel = Array.isArray(model) ? model : [model];\n\n\tfor (const m of model) {\n\t\t(limiters[m] || (limiters[m] = []))[channel] = modifier;\n\t}\n\n\tmodel = model[0];\n\n\treturn function (value) {\n\t\tlet result;\n\n\t\tif (value !== undefined) {\n\t\t\tif (modifier) {\n\t\t\t\tvalue = modifier(value);\n\t\t\t}\n\n\t\t\tresult = this[model]();\n\t\t\tresult.color[channel] = value;\n\t\t\treturn result;\n\t\t}\n\n\t\tresult = this[model]().color[channel];\n\t\tif (modifier) {\n\t\t\tresult = modifier(result);\n\t\t}\n\n\t\treturn result;\n\t};\n}\n\nfunction maxfn(max) {\n\treturn function (v) {\n\t\treturn Math.max(0, Math.min(max, v));\n\t};\n}\n\nfunction assertArray(value) {\n\treturn Array.isArray(value) ? value : [value];\n}\n\nfunction zeroArray(array, length) {\n\tfor (let i = 0; i < length; i++) {\n\t\tif (typeof array[i] !== 'number') {\n\t\t\tarray[i] = 0;\n\t\t}\n\t}\n\n\treturn array;\n}\n\nmodule.exports = Color;\n"], "mappings": ";;;;;AAAA;AAAA;AAAA;AAEA,WAAO,UAAU;AAAA,MAChB,aAAa,CAAC,KAAK,KAAK,GAAG;AAAA,MAC3B,gBAAgB,CAAC,KAAK,KAAK,GAAG;AAAA,MAC9B,QAAQ,CAAC,GAAG,KAAK,GAAG;AAAA,MACpB,cAAc,CAAC,KAAK,KAAK,GAAG;AAAA,MAC5B,SAAS,CAAC,KAAK,KAAK,GAAG;AAAA,MACvB,SAAS,CAAC,KAAK,KAAK,GAAG;AAAA,MACvB,UAAU,CAAC,KAAK,KAAK,GAAG;AAAA,MACxB,SAAS,CAAC,GAAG,GAAG,CAAC;AAAA,MACjB,kBAAkB,CAAC,KAAK,KAAK,GAAG;AAAA,MAChC,QAAQ,CAAC,GAAG,GAAG,GAAG;AAAA,MAClB,cAAc,CAAC,KAAK,IAAI,GAAG;AAAA,MAC3B,SAAS,CAAC,KAAK,IAAI,EAAE;AAAA,MACrB,aAAa,CAAC,KAAK,KAAK,GAAG;AAAA,MAC3B,aAAa,CAAC,IAAI,KAAK,GAAG;AAAA,MAC1B,cAAc,CAAC,KAAK,KAAK,CAAC;AAAA,MAC1B,aAAa,CAAC,KAAK,KAAK,EAAE;AAAA,MAC1B,SAAS,CAAC,KAAK,KAAK,EAAE;AAAA,MACtB,kBAAkB,CAAC,KAAK,KAAK,GAAG;AAAA,MAChC,YAAY,CAAC,KAAK,KAAK,GAAG;AAAA,MAC1B,WAAW,CAAC,KAAK,IAAI,EAAE;AAAA,MACvB,QAAQ,CAAC,GAAG,KAAK,GAAG;AAAA,MACpB,YAAY,CAAC,GAAG,GAAG,GAAG;AAAA,MACtB,YAAY,CAAC,GAAG,KAAK,GAAG;AAAA,MACxB,iBAAiB,CAAC,KAAK,KAAK,EAAE;AAAA,MAC9B,YAAY,CAAC,KAAK,KAAK,GAAG;AAAA,MAC1B,aAAa,CAAC,GAAG,KAAK,CAAC;AAAA,MACvB,YAAY,CAAC,KAAK,KAAK,GAAG;AAAA,MAC1B,aAAa,CAAC,KAAK,KAAK,GAAG;AAAA,MAC3B,eAAe,CAAC,KAAK,GAAG,GAAG;AAAA,MAC3B,kBAAkB,CAAC,IAAI,KAAK,EAAE;AAAA,MAC9B,cAAc,CAAC,KAAK,KAAK,CAAC;AAAA,MAC1B,cAAc,CAAC,KAAK,IAAI,GAAG;AAAA,MAC3B,WAAW,CAAC,KAAK,GAAG,CAAC;AAAA,MACrB,cAAc,CAAC,KAAK,KAAK,GAAG;AAAA,MAC5B,gBAAgB,CAAC,KAAK,KAAK,GAAG;AAAA,MAC9B,iBAAiB,CAAC,IAAI,IAAI,GAAG;AAAA,MAC7B,iBAAiB,CAAC,IAAI,IAAI,EAAE;AAAA,MAC5B,iBAAiB,CAAC,IAAI,IAAI,EAAE;AAAA,MAC5B,iBAAiB,CAAC,GAAG,KAAK,GAAG;AAAA,MAC7B,cAAc,CAAC,KAAK,GAAG,GAAG;AAAA,MAC1B,YAAY,CAAC,KAAK,IAAI,GAAG;AAAA,MACzB,eAAe,CAAC,GAAG,KAAK,GAAG;AAAA,MAC3B,WAAW,CAAC,KAAK,KAAK,GAAG;AAAA,MACzB,WAAW,CAAC,KAAK,KAAK,GAAG;AAAA,MACzB,cAAc,CAAC,IAAI,KAAK,GAAG;AAAA,MAC3B,aAAa,CAAC,KAAK,IAAI,EAAE;AAAA,MACzB,eAAe,CAAC,KAAK,KAAK,GAAG;AAAA,MAC7B,eAAe,CAAC,IAAI,KAAK,EAAE;AAAA,MAC3B,WAAW,CAAC,KAAK,GAAG,GAAG;AAAA,MACvB,aAAa,CAAC,KAAK,KAAK,GAAG;AAAA,MAC3B,cAAc,CAAC,KAAK,KAAK,GAAG;AAAA,MAC5B,QAAQ,CAAC,KAAK,KAAK,CAAC;AAAA,MACpB,aAAa,CAAC,KAAK,KAAK,EAAE;AAAA,MAC1B,QAAQ,CAAC,KAAK,KAAK,GAAG;AAAA,MACtB,SAAS,CAAC,GAAG,KAAK,CAAC;AAAA,MACnB,eAAe,CAAC,KAAK,KAAK,EAAE;AAAA,MAC5B,QAAQ,CAAC,KAAK,KAAK,GAAG;AAAA,MACtB,YAAY,CAAC,KAAK,KAAK,GAAG;AAAA,MAC1B,WAAW,CAAC,KAAK,KAAK,GAAG;AAAA,MACzB,aAAa,CAAC,KAAK,IAAI,EAAE;AAAA,MACzB,UAAU,CAAC,IAAI,GAAG,GAAG;AAAA,MACrB,SAAS,CAAC,KAAK,KAAK,GAAG;AAAA,MACvB,SAAS,CAAC,KAAK,KAAK,GAAG;AAAA,MACvB,YAAY,CAAC,KAAK,KAAK,GAAG;AAAA,MAC1B,iBAAiB,CAAC,KAAK,KAAK,GAAG;AAAA,MAC/B,aAAa,CAAC,KAAK,KAAK,CAAC;AAAA,MACzB,gBAAgB,CAAC,KAAK,KAAK,GAAG;AAAA,MAC9B,aAAa,CAAC,KAAK,KAAK,GAAG;AAAA,MAC3B,cAAc,CAAC,KAAK,KAAK,GAAG;AAAA,MAC5B,aAAa,CAAC,KAAK,KAAK,GAAG;AAAA,MAC3B,wBAAwB,CAAC,KAAK,KAAK,GAAG;AAAA,MACtC,aAAa,CAAC,KAAK,KAAK,GAAG;AAAA,MAC3B,cAAc,CAAC,KAAK,KAAK,GAAG;AAAA,MAC5B,aAAa,CAAC,KAAK,KAAK,GAAG;AAAA,MAC3B,aAAa,CAAC,KAAK,KAAK,GAAG;AAAA,MAC3B,eAAe,CAAC,KAAK,KAAK,GAAG;AAAA,MAC7B,iBAAiB,CAAC,IAAI,KAAK,GAAG;AAAA,MAC9B,gBAAgB,CAAC,KAAK,KAAK,GAAG;AAAA,MAC9B,kBAAkB,CAAC,KAAK,KAAK,GAAG;AAAA,MAChC,kBAAkB,CAAC,KAAK,KAAK,GAAG;AAAA,MAChC,kBAAkB,CAAC,KAAK,KAAK,GAAG;AAAA,MAChC,eAAe,CAAC,KAAK,KAAK,GAAG;AAAA,MAC7B,QAAQ,CAAC,GAAG,KAAK,CAAC;AAAA,MAClB,aAAa,CAAC,IAAI,KAAK,EAAE;AAAA,MACzB,SAAS,CAAC,KAAK,KAAK,GAAG;AAAA,MACvB,WAAW,CAAC,KAAK,GAAG,GAAG;AAAA,MACvB,UAAU,CAAC,KAAK,GAAG,CAAC;AAAA,MACpB,oBAAoB,CAAC,KAAK,KAAK,GAAG;AAAA,MAClC,cAAc,CAAC,GAAG,GAAG,GAAG;AAAA,MACxB,gBAAgB,CAAC,KAAK,IAAI,GAAG;AAAA,MAC7B,gBAAgB,CAAC,KAAK,KAAK,GAAG;AAAA,MAC9B,kBAAkB,CAAC,IAAI,KAAK,GAAG;AAAA,MAC/B,mBAAmB,CAAC,KAAK,KAAK,GAAG;AAAA,MACjC,qBAAqB,CAAC,GAAG,KAAK,GAAG;AAAA,MACjC,mBAAmB,CAAC,IAAI,KAAK,GAAG;AAAA,MAChC,mBAAmB,CAAC,KAAK,IAAI,GAAG;AAAA,MAChC,gBAAgB,CAAC,IAAI,IAAI,GAAG;AAAA,MAC5B,aAAa,CAAC,KAAK,KAAK,GAAG;AAAA,MAC3B,aAAa,CAAC,KAAK,KAAK,GAAG;AAAA,MAC3B,YAAY,CAAC,KAAK,KAAK,GAAG;AAAA,MAC1B,eAAe,CAAC,KAAK,KAAK,GAAG;AAAA,MAC7B,QAAQ,CAAC,GAAG,GAAG,GAAG;AAAA,MAClB,WAAW,CAAC,KAAK,KAAK,GAAG;AAAA,MACzB,SAAS,CAAC,KAAK,KAAK,CAAC;AAAA,MACrB,aAAa,CAAC,KAAK,KAAK,EAAE;AAAA,MAC1B,UAAU,CAAC,KAAK,KAAK,CAAC;AAAA,MACtB,aAAa,CAAC,KAAK,IAAI,CAAC;AAAA,MACxB,UAAU,CAAC,KAAK,KAAK,GAAG;AAAA,MACxB,iBAAiB,CAAC,KAAK,KAAK,GAAG;AAAA,MAC/B,aAAa,CAAC,KAAK,KAAK,GAAG;AAAA,MAC3B,iBAAiB,CAAC,KAAK,KAAK,GAAG;AAAA,MAC/B,iBAAiB,CAAC,KAAK,KAAK,GAAG;AAAA,MAC/B,cAAc,CAAC,KAAK,KAAK,GAAG;AAAA,MAC5B,aAAa,CAAC,KAAK,KAAK,GAAG;AAAA,MAC3B,QAAQ,CAAC,KAAK,KAAK,EAAE;AAAA,MACrB,QAAQ,CAAC,KAAK,KAAK,GAAG;AAAA,MACtB,QAAQ,CAAC,KAAK,KAAK,GAAG;AAAA,MACtB,cAAc,CAAC,KAAK,KAAK,GAAG;AAAA,MAC5B,UAAU,CAAC,KAAK,GAAG,GAAG;AAAA,MACtB,iBAAiB,CAAC,KAAK,IAAI,GAAG;AAAA,MAC9B,OAAO,CAAC,KAAK,GAAG,CAAC;AAAA,MACjB,aAAa,CAAC,KAAK,KAAK,GAAG;AAAA,MAC3B,aAAa,CAAC,IAAI,KAAK,GAAG;AAAA,MAC1B,eAAe,CAAC,KAAK,IAAI,EAAE;AAAA,MAC3B,UAAU,CAAC,KAAK,KAAK,GAAG;AAAA,MACxB,cAAc,CAAC,KAAK,KAAK,EAAE;AAAA,MAC3B,YAAY,CAAC,IAAI,KAAK,EAAE;AAAA,MACxB,YAAY,CAAC,KAAK,KAAK,GAAG;AAAA,MAC1B,UAAU,CAAC,KAAK,IAAI,EAAE;AAAA,MACtB,UAAU,CAAC,KAAK,KAAK,GAAG;AAAA,MACxB,WAAW,CAAC,KAAK,KAAK,GAAG;AAAA,MACzB,aAAa,CAAC,KAAK,IAAI,GAAG;AAAA,MAC1B,aAAa,CAAC,KAAK,KAAK,GAAG;AAAA,MAC3B,aAAa,CAAC,KAAK,KAAK,GAAG;AAAA,MAC3B,QAAQ,CAAC,KAAK,KAAK,GAAG;AAAA,MACtB,eAAe,CAAC,GAAG,KAAK,GAAG;AAAA,MAC3B,aAAa,CAAC,IAAI,KAAK,GAAG;AAAA,MAC1B,OAAO,CAAC,KAAK,KAAK,GAAG;AAAA,MACrB,QAAQ,CAAC,GAAG,KAAK,GAAG;AAAA,MACpB,WAAW,CAAC,KAAK,KAAK,GAAG;AAAA,MACzB,UAAU,CAAC,KAAK,IAAI,EAAE;AAAA,MACtB,aAAa,CAAC,IAAI,KAAK,GAAG;AAAA,MAC1B,UAAU,CAAC,KAAK,KAAK,GAAG;AAAA,MACxB,SAAS,CAAC,KAAK,KAAK,GAAG;AAAA,MACvB,SAAS,CAAC,KAAK,KAAK,GAAG;AAAA,MACvB,cAAc,CAAC,KAAK,KAAK,GAAG;AAAA,MAC5B,UAAU,CAAC,KAAK,KAAK,CAAC;AAAA,MACtB,eAAe,CAAC,KAAK,KAAK,EAAE;AAAA,IAC7B;AAAA;AAAA;;;ACvJA;AAAA;AAAA,WAAO,UAAU,SAAS,WAAW,KAAK;AACzC,UAAI,CAAC,OAAO,OAAO,QAAQ,UAAU;AACpC,eAAO;AAAA,MACR;AAEA,aAAO,eAAe,SAAS,MAAM,QAAQ,GAAG,KAC9C,IAAI,UAAU,MAAM,IAAI,kBAAkB,YACzC,OAAO,yBAAyB,KAAM,IAAI,SAAS,CAAE,KAAK,IAAI,YAAY,SAAS;AAAA,IACvF;AAAA;AAAA;;;ACRA;AAAA;AAAA;AAEA,QAAI,aAAa;AAEjB,QAAI,SAAS,MAAM,UAAU;AAC7B,QAAI,QAAQ,MAAM,UAAU;AAE5B,QAAI,UAAU,OAAO,UAAU,SAASA,SAAQ,MAAM;AACrD,UAAI,UAAU,CAAC;AAEf,eAAS,IAAI,GAAG,MAAM,KAAK,QAAQ,IAAI,KAAK,KAAK;AAChD,YAAI,MAAM,KAAK,CAAC;AAEhB,YAAI,WAAW,GAAG,GAAG;AAEpB,oBAAU,OAAO,KAAK,SAAS,MAAM,KAAK,GAAG,CAAC;AAAA,QAC/C,OAAO;AACN,kBAAQ,KAAK,GAAG;AAAA,QACjB;AAAA,MACD;AAEA,aAAO;AAAA,IACR;AAEA,YAAQ,OAAO,SAAU,IAAI;AAC5B,aAAO,WAAY;AAClB,eAAO,GAAG,QAAQ,SAAS,CAAC;AAAA,MAC7B;AAAA,IACD;AAAA;AAAA;;;AC5BA;AAAA;AACA,QAAI,aAAa;AACjB,QAAI,UAAU;AACd,QAAI,iBAAiB,OAAO;AAE5B,QAAI,eAAe,uBAAO,OAAO,IAAI;AAGrC,SAAS,QAAQ,YAAY;AAC5B,UAAI,eAAe,KAAK,YAAY,IAAI,GAAG;AAC1C,qBAAa,WAAW,IAAI,CAAC,IAAI;AAAA,MAClC;AAAA,IACD;AAJS;AAMT,QAAI,KAAK,OAAO,UAAU;AAAA,MACzB,IAAI,CAAC;AAAA,MACL,KAAK,CAAC;AAAA,IACP;AAEA,OAAG,MAAM,SAAU,QAAQ;AAC1B,UAAI,SAAS,OAAO,UAAU,GAAG,CAAC,EAAE,YAAY;AAChD,UAAI;AACJ,UAAI;AACJ,cAAQ,QAAQ;AAAA,QACf,KAAK;AACJ,gBAAM,GAAG,IAAI,IAAI,MAAM;AACvB,kBAAQ;AACR;AAAA,QACD,KAAK;AACJ,gBAAM,GAAG,IAAI,IAAI,MAAM;AACvB,kBAAQ;AACR;AAAA,QACD;AACC,gBAAM,GAAG,IAAI,IAAI,MAAM;AACvB,kBAAQ;AACR;AAAA,MACF;AAEA,UAAI,CAAC,KAAK;AACT,eAAO;AAAA,MACR;AAEA,aAAO,EAAC,OAAc,OAAO,IAAG;AAAA,IACjC;AAEA,OAAG,IAAI,MAAM,SAAU,QAAQ;AAC9B,UAAI,CAAC,QAAQ;AACZ,eAAO;AAAA,MACR;AAEA,UAAI,OAAO;AACX,UAAI,MAAM;AACV,UAAI,OAAO;AACX,UAAI,MAAM;AACV,UAAI,UAAU;AAEd,UAAI,MAAM,CAAC,GAAG,GAAG,GAAG,CAAC;AACrB,UAAI;AACJ,UAAI;AACJ,UAAI;AAEJ,UAAI,QAAQ,OAAO,MAAM,GAAG,GAAG;AAC9B,mBAAW,MAAM,CAAC;AAClB,gBAAQ,MAAM,CAAC;AAEf,aAAK,IAAI,GAAG,IAAI,GAAG,KAAK;AAEvB,cAAI,KAAK,IAAI;AACb,cAAI,CAAC,IAAI,SAAS,MAAM,MAAM,IAAI,KAAK,CAAC,GAAG,EAAE;AAAA,QAC9C;AAEA,YAAI,UAAU;AACb,cAAI,CAAC,IAAI,SAAS,UAAU,EAAE,IAAI;AAAA,QACnC;AAAA,MACD,WAAW,QAAQ,OAAO,MAAM,IAAI,GAAG;AACtC,gBAAQ,MAAM,CAAC;AACf,mBAAW,MAAM,CAAC;AAElB,aAAK,IAAI,GAAG,IAAI,GAAG,KAAK;AACvB,cAAI,CAAC,IAAI,SAAS,MAAM,CAAC,IAAI,MAAM,CAAC,GAAG,EAAE;AAAA,QAC1C;AAEA,YAAI,UAAU;AACb,cAAI,CAAC,IAAI,SAAS,WAAW,UAAU,EAAE,IAAI;AAAA,QAC9C;AAAA,MACD,WAAW,QAAQ,OAAO,MAAM,IAAI,GAAG;AACtC,aAAK,IAAI,GAAG,IAAI,GAAG,KAAK;AACvB,cAAI,CAAC,IAAI,SAAS,MAAM,IAAI,CAAC,GAAG,CAAC;AAAA,QAClC;AAEA,YAAI,MAAM,CAAC,GAAG;AACb,cAAI,MAAM,CAAC,GAAG;AACb,gBAAI,CAAC,IAAI,WAAW,MAAM,CAAC,CAAC,IAAI;AAAA,UACjC,OAAO;AACN,gBAAI,CAAC,IAAI,WAAW,MAAM,CAAC,CAAC;AAAA,UAC7B;AAAA,QACD;AAAA,MACD,WAAW,QAAQ,OAAO,MAAM,GAAG,GAAG;AACrC,aAAK,IAAI,GAAG,IAAI,GAAG,KAAK;AACvB,cAAI,CAAC,IAAI,KAAK,MAAM,WAAW,MAAM,IAAI,CAAC,CAAC,IAAI,IAAI;AAAA,QACpD;AAEA,YAAI,MAAM,CAAC,GAAG;AACb,cAAI,MAAM,CAAC,GAAG;AACb,gBAAI,CAAC,IAAI,WAAW,MAAM,CAAC,CAAC,IAAI;AAAA,UACjC,OAAO;AACN,gBAAI,CAAC,IAAI,WAAW,MAAM,CAAC,CAAC;AAAA,UAC7B;AAAA,QACD;AAAA,MACD,WAAW,QAAQ,OAAO,MAAM,OAAO,GAAG;AACzC,YAAI,MAAM,CAAC,MAAM,eAAe;AAC/B,iBAAO,CAAC,GAAG,GAAG,GAAG,CAAC;AAAA,QACnB;AAEA,YAAI,CAAC,eAAe,KAAK,YAAY,MAAM,CAAC,CAAC,GAAG;AAC/C,iBAAO;AAAA,QACR;AAEA,cAAM,WAAW,MAAM,CAAC,CAAC;AACzB,YAAI,CAAC,IAAI;AAET,eAAO;AAAA,MACR,OAAO;AACN,eAAO;AAAA,MACR;AAEA,WAAK,IAAI,GAAG,IAAI,GAAG,KAAK;AACvB,YAAI,CAAC,IAAI,MAAM,IAAI,CAAC,GAAG,GAAG,GAAG;AAAA,MAC9B;AACA,UAAI,CAAC,IAAI,MAAM,IAAI,CAAC,GAAG,GAAG,CAAC;AAE3B,aAAO;AAAA,IACR;AAEA,OAAG,IAAI,MAAM,SAAU,QAAQ;AAC9B,UAAI,CAAC,QAAQ;AACZ,eAAO;AAAA,MACR;AAEA,UAAI,MAAM;AACV,UAAI,QAAQ,OAAO,MAAM,GAAG;AAE5B,UAAI,OAAO;AACV,YAAI,QAAQ,WAAW,MAAM,CAAC,CAAC;AAC/B,YAAI,KAAM,WAAW,MAAM,CAAC,CAAC,IAAI,MAAO,OAAO;AAC/C,YAAI,IAAI,MAAM,WAAW,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG;AAC1C,YAAI,IAAI,MAAM,WAAW,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG;AAC1C,YAAI,IAAI,MAAM,MAAM,KAAK,IAAI,IAAI,OAAO,GAAG,CAAC;AAE5C,eAAO,CAAC,GAAG,GAAG,GAAG,CAAC;AAAA,MACnB;AAEA,aAAO;AAAA,IACR;AAEA,OAAG,IAAI,MAAM,SAAU,QAAQ;AAC9B,UAAI,CAAC,QAAQ;AACZ,eAAO;AAAA,MACR;AAEA,UAAI,MAAM;AACV,UAAI,QAAQ,OAAO,MAAM,GAAG;AAE5B,UAAI,OAAO;AACV,YAAI,QAAQ,WAAW,MAAM,CAAC,CAAC;AAC/B,YAAI,KAAM,WAAW,MAAM,CAAC,CAAC,IAAI,MAAO,OAAO;AAC/C,YAAI,IAAI,MAAM,WAAW,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG;AAC1C,YAAI,IAAI,MAAM,WAAW,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG;AAC1C,YAAI,IAAI,MAAM,MAAM,KAAK,IAAI,IAAI,OAAO,GAAG,CAAC;AAC5C,eAAO,CAAC,GAAG,GAAG,GAAG,CAAC;AAAA,MACnB;AAEA,aAAO;AAAA,IACR;AAEA,OAAG,GAAG,MAAM,WAAY;AACvB,UAAI,OAAO,QAAQ,SAAS;AAE5B,aACC,MACA,UAAU,KAAK,CAAC,CAAC,IACjB,UAAU,KAAK,CAAC,CAAC,IACjB,UAAU,KAAK,CAAC,CAAC,KAChB,KAAK,CAAC,IAAI,IACP,UAAU,KAAK,MAAM,KAAK,CAAC,IAAI,GAAG,CAAC,IACpC;AAAA,IAEL;AAEA,OAAG,GAAG,MAAM,WAAY;AACvB,UAAI,OAAO,QAAQ,SAAS;AAE5B,aAAO,KAAK,SAAS,KAAK,KAAK,CAAC,MAAM,IACnC,SAAS,KAAK,MAAM,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,MAAM,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,MAAM,KAAK,CAAC,CAAC,IAAI,MACzF,UAAU,KAAK,MAAM,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,MAAM,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,MAAM,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,CAAC,IAAI;AAAA,IAC/G;AAEA,OAAG,GAAG,IAAI,UAAU,WAAY;AAC/B,UAAI,OAAO,QAAQ,SAAS;AAE5B,UAAI,IAAI,KAAK,MAAM,KAAK,CAAC,IAAI,MAAM,GAAG;AACtC,UAAI,IAAI,KAAK,MAAM,KAAK,CAAC,IAAI,MAAM,GAAG;AACtC,UAAI,IAAI,KAAK,MAAM,KAAK,CAAC,IAAI,MAAM,GAAG;AAEtC,aAAO,KAAK,SAAS,KAAK,KAAK,CAAC,MAAM,IACnC,SAAS,IAAI,QAAQ,IAAI,QAAQ,IAAI,OACrC,UAAU,IAAI,QAAQ,IAAI,QAAQ,IAAI,QAAQ,KAAK,CAAC,IAAI;AAAA,IAC5D;AAEA,OAAG,GAAG,MAAM,WAAY;AACvB,UAAI,OAAO,QAAQ,SAAS;AAC5B,aAAO,KAAK,SAAS,KAAK,KAAK,CAAC,MAAM,IACnC,SAAS,KAAK,CAAC,IAAI,OAAO,KAAK,CAAC,IAAI,QAAQ,KAAK,CAAC,IAAI,OACtD,UAAU,KAAK,CAAC,IAAI,OAAO,KAAK,CAAC,IAAI,QAAQ,KAAK,CAAC,IAAI,QAAQ,KAAK,CAAC,IAAI;AAAA,IAC7E;AAIA,OAAG,GAAG,MAAM,WAAY;AACvB,UAAI,OAAO,QAAQ,SAAS;AAE5B,UAAI,IAAI;AACR,UAAI,KAAK,UAAU,KAAK,KAAK,CAAC,MAAM,GAAG;AACtC,YAAI,OAAO,KAAK,CAAC;AAAA,MAClB;AAEA,aAAO,SAAS,KAAK,CAAC,IAAI,OAAO,KAAK,CAAC,IAAI,QAAQ,KAAK,CAAC,IAAI,MAAM,IAAI;AAAA,IACxE;AAEA,OAAG,GAAG,UAAU,SAAU,KAAK;AAC9B,aAAO,aAAa,IAAI,MAAM,GAAG,CAAC,CAAC;AAAA,IACpC;AAGA,aAAS,MAAM,KAAK,KAAK,KAAK;AAC7B,aAAO,KAAK,IAAI,KAAK,IAAI,KAAK,GAAG,GAAG,GAAG;AAAA,IACxC;AAEA,aAAS,UAAU,KAAK;AACvB,UAAI,MAAM,KAAK,MAAM,GAAG,EAAE,SAAS,EAAE,EAAE,YAAY;AACnD,aAAQ,IAAI,SAAS,IAAK,MAAM,MAAM;AAAA,IACvC;AAAA;AAAA;;;ACjPA;AAAA;AAEA,QAAM,cAAc;AAMpB,QAAM,kBAAkB,CAAC;AACzB,eAAW,OAAO,OAAO,KAAK,WAAW,GAAG;AAC3C,sBAAgB,YAAY,GAAG,CAAC,IAAI;AAAA,IACrC;AAEA,QAAM,UAAU;AAAA,MACf,KAAK,EAAC,UAAU,GAAG,QAAQ,MAAK;AAAA,MAChC,KAAK,EAAC,UAAU,GAAG,QAAQ,MAAK;AAAA,MAChC,KAAK,EAAC,UAAU,GAAG,QAAQ,MAAK;AAAA,MAChC,KAAK,EAAC,UAAU,GAAG,QAAQ,MAAK;AAAA,MAChC,MAAM,EAAC,UAAU,GAAG,QAAQ,OAAM;AAAA,MAClC,KAAK,EAAC,UAAU,GAAG,QAAQ,MAAK;AAAA,MAChC,KAAK,EAAC,UAAU,GAAG,QAAQ,MAAK;AAAA,MAChC,KAAK,EAAC,UAAU,GAAG,QAAQ,MAAK;AAAA,MAChC,KAAK,EAAC,UAAU,GAAG,QAAQ,CAAC,KAAK,EAAC;AAAA,MAClC,SAAS,EAAC,UAAU,GAAG,QAAQ,CAAC,SAAS,EAAC;AAAA,MAC1C,QAAQ,EAAC,UAAU,GAAG,QAAQ,CAAC,QAAQ,EAAC;AAAA,MACxC,SAAS,EAAC,UAAU,GAAG,QAAQ,CAAC,SAAS,EAAC;AAAA,MAC1C,KAAK,EAAC,UAAU,GAAG,QAAQ,CAAC,KAAK,KAAK,GAAG,EAAC;AAAA,MAC1C,OAAO,EAAC,UAAU,GAAG,QAAQ,CAAC,OAAO,OAAO,KAAK,EAAC;AAAA,MAClD,MAAM,EAAC,UAAU,GAAG,QAAQ,CAAC,MAAM,EAAC;AAAA,IACrC;AAEA,WAAO,UAAU;AAGjB,eAAW,SAAS,OAAO,KAAK,OAAO,GAAG;AACzC,UAAI,EAAE,cAAc,QAAQ,KAAK,IAAI;AACpC,cAAM,IAAI,MAAM,gCAAgC,KAAK;AAAA,MACtD;AAEA,UAAI,EAAE,YAAY,QAAQ,KAAK,IAAI;AAClC,cAAM,IAAI,MAAM,sCAAsC,KAAK;AAAA,MAC5D;AAEA,UAAI,QAAQ,KAAK,EAAE,OAAO,WAAW,QAAQ,KAAK,EAAE,UAAU;AAC7D,cAAM,IAAI,MAAM,wCAAwC,KAAK;AAAA,MAC9D;AAEA,YAAM,EAAC,UAAU,OAAM,IAAI,QAAQ,KAAK;AACxC,aAAO,QAAQ,KAAK,EAAE;AACtB,aAAO,QAAQ,KAAK,EAAE;AACtB,aAAO,eAAe,QAAQ,KAAK,GAAG,YAAY,EAAC,OAAO,SAAQ,CAAC;AACnE,aAAO,eAAe,QAAQ,KAAK,GAAG,UAAU,EAAC,OAAO,OAAM,CAAC;AAAA,IAChE;AAEA,YAAQ,IAAI,MAAM,SAAU,KAAK;AAChC,YAAM,IAAI,IAAI,CAAC,IAAI;AACnB,YAAM,IAAI,IAAI,CAAC,IAAI;AACnB,YAAM,IAAI,IAAI,CAAC,IAAI;AACnB,YAAM,MAAM,KAAK,IAAI,GAAG,GAAG,CAAC;AAC5B,YAAM,MAAM,KAAK,IAAI,GAAG,GAAG,CAAC;AAC5B,YAAM,QAAQ,MAAM;AACpB,UAAI;AACJ,UAAI;AAEJ,UAAI,QAAQ,KAAK;AAChB,YAAI;AAAA,MACL,WAAW,MAAM,KAAK;AACrB,aAAK,IAAI,KAAK;AAAA,MACf,WAAW,MAAM,KAAK;AACrB,YAAI,KAAK,IAAI,KAAK;AAAA,MACnB,WAAW,MAAM,KAAK;AACrB,YAAI,KAAK,IAAI,KAAK;AAAA,MACnB;AAEA,UAAI,KAAK,IAAI,IAAI,IAAI,GAAG;AAExB,UAAI,IAAI,GAAG;AACV,aAAK;AAAA,MACN;AAEA,YAAM,KAAK,MAAM,OAAO;AAExB,UAAI,QAAQ,KAAK;AAChB,YAAI;AAAA,MACL,WAAW,KAAK,KAAK;AACpB,YAAI,SAAS,MAAM;AAAA,MACpB,OAAO;AACN,YAAI,SAAS,IAAI,MAAM;AAAA,MACxB;AAEA,aAAO,CAAC,GAAG,IAAI,KAAK,IAAI,GAAG;AAAA,IAC5B;AAEA,YAAQ,IAAI,MAAM,SAAU,KAAK;AAChC,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,UAAI;AAEJ,YAAM,IAAI,IAAI,CAAC,IAAI;AACnB,YAAM,IAAI,IAAI,CAAC,IAAI;AACnB,YAAM,IAAI,IAAI,CAAC,IAAI;AACnB,YAAM,IAAI,KAAK,IAAI,GAAG,GAAG,CAAC;AAC1B,YAAM,OAAO,IAAI,KAAK,IAAI,GAAG,GAAG,CAAC;AACjC,YAAM,QAAQ,SAAU,GAAG;AAC1B,gBAAQ,IAAI,KAAK,IAAI,OAAO,IAAI;AAAA,MACjC;AAEA,UAAI,SAAS,GAAG;AACf,YAAI;AACJ,YAAI;AAAA,MACL,OAAO;AACN,YAAI,OAAO;AACX,eAAO,MAAM,CAAC;AACd,eAAO,MAAM,CAAC;AACd,eAAO,MAAM,CAAC;AAEd,YAAI,MAAM,GAAG;AACZ,cAAI,OAAO;AAAA,QACZ,WAAW,MAAM,GAAG;AACnB,cAAK,IAAI,IAAK,OAAO;AAAA,QACtB,WAAW,MAAM,GAAG;AACnB,cAAK,IAAI,IAAK,OAAO;AAAA,QACtB;AAEA,YAAI,IAAI,GAAG;AACV,eAAK;AAAA,QACN,WAAW,IAAI,GAAG;AACjB,eAAK;AAAA,QACN;AAAA,MACD;AAEA,aAAO;AAAA,QACN,IAAI;AAAA,QACJ,IAAI;AAAA,QACJ,IAAI;AAAA,MACL;AAAA,IACD;AAEA,YAAQ,IAAI,MAAM,SAAU,KAAK;AAChC,YAAM,IAAI,IAAI,CAAC;AACf,YAAM,IAAI,IAAI,CAAC;AACf,UAAI,IAAI,IAAI,CAAC;AACb,YAAM,IAAI,QAAQ,IAAI,IAAI,GAAG,EAAE,CAAC;AAChC,YAAM,IAAI,IAAI,MAAM,KAAK,IAAI,GAAG,KAAK,IAAI,GAAG,CAAC,CAAC;AAE9C,UAAI,IAAI,IAAI,MAAM,KAAK,IAAI,GAAG,KAAK,IAAI,GAAG,CAAC,CAAC;AAE5C,aAAO,CAAC,GAAG,IAAI,KAAK,IAAI,GAAG;AAAA,IAC5B;AAEA,YAAQ,IAAI,OAAO,SAAU,KAAK;AACjC,YAAM,IAAI,IAAI,CAAC,IAAI;AACnB,YAAM,IAAI,IAAI,CAAC,IAAI;AACnB,YAAM,IAAI,IAAI,CAAC,IAAI;AAEnB,YAAM,IAAI,KAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IAAI,CAAC;AACtC,YAAM,KAAK,IAAI,IAAI,MAAM,IAAI,MAAM;AACnC,YAAM,KAAK,IAAI,IAAI,MAAM,IAAI,MAAM;AACnC,YAAM,KAAK,IAAI,IAAI,MAAM,IAAI,MAAM;AAEnC,aAAO,CAAC,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,GAAG;AAAA,IAC3C;AAEA,aAAS,oBAAoB,GAAG,GAAG;AAIlC,cACG,EAAE,CAAC,IAAI,EAAE,CAAC,MAAM,KAChB,EAAE,CAAC,IAAI,EAAE,CAAC,MAAM,KAChB,EAAE,CAAC,IAAI,EAAE,CAAC,MAAM;AAAA,IAEpB;AAEA,YAAQ,IAAI,UAAU,SAAU,KAAK;AACpC,YAAM,WAAW,gBAAgB,GAAG;AACpC,UAAI,UAAU;AACb,eAAO;AAAA,MACR;AAEA,UAAI,yBAAyB;AAC7B,UAAI;AAEJ,iBAAW,WAAW,OAAO,KAAK,WAAW,GAAG;AAC/C,cAAM,QAAQ,YAAY,OAAO;AAGjC,cAAM,WAAW,oBAAoB,KAAK,KAAK;AAG/C,YAAI,WAAW,wBAAwB;AACtC,mCAAyB;AACzB,kCAAwB;AAAA,QACzB;AAAA,MACD;AAEA,aAAO;AAAA,IACR;AAEA,YAAQ,QAAQ,MAAM,SAAU,SAAS;AACxC,aAAO,YAAY,OAAO;AAAA,IAC3B;AAEA,YAAQ,IAAI,MAAM,SAAU,KAAK;AAChC,UAAI,IAAI,IAAI,CAAC,IAAI;AACjB,UAAI,IAAI,IAAI,CAAC,IAAI;AACjB,UAAI,IAAI,IAAI,CAAC,IAAI;AAGjB,UAAI,IAAI,YAAa,IAAI,SAAS,UAAU,MAAQ,IAAI;AACxD,UAAI,IAAI,YAAa,IAAI,SAAS,UAAU,MAAQ,IAAI;AACxD,UAAI,IAAI,YAAa,IAAI,SAAS,UAAU,MAAQ,IAAI;AAExD,YAAM,IAAK,IAAI,SAAW,IAAI,SAAW,IAAI;AAC7C,YAAM,IAAK,IAAI,SAAW,IAAI,SAAW,IAAI;AAC7C,YAAM,IAAK,IAAI,SAAW,IAAI,SAAW,IAAI;AAE7C,aAAO,CAAC,IAAI,KAAK,IAAI,KAAK,IAAI,GAAG;AAAA,IAClC;AAEA,YAAQ,IAAI,MAAM,SAAU,KAAK;AAChC,YAAM,MAAM,QAAQ,IAAI,IAAI,GAAG;AAC/B,UAAI,IAAI,IAAI,CAAC;AACb,UAAI,IAAI,IAAI,CAAC;AACb,UAAI,IAAI,IAAI,CAAC;AAEb,WAAK;AACL,WAAK;AACL,WAAK;AAEL,UAAI,IAAI,UAAY,MAAM,IAAI,KAAO,QAAQ,IAAM,KAAK;AACxD,UAAI,IAAI,UAAY,MAAM,IAAI,KAAO,QAAQ,IAAM,KAAK;AACxD,UAAI,IAAI,UAAY,MAAM,IAAI,KAAO,QAAQ,IAAM,KAAK;AAExD,YAAM,IAAK,MAAM,IAAK;AACtB,YAAM,IAAI,OAAO,IAAI;AACrB,YAAM,IAAI,OAAO,IAAI;AAErB,aAAO,CAAC,GAAG,GAAG,CAAC;AAAA,IAChB;AAEA,YAAQ,IAAI,MAAM,SAAU,KAAK;AAChC,YAAM,IAAI,IAAI,CAAC,IAAI;AACnB,YAAM,IAAI,IAAI,CAAC,IAAI;AACnB,YAAM,IAAI,IAAI,CAAC,IAAI;AACnB,UAAI;AACJ,UAAI;AACJ,UAAI;AAEJ,UAAI,MAAM,GAAG;AACZ,cAAM,IAAI;AACV,eAAO,CAAC,KAAK,KAAK,GAAG;AAAA,MACtB;AAEA,UAAI,IAAI,KAAK;AACZ,aAAK,KAAK,IAAI;AAAA,MACf,OAAO;AACN,aAAK,IAAI,IAAI,IAAI;AAAA,MAClB;AAEA,YAAM,KAAK,IAAI,IAAI;AAEnB,YAAM,MAAM,CAAC,GAAG,GAAG,CAAC;AACpB,eAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC3B,aAAK,IAAI,IAAI,IAAI,EAAE,IAAI;AACvB,YAAI,KAAK,GAAG;AACX;AAAA,QACD;AAEA,YAAI,KAAK,GAAG;AACX;AAAA,QACD;AAEA,YAAI,IAAI,KAAK,GAAG;AACf,gBAAM,MAAM,KAAK,MAAM,IAAI;AAAA,QAC5B,WAAW,IAAI,KAAK,GAAG;AACtB,gBAAM;AAAA,QACP,WAAW,IAAI,KAAK,GAAG;AACtB,gBAAM,MAAM,KAAK,OAAO,IAAI,IAAI,MAAM;AAAA,QACvC,OAAO;AACN,gBAAM;AAAA,QACP;AAEA,YAAI,CAAC,IAAI,MAAM;AAAA,MAChB;AAEA,aAAO;AAAA,IACR;AAEA,YAAQ,IAAI,MAAM,SAAU,KAAK;AAChC,YAAM,IAAI,IAAI,CAAC;AACf,UAAI,IAAI,IAAI,CAAC,IAAI;AACjB,UAAI,IAAI,IAAI,CAAC,IAAI;AACjB,UAAI,OAAO;AACX,YAAM,OAAO,KAAK,IAAI,GAAG,IAAI;AAE7B,WAAK;AACL,WAAM,KAAK,IAAK,IAAI,IAAI;AACxB,cAAQ,QAAQ,IAAI,OAAO,IAAI;AAC/B,YAAM,KAAK,IAAI,KAAK;AACpB,YAAM,KAAK,MAAM,IAAK,IAAI,QAAS,OAAO,QAAS,IAAI,KAAM,IAAI;AAEjE,aAAO,CAAC,GAAG,KAAK,KAAK,IAAI,GAAG;AAAA,IAC7B;AAEA,YAAQ,IAAI,MAAM,SAAU,KAAK;AAChC,YAAM,IAAI,IAAI,CAAC,IAAI;AACnB,YAAM,IAAI,IAAI,CAAC,IAAI;AACnB,UAAI,IAAI,IAAI,CAAC,IAAI;AACjB,YAAM,KAAK,KAAK,MAAM,CAAC,IAAI;AAE3B,YAAM,IAAI,IAAI,KAAK,MAAM,CAAC;AAC1B,YAAM,IAAI,MAAM,KAAK,IAAI;AACzB,YAAM,IAAI,MAAM,KAAK,IAAK,IAAI;AAC9B,YAAM,IAAI,MAAM,KAAK,IAAK,KAAK,IAAI;AACnC,WAAK;AAEL,cAAQ,IAAI;AAAA,QACX,KAAK;AACJ,iBAAO,CAAC,GAAG,GAAG,CAAC;AAAA,QAChB,KAAK;AACJ,iBAAO,CAAC,GAAG,GAAG,CAAC;AAAA,QAChB,KAAK;AACJ,iBAAO,CAAC,GAAG,GAAG,CAAC;AAAA,QAChB,KAAK;AACJ,iBAAO,CAAC,GAAG,GAAG,CAAC;AAAA,QAChB,KAAK;AACJ,iBAAO,CAAC,GAAG,GAAG,CAAC;AAAA,QAChB,KAAK;AACJ,iBAAO,CAAC,GAAG,GAAG,CAAC;AAAA,MACjB;AAAA,IACD;AAEA,YAAQ,IAAI,MAAM,SAAU,KAAK;AAChC,YAAM,IAAI,IAAI,CAAC;AACf,YAAM,IAAI,IAAI,CAAC,IAAI;AACnB,YAAM,IAAI,IAAI,CAAC,IAAI;AACnB,YAAM,OAAO,KAAK,IAAI,GAAG,IAAI;AAC7B,UAAI;AACJ,UAAI;AAEJ,WAAK,IAAI,KAAK;AACd,YAAM,QAAQ,IAAI,KAAK;AACvB,WAAK,IAAI;AACT,YAAO,QAAQ,IAAK,OAAO,IAAI;AAC/B,WAAK,MAAM;AACX,WAAK;AAEL,aAAO,CAAC,GAAG,KAAK,KAAK,IAAI,GAAG;AAAA,IAC7B;AAGA,YAAQ,IAAI,MAAM,SAAU,KAAK;AAChC,YAAM,IAAI,IAAI,CAAC,IAAI;AACnB,UAAI,KAAK,IAAI,CAAC,IAAI;AAClB,UAAI,KAAK,IAAI,CAAC,IAAI;AAClB,YAAM,QAAQ,KAAK;AACnB,UAAI;AAGJ,UAAI,QAAQ,GAAG;AACd,cAAM;AACN,cAAM;AAAA,MACP;AAEA,YAAM,IAAI,KAAK,MAAM,IAAI,CAAC;AAC1B,YAAM,IAAI,IAAI;AACd,UAAI,IAAI,IAAI;AAEZ,WAAK,IAAI,OAAU,GAAG;AACrB,YAAI,IAAI;AAAA,MACT;AAEA,YAAM,IAAI,KAAK,KAAK,IAAI;AAExB,UAAI;AACJ,UAAI;AACJ,UAAI;AAEJ,cAAQ,GAAG;AAAA,QACV;AAAA,QACA,KAAK;AAAA,QACL,KAAK;AAAG,cAAI;AAAI,cAAI;AAAI,cAAI;AAAI;AAAA,QAChC,KAAK;AAAG,cAAI;AAAI,cAAI;AAAI,cAAI;AAAI;AAAA,QAChC,KAAK;AAAG,cAAI;AAAI,cAAI;AAAI,cAAI;AAAG;AAAA,QAC/B,KAAK;AAAG,cAAI;AAAI,cAAI;AAAI,cAAI;AAAG;AAAA,QAC/B,KAAK;AAAG,cAAI;AAAI,cAAI;AAAI,cAAI;AAAG;AAAA,QAC/B,KAAK;AAAG,cAAI;AAAI,cAAI;AAAI,cAAI;AAAG;AAAA,MAChC;AAGA,aAAO,CAAC,IAAI,KAAK,IAAI,KAAK,IAAI,GAAG;AAAA,IAClC;AAEA,YAAQ,KAAK,MAAM,SAAU,MAAM;AAClC,YAAM,IAAI,KAAK,CAAC,IAAI;AACpB,YAAM,IAAI,KAAK,CAAC,IAAI;AACpB,YAAM,IAAI,KAAK,CAAC,IAAI;AACpB,YAAM,IAAI,KAAK,CAAC,IAAI;AAEpB,YAAM,IAAI,IAAI,KAAK,IAAI,GAAG,KAAK,IAAI,KAAK,CAAC;AACzC,YAAM,IAAI,IAAI,KAAK,IAAI,GAAG,KAAK,IAAI,KAAK,CAAC;AACzC,YAAM,IAAI,IAAI,KAAK,IAAI,GAAG,KAAK,IAAI,KAAK,CAAC;AAEzC,aAAO,CAAC,IAAI,KAAK,IAAI,KAAK,IAAI,GAAG;AAAA,IAClC;AAEA,YAAQ,IAAI,MAAM,SAAU,KAAK;AAChC,YAAM,IAAI,IAAI,CAAC,IAAI;AACnB,YAAM,IAAI,IAAI,CAAC,IAAI;AACnB,YAAM,IAAI,IAAI,CAAC,IAAI;AACnB,UAAI;AACJ,UAAI;AACJ,UAAI;AAEJ,UAAK,IAAI,SAAW,IAAI,UAAY,IAAI;AACxC,UAAK,IAAI,UAAY,IAAI,SAAW,IAAI;AACxC,UAAK,IAAI,SAAW,IAAI,SAAY,IAAI;AAGxC,UAAI,IAAI,WACH,QAAS,MAAM,IAAM,OAAS,QAChC,IAAI;AAEP,UAAI,IAAI,WACH,QAAS,MAAM,IAAM,OAAS,QAChC,IAAI;AAEP,UAAI,IAAI,WACH,QAAS,MAAM,IAAM,OAAS,QAChC,IAAI;AAEP,UAAI,KAAK,IAAI,KAAK,IAAI,GAAG,CAAC,GAAG,CAAC;AAC9B,UAAI,KAAK,IAAI,KAAK,IAAI,GAAG,CAAC,GAAG,CAAC;AAC9B,UAAI,KAAK,IAAI,KAAK,IAAI,GAAG,CAAC,GAAG,CAAC;AAE9B,aAAO,CAAC,IAAI,KAAK,IAAI,KAAK,IAAI,GAAG;AAAA,IAClC;AAEA,YAAQ,IAAI,MAAM,SAAU,KAAK;AAChC,UAAI,IAAI,IAAI,CAAC;AACb,UAAI,IAAI,IAAI,CAAC;AACb,UAAI,IAAI,IAAI,CAAC;AAEb,WAAK;AACL,WAAK;AACL,WAAK;AAEL,UAAI,IAAI,UAAY,MAAM,IAAI,KAAO,QAAQ,IAAM,KAAK;AACxD,UAAI,IAAI,UAAY,MAAM,IAAI,KAAO,QAAQ,IAAM,KAAK;AACxD,UAAI,IAAI,UAAY,MAAM,IAAI,KAAO,QAAQ,IAAM,KAAK;AAExD,YAAM,IAAK,MAAM,IAAK;AACtB,YAAM,IAAI,OAAO,IAAI;AACrB,YAAM,IAAI,OAAO,IAAI;AAErB,aAAO,CAAC,GAAG,GAAG,CAAC;AAAA,IAChB;AAEA,YAAQ,IAAI,MAAM,SAAU,KAAK;AAChC,YAAM,IAAI,IAAI,CAAC;AACf,YAAM,IAAI,IAAI,CAAC;AACf,YAAM,IAAI,IAAI,CAAC;AACf,UAAI;AACJ,UAAI;AACJ,UAAI;AAEJ,WAAK,IAAI,MAAM;AACf,UAAI,IAAI,MAAM;AACd,UAAI,IAAI,IAAI;AAEZ,YAAM,KAAK,KAAK;AAChB,YAAM,KAAK,KAAK;AAChB,YAAM,KAAK,KAAK;AAChB,UAAI,KAAK,UAAW,MAAM,IAAI,KAAK,OAAO;AAC1C,UAAI,KAAK,UAAW,MAAM,IAAI,KAAK,OAAO;AAC1C,UAAI,KAAK,UAAW,MAAM,IAAI,KAAK,OAAO;AAE1C,WAAK;AACL,WAAK;AACL,WAAK;AAEL,aAAO,CAAC,GAAG,GAAG,CAAC;AAAA,IAChB;AAEA,YAAQ,IAAI,MAAM,SAAU,KAAK;AAChC,YAAM,IAAI,IAAI,CAAC;AACf,YAAM,IAAI,IAAI,CAAC;AACf,YAAM,IAAI,IAAI,CAAC;AACf,UAAI;AAEJ,YAAM,KAAK,KAAK,MAAM,GAAG,CAAC;AAC1B,UAAI,KAAK,MAAM,IAAI,KAAK;AAExB,UAAI,IAAI,GAAG;AACV,aAAK;AAAA,MACN;AAEA,YAAM,IAAI,KAAK,KAAK,IAAI,IAAI,IAAI,CAAC;AAEjC,aAAO,CAAC,GAAG,GAAG,CAAC;AAAA,IAChB;AAEA,YAAQ,IAAI,MAAM,SAAU,KAAK;AAChC,YAAM,IAAI,IAAI,CAAC;AACf,YAAM,IAAI,IAAI,CAAC;AACf,YAAM,IAAI,IAAI,CAAC;AAEf,YAAM,KAAK,IAAI,MAAM,IAAI,KAAK;AAC9B,YAAM,IAAI,IAAI,KAAK,IAAI,EAAE;AACzB,YAAM,IAAI,IAAI,KAAK,IAAI,EAAE;AAEzB,aAAO,CAAC,GAAG,GAAG,CAAC;AAAA,IAChB;AAEA,YAAQ,IAAI,SAAS,SAAU,MAAM,aAAa,MAAM;AACvD,YAAM,CAAC,GAAG,GAAG,CAAC,IAAI;AAClB,UAAI,QAAQ,eAAe,OAAO,QAAQ,IAAI,IAAI,IAAI,EAAE,CAAC,IAAI;AAE7D,cAAQ,KAAK,MAAM,QAAQ,EAAE;AAE7B,UAAI,UAAU,GAAG;AAChB,eAAO;AAAA,MACR;AAEA,UAAI,OAAO,MACN,KAAK,MAAM,IAAI,GAAG,KAAK,IACxB,KAAK,MAAM,IAAI,GAAG,KAAK,IACxB,KAAK,MAAM,IAAI,GAAG;AAErB,UAAI,UAAU,GAAG;AAChB,gBAAQ;AAAA,MACT;AAEA,aAAO;AAAA,IACR;AAEA,YAAQ,IAAI,SAAS,SAAU,MAAM;AAGpC,aAAO,QAAQ,IAAI,OAAO,QAAQ,IAAI,IAAI,IAAI,GAAG,KAAK,CAAC,CAAC;AAAA,IACzD;AAEA,YAAQ,IAAI,UAAU,SAAU,MAAM;AACrC,YAAM,IAAI,KAAK,CAAC;AAChB,YAAM,IAAI,KAAK,CAAC;AAChB,YAAM,IAAI,KAAK,CAAC;AAIhB,UAAI,MAAM,KAAK,MAAM,GAAG;AACvB,YAAI,IAAI,GAAG;AACV,iBAAO;AAAA,QACR;AAEA,YAAI,IAAI,KAAK;AACZ,iBAAO;AAAA,QACR;AAEA,eAAO,KAAK,OAAQ,IAAI,KAAK,MAAO,EAAE,IAAI;AAAA,MAC3C;AAEA,YAAM,OAAO,KACT,KAAK,KAAK,MAAM,IAAI,MAAM,CAAC,IAC3B,IAAI,KAAK,MAAM,IAAI,MAAM,CAAC,IAC3B,KAAK,MAAM,IAAI,MAAM,CAAC;AAEzB,aAAO;AAAA,IACR;AAEA,YAAQ,OAAO,MAAM,SAAU,MAAM;AACpC,UAAI,QAAQ,OAAO;AAGnB,UAAI,UAAU,KAAK,UAAU,GAAG;AAC/B,YAAI,OAAO,IAAI;AACd,mBAAS;AAAA,QACV;AAEA,gBAAQ,QAAQ,OAAO;AAEvB,eAAO,CAAC,OAAO,OAAO,KAAK;AAAA,MAC5B;AAEA,YAAM,QAAQ,CAAC,EAAE,OAAO,MAAM,KAAK;AACnC,YAAM,KAAM,QAAQ,KAAK,OAAQ;AACjC,YAAM,KAAO,SAAS,IAAK,KAAK,OAAQ;AACxC,YAAM,KAAO,SAAS,IAAK,KAAK,OAAQ;AAExC,aAAO,CAAC,GAAG,GAAG,CAAC;AAAA,IAChB;AAEA,YAAQ,QAAQ,MAAM,SAAU,MAAM;AAErC,UAAI,QAAQ,KAAK;AAChB,cAAM,KAAK,OAAO,OAAO,KAAK;AAC9B,eAAO,CAAC,GAAG,GAAG,CAAC;AAAA,MAChB;AAEA,cAAQ;AAER,UAAI;AACJ,YAAM,IAAI,KAAK,MAAM,OAAO,EAAE,IAAI,IAAI;AACtC,YAAM,IAAI,KAAK,OAAO,MAAM,OAAO,MAAM,CAAC,IAAI,IAAI;AAClD,YAAM,IAAK,MAAM,IAAK,IAAI;AAE1B,aAAO,CAAC,GAAG,GAAG,CAAC;AAAA,IAChB;AAEA,YAAQ,IAAI,MAAM,SAAU,MAAM;AACjC,YAAM,YAAY,KAAK,MAAM,KAAK,CAAC,CAAC,IAAI,QAAS,QAC5C,KAAK,MAAM,KAAK,CAAC,CAAC,IAAI,QAAS,MAChC,KAAK,MAAM,KAAK,CAAC,CAAC,IAAI;AAE1B,YAAM,SAAS,QAAQ,SAAS,EAAE,EAAE,YAAY;AAChD,aAAO,SAAS,UAAU,OAAO,MAAM,IAAI;AAAA,IAC5C;AAEA,YAAQ,IAAI,MAAM,SAAU,MAAM;AACjC,YAAM,QAAQ,KAAK,SAAS,EAAE,EAAE,MAAM,0BAA0B;AAChE,UAAI,CAAC,OAAO;AACX,eAAO,CAAC,GAAG,GAAG,CAAC;AAAA,MAChB;AAEA,UAAI,cAAc,MAAM,CAAC;AAEzB,UAAI,MAAM,CAAC,EAAE,WAAW,GAAG;AAC1B,sBAAc,YAAY,MAAM,EAAE,EAAE,IAAI,UAAQ;AAC/C,iBAAO,OAAO;AAAA,QACf,CAAC,EAAE,KAAK,EAAE;AAAA,MACX;AAEA,YAAM,UAAU,SAAS,aAAa,EAAE;AACxC,YAAM,IAAK,WAAW,KAAM;AAC5B,YAAM,IAAK,WAAW,IAAK;AAC3B,YAAM,IAAI,UAAU;AAEpB,aAAO,CAAC,GAAG,GAAG,CAAC;AAAA,IAChB;AAEA,YAAQ,IAAI,MAAM,SAAU,KAAK;AAChC,YAAM,IAAI,IAAI,CAAC,IAAI;AACnB,YAAM,IAAI,IAAI,CAAC,IAAI;AACnB,YAAM,IAAI,IAAI,CAAC,IAAI;AACnB,YAAM,MAAM,KAAK,IAAI,KAAK,IAAI,GAAG,CAAC,GAAG,CAAC;AACtC,YAAM,MAAM,KAAK,IAAI,KAAK,IAAI,GAAG,CAAC,GAAG,CAAC;AACtC,YAAM,SAAU,MAAM;AACtB,UAAI;AACJ,UAAI;AAEJ,UAAI,SAAS,GAAG;AACf,oBAAY,OAAO,IAAI;AAAA,MACxB,OAAO;AACN,oBAAY;AAAA,MACb;AAEA,UAAI,UAAU,GAAG;AAChB,cAAM;AAAA,MACP,WACI,QAAQ,GAAG;AACd,eAAQ,IAAI,KAAK,SAAU;AAAA,MAC5B,WACI,QAAQ,GAAG;AACd,cAAM,KAAK,IAAI,KAAK;AAAA,MACrB,OAAO;AACN,cAAM,KAAK,IAAI,KAAK;AAAA,MACrB;AAEA,aAAO;AACP,aAAO;AAEP,aAAO,CAAC,MAAM,KAAK,SAAS,KAAK,YAAY,GAAG;AAAA,IACjD;AAEA,YAAQ,IAAI,MAAM,SAAU,KAAK;AAChC,YAAM,IAAI,IAAI,CAAC,IAAI;AACnB,YAAM,IAAI,IAAI,CAAC,IAAI;AAEnB,YAAM,IAAI,IAAI,MAAO,IAAM,IAAI,IAAM,IAAM,KAAK,IAAM;AAEtD,UAAI,IAAI;AACR,UAAI,IAAI,GAAK;AACZ,aAAK,IAAI,MAAM,MAAM,IAAM;AAAA,MAC5B;AAEA,aAAO,CAAC,IAAI,CAAC,GAAG,IAAI,KAAK,IAAI,GAAG;AAAA,IACjC;AAEA,YAAQ,IAAI,MAAM,SAAU,KAAK;AAChC,YAAM,IAAI,IAAI,CAAC,IAAI;AACnB,YAAM,IAAI,IAAI,CAAC,IAAI;AAEnB,YAAM,IAAI,IAAI;AACd,UAAI,IAAI;AAER,UAAI,IAAI,GAAK;AACZ,aAAK,IAAI,MAAM,IAAI;AAAA,MACpB;AAEA,aAAO,CAAC,IAAI,CAAC,GAAG,IAAI,KAAK,IAAI,GAAG;AAAA,IACjC;AAEA,YAAQ,IAAI,MAAM,SAAU,KAAK;AAChC,YAAM,IAAI,IAAI,CAAC,IAAI;AACnB,YAAM,IAAI,IAAI,CAAC,IAAI;AACnB,YAAM,IAAI,IAAI,CAAC,IAAI;AAEnB,UAAI,MAAM,GAAK;AACd,eAAO,CAAC,IAAI,KAAK,IAAI,KAAK,IAAI,GAAG;AAAA,MAClC;AAEA,YAAM,OAAO,CAAC,GAAG,GAAG,CAAC;AACrB,YAAM,KAAM,IAAI,IAAK;AACrB,YAAM,IAAI,KAAK;AACf,YAAM,IAAI,IAAI;AACd,UAAI,KAAK;AAGT,cAAQ,KAAK,MAAM,EAAE,GAAG;AAAA,QACvB,KAAK;AACJ,eAAK,CAAC,IAAI;AAAG,eAAK,CAAC,IAAI;AAAG,eAAK,CAAC,IAAI;AAAG;AAAA,QACxC,KAAK;AACJ,eAAK,CAAC,IAAI;AAAG,eAAK,CAAC,IAAI;AAAG,eAAK,CAAC,IAAI;AAAG;AAAA,QACxC,KAAK;AACJ,eAAK,CAAC,IAAI;AAAG,eAAK,CAAC,IAAI;AAAG,eAAK,CAAC,IAAI;AAAG;AAAA,QACxC,KAAK;AACJ,eAAK,CAAC,IAAI;AAAG,eAAK,CAAC,IAAI;AAAG,eAAK,CAAC,IAAI;AAAG;AAAA,QACxC,KAAK;AACJ,eAAK,CAAC,IAAI;AAAG,eAAK,CAAC,IAAI;AAAG,eAAK,CAAC,IAAI;AAAG;AAAA,QACxC;AACC,eAAK,CAAC,IAAI;AAAG,eAAK,CAAC,IAAI;AAAG,eAAK,CAAC,IAAI;AAAA,MACtC;AAGA,YAAM,IAAM,KAAK;AAEjB,aAAO;AAAA,SACL,IAAI,KAAK,CAAC,IAAI,MAAM;AAAA,SACpB,IAAI,KAAK,CAAC,IAAI,MAAM;AAAA,SACpB,IAAI,KAAK,CAAC,IAAI,MAAM;AAAA,MACtB;AAAA,IACD;AAEA,YAAQ,IAAI,MAAM,SAAU,KAAK;AAChC,YAAM,IAAI,IAAI,CAAC,IAAI;AACnB,YAAM,IAAI,IAAI,CAAC,IAAI;AAEnB,YAAM,IAAI,IAAI,KAAK,IAAM;AACzB,UAAI,IAAI;AAER,UAAI,IAAI,GAAK;AACZ,YAAI,IAAI;AAAA,MACT;AAEA,aAAO,CAAC,IAAI,CAAC,GAAG,IAAI,KAAK,IAAI,GAAG;AAAA,IACjC;AAEA,YAAQ,IAAI,MAAM,SAAU,KAAK;AAChC,YAAM,IAAI,IAAI,CAAC,IAAI;AACnB,YAAM,IAAI,IAAI,CAAC,IAAI;AAEnB,YAAM,IAAI,KAAK,IAAM,KAAK,MAAM;AAChC,UAAI,IAAI;AAER,UAAI,IAAI,KAAO,IAAI,KAAK;AACvB,YAAI,KAAK,IAAI;AAAA,MACd,WACI,KAAK,OAAO,IAAI,GAAK;AACxB,YAAI,KAAK,KAAK,IAAI;AAAA,MACnB;AAEA,aAAO,CAAC,IAAI,CAAC,GAAG,IAAI,KAAK,IAAI,GAAG;AAAA,IACjC;AAEA,YAAQ,IAAI,MAAM,SAAU,KAAK;AAChC,YAAM,IAAI,IAAI,CAAC,IAAI;AACnB,YAAM,IAAI,IAAI,CAAC,IAAI;AACnB,YAAM,IAAI,IAAI,KAAK,IAAM;AACzB,aAAO,CAAC,IAAI,CAAC,IAAI,IAAI,KAAK,MAAM,IAAI,KAAK,GAAG;AAAA,IAC7C;AAEA,YAAQ,IAAI,MAAM,SAAU,KAAK;AAChC,YAAM,IAAI,IAAI,CAAC,IAAI;AACnB,YAAM,IAAI,IAAI,CAAC,IAAI;AACnB,YAAM,IAAI,IAAI;AACd,YAAM,IAAI,IAAI;AACd,UAAI,IAAI;AAER,UAAI,IAAI,GAAG;AACV,aAAK,IAAI,MAAM,IAAI;AAAA,MACpB;AAEA,aAAO,CAAC,IAAI,CAAC,GAAG,IAAI,KAAK,IAAI,GAAG;AAAA,IACjC;AAEA,YAAQ,MAAM,MAAM,SAAU,OAAO;AACpC,aAAO,CAAE,MAAM,CAAC,IAAI,QAAS,KAAM,MAAM,CAAC,IAAI,QAAS,KAAM,MAAM,CAAC,IAAI,QAAS,GAAG;AAAA,IACrF;AAEA,YAAQ,IAAI,QAAQ,SAAU,KAAK;AAClC,aAAO,CAAE,IAAI,CAAC,IAAI,MAAO,OAAQ,IAAI,CAAC,IAAI,MAAO,OAAQ,IAAI,CAAC,IAAI,MAAO,KAAK;AAAA,IAC/E;AAEA,YAAQ,KAAK,MAAM,SAAU,MAAM;AAClC,aAAO,CAAC,KAAK,CAAC,IAAI,MAAM,KAAK,KAAK,CAAC,IAAI,MAAM,KAAK,KAAK,CAAC,IAAI,MAAM,GAAG;AAAA,IACtE;AAEA,YAAQ,KAAK,MAAM,SAAU,MAAM;AAClC,aAAO,CAAC,GAAG,GAAG,KAAK,CAAC,CAAC;AAAA,IACtB;AAEA,YAAQ,KAAK,MAAM,QAAQ,KAAK;AAEhC,YAAQ,KAAK,MAAM,SAAU,MAAM;AAClC,aAAO,CAAC,GAAG,KAAK,KAAK,CAAC,CAAC;AAAA,IACxB;AAEA,YAAQ,KAAK,OAAO,SAAU,MAAM;AACnC,aAAO,CAAC,GAAG,GAAG,GAAG,KAAK,CAAC,CAAC;AAAA,IACzB;AAEA,YAAQ,KAAK,MAAM,SAAU,MAAM;AAClC,aAAO,CAAC,KAAK,CAAC,GAAG,GAAG,CAAC;AAAA,IACtB;AAEA,YAAQ,KAAK,MAAM,SAAU,MAAM;AAClC,YAAM,MAAM,KAAK,MAAM,KAAK,CAAC,IAAI,MAAM,GAAG,IAAI;AAC9C,YAAM,WAAW,OAAO,OAAO,OAAO,KAAK;AAE3C,YAAM,SAAS,QAAQ,SAAS,EAAE,EAAE,YAAY;AAChD,aAAO,SAAS,UAAU,OAAO,MAAM,IAAI;AAAA,IAC5C;AAEA,YAAQ,IAAI,OAAO,SAAU,KAAK;AACjC,YAAM,OAAO,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,IAAI,CAAC,KAAK;AACzC,aAAO,CAAC,MAAM,MAAM,GAAG;AAAA,IACxB;AAAA;AAAA;;;ACt0BA;AAAA;AAAA,QAAM,cAAc;AAapB,aAAS,aAAa;AACrB,YAAM,QAAQ,CAAC;AAEf,YAAM,SAAS,OAAO,KAAK,WAAW;AAEtC,eAAS,MAAM,OAAO,QAAQ,IAAI,GAAG,IAAI,KAAK,KAAK;AAClD,cAAM,OAAO,CAAC,CAAC,IAAI;AAAA;AAAA;AAAA,UAGlB,UAAU;AAAA,UACV,QAAQ;AAAA,QACT;AAAA,MACD;AAEA,aAAO;AAAA,IACR;AAGA,aAAS,UAAU,WAAW;AAC7B,YAAM,QAAQ,WAAW;AACzB,YAAM,QAAQ,CAAC,SAAS;AAExB,YAAM,SAAS,EAAE,WAAW;AAE5B,aAAO,MAAM,QAAQ;AACpB,cAAM,UAAU,MAAM,IAAI;AAC1B,cAAM,YAAY,OAAO,KAAK,YAAY,OAAO,CAAC;AAElD,iBAAS,MAAM,UAAU,QAAQ,IAAI,GAAG,IAAI,KAAK,KAAK;AACrD,gBAAM,WAAW,UAAU,CAAC;AAC5B,gBAAM,OAAO,MAAM,QAAQ;AAE3B,cAAI,KAAK,aAAa,IAAI;AACzB,iBAAK,WAAW,MAAM,OAAO,EAAE,WAAW;AAC1C,iBAAK,SAAS;AACd,kBAAM,QAAQ,QAAQ;AAAA,UACvB;AAAA,QACD;AAAA,MACD;AAEA,aAAO;AAAA,IACR;AAEA,aAAS,KAAK,MAAM,IAAI;AACvB,aAAO,SAAU,MAAM;AACtB,eAAO,GAAG,KAAK,IAAI,CAAC;AAAA,MACrB;AAAA,IACD;AAEA,aAAS,eAAe,SAAS,OAAO;AACvC,YAAM,OAAO,CAAC,MAAM,OAAO,EAAE,QAAQ,OAAO;AAC5C,UAAI,KAAK,YAAY,MAAM,OAAO,EAAE,MAAM,EAAE,OAAO;AAEnD,UAAI,MAAM,MAAM,OAAO,EAAE;AACzB,aAAO,MAAM,GAAG,EAAE,QAAQ;AACzB,aAAK,QAAQ,MAAM,GAAG,EAAE,MAAM;AAC9B,aAAK,KAAK,YAAY,MAAM,GAAG,EAAE,MAAM,EAAE,GAAG,GAAG,EAAE;AACjD,cAAM,MAAM,GAAG,EAAE;AAAA,MAClB;AAEA,SAAG,aAAa;AAChB,aAAO;AAAA,IACR;AAEA,WAAO,UAAU,SAAU,WAAW;AACrC,YAAM,QAAQ,UAAU,SAAS;AACjC,YAAM,aAAa,CAAC;AAEpB,YAAM,SAAS,OAAO,KAAK,KAAK;AAChC,eAAS,MAAM,OAAO,QAAQ,IAAI,GAAG,IAAI,KAAK,KAAK;AAClD,cAAM,UAAU,OAAO,CAAC;AACxB,cAAM,OAAO,MAAM,OAAO;AAE1B,YAAI,KAAK,WAAW,MAAM;AAEzB;AAAA,QACD;AAEA,mBAAW,OAAO,IAAI,eAAe,SAAS,KAAK;AAAA,MACpD;AAEA,aAAO;AAAA,IACR;AAAA;AAAA;;;AC/FA;AAAA;AAAA,QAAM,cAAc;AACpB,QAAM,QAAQ;AAEd,QAAM,UAAU,CAAC;AAEjB,QAAM,SAAS,OAAO,KAAK,WAAW;AAEtC,aAAS,QAAQ,IAAI;AACpB,YAAM,YAAY,YAAa,MAAM;AACpC,cAAM,OAAO,KAAK,CAAC;AACnB,YAAI,SAAS,UAAa,SAAS,MAAM;AACxC,iBAAO;AAAA,QACR;AAEA,YAAI,KAAK,SAAS,GAAG;AACpB,iBAAO;AAAA,QACR;AAEA,eAAO,GAAG,IAAI;AAAA,MACf;AAGA,UAAI,gBAAgB,IAAI;AACvB,kBAAU,aAAa,GAAG;AAAA,MAC3B;AAEA,aAAO;AAAA,IACR;AAEA,aAAS,YAAY,IAAI;AACxB,YAAM,YAAY,YAAa,MAAM;AACpC,cAAM,OAAO,KAAK,CAAC;AAEnB,YAAI,SAAS,UAAa,SAAS,MAAM;AACxC,iBAAO;AAAA,QACR;AAEA,YAAI,KAAK,SAAS,GAAG;AACpB,iBAAO;AAAA,QACR;AAEA,cAAM,SAAS,GAAG,IAAI;AAKtB,YAAI,OAAO,WAAW,UAAU;AAC/B,mBAAS,MAAM,OAAO,QAAQ,IAAI,GAAG,IAAI,KAAK,KAAK;AAClD,mBAAO,CAAC,IAAI,KAAK,MAAM,OAAO,CAAC,CAAC;AAAA,UACjC;AAAA,QACD;AAEA,eAAO;AAAA,MACR;AAGA,UAAI,gBAAgB,IAAI;AACvB,kBAAU,aAAa,GAAG;AAAA,MAC3B;AAEA,aAAO;AAAA,IACR;AAEA,WAAO,QAAQ,eAAa;AAC3B,cAAQ,SAAS,IAAI,CAAC;AAEtB,aAAO,eAAe,QAAQ,SAAS,GAAG,YAAY,EAAC,OAAO,YAAY,SAAS,EAAE,SAAQ,CAAC;AAC9F,aAAO,eAAe,QAAQ,SAAS,GAAG,UAAU,EAAC,OAAO,YAAY,SAAS,EAAE,OAAM,CAAC;AAE1F,YAAM,SAAS,MAAM,SAAS;AAC9B,YAAM,cAAc,OAAO,KAAK,MAAM;AAEtC,kBAAY,QAAQ,aAAW;AAC9B,cAAM,KAAK,OAAO,OAAO;AAEzB,gBAAQ,SAAS,EAAE,OAAO,IAAI,YAAY,EAAE;AAC5C,gBAAQ,SAAS,EAAE,OAAO,EAAE,MAAM,QAAQ,EAAE;AAAA,MAC7C,CAAC;AAAA,IACF,CAAC;AAED,WAAO,UAAU;AAAA;AAAA;;;AChFjB;AAAA;AAAA,QAAM,cAAc;AACpB,QAAM,UAAU;AAEhB,QAAM,gBAAgB;AAAA;AAAA,MAErB;AAAA;AAAA,MAGA;AAAA;AAAA,MAGA;AAAA,IACD;AAEA,QAAM,kBAAkB,CAAC;AACzB,eAAW,SAAS,OAAO,KAAK,OAAO,GAAG;AACzC,sBAAgB,CAAC,GAAG,QAAQ,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,IAAI;AAAA,IAC/D;AAEA,QAAM,WAAW,CAAC;AAElB,aAAS,MAAM,QAAQ,OAAO;AAC7B,UAAI,EAAE,gBAAgB,QAAQ;AAC7B,eAAO,IAAI,MAAM,QAAQ,KAAK;AAAA,MAC/B;AAEA,UAAI,SAAS,SAAS,eAAe;AACpC,gBAAQ;AAAA,MACT;AAEA,UAAI,SAAS,EAAE,SAAS,UAAU;AACjC,cAAM,IAAI,MAAM,oBAAoB,KAAK;AAAA,MAC1C;AAEA,UAAI;AACJ,UAAI;AAEJ,UAAI,UAAU,MAAM;AACnB,aAAK,QAAQ;AACb,aAAK,QAAQ,CAAC,GAAG,GAAG,CAAC;AACrB,aAAK,SAAS;AAAA,MACf,WAAW,kBAAkB,OAAO;AACnC,aAAK,QAAQ,OAAO;AACpB,aAAK,QAAQ,CAAC,GAAG,OAAO,KAAK;AAC7B,aAAK,SAAS,OAAO;AAAA,MACtB,WAAW,OAAO,WAAW,UAAU;AACtC,cAAM,SAAS,YAAY,IAAI,MAAM;AACrC,YAAI,WAAW,MAAM;AACpB,gBAAM,IAAI,MAAM,wCAAwC,MAAM;AAAA,QAC/D;AAEA,aAAK,QAAQ,OAAO;AACpB,mBAAW,QAAQ,KAAK,KAAK,EAAE;AAC/B,aAAK,QAAQ,OAAO,MAAM,MAAM,GAAG,QAAQ;AAC3C,aAAK,SAAS,OAAO,OAAO,MAAM,QAAQ,MAAM,WAAW,OAAO,MAAM,QAAQ,IAAI;AAAA,MACrF,WAAW,OAAO,SAAS,GAAG;AAC7B,aAAK,QAAQ,SAAS;AACtB,mBAAW,QAAQ,KAAK,KAAK,EAAE;AAC/B,cAAM,WAAW,MAAM,UAAU,MAAM,KAAK,QAAQ,GAAG,QAAQ;AAC/D,aAAK,QAAQ,UAAU,UAAU,QAAQ;AACzC,aAAK,SAAS,OAAO,OAAO,QAAQ,MAAM,WAAW,OAAO,QAAQ,IAAI;AAAA,MACzE,WAAW,OAAO,WAAW,UAAU;AAEtC,aAAK,QAAQ;AACb,aAAK,QAAQ;AAAA,UACX,UAAU,KAAM;AAAA,UAChB,UAAU,IAAK;AAAA,UAChB,SAAS;AAAA,QACV;AACA,aAAK,SAAS;AAAA,MACf,OAAO;AACN,aAAK,SAAS;AAEd,cAAM,OAAO,OAAO,KAAK,MAAM;AAC/B,YAAI,WAAW,QAAQ;AACtB,eAAK,OAAO,KAAK,QAAQ,OAAO,GAAG,CAAC;AACpC,eAAK,SAAS,OAAO,OAAO,UAAU,WAAW,OAAO,QAAQ;AAAA,QACjE;AAEA,cAAM,aAAa,KAAK,KAAK,EAAE,KAAK,EAAE;AACtC,YAAI,EAAE,cAAc,kBAAkB;AACrC,gBAAM,IAAI,MAAM,wCAAwC,KAAK,UAAU,MAAM,CAAC;AAAA,QAC/E;AAEA,aAAK,QAAQ,gBAAgB,UAAU;AAEvC,cAAM,EAAC,OAAM,IAAI,QAAQ,KAAK,KAAK;AACnC,cAAM,QAAQ,CAAC;AACf,aAAK,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACnC,gBAAM,KAAK,OAAO,OAAO,CAAC,CAAC,CAAC;AAAA,QAC7B;AAEA,aAAK,QAAQ,UAAU,KAAK;AAAA,MAC7B;AAGA,UAAI,SAAS,KAAK,KAAK,GAAG;AACzB,mBAAW,QAAQ,KAAK,KAAK,EAAE;AAC/B,aAAK,IAAI,GAAG,IAAI,UAAU,KAAK;AAC9B,gBAAM,QAAQ,SAAS,KAAK,KAAK,EAAE,CAAC;AACpC,cAAI,OAAO;AACV,iBAAK,MAAM,CAAC,IAAI,MAAM,KAAK,MAAM,CAAC,CAAC;AAAA,UACpC;AAAA,QACD;AAAA,MACD;AAEA,WAAK,SAAS,KAAK,IAAI,GAAG,KAAK,IAAI,GAAG,KAAK,MAAM,CAAC;AAElD,UAAI,OAAO,QAAQ;AAClB,eAAO,OAAO,IAAI;AAAA,MACnB;AAAA,IACD;AAEA,UAAM,YAAY;AAAA,MACjB,WAAW;AACV,eAAO,KAAK,OAAO;AAAA,MACpB;AAAA,MAEA,SAAS;AACR,eAAO,KAAK,KAAK,KAAK,EAAE;AAAA,MACzB;AAAA,MAEA,OAAO,QAAQ;AACd,YAAI,OAAO,KAAK,SAAS,YAAY,KAAK,OAAO,KAAK,IAAI;AAC1D,eAAO,KAAK,MAAM,OAAO,WAAW,WAAW,SAAS,CAAC;AACzD,cAAM,OAAO,KAAK,WAAW,IAAI,KAAK,QAAQ,CAAC,GAAG,KAAK,OAAO,KAAK,MAAM;AACzE,eAAO,YAAY,GAAG,KAAK,KAAK,EAAE,IAAI;AAAA,MACvC;AAAA,MAEA,cAAc,QAAQ;AACrB,cAAM,OAAO,KAAK,IAAI,EAAE,MAAM,OAAO,WAAW,WAAW,SAAS,CAAC;AACrE,cAAM,OAAO,KAAK,WAAW,IAAI,KAAK,QAAQ,CAAC,GAAG,KAAK,OAAO,KAAK,MAAM;AACzE,eAAO,YAAY,GAAG,IAAI,QAAQ,IAAI;AAAA,MACvC;AAAA,MAEA,QAAQ;AACP,eAAO,KAAK,WAAW,IAAI,CAAC,GAAG,KAAK,KAAK,IAAI,CAAC,GAAG,KAAK,OAAO,KAAK,MAAM;AAAA,MACzE;AAAA,MAEA,SAAS;AACR,cAAM,SAAS,CAAC;AAChB,cAAM,EAAC,SAAQ,IAAI,QAAQ,KAAK,KAAK;AACrC,cAAM,EAAC,OAAM,IAAI,QAAQ,KAAK,KAAK;AAEnC,iBAAS,IAAI,GAAG,IAAI,UAAU,KAAK;AAClC,iBAAO,OAAO,CAAC,CAAC,IAAI,KAAK,MAAM,CAAC;AAAA,QACjC;AAEA,YAAI,KAAK,WAAW,GAAG;AACtB,iBAAO,QAAQ,KAAK;AAAA,QACrB;AAEA,eAAO;AAAA,MACR;AAAA,MAEA,YAAY;AACX,cAAM,MAAM,KAAK,IAAI,EAAE;AACvB,YAAI,CAAC,KAAK;AACV,YAAI,CAAC,KAAK;AACV,YAAI,CAAC,KAAK;AAEV,YAAI,KAAK,WAAW,GAAG;AACtB,cAAI,KAAK,KAAK,MAAM;AAAA,QACrB;AAEA,eAAO;AAAA,MACR;AAAA,MAEA,aAAa;AACZ,cAAM,MAAM,KAAK,IAAI,EAAE,OAAO;AAC9B,YAAI,KAAK;AACT,YAAI,KAAK;AACT,YAAI,KAAK;AAET,YAAI,KAAK,WAAW,GAAG;AACtB,cAAI,QAAQ,KAAK;AAAA,QAClB;AAEA,eAAO;AAAA,MACR;AAAA,MAEA,MAAM,QAAQ;AACb,iBAAS,KAAK,IAAI,UAAU,GAAG,CAAC;AAChC,eAAO,IAAI,MAAM,CAAC,GAAG,KAAK,MAAM,IAAI,aAAa,MAAM,CAAC,GAAG,KAAK,MAAM,GAAG,KAAK,KAAK;AAAA,MACpF;AAAA,MAEA,MAAM,OAAO;AACZ,YAAI,UAAU,QAAW;AACxB,iBAAO,IAAI,MAAM,CAAC,GAAG,KAAK,OAAO,KAAK,IAAI,GAAG,KAAK,IAAI,GAAG,KAAK,CAAC,CAAC,GAAG,KAAK,KAAK;AAAA,QAC9E;AAEA,eAAO,KAAK;AAAA,MACb;AAAA;AAAA,MAGA,KAAK,OAAO,OAAO,GAAG,MAAM,GAAG,CAAC;AAAA,MAChC,OAAO,OAAO,OAAO,GAAG,MAAM,GAAG,CAAC;AAAA,MAClC,MAAM,OAAO,OAAO,GAAG,MAAM,GAAG,CAAC;AAAA,MAEjC,KAAK,OAAO,CAAC,OAAO,OAAO,OAAO,OAAO,KAAK,GAAG,GAAG,YAAW,QAAQ,MAAO,OAAO,GAAG;AAAA,MAExF,aAAa,OAAO,OAAO,GAAG,MAAM,GAAG,CAAC;AAAA,MACxC,WAAW,OAAO,OAAO,GAAG,MAAM,GAAG,CAAC;AAAA,MAEtC,aAAa,OAAO,OAAO,GAAG,MAAM,GAAG,CAAC;AAAA,MACxC,OAAO,OAAO,OAAO,GAAG,MAAM,GAAG,CAAC;AAAA,MAElC,QAAQ,OAAO,OAAO,GAAG,MAAM,GAAG,CAAC;AAAA,MACnC,MAAM,OAAO,OAAO,GAAG,MAAM,GAAG,CAAC;AAAA,MAEjC,OAAO,OAAO,OAAO,GAAG,MAAM,GAAG,CAAC;AAAA,MAClC,QAAQ,OAAO,OAAO,GAAG,MAAM,GAAG,CAAC;AAAA,MAEnC,MAAM,OAAO,QAAQ,GAAG,MAAM,GAAG,CAAC;AAAA,MAClC,SAAS,OAAO,QAAQ,GAAG,MAAM,GAAG,CAAC;AAAA,MACrC,QAAQ,OAAO,QAAQ,GAAG,MAAM,GAAG,CAAC;AAAA,MACpC,OAAO,OAAO,QAAQ,GAAG,MAAM,GAAG,CAAC;AAAA,MAEnC,GAAG,OAAO,OAAO,GAAG,MAAM,MAAM,CAAC;AAAA,MACjC,GAAG,OAAO,OAAO,GAAG,MAAM,GAAG,CAAC;AAAA,MAC9B,GAAG,OAAO,OAAO,GAAG,MAAM,OAAO,CAAC;AAAA,MAElC,GAAG,OAAO,OAAO,GAAG,MAAM,GAAG,CAAC;AAAA,MAC9B,GAAG,OAAO,OAAO,CAAC;AAAA,MAClB,GAAG,OAAO,OAAO,CAAC;AAAA,MAElB,QAAQ,OAAO;AACd,YAAI,UAAU,QAAW;AACxB,iBAAO,IAAI,MAAM,KAAK;AAAA,QACvB;AAEA,eAAO,QAAQ,KAAK,KAAK,EAAE,QAAQ,KAAK,KAAK;AAAA,MAC9C;AAAA,MAEA,IAAI,OAAO;AACV,YAAI,UAAU,QAAW;AACxB,iBAAO,IAAI,MAAM,KAAK;AAAA,QACvB;AAEA,eAAO,YAAY,GAAG,IAAI,KAAK,IAAI,EAAE,MAAM,EAAE,KAAK;AAAA,MACnD;AAAA,MAEA,KAAK,OAAO;AACX,YAAI,UAAU,QAAW;AACxB,iBAAO,IAAI,MAAM,KAAK;AAAA,QACvB;AAEA,cAAM,WAAW,KAAK,IAAI,EAAE,MAAM,EAAE;AAEpC,YAAI,WAAW,KAAK,MAAM,KAAK,SAAS,GAAG,EAAE,SAAS,EAAE,EAAE,YAAY;AACtE,YAAI,SAAS,WAAW,GAAG;AAC1B,qBAAW,MAAM;AAAA,QAClB;AAEA,eAAO,YAAY,GAAG,IAAI,QAAQ,IAAI;AAAA,MACvC;AAAA,MAEA,YAAY;AACX,cAAM,MAAM,KAAK,IAAI,EAAE;AACvB,gBAAS,IAAI,CAAC,IAAI,QAAS,MAAQ,IAAI,CAAC,IAAI,QAAS,IAAM,IAAI,CAAC,IAAI;AAAA,MACrE;AAAA,MAEA,aAAa;AAEZ,cAAM,MAAM,KAAK,IAAI,EAAE;AAEvB,cAAM,MAAM,CAAC;AACb,mBAAW,CAAC,GAAG,OAAO,KAAK,IAAI,QAAQ,GAAG;AACzC,gBAAM,OAAO,UAAU;AACvB,cAAI,CAAC,IAAK,QAAQ,UAAW,OAAO,UAAU,OAAO,SAAS,UAAU;AAAA,QACzE;AAEA,eAAO,SAAS,IAAI,CAAC,IAAI,SAAS,IAAI,CAAC,IAAI,SAAS,IAAI,CAAC;AAAA,MAC1D;AAAA,MAEA,SAAS,QAAQ;AAEhB,cAAM,OAAO,KAAK,WAAW;AAC7B,cAAM,OAAO,OAAO,WAAW;AAE/B,YAAI,OAAO,MAAM;AAChB,kBAAQ,OAAO,SAAS,OAAO;AAAA,QAChC;AAEA,gBAAQ,OAAO,SAAS,OAAO;AAAA,MAChC;AAAA,MAEA,MAAM,QAAQ;AAEb,cAAM,gBAAgB,KAAK,SAAS,MAAM;AAC1C,YAAI,iBAAiB,GAAG;AACvB,iBAAO;AAAA,QACR;AAEA,eAAQ,iBAAiB,MAAO,OAAO;AAAA,MACxC;AAAA,MAEA,SAAS;AAER,cAAM,MAAM,KAAK,IAAI,EAAE;AACvB,cAAM,OAAO,IAAI,CAAC,IAAI,OAAO,IAAI,CAAC,IAAI,OAAO,IAAI,CAAC,IAAI,OAAO;AAC7D,eAAO,MAAM;AAAA,MACd;AAAA,MAEA,UAAU;AACT,eAAO,CAAC,KAAK,OAAO;AAAA,MACrB;AAAA,MAEA,SAAS;AACR,cAAM,MAAM,KAAK,IAAI;AACrB,iBAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC3B,cAAI,MAAM,CAAC,IAAI,MAAM,IAAI,MAAM,CAAC;AAAA,QACjC;AAEA,eAAO;AAAA,MACR;AAAA,MAEA,QAAQ,OAAO;AACd,cAAM,MAAM,KAAK,IAAI;AACrB,YAAI,MAAM,CAAC,KAAK,IAAI,MAAM,CAAC,IAAI;AAC/B,eAAO;AAAA,MACR;AAAA,MAEA,OAAO,OAAO;AACb,cAAM,MAAM,KAAK,IAAI;AACrB,YAAI,MAAM,CAAC,KAAK,IAAI,MAAM,CAAC,IAAI;AAC/B,eAAO;AAAA,MACR;AAAA,MAEA,SAAS,OAAO;AACf,cAAM,MAAM,KAAK,IAAI;AACrB,YAAI,MAAM,CAAC,KAAK,IAAI,MAAM,CAAC,IAAI;AAC/B,eAAO;AAAA,MACR;AAAA,MAEA,WAAW,OAAO;AACjB,cAAM,MAAM,KAAK,IAAI;AACrB,YAAI,MAAM,CAAC,KAAK,IAAI,MAAM,CAAC,IAAI;AAC/B,eAAO;AAAA,MACR;AAAA,MAEA,OAAO,OAAO;AACb,cAAM,MAAM,KAAK,IAAI;AACrB,YAAI,MAAM,CAAC,KAAK,IAAI,MAAM,CAAC,IAAI;AAC/B,eAAO;AAAA,MACR;AAAA,MAEA,QAAQ,OAAO;AACd,cAAM,MAAM,KAAK,IAAI;AACrB,YAAI,MAAM,CAAC,KAAK,IAAI,MAAM,CAAC,IAAI;AAC/B,eAAO;AAAA,MACR;AAAA,MAEA,YAAY;AAEX,cAAM,MAAM,KAAK,IAAI,EAAE;AACvB,cAAM,QAAQ,IAAI,CAAC,IAAI,MAAM,IAAI,CAAC,IAAI,OAAO,IAAI,CAAC,IAAI;AACtD,eAAO,MAAM,IAAI,OAAO,OAAO,KAAK;AAAA,MACrC;AAAA,MAEA,KAAK,OAAO;AACX,eAAO,KAAK,MAAM,KAAK,SAAU,KAAK,SAAS,KAAM;AAAA,MACtD;AAAA,MAEA,QAAQ,OAAO;AACd,eAAO,KAAK,MAAM,KAAK,SAAU,KAAK,SAAS,KAAM;AAAA,MACtD;AAAA,MAEA,OAAO,SAAS;AACf,cAAM,MAAM,KAAK,IAAI;AACrB,YAAI,MAAM,IAAI,MAAM,CAAC;AACrB,eAAO,MAAM,WAAW;AACxB,cAAM,MAAM,IAAI,MAAM,MAAM;AAC5B,YAAI,MAAM,CAAC,IAAI;AACf,eAAO;AAAA,MACR;AAAA,MAEA,IAAI,YAAY,QAAQ;AAGvB,YAAI,CAAC,cAAc,CAAC,WAAW,KAAK;AACnC,gBAAM,IAAI,MAAM,2EAA2E,OAAO,UAAU;AAAA,QAC7G;AAEA,cAAM,SAAS,WAAW,IAAI;AAC9B,cAAM,SAAS,KAAK,IAAI;AACxB,cAAM,IAAI,WAAW,SAAY,MAAM;AAEvC,cAAM,IAAI,IAAI,IAAI;AAClB,cAAM,IAAI,OAAO,MAAM,IAAI,OAAO,MAAM;AAExC,cAAM,OAAQ,IAAI,MAAM,KAAM,KAAK,IAAI,MAAM,IAAI,IAAI,MAAM,KAAK;AAChE,cAAM,KAAK,IAAI;AAEf,eAAO,MAAM;AAAA,UACZ,KAAK,OAAO,IAAI,IAAI,KAAK,OAAO,IAAI;AAAA,UACpC,KAAK,OAAO,MAAM,IAAI,KAAK,OAAO,MAAM;AAAA,UACxC,KAAK,OAAO,KAAK,IAAI,KAAK,OAAO,KAAK;AAAA,UACtC,OAAO,MAAM,IAAI,IAAI,OAAO,MAAM,KAAK,IAAI;AAAA,QAAE;AAAA,MAC/C;AAAA,IACD;AAGA,eAAW,SAAS,OAAO,KAAK,OAAO,GAAG;AACzC,UAAI,cAAc,SAAS,KAAK,GAAG;AAClC;AAAA,MACD;AAEA,YAAM,EAAC,SAAQ,IAAI,QAAQ,KAAK;AAGhC,YAAM,UAAU,KAAK,IAAI,YAAa,MAAM;AAC3C,YAAI,KAAK,UAAU,OAAO;AACzB,iBAAO,IAAI,MAAM,IAAI;AAAA,QACtB;AAEA,YAAI,KAAK,SAAS,GAAG;AACpB,iBAAO,IAAI,MAAM,MAAM,KAAK;AAAA,QAC7B;AAEA,eAAO,IAAI,MAAM,CAAC,GAAG,YAAY,QAAQ,KAAK,KAAK,EAAE,KAAK,EAAE,IAAI,KAAK,KAAK,CAAC,GAAG,KAAK,MAAM,GAAG,KAAK;AAAA,MAClG;AAGA,YAAM,KAAK,IAAI,YAAa,MAAM;AACjC,YAAI,QAAQ,KAAK,CAAC;AAClB,YAAI,OAAO,UAAU,UAAU;AAC9B,kBAAQ,UAAU,MAAM,QAAQ;AAAA,QACjC;AAEA,eAAO,IAAI,MAAM,OAAO,KAAK;AAAA,MAC9B;AAAA,IACD;AAEA,aAAS,QAAQ,QAAQ,QAAQ;AAChC,aAAO,OAAO,OAAO,QAAQ,MAAM,CAAC;AAAA,IACrC;AAEA,aAAS,aAAa,QAAQ;AAC7B,aAAO,SAAU,QAAQ;AACxB,eAAO,QAAQ,QAAQ,MAAM;AAAA,MAC9B;AAAA,IACD;AAEA,aAAS,OAAO,OAAO,SAAS,UAAU;AACzC,cAAQ,MAAM,QAAQ,KAAK,IAAI,QAAQ,CAAC,KAAK;AAE7C,iBAAW,KAAK,OAAO;AACtB,SAAC,SAAS,CAAC,MAAM,SAAS,CAAC,IAAI,CAAC,IAAI,OAAO,IAAI;AAAA,MAChD;AAEA,cAAQ,MAAM,CAAC;AAEf,aAAO,SAAU,OAAO;AACvB,YAAI;AAEJ,YAAI,UAAU,QAAW;AACxB,cAAI,UAAU;AACb,oBAAQ,SAAS,KAAK;AAAA,UACvB;AAEA,mBAAS,KAAK,KAAK,EAAE;AACrB,iBAAO,MAAM,OAAO,IAAI;AACxB,iBAAO;AAAA,QACR;AAEA,iBAAS,KAAK,KAAK,EAAE,EAAE,MAAM,OAAO;AACpC,YAAI,UAAU;AACb,mBAAS,SAAS,MAAM;AAAA,QACzB;AAEA,eAAO;AAAA,MACR;AAAA,IACD;AAEA,aAAS,MAAM,KAAK;AACnB,aAAO,SAAU,GAAG;AACnB,eAAO,KAAK,IAAI,GAAG,KAAK,IAAI,KAAK,CAAC,CAAC;AAAA,MACpC;AAAA,IACD;AAEA,aAAS,YAAY,OAAO;AAC3B,aAAO,MAAM,QAAQ,KAAK,IAAI,QAAQ,CAAC,KAAK;AAAA,IAC7C;AAEA,aAAS,UAAU,OAAO,QAAQ;AACjC,eAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAChC,YAAI,OAAO,MAAM,CAAC,MAAM,UAAU;AACjC,gBAAM,CAAC,IAAI;AAAA,QACZ;AAAA,MACD;AAEA,aAAO;AAAA,IACR;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": ["swizzle"]}