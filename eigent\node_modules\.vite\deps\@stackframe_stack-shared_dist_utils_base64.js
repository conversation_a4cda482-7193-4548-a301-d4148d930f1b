import "./chunk-7UVSMXVG.js";

// package/@stackframe/react/node_modules/@stackframe/stack-shared/dist/esm/utils/base64.js
function fileToBase64(file) {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result);
    reader.onerror = (error) => reject(error);
  });
}
function validateBase64Image(base64) {
  const base64ImageRegex = /^data:image\/(png|jpg|jpeg|gif|bmp|webp);base64,[A-Za-z0-9+/]+={0,2}$|^[A-Za-z0-9+/]+={0,2}$/;
  return base64ImageRegex.test(base64);
}
export {
  fileToBase64,
  validateBase64Image
};
//# sourceMappingURL=@stackframe_stack-shared_dist_utils_base64.js.map
