{"version": 3, "sources": ["../../@oslojs/binary/dist/uint.js", "../../@oslojs/binary/dist/bits.js", "../../@oslojs/crypto/dist/hmac/index.js", "../../@oslojs/crypto/dist/sha1/index.js", "../../@oslojs/crypto/dist/subtle/index.js", "../../@oslojs/encoding/dist/base32.js", "../../@oslojs/encoding/dist/base64.js", "../../@oslojs/otp/dist/hotp.js", "../../@oslojs/otp/dist/totp.js"], "sourcesContent": ["class BigEndian {\n    uint8(data, offset) {\n        if (data.byteLength < offset + 1) {\n            throw new TypeError(\"Insufficient bytes\");\n        }\n        return data[offset];\n    }\n    uint16(data, offset) {\n        if (data.byteLength < offset + 2) {\n            throw new TypeError(\"Insufficient bytes\");\n        }\n        return (data[offset] << 8) | data[offset + 1];\n    }\n    uint32(data, offset) {\n        if (data.byteLength < offset + 4) {\n            throw new TypeError(\"Insufficient bytes\");\n        }\n        let result = 0;\n        for (let i = 0; i < 4; i++) {\n            result |= data[offset + i] << (24 - i * 8);\n        }\n        return result;\n    }\n    uint64(data, offset) {\n        if (data.byteLength < offset + 8) {\n            throw new TypeError(\"Insufficient bytes\");\n        }\n        let result = 0n;\n        for (let i = 0; i < 8; i++) {\n            result |= BigInt(data[offset + i]) << BigInt(56 - i * 8);\n        }\n        return result;\n    }\n    putUint8(target, value, offset) {\n        if (target.length < offset + 1) {\n            throw new TypeError(\"Not enough space\");\n        }\n        if (value < 0 || value > 255) {\n            throw new TypeError(\"Invalid uint8 value\");\n        }\n        target[offset] = value;\n    }\n    putUint16(target, value, offset) {\n        if (target.length < offset + 2) {\n            throw new TypeError(\"Not enough space\");\n        }\n        if (value < 0 || value > 65535) {\n            throw new TypeError(\"Invalid uint16 value\");\n        }\n        target[offset] = value >> 8;\n        target[offset + 1] = value & 0xff;\n    }\n    putUint32(target, value, offset) {\n        if (target.length < offset + 4) {\n            throw new TypeError(\"Not enough space\");\n        }\n        if (value < 0 || value > 4294967295) {\n            throw new TypeError(\"Invalid uint32 value\");\n        }\n        for (let i = 0; i < 4; i++) {\n            target[offset + i] = (value >> ((3 - i) * 8)) & 0xff;\n        }\n    }\n    putUint64(target, value, offset) {\n        if (target.length < offset + 8) {\n            throw new TypeError(\"Not enough space\");\n        }\n        if (value < 0 || value > 18446744073709551615n) {\n            throw new TypeError(\"Invalid uint64 value\");\n        }\n        for (let i = 0; i < 8; i++) {\n            target[offset + i] = Number((value >> BigInt((7 - i) * 8)) & 0xffn);\n        }\n    }\n}\nclass LittleEndian {\n    uint8(data, offset) {\n        if (data.byteLength < offset + 1) {\n            throw new TypeError(\"Insufficient bytes\");\n        }\n        return data[offset];\n    }\n    uint16(data, offset) {\n        if (data.byteLength < offset + 2) {\n            throw new TypeError(\"Insufficient bytes\");\n        }\n        return data[offset] | (data[offset + 1] << 8);\n    }\n    uint32(data, offset) {\n        if (data.byteLength < offset + 4) {\n            throw new TypeError(\"Insufficient bytes\");\n        }\n        let result = 0;\n        for (let i = 0; i < 4; i++) {\n            result |= data[offset + i] << (i * 8);\n        }\n        return result;\n    }\n    uint64(data, offset) {\n        if (data.byteLength < offset + 8) {\n            throw new TypeError(\"Insufficient bytes\");\n        }\n        let result = 0n;\n        for (let i = 0; i < 8; i++) {\n            result |= BigInt(data[offset + i]) << BigInt(i * 8);\n        }\n        return result;\n    }\n    putUint8(target, value, offset) {\n        if (target.length < 1 + offset) {\n            throw new TypeError(\"Insufficient space\");\n        }\n        if (value < 0 || value > 255) {\n            throw new TypeError(\"Invalid uint8 value\");\n        }\n        target[offset] = value;\n    }\n    putUint16(target, value, offset) {\n        if (target.length < 2 + offset) {\n            throw new TypeError(\"Insufficient space\");\n        }\n        if (value < 0 || value > 65535) {\n            throw new TypeError(\"Invalid uint16 value\");\n        }\n        target[offset + 1] = value >> 8;\n        target[offset] = value & 0xff;\n    }\n    putUint32(target, value, offset) {\n        if (target.length < 4 + offset) {\n            throw new TypeError(\"Insufficient space\");\n        }\n        if (value < 0 || value > 4294967295) {\n            throw new TypeError(\"Invalid uint32 value\");\n        }\n        for (let i = 0; i < 4; i++) {\n            target[offset + i] = (value >> (i * 8)) & 0xff;\n        }\n    }\n    putUint64(target, value, offset) {\n        if (target.length < 8 + offset) {\n            throw new TypeError(\"Insufficient space\");\n        }\n        if (value < 0 || value > 18446744073709551615n) {\n            throw new TypeError(\"Invalid uint64 value\");\n        }\n        for (let i = 0; i < 8; i++) {\n            target[offset + i] = Number((value >> BigInt(i * 8)) & 0xffn);\n        }\n    }\n}\nexport const bigEndian = new BigEndian();\nexport const littleEndian = new LittleEndian();\n", "export function rotl32(x, n) {\n    return ((x << n) | (x >>> (32 - n))) >>> 0;\n}\nexport function rotr32(x, n) {\n    return ((x << (32 - n)) | (x >>> n)) >>> 0;\n}\nexport function rotr64(x, n) {\n    return ((x << BigInt(64 - n)) | (x >> BigInt(n))) & 0xffffffffffffffffn;\n}\nexport function rotl64(x, n) {\n    return ((x << BigInt(n)) | (x >> BigInt(64 - n))) & 0xffffffffffffffffn;\n}\n", "export class HMAC {\n    k0;\n    inner;\n    outer;\n    constructor(Algorithm, key) {\n        const keyHash = new Algorithm();\n        if (key.byteLength === keyHash.blockSize) {\n            this.k0 = key;\n        }\n        else if (key.byteLength > keyHash.blockSize) {\n            this.k0 = new Uint8Array(keyHash.blockSize);\n            keyHash.update(key);\n            this.k0.set(keyHash.digest());\n        }\n        else {\n            this.k0 = new Uint8Array(keyHash.blockSize);\n            this.k0.set(key);\n        }\n        this.inner = new Algorithm();\n        const ipad = new Uint8Array(this.k0.byteLength).fill(0x36);\n        for (let i = 0; i < ipad.byteLength; i++) {\n            ipad[i] ^= this.k0[i];\n        }\n        this.inner.update(ipad);\n        this.outer = new Algorithm();\n        const opad = new Uint8Array(this.k0.byteLength).fill(0x5c);\n        for (let i = 0; i < opad.byteLength; i++) {\n            opad[i] ^= this.k0[i];\n        }\n        this.outer.update(opad);\n    }\n    update(message) {\n        this.inner.update(message);\n    }\n    digest() {\n        this.outer.update(this.inner.digest());\n        return this.outer.digest();\n    }\n}\nexport function hmac(Algorithm, key, message) {\n    const mac = new HMAC(Algorithm, key);\n    mac.update(message);\n    return mac.digest();\n}\n", "import { big<PERSON>ndian } from \"@oslojs/binary\";\nimport { rotl32 } from \"@oslojs/binary\";\n// Faster or comparable to Web Crypto < 2000 bytes.\nexport function sha1(data) {\n    const hash = new SHA1();\n    hash.update(data);\n    return hash.digest();\n}\nexport class SHA1 {\n    blockSize = 64;\n    size = 20;\n    blocks = new Uint8Array(64);\n    currentBlockSize = 0;\n    H = new Uint32Array([0x67452301, 0xefcdab89, 0x98badcfe, 0x10325476, 0xc3d2e1f0]);\n    l = 0;\n    w = new Uint32Array(80);\n    update(data) {\n        this.l += data.byteLength * 8;\n        if (this.currentBlockSize + data.byteLength < 64) {\n            this.blocks.set(data, this.currentBlockSize);\n            this.currentBlockSize += data.byteLength;\n            return;\n        }\n        let processed = 0;\n        if (this.currentBlockSize > 0) {\n            const next = data.slice(0, 64 - this.currentBlockSize);\n            this.blocks.set(next, this.currentBlockSize);\n            this.process();\n            processed += next.byteLength;\n            this.currentBlockSize = 0;\n        }\n        while (processed + 64 <= data.byteLength) {\n            const next = data.slice(processed, processed + 64);\n            this.blocks.set(next);\n            this.process();\n            processed += 64;\n        }\n        if (data.byteLength - processed > 0) {\n            const remaining = data.slice(processed);\n            this.blocks.set(remaining);\n            this.currentBlockSize = remaining.byteLength;\n        }\n    }\n    digest() {\n        this.blocks[this.currentBlockSize] = 0x80;\n        this.currentBlockSize += 1;\n        if (64 - this.currentBlockSize < 8) {\n            this.blocks.fill(0, this.currentBlockSize);\n            this.process();\n            this.currentBlockSize = 0;\n        }\n        this.blocks.fill(0, this.currentBlockSize);\n        bigEndian.putUint64(this.blocks, BigInt(this.l), this.blockSize - 8);\n        this.process();\n        const result = new Uint8Array(20);\n        for (let i = 0; i < 5; i++) {\n            bigEndian.putUint32(result, this.H[i], i * 4);\n        }\n        return result;\n    }\n    process() {\n        for (let t = 0; t < 16; t++) {\n            this.w[t] =\n                ((this.blocks[t * 4] << 24) |\n                    (this.blocks[t * 4 + 1] << 16) |\n                    (this.blocks[t * 4 + 2] << 8) |\n                    this.blocks[t * 4 + 3]) >>>\n                    0;\n        }\n        for (let t = 16; t < 80; t++) {\n            this.w[t] = rotl32((this.w[t - 3] ^ this.w[t - 8] ^ this.w[t - 14] ^ this.w[t - 16]) >>> 0, 1);\n        }\n        let a = this.H[0];\n        let b = this.H[1];\n        let c = this.H[2];\n        let d = this.H[3];\n        let e = this.H[4];\n        for (let t = 0; t < 80; t++) {\n            let F, K;\n            if (t < 20) {\n                F = ((b & c) ^ (~b & d)) >>> 0;\n                K = 0x5a827999;\n            }\n            else if (t < 40) {\n                F = (b ^ c ^ d) >>> 0;\n                K = 0x6ed9eba1;\n            }\n            else if (t < 60) {\n                F = ((b & c) ^ (b & d) ^ (c & d)) >>> 0;\n                K = 0x8f1bbcdc;\n            }\n            else {\n                F = (b ^ c ^ d) >>> 0;\n                K = 0xca62c1d6;\n            }\n            const T = (rotl32(a, 5) + e + F + this.w[t] + K) | 0;\n            e = d;\n            d = c;\n            c = rotl32(b, 30);\n            b = a;\n            a = T;\n        }\n        this.H[0] = (this.H[0] + a) | 0;\n        this.H[1] = (this.H[1] + b) | 0;\n        this.H[2] = (this.H[2] + c) | 0;\n        this.H[3] = (this.H[3] + d) | 0;\n        this.H[4] = (this.H[4] + e) | 0;\n    }\n}\n", "export function constantTimeEqual(a, b) {\n    if (a.length !== b.length) {\n        return false;\n    }\n    let c = 0;\n    for (let i = 0; i < a.length; i++) {\n        c |= a[i] ^ b[i];\n    }\n    return c === 0;\n}\n", "export function encodeBase32(bytes) {\n    return encodeBase32_internal(bytes, base32<PERSON><PERSON><PERSON>bet, EncodingPadding.Include);\n}\nexport function encodeBase32NoPadding(bytes) {\n    return encodeBase32_internal(bytes, base32<PERSON>lphabet, EncodingPadding.None);\n}\nfunction encodeBase32_internal(bytes, alphabet, padding) {\n    let result = \"\";\n    for (let i = 0; i < bytes.byteLength; i += 5) {\n        let buffer = 0n;\n        let bufferBitSize = 0;\n        for (let j = 0; j < 5 && i + j < bytes.byteLength; j++) {\n            buffer = (buffer << 8n) | BigInt(bytes[i + j]);\n            bufferBitSize += 8;\n        }\n        if (bufferBitSize % 5 !== 0) {\n            buffer = buffer << BigInt(5 - (bufferBitSize % 5));\n            bufferBitSize += 5 - (bufferBitSize % 5);\n        }\n        for (let j = 0; j < 8; j++) {\n            if (bufferBitSize >= 5) {\n                result += alphabet[Number((buffer >> BigInt(bufferBitSize - 5)) & 0x1fn)];\n                bufferBitSize -= 5;\n            }\n            else if (bufferBitSize > 0) {\n                result += alphabet[Number((buffer << BigInt(6 - bufferBitSize)) & 0x3fn)];\n                bufferBitSize = 0;\n            }\n            else if (padding === EncodingPadding.Include) {\n                result += \"=\";\n            }\n        }\n    }\n    return result;\n}\nexport function decodeBase32(encoded) {\n    return decodeBase32_internal(encoded, base32DecodeMap, DecodingPadding.Required);\n}\nexport function decodeBase32IgnorePadding(encoded) {\n    return decodeBase32_internal(encoded, base32DecodeMap, DecodingPadding.Ignore);\n}\nfunction decodeBase32_internal(encoded, decodeMap, padding) {\n    const result = new Uint8Array(Math.ceil(encoded.length / 8) * 5);\n    let totalBytes = 0;\n    for (let i = 0; i < encoded.length; i += 8) {\n        let chunk = 0n;\n        let bitsRead = 0;\n        for (let j = 0; j < 8; j++) {\n            if (padding === DecodingPadding.Required) {\n                if (encoded[i + j] === \"=\") {\n                    continue;\n                }\n                if (i + j >= encoded.length) {\n                    throw new Error(\"Invalid padding\");\n                }\n            }\n            if (padding === DecodingPadding.Ignore) {\n                if (i + j >= encoded.length || encoded[i + j] === \"=\") {\n                    continue;\n                }\n            }\n            if (j > 0 && encoded[i + j - 1] === \"=\") {\n                throw new Error(\"Invalid padding\");\n            }\n            if (!(encoded[i + j] in decodeMap)) {\n                throw new Error(\"Invalid character\");\n            }\n            chunk |= BigInt(decodeMap[encoded[i + j]]) << BigInt((7 - j) * 5);\n            bitsRead += 5;\n        }\n        if (bitsRead < 40) {\n            let unused;\n            if (bitsRead === 10) {\n                unused = chunk & 0xffffffffn;\n            }\n            else if (bitsRead === 20) {\n                unused = chunk & 0xffffffn;\n            }\n            else if (bitsRead === 25) {\n                unused = chunk & 0xffffn;\n            }\n            else if (bitsRead === 35) {\n                unused = chunk & 0xffn;\n            }\n            else {\n                throw new Error(\"Invalid padding\");\n            }\n            if (unused !== 0n) {\n                throw new Error(\"Invalid padding\");\n            }\n        }\n        const byteLength = Math.floor(bitsRead / 8);\n        for (let i = 0; i < byteLength; i++) {\n            result[totalBytes] = Number((chunk >> BigInt(32 - i * 8)) & 0xffn);\n            totalBytes++;\n        }\n    }\n    return result.slice(0, totalBytes);\n}\nconst base32Alphabet = \"ABCDEFGHIJKLMNOPQRSTUVWXYZ234567\";\nconst base32DecodeMap = {\n    A: 0,\n    B: 1,\n    C: 2,\n    D: 3,\n    E: 4,\n    F: 5,\n    G: 6,\n    H: 7,\n    I: 8,\n    J: 9,\n    K: 10,\n    L: 11,\n    M: 12,\n    N: 13,\n    O: 14,\n    P: 15,\n    Q: 16,\n    R: 17,\n    S: 18,\n    T: 19,\n    U: 20,\n    V: 21,\n    W: 22,\n    X: 23,\n    Y: 24,\n    Z: 25,\n    a: 0,\n    b: 1,\n    c: 2,\n    d: 3,\n    e: 4,\n    f: 5,\n    g: 6,\n    h: 7,\n    i: 8,\n    j: 9,\n    k: 10,\n    l: 11,\n    m: 12,\n    n: 13,\n    o: 14,\n    p: 15,\n    q: 16,\n    r: 17,\n    s: 18,\n    t: 19,\n    u: 20,\n    v: 21,\n    w: 22,\n    x: 23,\n    y: 24,\n    z: 25,\n    \"2\": 26,\n    \"3\": 27,\n    \"4\": 28,\n    \"5\": 29,\n    \"6\": 30,\n    \"7\": 31\n};\nvar EncodingPadding;\n(function (EncodingPadding) {\n    EncodingPadding[EncodingPadding[\"Include\"] = 0] = \"Include\";\n    EncodingPadding[EncodingPadding[\"None\"] = 1] = \"None\";\n})(EncodingPadding || (EncodingPadding = {}));\nvar DecodingPadding;\n(function (DecodingPadding) {\n    DecodingPadding[DecodingPadding[\"Required\"] = 0] = \"Required\";\n    DecodingPadding[DecodingPadding[\"Ignore\"] = 1] = \"Ignore\";\n})(DecodingPadding || (DecodingPadding = {}));\n", "export function encodeBase64(bytes) {\n    return encodeBase64_internal(bytes, base64<PERSON><PERSON><PERSON>bet, EncodingPadding.Include);\n}\nexport function encodeBase64NoPadding(bytes) {\n    return encodeBase64_internal(bytes, base64<PERSON>lphabet, EncodingPadding.None);\n}\nexport function encodeBase64url(bytes) {\n    return encodeBase64_internal(bytes, base64urlAlphabet, EncodingPadding.Include);\n}\nexport function encodeBase64urlNoPadding(bytes) {\n    return encodeBase64_internal(bytes, base64urlAlphabet, EncodingPadding.None);\n}\nfunction encodeBase64_internal(bytes, alphabet, padding) {\n    let result = \"\";\n    for (let i = 0; i < bytes.byteLength; i += 3) {\n        let buffer = 0;\n        let bufferBitSize = 0;\n        for (let j = 0; j < 3 && i + j < bytes.byteLength; j++) {\n            buffer = (buffer << 8) | bytes[i + j];\n            bufferBitSize += 8;\n        }\n        for (let j = 0; j < 4; j++) {\n            if (bufferBitSize >= 6) {\n                result += alphabet[(buffer >> (bufferBitSize - 6)) & 0x3f];\n                bufferBitSize -= 6;\n            }\n            else if (bufferBitSize > 0) {\n                result += alphabet[(buffer << (6 - bufferBitSize)) & 0x3f];\n                bufferBitSize = 0;\n            }\n            else if (padding === EncodingPadding.Include) {\n                result += \"=\";\n            }\n        }\n    }\n    return result;\n}\nconst base64Alphabet = \"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/\";\nconst base64urlAlphabet = \"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_\";\nexport function decodeBase64(encoded) {\n    return decodeBase64_internal(encoded, base64DecodeMap, DecodingPadding.Required);\n}\nexport function decodeBase64IgnorePadding(encoded) {\n    return decodeBase64_internal(encoded, base64DecodeMap, DecodingPadding.Ignore);\n}\nexport function decodeBase64url(encoded) {\n    return decodeBase64_internal(encoded, base64urlDecodeMap, DecodingPadding.Required);\n}\nexport function decodeBase64urlIgnorePadding(encoded) {\n    return decodeBase64_internal(encoded, base64urlDecodeMap, DecodingPadding.Ignore);\n}\nfunction decodeBase64_internal(encoded, decodeMap, padding) {\n    const result = new Uint8Array(Math.ceil(encoded.length / 4) * 3);\n    let totalBytes = 0;\n    for (let i = 0; i < encoded.length; i += 4) {\n        let chunk = 0;\n        let bitsRead = 0;\n        for (let j = 0; j < 4; j++) {\n            if (padding === DecodingPadding.Required && encoded[i + j] === \"=\") {\n                continue;\n            }\n            if (padding === DecodingPadding.Ignore &&\n                (i + j >= encoded.length || encoded[i + j] === \"=\")) {\n                continue;\n            }\n            if (j > 0 && encoded[i + j - 1] === \"=\") {\n                throw new Error(\"Invalid padding\");\n            }\n            if (!(encoded[i + j] in decodeMap)) {\n                throw new Error(\"Invalid character\");\n            }\n            chunk |= decodeMap[encoded[i + j]] << ((3 - j) * 6);\n            bitsRead += 6;\n        }\n        if (bitsRead < 24) {\n            let unused;\n            if (bitsRead === 12) {\n                unused = chunk & 0xffff;\n            }\n            else if (bitsRead === 18) {\n                unused = chunk & 0xff;\n            }\n            else {\n                throw new Error(\"Invalid padding\");\n            }\n            if (unused !== 0) {\n                throw new Error(\"Invalid padding\");\n            }\n        }\n        const byteLength = Math.floor(bitsRead / 8);\n        for (let i = 0; i < byteLength; i++) {\n            result[totalBytes] = (chunk >> (16 - i * 8)) & 0xff;\n            totalBytes++;\n        }\n    }\n    return result.slice(0, totalBytes);\n}\nvar EncodingPadding;\n(function (EncodingPadding) {\n    EncodingPadding[EncodingPadding[\"Include\"] = 0] = \"Include\";\n    EncodingPadding[EncodingPadding[\"None\"] = 1] = \"None\";\n})(EncodingPadding || (EncodingPadding = {}));\nvar DecodingPadding;\n(function (DecodingPadding) {\n    DecodingPadding[DecodingPadding[\"Required\"] = 0] = \"Required\";\n    DecodingPadding[DecodingPadding[\"Ignore\"] = 1] = \"Ignore\";\n})(DecodingPadding || (DecodingPadding = {}));\nconst base64DecodeMap = {\n    \"0\": 52,\n    \"1\": 53,\n    \"2\": 54,\n    \"3\": 55,\n    \"4\": 56,\n    \"5\": 57,\n    \"6\": 58,\n    \"7\": 59,\n    \"8\": 60,\n    \"9\": 61,\n    A: 0,\n    B: 1,\n    C: 2,\n    D: 3,\n    E: 4,\n    F: 5,\n    G: 6,\n    H: 7,\n    I: 8,\n    J: 9,\n    K: 10,\n    L: 11,\n    M: 12,\n    N: 13,\n    O: 14,\n    P: 15,\n    Q: 16,\n    R: 17,\n    S: 18,\n    T: 19,\n    U: 20,\n    V: 21,\n    W: 22,\n    X: 23,\n    Y: 24,\n    Z: 25,\n    a: 26,\n    b: 27,\n    c: 28,\n    d: 29,\n    e: 30,\n    f: 31,\n    g: 32,\n    h: 33,\n    i: 34,\n    j: 35,\n    k: 36,\n    l: 37,\n    m: 38,\n    n: 39,\n    o: 40,\n    p: 41,\n    q: 42,\n    r: 43,\n    s: 44,\n    t: 45,\n    u: 46,\n    v: 47,\n    w: 48,\n    x: 49,\n    y: 50,\n    z: 51,\n    \"+\": 62,\n    \"/\": 63\n};\nconst base64urlDecodeMap = {\n    \"0\": 52,\n    \"1\": 53,\n    \"2\": 54,\n    \"3\": 55,\n    \"4\": 56,\n    \"5\": 57,\n    \"6\": 58,\n    \"7\": 59,\n    \"8\": 60,\n    \"9\": 61,\n    A: 0,\n    B: 1,\n    C: 2,\n    D: 3,\n    E: 4,\n    F: 5,\n    G: 6,\n    H: 7,\n    I: 8,\n    J: 9,\n    K: 10,\n    L: 11,\n    M: 12,\n    N: 13,\n    O: 14,\n    P: 15,\n    Q: 16,\n    R: 17,\n    S: 18,\n    T: 19,\n    U: 20,\n    V: 21,\n    W: 22,\n    X: 23,\n    Y: 24,\n    Z: 25,\n    a: 26,\n    b: 27,\n    c: 28,\n    d: 29,\n    e: 30,\n    f: 31,\n    g: 32,\n    h: 33,\n    i: 34,\n    j: 35,\n    k: 36,\n    l: 37,\n    m: 38,\n    n: 39,\n    o: 40,\n    p: 41,\n    q: 42,\n    r: 43,\n    s: 44,\n    t: 45,\n    u: 46,\n    v: 47,\n    w: 48,\n    x: 49,\n    y: 50,\n    z: 51,\n    \"-\": 62,\n    _: 63\n};\n", "import { bigEndian } from \"@oslojs/binary\";\nimport { hmac } from \"@oslojs/crypto/hmac\";\nimport { SHA1 } from \"@oslojs/crypto/sha1\";\nimport { constantTimeEqual } from \"@oslojs/crypto/subtle\";\nimport { encodeBase32NoPadding } from \"@oslojs/encoding\";\nexport function generateHOTP(key, counter, digits) {\n    if (digits < 6 || digits > 8) {\n        throw new TypeError(\"Digits must be between 6 and 8\");\n    }\n    const counterBytes = new Uint8Array(8);\n    bigEndian.putUint64(counterBytes, counter, 0);\n    const HS = hmac(SHA1, key, counterBytes);\n    const offset = HS[HS.byteLength - 1] & 0x0f;\n    const truncated = HS.slice(offset, offset + 4);\n    truncated[0] &= 0x7f;\n    const SNum = bigEndian.uint32(truncated, 0);\n    const D = SNum % 10 ** digits;\n    return D.toString().padStart(digits, \"0\");\n}\nexport function verifyHOTP(key, counter, digits, otp) {\n    if (digits < 6 || digits > 8) {\n        throw new TypeError(\"Digits must be between 6 and 8\");\n    }\n    if (otp.length !== digits) {\n        return false;\n    }\n    const bytes = new TextEncoder().encode(otp);\n    const expected = generateHOTP(key, counter, digits);\n    const expectedBytes = new TextEncoder().encode(expected);\n    const valid = constantTimeEqual(bytes, expectedBytes);\n    return valid;\n}\nexport function createHOTPKeyURI(issuer, accountName, key, counter, digits) {\n    const encodedIssuer = encodeURIComponent(issuer);\n    const encodedAccountName = encodeURIComponent(accountName);\n    const base = `otpauth://hotp/${encodedIssuer}:${encodedAccountName}`;\n    const params = new URLSearchParams();\n    params.set(\"issuer\", issuer);\n    params.set(\"algorithm\", \"SHA1\");\n    params.set(\"secret\", encodeBase32NoPadding(key));\n    params.set(\"counter\", counter.toString());\n    params.set(\"digits\", digits.toString());\n    return base + \"?\" + params.toString();\n}\n", "import { encodeBase32NoPadding } from \"@oslojs/encoding\";\nimport { generateHOTP, verifyHOTP } from \"./hotp.js\";\nexport function generateTOTP(key, intervalInSeconds, digits) {\n    if (digits < 6 || digits > 8) {\n        throw new TypeError(\"Digits must be between 6 and 8\");\n    }\n    const counter = BigInt(Math.floor(Date.now() / (intervalInSeconds * 1000)));\n    const hotp = generateHOTP(key, counter, digits);\n    return hotp;\n}\nexport function verifyTOTP(key, intervalInSeconds, digits, otp) {\n    const counter = BigInt(Math.floor(Date.now() / (intervalInSeconds * 1000)));\n    const valid = verifyHOTP(key, counter, digits, otp);\n    return valid;\n}\nexport function verifyTOTPWithGracePeriod(key, intervalInSeconds, digits, otp, gracePeriodInSeconds) {\n    if (gracePeriodInSeconds < 0) {\n        throw new TypeError(\"Grace period must be a positive number\");\n    }\n    const nowUnixMilliseconds = Date.now();\n    let counter = BigInt(Math.floor((nowUnixMilliseconds - gracePeriodInSeconds * 1000) / (intervalInSeconds * 1000)));\n    const maxCounterInclusive = BigInt(Math.floor((nowUnixMilliseconds + gracePeriodInSeconds * 1000) / (intervalInSeconds * 1000)));\n    while (counter <= maxCounterInclusive) {\n        const valid = verifyHOTP(key, counter, digits, otp);\n        if (valid) {\n            return true;\n        }\n        counter++;\n    }\n    return false;\n}\nexport function createTOTPKeyURI(issuer, accountName, key, periodInSeconds, digits) {\n    const encodedIssuer = encodeURIComponent(issuer);\n    const encodedAccountName = encodeURIComponent(accountName);\n    const base = `otpauth://totp/${encodedIssuer}:${encodedAccountName}`;\n    const params = new URLSearchParams();\n    params.set(\"issuer\", issuer);\n    params.set(\"algorithm\", \"SHA1\");\n    params.set(\"secret\", encodeBase32NoPadding(key));\n    params.set(\"period\", periodInSeconds.toString());\n    params.set(\"digits\", digits.toString());\n    return base + \"?\" + params.toString();\n}\n"], "mappings": ";;;;;AAAA,IAAM,YAAN,MAAgB;AAAA,EACZ,MAAM,MAAM,QAAQ;AAChB,QAAI,KAAK,aAAa,SAAS,GAAG;AAC9B,YAAM,IAAI,UAAU,oBAAoB;AAAA,IAC5C;AACA,WAAO,KAAK,MAAM;AAAA,EACtB;AAAA,EACA,OAAO,MAAM,QAAQ;AACjB,QAAI,KAAK,aAAa,SAAS,GAAG;AAC9B,YAAM,IAAI,UAAU,oBAAoB;AAAA,IAC5C;AACA,WAAQ,KAAK,MAAM,KAAK,IAAK,KAAK,SAAS,CAAC;AAAA,EAChD;AAAA,EACA,OAAO,MAAM,QAAQ;AACjB,QAAI,KAAK,aAAa,SAAS,GAAG;AAC9B,YAAM,IAAI,UAAU,oBAAoB;AAAA,IAC5C;AACA,QAAI,SAAS;AACb,aAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AACxB,gBAAU,KAAK,SAAS,CAAC,KAAM,KAAK,IAAI;AAAA,IAC5C;AACA,WAAO;AAAA,EACX;AAAA,EACA,OAAO,MAAM,QAAQ;AACjB,QAAI,KAAK,aAAa,SAAS,GAAG;AAC9B,YAAM,IAAI,UAAU,oBAAoB;AAAA,IAC5C;AACA,QAAI,SAAS;AACb,aAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AACxB,gBAAU,OAAO,KAAK,SAAS,CAAC,CAAC,KAAK,OAAO,KAAK,IAAI,CAAC;AAAA,IAC3D;AACA,WAAO;AAAA,EACX;AAAA,EACA,SAAS,QAAQ,OAAO,QAAQ;AAC5B,QAAI,OAAO,SAAS,SAAS,GAAG;AAC5B,YAAM,IAAI,UAAU,kBAAkB;AAAA,IAC1C;AACA,QAAI,QAAQ,KAAK,QAAQ,KAAK;AAC1B,YAAM,IAAI,UAAU,qBAAqB;AAAA,IAC7C;AACA,WAAO,MAAM,IAAI;AAAA,EACrB;AAAA,EACA,UAAU,QAAQ,OAAO,QAAQ;AAC7B,QAAI,OAAO,SAAS,SAAS,GAAG;AAC5B,YAAM,IAAI,UAAU,kBAAkB;AAAA,IAC1C;AACA,QAAI,QAAQ,KAAK,QAAQ,OAAO;AAC5B,YAAM,IAAI,UAAU,sBAAsB;AAAA,IAC9C;AACA,WAAO,MAAM,IAAI,SAAS;AAC1B,WAAO,SAAS,CAAC,IAAI,QAAQ;AAAA,EACjC;AAAA,EACA,UAAU,QAAQ,OAAO,QAAQ;AAC7B,QAAI,OAAO,SAAS,SAAS,GAAG;AAC5B,YAAM,IAAI,UAAU,kBAAkB;AAAA,IAC1C;AACA,QAAI,QAAQ,KAAK,QAAQ,YAAY;AACjC,YAAM,IAAI,UAAU,sBAAsB;AAAA,IAC9C;AACA,aAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AACxB,aAAO,SAAS,CAAC,IAAK,UAAW,IAAI,KAAK,IAAM;AAAA,IACpD;AAAA,EACJ;AAAA,EACA,UAAU,QAAQ,OAAO,QAAQ;AAC7B,QAAI,OAAO,SAAS,SAAS,GAAG;AAC5B,YAAM,IAAI,UAAU,kBAAkB;AAAA,IAC1C;AACA,QAAI,QAAQ,KAAK,QAAQ,uBAAuB;AAC5C,YAAM,IAAI,UAAU,sBAAsB;AAAA,IAC9C;AACA,aAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AACxB,aAAO,SAAS,CAAC,IAAI,OAAQ,SAAS,QAAQ,IAAI,KAAK,CAAC,IAAK,KAAK;AAAA,IACtE;AAAA,EACJ;AACJ;AACA,IAAM,eAAN,MAAmB;AAAA,EACf,MAAM,MAAM,QAAQ;AAChB,QAAI,KAAK,aAAa,SAAS,GAAG;AAC9B,YAAM,IAAI,UAAU,oBAAoB;AAAA,IAC5C;AACA,WAAO,KAAK,MAAM;AAAA,EACtB;AAAA,EACA,OAAO,MAAM,QAAQ;AACjB,QAAI,KAAK,aAAa,SAAS,GAAG;AAC9B,YAAM,IAAI,UAAU,oBAAoB;AAAA,IAC5C;AACA,WAAO,KAAK,MAAM,IAAK,KAAK,SAAS,CAAC,KAAK;AAAA,EAC/C;AAAA,EACA,OAAO,MAAM,QAAQ;AACjB,QAAI,KAAK,aAAa,SAAS,GAAG;AAC9B,YAAM,IAAI,UAAU,oBAAoB;AAAA,IAC5C;AACA,QAAI,SAAS;AACb,aAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AACxB,gBAAU,KAAK,SAAS,CAAC,KAAM,IAAI;AAAA,IACvC;AACA,WAAO;AAAA,EACX;AAAA,EACA,OAAO,MAAM,QAAQ;AACjB,QAAI,KAAK,aAAa,SAAS,GAAG;AAC9B,YAAM,IAAI,UAAU,oBAAoB;AAAA,IAC5C;AACA,QAAI,SAAS;AACb,aAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AACxB,gBAAU,OAAO,KAAK,SAAS,CAAC,CAAC,KAAK,OAAO,IAAI,CAAC;AAAA,IACtD;AACA,WAAO;AAAA,EACX;AAAA,EACA,SAAS,QAAQ,OAAO,QAAQ;AAC5B,QAAI,OAAO,SAAS,IAAI,QAAQ;AAC5B,YAAM,IAAI,UAAU,oBAAoB;AAAA,IAC5C;AACA,QAAI,QAAQ,KAAK,QAAQ,KAAK;AAC1B,YAAM,IAAI,UAAU,qBAAqB;AAAA,IAC7C;AACA,WAAO,MAAM,IAAI;AAAA,EACrB;AAAA,EACA,UAAU,QAAQ,OAAO,QAAQ;AAC7B,QAAI,OAAO,SAAS,IAAI,QAAQ;AAC5B,YAAM,IAAI,UAAU,oBAAoB;AAAA,IAC5C;AACA,QAAI,QAAQ,KAAK,QAAQ,OAAO;AAC5B,YAAM,IAAI,UAAU,sBAAsB;AAAA,IAC9C;AACA,WAAO,SAAS,CAAC,IAAI,SAAS;AAC9B,WAAO,MAAM,IAAI,QAAQ;AAAA,EAC7B;AAAA,EACA,UAAU,QAAQ,OAAO,QAAQ;AAC7B,QAAI,OAAO,SAAS,IAAI,QAAQ;AAC5B,YAAM,IAAI,UAAU,oBAAoB;AAAA,IAC5C;AACA,QAAI,QAAQ,KAAK,QAAQ,YAAY;AACjC,YAAM,IAAI,UAAU,sBAAsB;AAAA,IAC9C;AACA,aAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AACxB,aAAO,SAAS,CAAC,IAAK,SAAU,IAAI,IAAM;AAAA,IAC9C;AAAA,EACJ;AAAA,EACA,UAAU,QAAQ,OAAO,QAAQ;AAC7B,QAAI,OAAO,SAAS,IAAI,QAAQ;AAC5B,YAAM,IAAI,UAAU,oBAAoB;AAAA,IAC5C;AACA,QAAI,QAAQ,KAAK,QAAQ,uBAAuB;AAC5C,YAAM,IAAI,UAAU,sBAAsB;AAAA,IAC9C;AACA,aAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AACxB,aAAO,SAAS,CAAC,IAAI,OAAQ,SAAS,OAAO,IAAI,CAAC,IAAK,KAAK;AAAA,IAChE;AAAA,EACJ;AACJ;AACO,IAAM,YAAY,IAAI,UAAU;AAChC,IAAM,eAAe,IAAI,aAAa;;;ACvJtC,SAAS,OAAO,GAAG,GAAG;AACzB,UAAS,KAAK,IAAM,MAAO,KAAK,OAAS;AAC7C;;;ACFO,IAAM,OAAN,MAAW;AAAA,EAId,YAAY,WAAW,KAAK;AAH5B;AACA;AACA;AAEI,UAAM,UAAU,IAAI,UAAU;AAC9B,QAAI,IAAI,eAAe,QAAQ,WAAW;AACtC,WAAK,KAAK;AAAA,IACd,WACS,IAAI,aAAa,QAAQ,WAAW;AACzC,WAAK,KAAK,IAAI,WAAW,QAAQ,SAAS;AAC1C,cAAQ,OAAO,GAAG;AAClB,WAAK,GAAG,IAAI,QAAQ,OAAO,CAAC;AAAA,IAChC,OACK;AACD,WAAK,KAAK,IAAI,WAAW,QAAQ,SAAS;AAC1C,WAAK,GAAG,IAAI,GAAG;AAAA,IACnB;AACA,SAAK,QAAQ,IAAI,UAAU;AAC3B,UAAM,OAAO,IAAI,WAAW,KAAK,GAAG,UAAU,EAAE,KAAK,EAAI;AACzD,aAAS,IAAI,GAAG,IAAI,KAAK,YAAY,KAAK;AACtC,WAAK,CAAC,KAAK,KAAK,GAAG,CAAC;AAAA,IACxB;AACA,SAAK,MAAM,OAAO,IAAI;AACtB,SAAK,QAAQ,IAAI,UAAU;AAC3B,UAAM,OAAO,IAAI,WAAW,KAAK,GAAG,UAAU,EAAE,KAAK,EAAI;AACzD,aAAS,IAAI,GAAG,IAAI,KAAK,YAAY,KAAK;AACtC,WAAK,CAAC,KAAK,KAAK,GAAG,CAAC;AAAA,IACxB;AACA,SAAK,MAAM,OAAO,IAAI;AAAA,EAC1B;AAAA,EACA,OAAO,SAAS;AACZ,SAAK,MAAM,OAAO,OAAO;AAAA,EAC7B;AAAA,EACA,SAAS;AACL,SAAK,MAAM,OAAO,KAAK,MAAM,OAAO,CAAC;AACrC,WAAO,KAAK,MAAM,OAAO;AAAA,EAC7B;AACJ;AACO,SAAS,KAAK,WAAW,KAAK,SAAS;AAC1C,QAAM,MAAM,IAAI,KAAK,WAAW,GAAG;AACnC,MAAI,OAAO,OAAO;AAClB,SAAO,IAAI,OAAO;AACtB;;;ACnCO,IAAM,OAAN,MAAW;AAAA,EAAX;AACH,qCAAY;AACZ,gCAAO;AACP,kCAAS,IAAI,WAAW,EAAE;AAC1B,4CAAmB;AACnB,6BAAI,IAAI,YAAY,CAAC,YAAY,YAAY,YAAY,WAAY,UAAU,CAAC;AAChF,6BAAI;AACJ,6BAAI,IAAI,YAAY,EAAE;AAAA;AAAA,EACtB,OAAO,MAAM;AACT,SAAK,KAAK,KAAK,aAAa;AAC5B,QAAI,KAAK,mBAAmB,KAAK,aAAa,IAAI;AAC9C,WAAK,OAAO,IAAI,MAAM,KAAK,gBAAgB;AAC3C,WAAK,oBAAoB,KAAK;AAC9B;AAAA,IACJ;AACA,QAAI,YAAY;AAChB,QAAI,KAAK,mBAAmB,GAAG;AAC3B,YAAM,OAAO,KAAK,MAAM,GAAG,KAAK,KAAK,gBAAgB;AACrD,WAAK,OAAO,IAAI,MAAM,KAAK,gBAAgB;AAC3C,WAAK,QAAQ;AACb,mBAAa,KAAK;AAClB,WAAK,mBAAmB;AAAA,IAC5B;AACA,WAAO,YAAY,MAAM,KAAK,YAAY;AACtC,YAAM,OAAO,KAAK,MAAM,WAAW,YAAY,EAAE;AACjD,WAAK,OAAO,IAAI,IAAI;AACpB,WAAK,QAAQ;AACb,mBAAa;AAAA,IACjB;AACA,QAAI,KAAK,aAAa,YAAY,GAAG;AACjC,YAAM,YAAY,KAAK,MAAM,SAAS;AACtC,WAAK,OAAO,IAAI,SAAS;AACzB,WAAK,mBAAmB,UAAU;AAAA,IACtC;AAAA,EACJ;AAAA,EACA,SAAS;AACL,SAAK,OAAO,KAAK,gBAAgB,IAAI;AACrC,SAAK,oBAAoB;AACzB,QAAI,KAAK,KAAK,mBAAmB,GAAG;AAChC,WAAK,OAAO,KAAK,GAAG,KAAK,gBAAgB;AACzC,WAAK,QAAQ;AACb,WAAK,mBAAmB;AAAA,IAC5B;AACA,SAAK,OAAO,KAAK,GAAG,KAAK,gBAAgB;AACzC,cAAU,UAAU,KAAK,QAAQ,OAAO,KAAK,CAAC,GAAG,KAAK,YAAY,CAAC;AACnE,SAAK,QAAQ;AACb,UAAM,SAAS,IAAI,WAAW,EAAE;AAChC,aAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AACxB,gBAAU,UAAU,QAAQ,KAAK,EAAE,CAAC,GAAG,IAAI,CAAC;AAAA,IAChD;AACA,WAAO;AAAA,EACX;AAAA,EACA,UAAU;AACN,aAAS,IAAI,GAAG,IAAI,IAAI,KAAK;AACzB,WAAK,EAAE,CAAC,KACF,KAAK,OAAO,IAAI,CAAC,KAAK,KACnB,KAAK,OAAO,IAAI,IAAI,CAAC,KAAK,KAC1B,KAAK,OAAO,IAAI,IAAI,CAAC,KAAK,IAC3B,KAAK,OAAO,IAAI,IAAI,CAAC,OACrB;AAAA,IACZ;AACA,aAAS,IAAI,IAAI,IAAI,IAAI,KAAK;AAC1B,WAAK,EAAE,CAAC,IAAI,QAAQ,KAAK,EAAE,IAAI,CAAC,IAAI,KAAK,EAAE,IAAI,CAAC,IAAI,KAAK,EAAE,IAAI,EAAE,IAAI,KAAK,EAAE,IAAI,EAAE,OAAO,GAAG,CAAC;AAAA,IACjG;AACA,QAAI,IAAI,KAAK,EAAE,CAAC;AAChB,QAAI,IAAI,KAAK,EAAE,CAAC;AAChB,QAAI,IAAI,KAAK,EAAE,CAAC;AAChB,QAAI,IAAI,KAAK,EAAE,CAAC;AAChB,QAAI,IAAI,KAAK,EAAE,CAAC;AAChB,aAAS,IAAI,GAAG,IAAI,IAAI,KAAK;AACzB,UAAI,GAAG;AACP,UAAI,IAAI,IAAI;AACR,aAAM,IAAI,IAAM,CAAC,IAAI,OAAQ;AAC7B,YAAI;AAAA,MACR,WACS,IAAI,IAAI;AACb,aAAK,IAAI,IAAI,OAAO;AACpB,YAAI;AAAA,MACR,WACS,IAAI,IAAI;AACb,aAAM,IAAI,IAAM,IAAI,IAAM,IAAI,OAAQ;AACtC,YAAI;AAAA,MACR,OACK;AACD,aAAK,IAAI,IAAI,OAAO;AACpB,YAAI;AAAA,MACR;AACA,YAAM,IAAK,OAAO,GAAG,CAAC,IAAI,IAAI,IAAI,KAAK,EAAE,CAAC,IAAI,IAAK;AACnD,UAAI;AACJ,UAAI;AACJ,UAAI,OAAO,GAAG,EAAE;AAChB,UAAI;AACJ,UAAI;AAAA,IACR;AACA,SAAK,EAAE,CAAC,IAAK,KAAK,EAAE,CAAC,IAAI,IAAK;AAC9B,SAAK,EAAE,CAAC,IAAK,KAAK,EAAE,CAAC,IAAI,IAAK;AAC9B,SAAK,EAAE,CAAC,IAAK,KAAK,EAAE,CAAC,IAAI,IAAK;AAC9B,SAAK,EAAE,CAAC,IAAK,KAAK,EAAE,CAAC,IAAI,IAAK;AAC9B,SAAK,EAAE,CAAC,IAAK,KAAK,EAAE,CAAC,IAAI,IAAK;AAAA,EAClC;AACJ;;;AC5GO,SAAS,kBAAkB,GAAG,GAAG;AACpC,MAAI,EAAE,WAAW,EAAE,QAAQ;AACvB,WAAO;AAAA,EACX;AACA,MAAI,IAAI;AACR,WAAS,IAAI,GAAG,IAAI,EAAE,QAAQ,KAAK;AAC/B,SAAK,EAAE,CAAC,IAAI,EAAE,CAAC;AAAA,EACnB;AACA,SAAO,MAAM;AACjB;;;ACNO,SAAS,sBAAsB,OAAO;AACzC,SAAO,sBAAsB,OAAO,gBAAgB,gBAAgB,IAAI;AAC5E;AACA,SAAS,sBAAsB,OAAO,UAAU,SAAS;AACrD,MAAI,SAAS;AACb,WAAS,IAAI,GAAG,IAAI,MAAM,YAAY,KAAK,GAAG;AAC1C,QAAI,SAAS;AACb,QAAI,gBAAgB;AACpB,aAAS,IAAI,GAAG,IAAI,KAAK,IAAI,IAAI,MAAM,YAAY,KAAK;AACpD,eAAU,UAAU,KAAM,OAAO,MAAM,IAAI,CAAC,CAAC;AAC7C,uBAAiB;AAAA,IACrB;AACA,QAAI,gBAAgB,MAAM,GAAG;AACzB,eAAS,UAAU,OAAO,IAAK,gBAAgB,CAAE;AACjD,uBAAiB,IAAK,gBAAgB;AAAA,IAC1C;AACA,aAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AACxB,UAAI,iBAAiB,GAAG;AACpB,kBAAU,SAAS,OAAQ,UAAU,OAAO,gBAAgB,CAAC,IAAK,KAAK,CAAC;AACxE,yBAAiB;AAAA,MACrB,WACS,gBAAgB,GAAG;AACxB,kBAAU,SAAS,OAAQ,UAAU,OAAO,IAAI,aAAa,IAAK,KAAK,CAAC;AACxE,wBAAgB;AAAA,MACpB,WACS,YAAY,gBAAgB,SAAS;AAC1C,kBAAU;AAAA,MACd;AAAA,IACJ;AAAA,EACJ;AACA,SAAO;AACX;AAiEA,IAAM,iBAAiB;AA6DvB,IAAI;AAAA,CACH,SAAUA,kBAAiB;AACxB,EAAAA,iBAAgBA,iBAAgB,SAAS,IAAI,CAAC,IAAI;AAClD,EAAAA,iBAAgBA,iBAAgB,MAAM,IAAI,CAAC,IAAI;AACnD,GAAG,oBAAoB,kBAAkB,CAAC,EAAE;AAC5C,IAAI;AAAA,CACH,SAAUC,kBAAiB;AACxB,EAAAA,iBAAgBA,iBAAgB,UAAU,IAAI,CAAC,IAAI;AACnD,EAAAA,iBAAgBA,iBAAgB,QAAQ,IAAI,CAAC,IAAI;AACrD,GAAG,oBAAoB,kBAAkB,CAAC,EAAE;;;ACxE5C,IAAIC;AAAA,CACH,SAAUA,kBAAiB;AACxB,EAAAA,iBAAgBA,iBAAgB,SAAS,IAAI,CAAC,IAAI;AAClD,EAAAA,iBAAgBA,iBAAgB,MAAM,IAAI,CAAC,IAAI;AACnD,GAAGA,qBAAoBA,mBAAkB,CAAC,EAAE;AAC5C,IAAIC;AAAA,CACH,SAAUA,kBAAiB;AACxB,EAAAA,iBAAgBA,iBAAgB,UAAU,IAAI,CAAC,IAAI;AACnD,EAAAA,iBAAgBA,iBAAgB,QAAQ,IAAI,CAAC,IAAI;AACrD,GAAGA,qBAAoBA,mBAAkB,CAAC,EAAE;;;ACrGrC,SAAS,aAAa,KAAK,SAAS,QAAQ;AAC/C,MAAI,SAAS,KAAK,SAAS,GAAG;AAC1B,UAAM,IAAI,UAAU,gCAAgC;AAAA,EACxD;AACA,QAAM,eAAe,IAAI,WAAW,CAAC;AACrC,YAAU,UAAU,cAAc,SAAS,CAAC;AAC5C,QAAM,KAAK,KAAK,MAAM,KAAK,YAAY;AACvC,QAAM,SAAS,GAAG,GAAG,aAAa,CAAC,IAAI;AACvC,QAAM,YAAY,GAAG,MAAM,QAAQ,SAAS,CAAC;AAC7C,YAAU,CAAC,KAAK;AAChB,QAAM,OAAO,UAAU,OAAO,WAAW,CAAC;AAC1C,QAAM,IAAI,OAAO,MAAM;AACvB,SAAO,EAAE,SAAS,EAAE,SAAS,QAAQ,GAAG;AAC5C;AACO,SAAS,WAAW,KAAK,SAAS,QAAQ,KAAK;AAClD,MAAI,SAAS,KAAK,SAAS,GAAG;AAC1B,UAAM,IAAI,UAAU,gCAAgC;AAAA,EACxD;AACA,MAAI,IAAI,WAAW,QAAQ;AACvB,WAAO;AAAA,EACX;AACA,QAAM,QAAQ,IAAI,YAAY,EAAE,OAAO,GAAG;AAC1C,QAAM,WAAW,aAAa,KAAK,SAAS,MAAM;AAClD,QAAM,gBAAgB,IAAI,YAAY,EAAE,OAAO,QAAQ;AACvD,QAAM,QAAQ,kBAAkB,OAAO,aAAa;AACpD,SAAO;AACX;AACO,SAAS,iBAAiB,QAAQ,aAAa,KAAK,SAAS,QAAQ;AACxE,QAAM,gBAAgB,mBAAmB,MAAM;AAC/C,QAAM,qBAAqB,mBAAmB,WAAW;AACzD,QAAM,OAAO,kBAAkB,aAAa,IAAI,kBAAkB;AAClE,QAAM,SAAS,IAAI,gBAAgB;AACnC,SAAO,IAAI,UAAU,MAAM;AAC3B,SAAO,IAAI,aAAa,MAAM;AAC9B,SAAO,IAAI,UAAU,sBAAsB,GAAG,CAAC;AAC/C,SAAO,IAAI,WAAW,QAAQ,SAAS,CAAC;AACxC,SAAO,IAAI,UAAU,OAAO,SAAS,CAAC;AACtC,SAAO,OAAO,MAAM,OAAO,SAAS;AACxC;;;ACzCO,SAAS,aAAa,KAAK,mBAAmB,QAAQ;AACzD,MAAI,SAAS,KAAK,SAAS,GAAG;AAC1B,UAAM,IAAI,UAAU,gCAAgC;AAAA,EACxD;AACA,QAAM,UAAU,OAAO,KAAK,MAAM,KAAK,IAAI,KAAK,oBAAoB,IAAK,CAAC;AAC1E,QAAM,OAAO,aAAa,KAAK,SAAS,MAAM;AAC9C,SAAO;AACX;AACO,SAAS,WAAW,KAAK,mBAAmB,QAAQ,KAAK;AAC5D,QAAM,UAAU,OAAO,KAAK,MAAM,KAAK,IAAI,KAAK,oBAAoB,IAAK,CAAC;AAC1E,QAAM,QAAQ,WAAW,KAAK,SAAS,QAAQ,GAAG;AAClD,SAAO;AACX;AACO,SAAS,0BAA0B,KAAK,mBAAmB,QAAQ,KAAK,sBAAsB;AACjG,MAAI,uBAAuB,GAAG;AAC1B,UAAM,IAAI,UAAU,wCAAwC;AAAA,EAChE;AACA,QAAM,sBAAsB,KAAK,IAAI;AACrC,MAAI,UAAU,OAAO,KAAK,OAAO,sBAAsB,uBAAuB,QAAS,oBAAoB,IAAK,CAAC;AACjH,QAAM,sBAAsB,OAAO,KAAK,OAAO,sBAAsB,uBAAuB,QAAS,oBAAoB,IAAK,CAAC;AAC/H,SAAO,WAAW,qBAAqB;AACnC,UAAM,QAAQ,WAAW,KAAK,SAAS,QAAQ,GAAG;AAClD,QAAI,OAAO;AACP,aAAO;AAAA,IACX;AACA;AAAA,EACJ;AACA,SAAO;AACX;AACO,SAAS,iBAAiB,QAAQ,aAAa,KAAK,iBAAiB,QAAQ;AAChF,QAAM,gBAAgB,mBAAmB,MAAM;AAC/C,QAAM,qBAAqB,mBAAmB,WAAW;AACzD,QAAM,OAAO,kBAAkB,aAAa,IAAI,kBAAkB;AAClE,QAAM,SAAS,IAAI,gBAAgB;AACnC,SAAO,IAAI,UAAU,MAAM;AAC3B,SAAO,IAAI,aAAa,MAAM;AAC9B,SAAO,IAAI,UAAU,sBAAsB,GAAG,CAAC;AAC/C,SAAO,IAAI,UAAU,gBAAgB,SAAS,CAAC;AAC/C,SAAO,IAAI,UAAU,OAAO,SAAS,CAAC;AACtC,SAAO,OAAO,MAAM,OAAO,SAAS;AACxC;", "names": ["EncodingPadding", "DecodingPadding", "EncodingPadding", "DecodingPadding"]}