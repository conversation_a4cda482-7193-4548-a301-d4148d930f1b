{"version": 3, "sources": ["../../../package/@stackframe/react/node_modules/@stackframe/stack-shared/src/helpers/password.ts"], "sourcesContent": ["import { KnownErrors } from \"..\";\n\nconst minLength = 8;\nconst maxLength = 70;\n\nexport function getPasswordError(password: string): KnownErrors[\"PasswordRequirementsNotMet\"] | undefined {\n  if (password.length < minLength) {\n    return new KnownErrors.PasswordTooShort(minLength);\n  }\n\n  if (password.length > maxLength) {\n    return new KnownErrors.PasswordTooLong(maxLength);\n  }\n\n  return undefined;\n}\n"], "mappings": ";;;;;;;;;;;;;;AAEA,IAAM,YAAY;AAClB,IAAM,YAAY;AAEX,SAAS,iBAAiB,UAAyE;AACxG,MAAI,SAAS,SAAS,WAAW;AAC/B,WAAO,IAAI,YAAY,iBAAiB,SAAS;EACnD;AAEA,MAAI,SAAS,SAAS,WAAW;AAC/B,WAAO,IAAI,YAAY,gBAAgB,SAAS;EAClD;AAEA,SAAO;AACT;", "names": []}