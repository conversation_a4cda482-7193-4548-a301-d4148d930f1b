import {
  __publicField
} from "./chunk-7UVSMXVG.js";

// node_modules/@oslojs/binary/dist/uint.js
var BigEndian = class {
  uint8(data, offset) {
    if (data.byteLength < offset + 1) {
      throw new TypeError("Insufficient bytes");
    }
    return data[offset];
  }
  uint16(data, offset) {
    if (data.byteLength < offset + 2) {
      throw new TypeError("Insufficient bytes");
    }
    return data[offset] << 8 | data[offset + 1];
  }
  uint32(data, offset) {
    if (data.byteLength < offset + 4) {
      throw new TypeError("Insufficient bytes");
    }
    let result = 0;
    for (let i = 0; i < 4; i++) {
      result |= data[offset + i] << 24 - i * 8;
    }
    return result;
  }
  uint64(data, offset) {
    if (data.byteLength < offset + 8) {
      throw new TypeError("Insufficient bytes");
    }
    let result = 0n;
    for (let i = 0; i < 8; i++) {
      result |= BigInt(data[offset + i]) << BigInt(56 - i * 8);
    }
    return result;
  }
  putUint8(target, value, offset) {
    if (target.length < offset + 1) {
      throw new TypeError("Not enough space");
    }
    if (value < 0 || value > 255) {
      throw new TypeError("Invalid uint8 value");
    }
    target[offset] = value;
  }
  putUint16(target, value, offset) {
    if (target.length < offset + 2) {
      throw new TypeError("Not enough space");
    }
    if (value < 0 || value > 65535) {
      throw new TypeError("Invalid uint16 value");
    }
    target[offset] = value >> 8;
    target[offset + 1] = value & 255;
  }
  putUint32(target, value, offset) {
    if (target.length < offset + 4) {
      throw new TypeError("Not enough space");
    }
    if (value < 0 || value > 4294967295) {
      throw new TypeError("Invalid uint32 value");
    }
    for (let i = 0; i < 4; i++) {
      target[offset + i] = value >> (3 - i) * 8 & 255;
    }
  }
  putUint64(target, value, offset) {
    if (target.length < offset + 8) {
      throw new TypeError("Not enough space");
    }
    if (value < 0 || value > 18446744073709551615n) {
      throw new TypeError("Invalid uint64 value");
    }
    for (let i = 0; i < 8; i++) {
      target[offset + i] = Number(value >> BigInt((7 - i) * 8) & 0xffn);
    }
  }
};
var LittleEndian = class {
  uint8(data, offset) {
    if (data.byteLength < offset + 1) {
      throw new TypeError("Insufficient bytes");
    }
    return data[offset];
  }
  uint16(data, offset) {
    if (data.byteLength < offset + 2) {
      throw new TypeError("Insufficient bytes");
    }
    return data[offset] | data[offset + 1] << 8;
  }
  uint32(data, offset) {
    if (data.byteLength < offset + 4) {
      throw new TypeError("Insufficient bytes");
    }
    let result = 0;
    for (let i = 0; i < 4; i++) {
      result |= data[offset + i] << i * 8;
    }
    return result;
  }
  uint64(data, offset) {
    if (data.byteLength < offset + 8) {
      throw new TypeError("Insufficient bytes");
    }
    let result = 0n;
    for (let i = 0; i < 8; i++) {
      result |= BigInt(data[offset + i]) << BigInt(i * 8);
    }
    return result;
  }
  putUint8(target, value, offset) {
    if (target.length < 1 + offset) {
      throw new TypeError("Insufficient space");
    }
    if (value < 0 || value > 255) {
      throw new TypeError("Invalid uint8 value");
    }
    target[offset] = value;
  }
  putUint16(target, value, offset) {
    if (target.length < 2 + offset) {
      throw new TypeError("Insufficient space");
    }
    if (value < 0 || value > 65535) {
      throw new TypeError("Invalid uint16 value");
    }
    target[offset + 1] = value >> 8;
    target[offset] = value & 255;
  }
  putUint32(target, value, offset) {
    if (target.length < 4 + offset) {
      throw new TypeError("Insufficient space");
    }
    if (value < 0 || value > 4294967295) {
      throw new TypeError("Invalid uint32 value");
    }
    for (let i = 0; i < 4; i++) {
      target[offset + i] = value >> i * 8 & 255;
    }
  }
  putUint64(target, value, offset) {
    if (target.length < 8 + offset) {
      throw new TypeError("Insufficient space");
    }
    if (value < 0 || value > 18446744073709551615n) {
      throw new TypeError("Invalid uint64 value");
    }
    for (let i = 0; i < 8; i++) {
      target[offset + i] = Number(value >> BigInt(i * 8) & 0xffn);
    }
  }
};
var bigEndian = new BigEndian();
var littleEndian = new LittleEndian();

// node_modules/@oslojs/binary/dist/bits.js
function rotl32(x, n) {
  return (x << n | x >>> 32 - n) >>> 0;
}

// node_modules/@oslojs/crypto/dist/hmac/index.js
var HMAC = class {
  constructor(Algorithm, key) {
    __publicField(this, "k0");
    __publicField(this, "inner");
    __publicField(this, "outer");
    const keyHash = new Algorithm();
    if (key.byteLength === keyHash.blockSize) {
      this.k0 = key;
    } else if (key.byteLength > keyHash.blockSize) {
      this.k0 = new Uint8Array(keyHash.blockSize);
      keyHash.update(key);
      this.k0.set(keyHash.digest());
    } else {
      this.k0 = new Uint8Array(keyHash.blockSize);
      this.k0.set(key);
    }
    this.inner = new Algorithm();
    const ipad = new Uint8Array(this.k0.byteLength).fill(54);
    for (let i = 0; i < ipad.byteLength; i++) {
      ipad[i] ^= this.k0[i];
    }
    this.inner.update(ipad);
    this.outer = new Algorithm();
    const opad = new Uint8Array(this.k0.byteLength).fill(92);
    for (let i = 0; i < opad.byteLength; i++) {
      opad[i] ^= this.k0[i];
    }
    this.outer.update(opad);
  }
  update(message) {
    this.inner.update(message);
  }
  digest() {
    this.outer.update(this.inner.digest());
    return this.outer.digest();
  }
};
function hmac(Algorithm, key, message) {
  const mac = new HMAC(Algorithm, key);
  mac.update(message);
  return mac.digest();
}

// node_modules/@oslojs/crypto/dist/sha1/index.js
var SHA1 = class {
  constructor() {
    __publicField(this, "blockSize", 64);
    __publicField(this, "size", 20);
    __publicField(this, "blocks", new Uint8Array(64));
    __publicField(this, "currentBlockSize", 0);
    __publicField(this, "H", new Uint32Array([1732584193, 4023233417, 2562383102, 271733878, 3285377520]));
    __publicField(this, "l", 0);
    __publicField(this, "w", new Uint32Array(80));
  }
  update(data) {
    this.l += data.byteLength * 8;
    if (this.currentBlockSize + data.byteLength < 64) {
      this.blocks.set(data, this.currentBlockSize);
      this.currentBlockSize += data.byteLength;
      return;
    }
    let processed = 0;
    if (this.currentBlockSize > 0) {
      const next = data.slice(0, 64 - this.currentBlockSize);
      this.blocks.set(next, this.currentBlockSize);
      this.process();
      processed += next.byteLength;
      this.currentBlockSize = 0;
    }
    while (processed + 64 <= data.byteLength) {
      const next = data.slice(processed, processed + 64);
      this.blocks.set(next);
      this.process();
      processed += 64;
    }
    if (data.byteLength - processed > 0) {
      const remaining = data.slice(processed);
      this.blocks.set(remaining);
      this.currentBlockSize = remaining.byteLength;
    }
  }
  digest() {
    this.blocks[this.currentBlockSize] = 128;
    this.currentBlockSize += 1;
    if (64 - this.currentBlockSize < 8) {
      this.blocks.fill(0, this.currentBlockSize);
      this.process();
      this.currentBlockSize = 0;
    }
    this.blocks.fill(0, this.currentBlockSize);
    bigEndian.putUint64(this.blocks, BigInt(this.l), this.blockSize - 8);
    this.process();
    const result = new Uint8Array(20);
    for (let i = 0; i < 5; i++) {
      bigEndian.putUint32(result, this.H[i], i * 4);
    }
    return result;
  }
  process() {
    for (let t = 0; t < 16; t++) {
      this.w[t] = (this.blocks[t * 4] << 24 | this.blocks[t * 4 + 1] << 16 | this.blocks[t * 4 + 2] << 8 | this.blocks[t * 4 + 3]) >>> 0;
    }
    for (let t = 16; t < 80; t++) {
      this.w[t] = rotl32((this.w[t - 3] ^ this.w[t - 8] ^ this.w[t - 14] ^ this.w[t - 16]) >>> 0, 1);
    }
    let a = this.H[0];
    let b = this.H[1];
    let c = this.H[2];
    let d = this.H[3];
    let e = this.H[4];
    for (let t = 0; t < 80; t++) {
      let F, K;
      if (t < 20) {
        F = (b & c ^ ~b & d) >>> 0;
        K = 1518500249;
      } else if (t < 40) {
        F = (b ^ c ^ d) >>> 0;
        K = 1859775393;
      } else if (t < 60) {
        F = (b & c ^ b & d ^ c & d) >>> 0;
        K = 2400959708;
      } else {
        F = (b ^ c ^ d) >>> 0;
        K = 3395469782;
      }
      const T = rotl32(a, 5) + e + F + this.w[t] + K | 0;
      e = d;
      d = c;
      c = rotl32(b, 30);
      b = a;
      a = T;
    }
    this.H[0] = this.H[0] + a | 0;
    this.H[1] = this.H[1] + b | 0;
    this.H[2] = this.H[2] + c | 0;
    this.H[3] = this.H[3] + d | 0;
    this.H[4] = this.H[4] + e | 0;
  }
};

// node_modules/@oslojs/crypto/dist/subtle/index.js
function constantTimeEqual(a, b) {
  if (a.length !== b.length) {
    return false;
  }
  let c = 0;
  for (let i = 0; i < a.length; i++) {
    c |= a[i] ^ b[i];
  }
  return c === 0;
}

// node_modules/@oslojs/encoding/dist/base32.js
function encodeBase32NoPadding(bytes) {
  return encodeBase32_internal(bytes, base32Alphabet, EncodingPadding.None);
}
function encodeBase32_internal(bytes, alphabet, padding) {
  let result = "";
  for (let i = 0; i < bytes.byteLength; i += 5) {
    let buffer = 0n;
    let bufferBitSize = 0;
    for (let j = 0; j < 5 && i + j < bytes.byteLength; j++) {
      buffer = buffer << 8n | BigInt(bytes[i + j]);
      bufferBitSize += 8;
    }
    if (bufferBitSize % 5 !== 0) {
      buffer = buffer << BigInt(5 - bufferBitSize % 5);
      bufferBitSize += 5 - bufferBitSize % 5;
    }
    for (let j = 0; j < 8; j++) {
      if (bufferBitSize >= 5) {
        result += alphabet[Number(buffer >> BigInt(bufferBitSize - 5) & 0x1fn)];
        bufferBitSize -= 5;
      } else if (bufferBitSize > 0) {
        result += alphabet[Number(buffer << BigInt(6 - bufferBitSize) & 0x3fn)];
        bufferBitSize = 0;
      } else if (padding === EncodingPadding.Include) {
        result += "=";
      }
    }
  }
  return result;
}
var base32Alphabet = "ABCDEFGHIJKLMNOPQRSTUVWXYZ234567";
var EncodingPadding;
(function(EncodingPadding3) {
  EncodingPadding3[EncodingPadding3["Include"] = 0] = "Include";
  EncodingPadding3[EncodingPadding3["None"] = 1] = "None";
})(EncodingPadding || (EncodingPadding = {}));
var DecodingPadding;
(function(DecodingPadding3) {
  DecodingPadding3[DecodingPadding3["Required"] = 0] = "Required";
  DecodingPadding3[DecodingPadding3["Ignore"] = 1] = "Ignore";
})(DecodingPadding || (DecodingPadding = {}));

// node_modules/@oslojs/encoding/dist/base64.js
var EncodingPadding2;
(function(EncodingPadding3) {
  EncodingPadding3[EncodingPadding3["Include"] = 0] = "Include";
  EncodingPadding3[EncodingPadding3["None"] = 1] = "None";
})(EncodingPadding2 || (EncodingPadding2 = {}));
var DecodingPadding2;
(function(DecodingPadding3) {
  DecodingPadding3[DecodingPadding3["Required"] = 0] = "Required";
  DecodingPadding3[DecodingPadding3["Ignore"] = 1] = "Ignore";
})(DecodingPadding2 || (DecodingPadding2 = {}));

// node_modules/@oslojs/otp/dist/hotp.js
function generateHOTP(key, counter, digits) {
  if (digits < 6 || digits > 8) {
    throw new TypeError("Digits must be between 6 and 8");
  }
  const counterBytes = new Uint8Array(8);
  bigEndian.putUint64(counterBytes, counter, 0);
  const HS = hmac(SHA1, key, counterBytes);
  const offset = HS[HS.byteLength - 1] & 15;
  const truncated = HS.slice(offset, offset + 4);
  truncated[0] &= 127;
  const SNum = bigEndian.uint32(truncated, 0);
  const D = SNum % 10 ** digits;
  return D.toString().padStart(digits, "0");
}
function verifyHOTP(key, counter, digits, otp) {
  if (digits < 6 || digits > 8) {
    throw new TypeError("Digits must be between 6 and 8");
  }
  if (otp.length !== digits) {
    return false;
  }
  const bytes = new TextEncoder().encode(otp);
  const expected = generateHOTP(key, counter, digits);
  const expectedBytes = new TextEncoder().encode(expected);
  const valid = constantTimeEqual(bytes, expectedBytes);
  return valid;
}
function createHOTPKeyURI(issuer, accountName, key, counter, digits) {
  const encodedIssuer = encodeURIComponent(issuer);
  const encodedAccountName = encodeURIComponent(accountName);
  const base = `otpauth://hotp/${encodedIssuer}:${encodedAccountName}`;
  const params = new URLSearchParams();
  params.set("issuer", issuer);
  params.set("algorithm", "SHA1");
  params.set("secret", encodeBase32NoPadding(key));
  params.set("counter", counter.toString());
  params.set("digits", digits.toString());
  return base + "?" + params.toString();
}

// node_modules/@oslojs/otp/dist/totp.js
function generateTOTP(key, intervalInSeconds, digits) {
  if (digits < 6 || digits > 8) {
    throw new TypeError("Digits must be between 6 and 8");
  }
  const counter = BigInt(Math.floor(Date.now() / (intervalInSeconds * 1e3)));
  const hotp = generateHOTP(key, counter, digits);
  return hotp;
}
function verifyTOTP(key, intervalInSeconds, digits, otp) {
  const counter = BigInt(Math.floor(Date.now() / (intervalInSeconds * 1e3)));
  const valid = verifyHOTP(key, counter, digits, otp);
  return valid;
}
function verifyTOTPWithGracePeriod(key, intervalInSeconds, digits, otp, gracePeriodInSeconds) {
  if (gracePeriodInSeconds < 0) {
    throw new TypeError("Grace period must be a positive number");
  }
  const nowUnixMilliseconds = Date.now();
  let counter = BigInt(Math.floor((nowUnixMilliseconds - gracePeriodInSeconds * 1e3) / (intervalInSeconds * 1e3)));
  const maxCounterInclusive = BigInt(Math.floor((nowUnixMilliseconds + gracePeriodInSeconds * 1e3) / (intervalInSeconds * 1e3)));
  while (counter <= maxCounterInclusive) {
    const valid = verifyHOTP(key, counter, digits, otp);
    if (valid) {
      return true;
    }
    counter++;
  }
  return false;
}
function createTOTPKeyURI(issuer, accountName, key, periodInSeconds, digits) {
  const encodedIssuer = encodeURIComponent(issuer);
  const encodedAccountName = encodeURIComponent(accountName);
  const base = `otpauth://totp/${encodedIssuer}:${encodedAccountName}`;
  const params = new URLSearchParams();
  params.set("issuer", issuer);
  params.set("algorithm", "SHA1");
  params.set("secret", encodeBase32NoPadding(key));
  params.set("period", periodInSeconds.toString());
  params.set("digits", digits.toString());
  return base + "?" + params.toString();
}
export {
  createHOTPKeyURI,
  createTOTPKeyURI,
  generateHOTP,
  generateTOTP,
  verifyHOTP,
  verifyTOTP,
  verifyTOTPWithGracePeriod
};
//# sourceMappingURL=@oslojs_otp.js.map
