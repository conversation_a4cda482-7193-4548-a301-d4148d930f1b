import {
  captureError
} from "./chunk-Z26222H5.js";
import {
  require_react
} from "./chunk-6OBIWUOU.js";
import {
  __toESM
} from "./chunk-7UVSMXVG.js";

// package/@stackframe/react/node_modules/@stackframe/stack-shared/dist/esm/hooks/use-async-callback.js
var import_react = __toESM(require_react());
function useAsyncCallback(callback, deps) {
  const [error, setError] = import_react.default.useState(void 0);
  const [loadingCount, setLoadingCount] = import_react.default.useState(0);
  const cb = import_react.default.useCallback(
    async (...args) => {
      setLoadingCount((c) => c + 1);
      try {
        return await callback(...args);
      } catch (e) {
        setError(e);
        throw e;
      } finally {
        setLoadingCount((c) => c - 1);
      }
    },
    deps
  );
  return [cb, loadingCount > 0, error];
}
function useAsyncCallbackWithLoggedError(callback, deps) {
  const [newCallback, loading] = useAsyncCallback(async (...args) => {
    try {
      return await callback(...args);
    } catch (e) {
      captureError("async-callback", e);
      throw e;
    }
  }, deps);
  return [newCallback, loading];
}

export {
  useAsyncCallback,
  useAsyncCallbackWithLoggedError
};
//# sourceMappingURL=chunk-CMJU6RUN.js.map
