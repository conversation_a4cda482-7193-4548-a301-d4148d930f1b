{"version": 3, "sources": ["../../jose/dist/browser/runtime/webcrypto.js", "../../jose/dist/browser/lib/buffer_utils.js", "../../jose/dist/browser/runtime/base64url.js", "../../jose/dist/browser/util/errors.js", "../../jose/dist/browser/runtime/random.js", "../../jose/dist/browser/lib/invalid_key_input.js", "../../jose/dist/browser/runtime/is_key_like.js", "../../jose/dist/browser/lib/is_object.js", "../../jose/dist/browser/lib/is_jwk.js", "../../jose/dist/browser/lib/check_key_type.js", "../../jose/dist/browser/lib/private_symbols.js", "../../jose/dist/browser/lib/secs.js", "../../jose/dist/browser/jwks/remote.js", "../../jose/dist/browser/util/base64url.js", "../../jose/dist/browser/util/decode_jwt.js", "../../async-mutex/index.mjs", "../../../package/@stackframe/react/node_modules/@stackframe/stack-shared/src/utils/locks.tsx", "../../../package/@stackframe/react/node_modules/@stackframe/stack-shared/src/utils/stores.tsx", "../../../package/@stackframe/react/node_modules/@stackframe/stack-shared/src/sessions.ts", "../../../package/@stackframe/react/node_modules/@stackframe/stack-shared/src/utils/http.tsx", "../../../package/@stackframe/react/node_modules/node_modules/.pnpm/oauth4webapi@2.10.4/node_modules/oauth4webapi/build/index.js", "../../../package/@stackframe/react/node_modules/@stackframe/stack-shared/src/interface/clientInterface.ts", "../../../package/@stackframe/react/node_modules/@stackframe/stack-shared/src/interface/serverInterface.ts", "../../../package/@stackframe/react/node_modules/@stackframe/stack-shared/src/interface/adminInterface.ts", "../../../package/@stackframe/react/node_modules/@stackframe/stack-shared/src/utils/promises.tsx", "../../../package/@stackframe/react/node_modules/@stackframe/stack-shared/src/utils/results.tsx", "../../../package/@stackframe/react/node_modules/@stackframe/stack-shared/src/utils/maps.tsx"], "sourcesContent": ["export default crypto;\nexport const isCryptoKey = (key) => key instanceof CryptoKey;\n", "import digest from '../runtime/digest.js';\nexport const encoder = new TextEncoder();\nexport const decoder = new TextDecoder();\nconst MAX_INT32 = 2 ** 32;\nexport function concat(...buffers) {\n    const size = buffers.reduce((acc, { length }) => acc + length, 0);\n    const buf = new Uint8Array(size);\n    let i = 0;\n    for (const buffer of buffers) {\n        buf.set(buffer, i);\n        i += buffer.length;\n    }\n    return buf;\n}\nexport function p2s(alg, p2sInput) {\n    return concat(encoder.encode(alg), new Uint8Array([0]), p2sInput);\n}\nfunction writeUInt32BE(buf, value, offset) {\n    if (value < 0 || value >= MAX_INT32) {\n        throw new RangeError(`value must be >= 0 and <= ${MAX_INT32 - 1}. Received ${value}`);\n    }\n    buf.set([value >>> 24, value >>> 16, value >>> 8, value & 0xff], offset);\n}\nexport function uint64be(value) {\n    const high = Math.floor(value / MAX_INT32);\n    const low = value % MAX_INT32;\n    const buf = new Uint8Array(8);\n    writeUInt32BE(buf, high, 0);\n    writeUInt32BE(buf, low, 4);\n    return buf;\n}\nexport function uint32be(value) {\n    const buf = new Uint8Array(4);\n    writeUInt32BE(buf, value);\n    return buf;\n}\nexport function lengthAndInput(input) {\n    return concat(uint32be(input.length), input);\n}\nexport async function concatKdf(secret, bits, value) {\n    const iterations = Math.ceil((bits >> 3) / 32);\n    const res = new Uint8Array(iterations * 32);\n    for (let iter = 0; iter < iterations; iter++) {\n        const buf = new Uint8Array(4 + secret.length + value.length);\n        buf.set(uint32be(iter + 1));\n        buf.set(secret, 4);\n        buf.set(value, 4 + secret.length);\n        res.set(await digest('sha256', buf), iter * 32);\n    }\n    return res.slice(0, bits >> 3);\n}\n", "import { encoder, decoder } from '../lib/buffer_utils.js';\nexport const encodeBase64 = (input) => {\n    let unencoded = input;\n    if (typeof unencoded === 'string') {\n        unencoded = encoder.encode(unencoded);\n    }\n    const CHUNK_SIZE = 0x8000;\n    const arr = [];\n    for (let i = 0; i < unencoded.length; i += CHUNK_SIZE) {\n        arr.push(String.fromCharCode.apply(null, unencoded.subarray(i, i + CHUNK_SIZE)));\n    }\n    return btoa(arr.join(''));\n};\nexport const encode = (input) => {\n    return encodeBase64(input).replace(/=/g, '').replace(/\\+/g, '-').replace(/\\//g, '_');\n};\nexport const decodeBase64 = (encoded) => {\n    const binary = atob(encoded);\n    const bytes = new Uint8Array(binary.length);\n    for (let i = 0; i < binary.length; i++) {\n        bytes[i] = binary.charCodeAt(i);\n    }\n    return bytes;\n};\nexport const decode = (input) => {\n    let encoded = input;\n    if (encoded instanceof Uint8Array) {\n        encoded = decoder.decode(encoded);\n    }\n    encoded = encoded.replace(/-/g, '+').replace(/_/g, '/').replace(/\\s/g, '');\n    try {\n        return decodeBase64(encoded);\n    }\n    catch {\n        throw new TypeError('The input to be decoded is not correctly encoded.');\n    }\n};\n", "export class JOSEError extends Error {\n    constructor(message, options) {\n        super(message, options);\n        this.code = 'ERR_JOSE_GENERIC';\n        this.name = this.constructor.name;\n        Error.captureStackTrace?.(this, this.constructor);\n    }\n}\nJOSEError.code = 'ERR_JOSE_GENERIC';\nexport class JWTClaimValidationFailed extends JOSEError {\n    constructor(message, payload, claim = 'unspecified', reason = 'unspecified') {\n        super(message, { cause: { claim, reason, payload } });\n        this.code = 'ERR_JWT_CLAIM_VALIDATION_FAILED';\n        this.claim = claim;\n        this.reason = reason;\n        this.payload = payload;\n    }\n}\nJWTClaimValidationFailed.code = 'ERR_JWT_CLAIM_VALIDATION_FAILED';\nexport class JWTExpired extends JOSEError {\n    constructor(message, payload, claim = 'unspecified', reason = 'unspecified') {\n        super(message, { cause: { claim, reason, payload } });\n        this.code = 'ERR_JWT_EXPIRED';\n        this.claim = claim;\n        this.reason = reason;\n        this.payload = payload;\n    }\n}\nJWTExpired.code = 'ERR_JWT_EXPIRED';\nexport class JOSEAlgNotAllowed extends JOSEError {\n    constructor() {\n        super(...arguments);\n        this.code = 'ERR_JOSE_ALG_NOT_ALLOWED';\n    }\n}\nJOSEAlgNotAllowed.code = 'ERR_JOSE_ALG_NOT_ALLOWED';\nexport class JOSENotSupported extends JOSEError {\n    constructor() {\n        super(...arguments);\n        this.code = 'ERR_JOSE_NOT_SUPPORTED';\n    }\n}\nJOSENotSupported.code = 'ERR_JOSE_NOT_SUPPORTED';\nexport class JWEDecryptionFailed extends JOSEError {\n    constructor(message = 'decryption operation failed', options) {\n        super(message, options);\n        this.code = 'ERR_JWE_DECRYPTION_FAILED';\n    }\n}\nJWEDecryptionFailed.code = 'ERR_JWE_DECRYPTION_FAILED';\nexport class JWEInvalid extends JOSEError {\n    constructor() {\n        super(...arguments);\n        this.code = 'ERR_JWE_INVALID';\n    }\n}\nJWEInvalid.code = 'ERR_JWE_INVALID';\nexport class JWSInvalid extends JOSEError {\n    constructor() {\n        super(...arguments);\n        this.code = 'ERR_JWS_INVALID';\n    }\n}\nJWSInvalid.code = 'ERR_JWS_INVALID';\nexport class JWTInvalid extends JOSEError {\n    constructor() {\n        super(...arguments);\n        this.code = 'ERR_JWT_INVALID';\n    }\n}\nJWTInvalid.code = 'ERR_JWT_INVALID';\nexport class JWKInvalid extends JOSEError {\n    constructor() {\n        super(...arguments);\n        this.code = 'ERR_JWK_INVALID';\n    }\n}\nJWKInvalid.code = 'ERR_JWK_INVALID';\nexport class JWKSInvalid extends JOSEError {\n    constructor() {\n        super(...arguments);\n        this.code = 'ERR_JWKS_INVALID';\n    }\n}\nJWKSInvalid.code = 'ERR_JWKS_INVALID';\nexport class JWKSNoMatchingKey extends JOSEError {\n    constructor(message = 'no applicable key found in the JSON Web Key Set', options) {\n        super(message, options);\n        this.code = 'ERR_JWKS_NO_MATCHING_KEY';\n    }\n}\nJWKSNoMatchingKey.code = 'ERR_JWKS_NO_MATCHING_KEY';\nexport class JWKSMultipleMatchingKeys extends JOSEError {\n    constructor(message = 'multiple matching keys found in the JSON Web Key Set', options) {\n        super(message, options);\n        this.code = 'ERR_JWKS_MULTIPLE_MATCHING_KEYS';\n    }\n}\nSymbol.asyncIterator;\nJWKSMultipleMatchingKeys.code = 'ERR_JWKS_MULTIPLE_MATCHING_KEYS';\nexport class JWKSTimeout extends JOSEError {\n    constructor(message = 'request timed out', options) {\n        super(message, options);\n        this.code = 'ERR_JWKS_TIMEOUT';\n    }\n}\nJWKSTimeout.code = 'ERR_JWKS_TIMEOUT';\nexport class JWSSignatureVerificationFailed extends JOSEError {\n    constructor(message = 'signature verification failed', options) {\n        super(message, options);\n        this.code = 'ERR_JWS_SIGNATURE_VERIFICATION_FAILED';\n    }\n}\nJWSSignatureVerificationFailed.code = 'ERR_JWS_SIGNATURE_VERIFICATION_FAILED';\n", "import crypto from './webcrypto.js';\nexport default crypto.getRandomValues.bind(crypto);\n", "function message(msg, actual, ...types) {\n    types = types.filter(<PERSON><PERSON><PERSON>);\n    if (types.length > 2) {\n        const last = types.pop();\n        msg += `one of type ${types.join(', ')}, or ${last}.`;\n    }\n    else if (types.length === 2) {\n        msg += `one of type ${types[0]} or ${types[1]}.`;\n    }\n    else {\n        msg += `of type ${types[0]}.`;\n    }\n    if (actual == null) {\n        msg += ` Received ${actual}`;\n    }\n    else if (typeof actual === 'function' && actual.name) {\n        msg += ` Received function ${actual.name}`;\n    }\n    else if (typeof actual === 'object' && actual != null) {\n        if (actual.constructor?.name) {\n            msg += ` Received an instance of ${actual.constructor.name}`;\n        }\n    }\n    return msg;\n}\nexport default (actual, ...types) => {\n    return message('Key must be ', actual, ...types);\n};\nexport function withAlg(alg, actual, ...types) {\n    return message(`Key for the ${alg} algorithm must be `, actual, ...types);\n}\n", "import { isCrypto<PERSON>ey } from './webcrypto.js';\nexport default (key) => {\n    if (isCryptoKey(key)) {\n        return true;\n    }\n    return key?.[Symbol.toStringTag] === 'KeyObject';\n};\nexport const types = ['CryptoKey'];\n", "function isObjectLike(value) {\n    return typeof value === 'object' && value !== null;\n}\nexport default function isObject(input) {\n    if (!isObjectLike(input) || Object.prototype.toString.call(input) !== '[object Object]') {\n        return false;\n    }\n    if (Object.getPrototypeOf(input) === null) {\n        return true;\n    }\n    let proto = input;\n    while (Object.getPrototypeOf(proto) !== null) {\n        proto = Object.getPrototypeOf(proto);\n    }\n    return Object.getPrototypeOf(input) === proto;\n}\n", "import isObject from './is_object.js';\nexport function isJWK(key) {\n    return isObject(key) && typeof key.kty === 'string';\n}\nexport function isPrivateJWK(key) {\n    return key.kty !== 'oct' && typeof key.d === 'string';\n}\nexport function isPublicJWK(key) {\n    return key.kty !== 'oct' && typeof key.d === 'undefined';\n}\nexport function isSecretJWK(key) {\n    return isJWK(key) && key.kty === 'oct' && typeof key.k === 'string';\n}\n", "import { withAlg as invalidKeyInput } from './invalid_key_input.js';\nimport isKeyLike, { types } from '../runtime/is_key_like.js';\nimport * as jwk from './is_jwk.js';\nconst tag = (key) => key?.[Symbol.toStringTag];\nconst jwkMatchesOp = (alg, key, usage) => {\n    if (key.use !== undefined && key.use !== 'sig') {\n        throw new TypeError('Invalid key for this operation, when present its use must be sig');\n    }\n    if (key.key_ops !== undefined && key.key_ops.includes?.(usage) !== true) {\n        throw new TypeError(`Invalid key for this operation, when present its key_ops must include ${usage}`);\n    }\n    if (key.alg !== undefined && key.alg !== alg) {\n        throw new TypeError(`Invalid key for this operation, when present its alg must be ${alg}`);\n    }\n    return true;\n};\nconst symmetricTypeCheck = (alg, key, usage, allowJwk) => {\n    if (key instanceof Uint8Array)\n        return;\n    if (allowJwk && jwk.isJWK(key)) {\n        if (jwk.isSecretJWK(key) && jwkMatchesOp(alg, key, usage))\n            return;\n        throw new TypeError(`JSON Web Key for symmetric algorithms must have JWK \"kty\" (Key Type) equal to \"oct\" and the JWK \"k\" (Key Value) present`);\n    }\n    if (!isKeyLike(key)) {\n        throw new TypeError(invalidKeyInput(alg, key, ...types, 'Uint8Array', allowJwk ? 'JSON Web Key' : null));\n    }\n    if (key.type !== 'secret') {\n        throw new TypeError(`${tag(key)} instances for symmetric algorithms must be of type \"secret\"`);\n    }\n};\nconst asymmetricTypeCheck = (alg, key, usage, allowJwk) => {\n    if (allowJwk && jwk.isJWK(key)) {\n        switch (usage) {\n            case 'sign':\n                if (jwk.isPrivateJWK(key) && jwkMatchesOp(alg, key, usage))\n                    return;\n                throw new TypeError(`JSON Web Key for this operation be a private JWK`);\n            case 'verify':\n                if (jwk.isPublicJWK(key) && jwkMatchesOp(alg, key, usage))\n                    return;\n                throw new TypeError(`JSON Web Key for this operation be a public JWK`);\n        }\n    }\n    if (!isKeyLike(key)) {\n        throw new TypeError(invalidKeyInput(alg, key, ...types, allowJwk ? 'JSON Web Key' : null));\n    }\n    if (key.type === 'secret') {\n        throw new TypeError(`${tag(key)} instances for asymmetric algorithms must not be of type \"secret\"`);\n    }\n    if (usage === 'sign' && key.type === 'public') {\n        throw new TypeError(`${tag(key)} instances for asymmetric algorithm signing must be of type \"private\"`);\n    }\n    if (usage === 'decrypt' && key.type === 'public') {\n        throw new TypeError(`${tag(key)} instances for asymmetric algorithm decryption must be of type \"private\"`);\n    }\n    if (key.algorithm && usage === 'verify' && key.type === 'private') {\n        throw new TypeError(`${tag(key)} instances for asymmetric algorithm verifying must be of type \"public\"`);\n    }\n    if (key.algorithm && usage === 'encrypt' && key.type === 'private') {\n        throw new TypeError(`${tag(key)} instances for asymmetric algorithm encryption must be of type \"public\"`);\n    }\n};\nfunction checkKeyType(allowJwk, alg, key, usage) {\n    const symmetric = alg.startsWith('HS') ||\n        alg === 'dir' ||\n        alg.startsWith('PBES2') ||\n        /^A\\d{3}(?:GCM)?KW$/.test(alg);\n    if (symmetric) {\n        symmetricTypeCheck(alg, key, usage, allowJwk);\n    }\n    else {\n        asymmetricTypeCheck(alg, key, usage, allowJwk);\n    }\n}\nexport default checkKeyType.bind(undefined, false);\nexport const checkKeyTypeWithJwk = checkKeyType.bind(undefined, true);\n", "export const unprotected = Symbol();\n", "const minute = 60;\nconst hour = minute * 60;\nconst day = hour * 24;\nconst week = day * 7;\nconst year = day * 365.25;\nconst REGEX = /^(\\+|\\-)? ?(\\d+|\\d+\\.\\d+) ?(seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)(?: (ago|from now))?$/i;\nexport default (str) => {\n    const matched = REGEX.exec(str);\n    if (!matched || (matched[4] && matched[1])) {\n        throw new TypeError('Invalid time period format');\n    }\n    const value = parseFloat(matched[2]);\n    const unit = matched[3].toLowerCase();\n    let numericDate;\n    switch (unit) {\n        case 'sec':\n        case 'secs':\n        case 'second':\n        case 'seconds':\n        case 's':\n            numericDate = Math.round(value);\n            break;\n        case 'minute':\n        case 'minutes':\n        case 'min':\n        case 'mins':\n        case 'm':\n            numericDate = Math.round(value * minute);\n            break;\n        case 'hour':\n        case 'hours':\n        case 'hr':\n        case 'hrs':\n        case 'h':\n            numericDate = Math.round(value * hour);\n            break;\n        case 'day':\n        case 'days':\n        case 'd':\n            numericDate = Math.round(value * day);\n            break;\n        case 'week':\n        case 'weeks':\n        case 'w':\n            numericDate = Math.round(value * week);\n            break;\n        default:\n            numericDate = Math.round(value * year);\n            break;\n    }\n    if (matched[1] === '-' || matched[4] === 'ago') {\n        return -numericDate;\n    }\n    return numericDate;\n};\n", "import fetchJwks from '../runtime/fetch_jwks.js';\nimport { JWKSNoMatchingKey } from '../util/errors.js';\nimport { createLocalJWKSet } from './local.js';\nimport isObject from '../lib/is_object.js';\nfunction isCloudflareWorkers() {\n    return (typeof WebSocketPair !== 'undefined' ||\n        (typeof navigator !== 'undefined' && navigator.userAgent === 'Cloudflare-Workers') ||\n        (typeof EdgeRuntime !== 'undefined' && EdgeRuntime === 'vercel'));\n}\nlet USER_AGENT;\nif (typeof navigator === 'undefined' || !navigator.userAgent?.startsWith?.('Mozilla/5.0 ')) {\n    const NAME = 'jose';\n    const VERSION = 'v5.10.0';\n    USER_AGENT = `${NAME}/${VERSION}`;\n}\nexport const jwksCache = Symbol();\nfunction isFreshJwksCache(input, cacheMaxAge) {\n    if (typeof input !== 'object' || input === null) {\n        return false;\n    }\n    if (!('uat' in input) || typeof input.uat !== 'number' || Date.now() - input.uat >= cacheMaxAge) {\n        return false;\n    }\n    if (!('jwks' in input) ||\n        !isObject(input.jwks) ||\n        !Array.isArray(input.jwks.keys) ||\n        !Array.prototype.every.call(input.jwks.keys, isObject)) {\n        return false;\n    }\n    return true;\n}\nclass RemoteJWKSet {\n    constructor(url, options) {\n        if (!(url instanceof URL)) {\n            throw new TypeError('url must be an instance of URL');\n        }\n        this._url = new URL(url.href);\n        this._options = { agent: options?.agent, headers: options?.headers };\n        this._timeoutDuration =\n            typeof options?.timeoutDuration === 'number' ? options?.timeoutDuration : 5000;\n        this._cooldownDuration =\n            typeof options?.cooldownDuration === 'number' ? options?.cooldownDuration : 30000;\n        this._cacheMaxAge = typeof options?.cacheMaxAge === 'number' ? options?.cacheMaxAge : 600000;\n        if (options?.[jwksCache] !== undefined) {\n            this._cache = options?.[jwksCache];\n            if (isFreshJwksCache(options?.[jwksCache], this._cacheMaxAge)) {\n                this._jwksTimestamp = this._cache.uat;\n                this._local = createLocalJWKSet(this._cache.jwks);\n            }\n        }\n    }\n    coolingDown() {\n        return typeof this._jwksTimestamp === 'number'\n            ? Date.now() < this._jwksTimestamp + this._cooldownDuration\n            : false;\n    }\n    fresh() {\n        return typeof this._jwksTimestamp === 'number'\n            ? Date.now() < this._jwksTimestamp + this._cacheMaxAge\n            : false;\n    }\n    async getKey(protectedHeader, token) {\n        if (!this._local || !this.fresh()) {\n            await this.reload();\n        }\n        try {\n            return await this._local(protectedHeader, token);\n        }\n        catch (err) {\n            if (err instanceof JWKSNoMatchingKey) {\n                if (this.coolingDown() === false) {\n                    await this.reload();\n                    return this._local(protectedHeader, token);\n                }\n            }\n            throw err;\n        }\n    }\n    async reload() {\n        if (this._pendingFetch && isCloudflareWorkers()) {\n            this._pendingFetch = undefined;\n        }\n        const headers = new Headers(this._options.headers);\n        if (USER_AGENT && !headers.has('User-Agent')) {\n            headers.set('User-Agent', USER_AGENT);\n            this._options.headers = Object.fromEntries(headers.entries());\n        }\n        this._pendingFetch || (this._pendingFetch = fetchJwks(this._url, this._timeoutDuration, this._options)\n            .then((json) => {\n            this._local = createLocalJWKSet(json);\n            if (this._cache) {\n                this._cache.uat = Date.now();\n                this._cache.jwks = json;\n            }\n            this._jwksTimestamp = Date.now();\n            this._pendingFetch = undefined;\n        })\n            .catch((err) => {\n            this._pendingFetch = undefined;\n            throw err;\n        }));\n        await this._pendingFetch;\n    }\n}\nexport function createRemoteJWKSet(url, options) {\n    const set = new RemoteJWKSet(url, options);\n    const remoteJWKSet = async (protectedHeader, token) => set.getKey(protectedHeader, token);\n    Object.defineProperties(remoteJWKSet, {\n        coolingDown: {\n            get: () => set.coolingDown(),\n            enumerable: true,\n            configurable: false,\n        },\n        fresh: {\n            get: () => set.fresh(),\n            enumerable: true,\n            configurable: false,\n        },\n        reload: {\n            value: () => set.reload(),\n            enumerable: true,\n            configurable: false,\n            writable: false,\n        },\n        reloading: {\n            get: () => !!set._pendingFetch,\n            enumerable: true,\n            configurable: false,\n        },\n        jwks: {\n            value: () => set._local?.jwks(),\n            enumerable: true,\n            configurable: false,\n            writable: false,\n        },\n    });\n    return remoteJWKSet;\n}\nexport const experimental_jwksCache = jwksCache;\n", "import * as base64url from '../runtime/base64url.js';\nexport const encode = base64url.encode;\nexport const decode = base64url.decode;\n", "import { decode as base64url } from './base64url.js';\nimport { decoder } from '../lib/buffer_utils.js';\nimport isObject from '../lib/is_object.js';\nimport { JWTInvalid } from './errors.js';\nexport function decodeJwt(jwt) {\n    if (typeof jwt !== 'string')\n        throw new JWTInvalid('JWTs must use Compact JWS serialization, JWT must be a string');\n    const { 1: payload, length } = jwt.split('.');\n    if (length === 5)\n        throw new JWTInvalid('Only JWTs using Compact JWS serialization can be decoded');\n    if (length !== 3)\n        throw new JWTInvalid('Invalid JWT');\n    if (!payload)\n        throw new JWTInvalid('JWTs must contain a payload');\n    let decoded;\n    try {\n        decoded = base64url(payload);\n    }\n    catch {\n        throw new JWTInvalid('Failed to base64url decode the payload');\n    }\n    let result;\n    try {\n        result = JSON.parse(decoder.decode(decoded));\n    }\n    catch {\n        throw new JWTInvalid('Failed to parse the decoded payload as JSON');\n    }\n    if (!isObject(result))\n        throw new JWTInvalid('Invalid JWT Claims Set');\n    return result;\n}\n", "const E_TIMEOUT = new Error('timeout while waiting for mutex to become available');\nconst E_ALREADY_LOCKED = new Error('mutex already locked');\nconst E_CANCELED = new Error('request for lock canceled');\n\nvar __awaiter$2 = (undefined && undefined.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nclass Semaphore {\n    constructor(_value, _cancelError = E_CANCELED) {\n        this._value = _value;\n        this._cancelError = _cancelError;\n        this._queue = [];\n        this._weightedWaiters = [];\n    }\n    acquire(weight = 1, priority = 0) {\n        if (weight <= 0)\n            throw new Error(`invalid weight ${weight}: must be positive`);\n        return new Promise((resolve, reject) => {\n            const task = { resolve, reject, weight, priority };\n            const i = findIndexFromEnd(this._queue, (other) => priority <= other.priority);\n            if (i === -1 && weight <= this._value) {\n                // Needs immediate dispatch, skip the queue\n                this._dispatchItem(task);\n            }\n            else {\n                this._queue.splice(i + 1, 0, task);\n            }\n        });\n    }\n    runExclusive(callback_1) {\n        return __awaiter$2(this, arguments, void 0, function* (callback, weight = 1, priority = 0) {\n            const [value, release] = yield this.acquire(weight, priority);\n            try {\n                return yield callback(value);\n            }\n            finally {\n                release();\n            }\n        });\n    }\n    waitForUnlock(weight = 1, priority = 0) {\n        if (weight <= 0)\n            throw new Error(`invalid weight ${weight}: must be positive`);\n        if (this._couldLockImmediately(weight, priority)) {\n            return Promise.resolve();\n        }\n        else {\n            return new Promise((resolve) => {\n                if (!this._weightedWaiters[weight - 1])\n                    this._weightedWaiters[weight - 1] = [];\n                insertSorted(this._weightedWaiters[weight - 1], { resolve, priority });\n            });\n        }\n    }\n    isLocked() {\n        return this._value <= 0;\n    }\n    getValue() {\n        return this._value;\n    }\n    setValue(value) {\n        this._value = value;\n        this._dispatchQueue();\n    }\n    release(weight = 1) {\n        if (weight <= 0)\n            throw new Error(`invalid weight ${weight}: must be positive`);\n        this._value += weight;\n        this._dispatchQueue();\n    }\n    cancel() {\n        this._queue.forEach((entry) => entry.reject(this._cancelError));\n        this._queue = [];\n    }\n    _dispatchQueue() {\n        this._drainUnlockWaiters();\n        while (this._queue.length > 0 && this._queue[0].weight <= this._value) {\n            this._dispatchItem(this._queue.shift());\n            this._drainUnlockWaiters();\n        }\n    }\n    _dispatchItem(item) {\n        const previousValue = this._value;\n        this._value -= item.weight;\n        item.resolve([previousValue, this._newReleaser(item.weight)]);\n    }\n    _newReleaser(weight) {\n        let called = false;\n        return () => {\n            if (called)\n                return;\n            called = true;\n            this.release(weight);\n        };\n    }\n    _drainUnlockWaiters() {\n        if (this._queue.length === 0) {\n            for (let weight = this._value; weight > 0; weight--) {\n                const waiters = this._weightedWaiters[weight - 1];\n                if (!waiters)\n                    continue;\n                waiters.forEach((waiter) => waiter.resolve());\n                this._weightedWaiters[weight - 1] = [];\n            }\n        }\n        else {\n            const queuedPriority = this._queue[0].priority;\n            for (let weight = this._value; weight > 0; weight--) {\n                const waiters = this._weightedWaiters[weight - 1];\n                if (!waiters)\n                    continue;\n                const i = waiters.findIndex((waiter) => waiter.priority <= queuedPriority);\n                (i === -1 ? waiters : waiters.splice(0, i))\n                    .forEach((waiter => waiter.resolve()));\n            }\n        }\n    }\n    _couldLockImmediately(weight, priority) {\n        return (this._queue.length === 0 || this._queue[0].priority < priority) &&\n            weight <= this._value;\n    }\n}\nfunction insertSorted(a, v) {\n    const i = findIndexFromEnd(a, (other) => v.priority <= other.priority);\n    a.splice(i + 1, 0, v);\n}\nfunction findIndexFromEnd(a, predicate) {\n    for (let i = a.length - 1; i >= 0; i--) {\n        if (predicate(a[i])) {\n            return i;\n        }\n    }\n    return -1;\n}\n\nvar __awaiter$1 = (undefined && undefined.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nclass Mutex {\n    constructor(cancelError) {\n        this._semaphore = new Semaphore(1, cancelError);\n    }\n    acquire() {\n        return __awaiter$1(this, arguments, void 0, function* (priority = 0) {\n            const [, releaser] = yield this._semaphore.acquire(1, priority);\n            return releaser;\n        });\n    }\n    runExclusive(callback, priority = 0) {\n        return this._semaphore.runExclusive(() => callback(), 1, priority);\n    }\n    isLocked() {\n        return this._semaphore.isLocked();\n    }\n    waitForUnlock(priority = 0) {\n        return this._semaphore.waitForUnlock(1, priority);\n    }\n    release() {\n        if (this._semaphore.isLocked())\n            this._semaphore.release();\n    }\n    cancel() {\n        return this._semaphore.cancel();\n    }\n}\n\nvar __awaiter = (undefined && undefined.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nfunction withTimeout(sync, timeout, timeoutError = E_TIMEOUT) {\n    return {\n        acquire: (weightOrPriority, priority) => {\n            let weight;\n            if (isSemaphore(sync)) {\n                weight = weightOrPriority;\n            }\n            else {\n                weight = undefined;\n                priority = weightOrPriority;\n            }\n            if (weight !== undefined && weight <= 0) {\n                throw new Error(`invalid weight ${weight}: must be positive`);\n            }\n            return new Promise((resolve, reject) => __awaiter(this, void 0, void 0, function* () {\n                let isTimeout = false;\n                const handle = setTimeout(() => {\n                    isTimeout = true;\n                    reject(timeoutError);\n                }, timeout);\n                try {\n                    const ticket = yield (isSemaphore(sync)\n                        ? sync.acquire(weight, priority)\n                        : sync.acquire(priority));\n                    if (isTimeout) {\n                        const release = Array.isArray(ticket) ? ticket[1] : ticket;\n                        release();\n                    }\n                    else {\n                        clearTimeout(handle);\n                        resolve(ticket);\n                    }\n                }\n                catch (e) {\n                    if (!isTimeout) {\n                        clearTimeout(handle);\n                        reject(e);\n                    }\n                }\n            }));\n        },\n        runExclusive(callback, weight, priority) {\n            return __awaiter(this, void 0, void 0, function* () {\n                let release = () => undefined;\n                try {\n                    const ticket = yield this.acquire(weight, priority);\n                    if (Array.isArray(ticket)) {\n                        release = ticket[1];\n                        return yield callback(ticket[0]);\n                    }\n                    else {\n                        release = ticket;\n                        return yield callback();\n                    }\n                }\n                finally {\n                    release();\n                }\n            });\n        },\n        release(weight) {\n            sync.release(weight);\n        },\n        cancel() {\n            return sync.cancel();\n        },\n        waitForUnlock: (weightOrPriority, priority) => {\n            let weight;\n            if (isSemaphore(sync)) {\n                weight = weightOrPriority;\n            }\n            else {\n                weight = undefined;\n                priority = weightOrPriority;\n            }\n            if (weight !== undefined && weight <= 0) {\n                throw new Error(`invalid weight ${weight}: must be positive`);\n            }\n            return new Promise((resolve, reject) => {\n                const handle = setTimeout(() => reject(timeoutError), timeout);\n                (isSemaphore(sync)\n                    ? sync.waitForUnlock(weight, priority)\n                    : sync.waitForUnlock(priority)).then(() => {\n                    clearTimeout(handle);\n                    resolve();\n                });\n            });\n        },\n        isLocked: () => sync.isLocked(),\n        getValue: () => sync.getValue(),\n        setValue: (value) => sync.setValue(value),\n    };\n}\nfunction isSemaphore(sync) {\n    return sync.getValue !== undefined;\n}\n\n// eslint-disable-next-lisne @typescript-eslint/explicit-module-boundary-types\nfunction tryAcquire(sync, alreadyAcquiredError = E_ALREADY_LOCKED) {\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    return withTimeout(sync, 0, alreadyAcquiredError);\n}\n\nexport { E_ALREADY_LOCKED, E_CANCELED, E_TIMEOUT, Mutex, Semaphore, tryAcquire, withTimeout };\n", "import { Semaphore } from 'async-mutex';\n\ntype LockCallback<T> = () => Promise<T>;\n\nexport class ReadWriteLock {\n  private semaphore: Semaphore;\n  private readers: number;\n  private readersMutex: Semaphore;\n\n  constructor() {\n    this.semaphore = new Semaphore(1); // Semaphore with 1 permit\n    this.readers = 0; // Track the number of readers\n    this.readersMutex = new Semaphore(1); // Protect access to `readers` count\n  }\n\n  async withReadLock<T>(callback: LockCallback<T>): Promise<T> {\n    await this._acquireReadLock();\n    try {\n      return await callback();\n    } finally {\n      await this._releaseReadLock();\n    }\n  }\n\n  async withWriteLock<T>(callback: LockCallback<T>): Promise<T> {\n    await this._acquireWriteLock();\n    try {\n      return await callback();\n    } finally {\n      await this._releaseWriteLock();\n    }\n  }\n\n  private async _acquireReadLock(): Promise<void> {\n    // Increment the readers count\n    await this.readersMutex.acquire();\n    try {\n      this.readers += 1;\n      // If this is the first reader, block writers\n      if (this.readers === 1) {\n        await this.semaphore.acquire();\n      }\n    } finally {\n      this.readersMutex.release();\n    }\n  }\n\n  private async _releaseReadLock(): Promise<void> {\n    // Decrement the readers count\n    await this.readersMutex.acquire();\n    try {\n      this.readers -= 1;\n      // If this was the last reader, release the writer block\n      if (this.readers === 0) {\n        this.semaphore.release();\n      }\n    } finally {\n      this.readersMutex.release();\n    }\n  }\n\n  private async _acquireWriteLock(): Promise<void> {\n    // Writers acquire the main semaphore exclusively\n    await this.semaphore.acquire();\n  }\n\n  private async _releaseWriteLock(): Promise<void> {\n    // Writers release the main semaphore\n    this.semaphore.release();\n  }\n}\n", "import { ReadWriteLock } from \"./locks\";\nimport { ReactPromise, pending, rejected, resolved } from \"./promises\";\nimport { AsyncResult, Result } from \"./results\";\nimport { generateUuid } from \"./uuids\";\n\nexport type ReadonlyStore<T> = {\n  get(): T,\n  onChange(callback: (value: T, oldValue: T | undefined) => void): { unsubscribe: () => void },\n  onceChange(callback: (value: T, oldValue: T | undefined) => void): { unsubscribe: () => void },\n};\n\nexport type AsyncStoreStateChangeCallback<T> = (args: { state: AsyncResult<T>, oldState: AsyncResult<T>, lastOkValue: T | undefined }) => void;\n\nexport type ReadonlyAsyncStore<T> = {\n  isAvailable(): boolean,\n  get(): AsyncResult<T, unknown, void>,\n  getOrWait(): ReactPromise<T>,\n  onChange(callback: (value: T, oldValue: T | undefined) => void): { unsubscribe: () => void },\n  onceChange(callback: (value: T, oldValue: T | undefined) => void): { unsubscribe: () => void },\n  onStateChange(callback: AsyncStoreStateChangeCallback<T>): { unsubscribe: () => void },\n  onceStateChange(callback: AsyncStoreStateChangeCallback<T>): { unsubscribe: () => void },\n};\n\nexport class Store<T> implements ReadonlyStore<T> {\n  private readonly _callbacks: Map<string, ((value: T, oldValue: T | undefined) => void)> = new Map();\n\n  constructor(\n    private _value: T\n  ) {}\n\n  get(): T {\n    return this._value;\n  }\n\n  set(value: T): void {\n    const oldValue = this._value;\n    this._value = value;\n    this._callbacks.forEach((callback) => callback(value, oldValue));\n  }\n\n  update(updater: (value: T) => T): T {\n    const value = updater(this._value);\n    this.set(value);\n    return value;\n  }\n\n  onChange(callback: (value: T, oldValue: T | undefined) => void): { unsubscribe: () => void } {\n    const uuid = generateUuid();\n    this._callbacks.set(uuid, callback);\n    return {\n      unsubscribe: () => {\n        this._callbacks.delete(uuid);\n      },\n    };\n  }\n\n  onceChange(callback: (value: T, oldValue: T | undefined) => void): { unsubscribe: () => void } {\n    const { unsubscribe } = this.onChange((...args) => {\n      unsubscribe();\n      callback(...args);\n    });\n    return { unsubscribe };\n  }\n}\n\nexport const storeLock = new ReadWriteLock();\n\n\nexport class AsyncStore<T> implements ReadonlyAsyncStore<T> {\n  private _isAvailable: boolean;\n  private _mostRecentOkValue: T | undefined = undefined;\n\n  private _isRejected = false;\n  private _rejectionError: unknown;\n  private readonly _waitingRejectFunctions = new Map<string, ((error: unknown) => void)>();\n\n  private readonly _callbacks: Map<string, AsyncStoreStateChangeCallback<T>> = new Map();\n\n  private _updateCounter = 0;\n  private _lastSuccessfulUpdate = -1;\n\n  constructor(...args: [] | [T]) {\n    if (args.length === 0) {\n      this._isAvailable = false;\n    } else {\n      this._isAvailable = true;\n      this._mostRecentOkValue = args[0];\n    }\n  }\n\n  isAvailable(): boolean {\n    return this._isAvailable;\n  }\n\n  isRejected(): boolean {\n    return this._isRejected;\n  }\n\n  get() {\n    if (this.isRejected()) {\n      return AsyncResult.error(this._rejectionError);\n    } else if (this.isAvailable()) {\n      return AsyncResult.ok(this._mostRecentOkValue as T);\n    } else {\n      return AsyncResult.pending();\n    }\n  }\n\n  getOrWait(): ReactPromise<T> {\n    const uuid = generateUuid();\n    if (this.isRejected()) {\n      return rejected(this._rejectionError);\n    } else if (this.isAvailable()) {\n      return resolved(this._mostRecentOkValue as T);\n    }\n    const promise = new Promise<T>((resolve, reject) => {\n      this.onceChange((value) => {\n        resolve(value);\n      });\n      this._waitingRejectFunctions.set(uuid, reject);\n    });\n    const withFinally = promise.finally(() => {\n      this._waitingRejectFunctions.delete(uuid);\n    });\n    return pending(withFinally);\n  }\n\n  _setIfLatest(result: Result<T>, curCounter: number) {\n    const oldState = this.get();\n    const oldValue = this._mostRecentOkValue;\n    if (curCounter > this._lastSuccessfulUpdate) {\n      switch (result.status) {\n        case \"ok\": {\n          if (!this._isAvailable || this._isRejected || this._mostRecentOkValue !== result.data) {\n            this._lastSuccessfulUpdate = curCounter;\n            this._isAvailable = true;\n            this._isRejected = false;\n            this._mostRecentOkValue = result.data;\n            this._rejectionError = undefined;\n            this._callbacks.forEach((callback) => callback({\n              state: this.get(),\n              oldState,\n              lastOkValue: oldValue,\n            }));\n            return true;\n          }\n          return false;\n        }\n        case \"error\": {\n          this._lastSuccessfulUpdate = curCounter;\n          this._isAvailable = false;\n          this._isRejected = true;\n          this._rejectionError = result.error;\n          this._waitingRejectFunctions.forEach((reject) => reject(result.error));\n          this._callbacks.forEach((callback) => callback({\n            state: this.get(),\n            oldState,\n            lastOkValue: oldValue,\n          }));\n          return true;\n        }\n      }\n    }\n    return false;\n  }\n\n  set(value: T): void {\n    this._setIfLatest(Result.ok(value), ++this._updateCounter);\n  }\n\n  update(updater: (value: T | undefined) => T): T {\n    const value = updater(this._mostRecentOkValue);\n    this.set(value);\n    return value;\n  }\n\n  async setAsync(promise: Promise<T>): Promise<boolean> {\n    return await storeLock.withReadLock(async () => {\n      const curCounter = ++this._updateCounter;\n      const result = await Result.fromPromise(promise);\n      return this._setIfLatest(result, curCounter);\n    });\n  }\n\n  setUnavailable(): void {\n    this._lastSuccessfulUpdate = ++this._updateCounter;\n    this._isAvailable = false;\n    this._isRejected = false;\n    this._rejectionError = undefined;\n  }\n\n  setRejected(error: unknown): void {\n    this._setIfLatest(Result.error(error), ++this._updateCounter);\n  }\n\n  map<U>(mapper: (value: T) => U): AsyncStore<U> {\n    const store = new AsyncStore<U>();\n    this.onChange((value) => {\n      store.set(mapper(value));\n    });\n    return store;\n  }\n\n  onChange(callback: (value: T, oldValue: T | undefined) => void): { unsubscribe: () => void } {\n    return this.onStateChange(({ state, lastOkValue }) => {\n      if (state.status === \"ok\") {\n        callback(state.data, lastOkValue);\n      }\n    });\n  }\n\n  onStateChange(callback: AsyncStoreStateChangeCallback<T>): { unsubscribe: () => void } {\n    const uuid = generateUuid();\n    this._callbacks.set(uuid, callback);\n    return {\n      unsubscribe: () => {\n        this._callbacks.delete(uuid);\n      },\n    };\n  }\n\n  onceChange(callback: (value: T, oldValue: T | undefined) => void): { unsubscribe: () => void } {\n    const { unsubscribe } = this.onChange((...args) => {\n      unsubscribe();\n      callback(...args);\n    });\n    return { unsubscribe };\n  }\n\n  onceStateChange(callback: AsyncStoreStateChangeCallback<T>): { unsubscribe: () => void } {\n    const { unsubscribe } = this.onStateChange((...args) => {\n      unsubscribe();\n      callback(...args);\n    });\n    return { unsubscribe };\n  }\n}\n", "import * as jose from 'jose';\nimport { StackAssertionError } from \"./utils/errors\";\nimport { Store } from \"./utils/stores\";\n\nexport class AccessToken {\n  constructor(\n    public readonly token: string,\n  ) {\n    if (token === \"undefined\") {\n      throw new StackAssertionError(\"Access token is the string 'undefined'; it's unlikely this is the correct value. They're supposed to be unguessable!\");\n    }\n  }\n\n  get decoded() {\n    return jose.decodeJwt(this.token);\n  }\n\n  get expiresAt(): Date {\n    const { exp } = this.decoded;\n    if (exp === undefined) return new Date(8640000000000000);  // max date value\n    return new Date(exp * 1000);\n  }\n\n  /**\n   * @returns The number of milliseconds until the access token expires, or 0 if it has already expired.\n   */\n  get expiresInMillis(): number {\n    return Math.max(0, this.expiresAt.getTime() - Date.now());\n  }\n\n  isExpired(): boolean {\n    return this.expiresInMillis <= 0;\n  }\n}\n\nexport class RefreshToken {\n  constructor(\n    public readonly token: string,\n  ) {\n    if (token === \"undefined\") {\n      throw new StackAssertionError(\"Refresh token is the string 'undefined'; it's unlikely this is the correct value. They're supposed to be unguessable!\");\n    }\n  }\n}\n\n/**\n * An InternalSession represents a user's session, which may or may not be valid. It may contain an access token, a refresh token, or both.\n *\n * A session never changes which user or session it belongs to, but the tokens in it may change over time.\n */\nexport class InternalSession {\n  /**\n  * Each session has a session key that depends on the tokens inside. If the session has a refresh token, the session key depends only on the refresh token. If the session does not have a refresh token, the session key depends only on the access token.\n  *\n  * Multiple Session objects may have the same session key, which implies that they represent the same session by the same user. Furthermore, a session's key never changes over the lifetime of a session object.\n  *\n  * This is useful for caching and indexing sessions.\n  */\n  public readonly sessionKey: string;\n\n  /**\n   * An access token that is not known to be invalid (ie. may be valid, but may have expired).\n   */\n  private _accessToken: Store<AccessToken | null>;\n  private readonly _refreshToken: RefreshToken | null;\n\n  /**\n   * Whether the session as a whole is known to be invalid (ie. both access and refresh tokens are invalid). Used as a cache to avoid making multiple requests to the server (sessions never go back to being valid after being invalidated).\n   *\n   * It is possible for the access token to be invalid but the refresh token to be valid, in which case the session is\n   * still valid (just needs a refresh). It is also possible for the access token to be valid but the refresh token to\n   * be invalid, in which case the session is also valid (eg. if the refresh token is null because the user only passed\n   * in an access token, eg. in a server-side request handler).\n   */\n  private _knownToBeInvalid = new Store<boolean>(false);\n\n  private _refreshPromise: Promise<AccessToken | null> | null = null;\n\n  constructor(private readonly _options: {\n    refreshAccessTokenCallback(refreshToken: RefreshToken): Promise<AccessToken | null>,\n    refreshToken: string | null,\n    accessToken?: string | null,\n  }) {\n    this._accessToken = new Store(_options.accessToken ? new AccessToken(_options.accessToken) : null);\n    this._refreshToken = _options.refreshToken ? new RefreshToken(_options.refreshToken) : null;\n    if (_options.accessToken === null && _options.refreshToken === null) {\n      // this session is already invalid\n      this._knownToBeInvalid.set(true);\n    }\n    this.sessionKey = InternalSession.calculateSessionKey({ accessToken: _options.accessToken ?? null, refreshToken: _options.refreshToken });\n  }\n\n  static calculateSessionKey(ofTokens: { refreshToken: string | null, accessToken?: string | null }): string {\n    if (ofTokens.refreshToken) {\n      return `refresh-${ofTokens.refreshToken}`;\n    } else if (ofTokens.accessToken) {\n      return `access-${ofTokens.accessToken}`;\n    } else {\n      return \"not-logged-in\";\n    }\n  }\n\n  isKnownToBeInvalid() {\n    return this._knownToBeInvalid.get();\n  }\n\n  /**\n   * Marks the session object as invalid, meaning that the refresh and access tokens can no longer be used.\n   */\n  markInvalid() {\n    this._accessToken.set(null);\n    this._knownToBeInvalid.set(true);\n  }\n\n  onInvalidate(callback: () => void): { unsubscribe: () => void } {\n    return this._knownToBeInvalid.onChange(() => callback());\n  }\n\n  /**\n   * Returns the access token if it is found in the cache, fetching it otherwise.\n   *\n   * This is usually the function you want to call to get an access token. Either set `minMillisUntilExpiration` to a reasonable value, or catch errors that occur if it expires, and call `markAccessTokenExpired` to mark the token as expired if so (after which a call to this function will always refetch the token).\n   *\n   * @returns null if the session is known to be invalid, cached tokens if they exist in the cache (which may or may not be valid still), or new tokens otherwise.\n   */\n  async getOrFetchLikelyValidTokens(minMillisUntilExpiration: number): Promise<{ accessToken: AccessToken, refreshToken: RefreshToken | null } | null> {\n    if (minMillisUntilExpiration >= 60_000) {\n      throw new Error(`Required access token expiry ${minMillisUntilExpiration}ms is too long; access tokens are too short to be used for more than 60s`);\n    }\n\n    const accessToken = this._getPotentiallyInvalidAccessTokenIfAvailable();\n    if (!accessToken || accessToken.expiresInMillis < minMillisUntilExpiration) {\n      const newTokens = await this.fetchNewTokens();\n      const expiresInMillis = newTokens?.accessToken.expiresInMillis;\n      if (expiresInMillis && expiresInMillis < minMillisUntilExpiration) {\n        throw new StackAssertionError(`Required access token expiry ${minMillisUntilExpiration}ms is too long; access tokens are too short when they're generated (${expiresInMillis}ms)`);\n      }\n      return newTokens;\n    }\n    return { accessToken, refreshToken: this._refreshToken };\n  }\n\n  /**\n   * Fetches new tokens that are, at the time of fetching, guaranteed to be valid.\n   *\n   * The newly generated tokens are short-lived, so it's good practice not to rely on their validity (if possible). However, this function is useful in some cases where you only want to pass access tokens to a service, and you want to make sure said access token has the longest possible lifetime.\n   *\n   * In most cases, you should prefer `getOrFetchLikelyValidTokens`.\n   *\n   * @returns null if the session is known to be invalid, or new tokens otherwise (which, at the time of fetching, are guaranteed to be valid).\n   */\n  async fetchNewTokens(): Promise<{ accessToken: AccessToken, refreshToken: RefreshToken | null } | null> {\n    const accessToken = await this._getNewlyFetchedAccessToken();\n    return accessToken ? { accessToken, refreshToken: this._refreshToken } : null;\n  }\n\n  markAccessTokenExpired(accessToken: AccessToken) {\n    // TODO we don't need this anymore, since we now check the expiry by ourselves\n    if (this._accessToken.get() === accessToken) {\n      this._accessToken.set(null);\n    }\n  }\n\n  /**\n   * Note that a callback invocation with `null` does not mean the session has been invalidated; the access token may just have expired. Use `onInvalidate` to detect invalidation.\n   */\n  onAccessTokenChange(callback: (newAccessToken: AccessToken | null) => void): { unsubscribe: () => void } {\n    return this._accessToken.onChange(callback);\n  }\n\n  /**\n   * @returns An access token, which may be expired or expire soon, or null if it is known to be invalid.\n   */\n  private _getPotentiallyInvalidAccessTokenIfAvailable(): AccessToken | null {\n    if (!this._refreshToken) return null;\n    if (this.isKnownToBeInvalid()) return null;\n\n    const accessToken = this._accessToken.get();\n    if (accessToken && !accessToken.isExpired()) return accessToken;\n\n    return null;\n  }\n\n  /**\n   * You should prefer `_getOrFetchPotentiallyInvalidAccessToken` in almost all cases.\n   *\n   * @returns A newly fetched access token (never read from cache), or null if the session either does not represent a user or the session is invalid.\n   */\n  private async _getNewlyFetchedAccessToken(): Promise<AccessToken | null> {\n    if (!this._refreshToken) return null;\n    if (this._knownToBeInvalid.get()) return null;\n\n    if (!this._refreshPromise) {\n      this._refreshAndSetRefreshPromise(this._refreshToken);\n    }\n    return await this._refreshPromise;\n  }\n\n  private _refreshAndSetRefreshPromise(refreshToken: RefreshToken) {\n    let refreshPromise: Promise<AccessToken | null> = this._options.refreshAccessTokenCallback(refreshToken).then((accessToken) => {\n      if (refreshPromise === this._refreshPromise) {\n        this._refreshPromise = null;\n        this._accessToken.set(accessToken);\n        if (!accessToken) {\n          this.markInvalid();\n        }\n      }\n      return accessToken;\n    });\n    this._refreshPromise = refreshPromise;\n  }\n}\n", "import { decodeBase64, encodeBase64, isBase64 } from \"./bytes\";\n\nexport const HTTP_METHODS = {\n  \"GET\": {\n    safe: true,\n    idempotent: true,\n  },\n  \"POST\": {\n    safe: false,\n    idempotent: false,\n  },\n  \"PUT\": {\n    safe: false,\n    idempotent: true,\n  },\n  \"DELETE\": {\n    safe: false,\n    idempotent: true,\n  },\n  \"PATCH\": {\n    safe: false,\n    idempotent: false,\n  },\n  \"OPTIONS\": {\n    safe: true,\n    idempotent: true,\n  },\n  \"HEAD\": {\n    safe: true,\n    idempotent: true,\n  },\n  \"TRACE\": {\n    safe: true,\n    idempotent: true,\n  },\n  \"CONNECT\": {\n    safe: false,\n    idempotent: false,\n  },\n} as const;\nexport type HttpMethod = keyof typeof HTTP_METHODS;\n\nexport function decodeBasicAuthorizationHeader(value: string): [string, string] | null {\n  const [type, encoded, ...rest] = value.split(' ');\n  if (rest.length > 0) return null;\n  if (!encoded) return null;\n  if (type !== 'Basic') return null;\n  if (!isBase64(encoded)) return null;\n  const decoded = new TextDecoder().decode(decodeBase64(encoded));\n  const split = decoded.split(':');\n  return [split[0], split.slice(1).join(':')];\n}\nundefined?.test(\"decodeBasicAuthorizationHeader\", ({ expect }) => {\n  // Test with valid Basic Authorization header\n  const username = \"user\";\n  const password = \"pass\";\n  const encoded = encodeBasicAuthorizationHeader(username, password);\n  expect(decodeBasicAuthorizationHeader(encoded)).toEqual([username, password]);\n\n  // Test with password containing colons\n  const complexPassword = \"pass:with:colons\";\n  const encodedComplex = encodeBasicAuthorizationHeader(username, complexPassword);\n  expect(decodeBasicAuthorizationHeader(encodedComplex)).toEqual([username, complexPassword]);\n\n  // Test with invalid headers\n  expect(decodeBasicAuthorizationHeader(\"NotBasic dXNlcjpwYXNz\")).toBe(null); // Wrong type\n  expect(decodeBasicAuthorizationHeader(\"Basic\")).toBe(null); // Missing encoded part\n  expect(decodeBasicAuthorizationHeader(\"Basic not-base64\")).toBe(null); // Not base64\n  expect(decodeBasicAuthorizationHeader(\"Basic dXNlcjpwYXNz extra\")).toBe(null); // Extra parts\n});\n\nexport function encodeBasicAuthorizationHeader(id: string, password: string): string {\n  if (id.includes(':')) throw new Error(\"Basic authorization header id cannot contain ':'\");\n  return `Basic ${encodeBase64(new TextEncoder().encode(`${id}:${password}`))}`;\n}\nundefined?.test(\"encodeBasicAuthorizationHeader\", ({ expect }) => {\n  // Test with simple username and password\n  const encoded = encodeBasicAuthorizationHeader(\"user\", \"pass\");\n  expect(encoded).toMatch(/^Basic [A-Za-z0-9+/=]+$/); // Should start with \"Basic \" followed by base64\n\n  // Test with empty password\n  const encodedEmptyPass = encodeBasicAuthorizationHeader(\"user\", \"\");\n  expect(encodedEmptyPass).toMatch(/^Basic [A-Za-z0-9+/=]+$/);\n\n  // Test with password containing special characters\n  const encodedSpecialChars = encodeBasicAuthorizationHeader(\"user\", \"p@ss!w0rd\");\n  expect(encodedSpecialChars).toMatch(/^Basic [A-Za-z0-9+/=]+$/);\n\n  // Test with username containing colon should throw\n  expect(() => encodeBasicAuthorizationHeader(\"user:name\", \"pass\")).toThrow();\n});\n", "let USER_AGENT;\nif (typeof navigator === 'undefined' || !navigator.userAgent?.startsWith?.('Mozilla/5.0 ')) {\n    const NAME = 'oauth4webapi';\n    const VERSION = 'v2.10.4';\n    USER_AGENT = `${NAME}/${VERSION}`;\n}\nfunction looseInstanceOf(input, expected) {\n    if (input == null) {\n        return false;\n    }\n    try {\n        return (input instanceof expected ||\n            Object.getPrototypeOf(input)[Symbol.toStringTag] === expected.prototype[Symbol.toStringTag]);\n    }\n    catch {\n        return false;\n    }\n}\nexport const clockSkew = Symbol();\nexport const clockTolerance = Symbol();\nexport const customFetch = Symbol();\nexport const useMtlsAlias = Symbol();\nconst encoder = new TextEncoder();\nconst decoder = new TextDecoder();\nfunction buf(input) {\n    if (typeof input === 'string') {\n        return encoder.encode(input);\n    }\n    return decoder.decode(input);\n}\nconst CHUNK_SIZE = 0x8000;\nfunction encodeBase64Url(input) {\n    if (input instanceof ArrayBuffer) {\n        input = new Uint8Array(input);\n    }\n    const arr = [];\n    for (let i = 0; i < input.byteLength; i += CHUNK_SIZE) {\n        arr.push(String.fromCharCode.apply(null, input.subarray(i, i + CHUNK_SIZE)));\n    }\n    return btoa(arr.join('')).replace(/=/g, '').replace(/\\+/g, '-').replace(/\\//g, '_');\n}\nfunction decodeBase64Url(input) {\n    try {\n        const binary = atob(input.replace(/-/g, '+').replace(/_/g, '/').replace(/\\s/g, ''));\n        const bytes = new Uint8Array(binary.length);\n        for (let i = 0; i < binary.length; i++) {\n            bytes[i] = binary.charCodeAt(i);\n        }\n        return bytes;\n    }\n    catch (cause) {\n        throw new OPE('The input to be decoded is not correctly encoded.', { cause });\n    }\n}\nfunction b64u(input) {\n    if (typeof input === 'string') {\n        return decodeBase64Url(input);\n    }\n    return encodeBase64Url(input);\n}\nclass LRU {\n    constructor(maxSize) {\n        this.cache = new Map();\n        this._cache = new Map();\n        this.maxSize = maxSize;\n    }\n    get(key) {\n        let v = this.cache.get(key);\n        if (v) {\n            return v;\n        }\n        if ((v = this._cache.get(key))) {\n            this.update(key, v);\n            return v;\n        }\n        return undefined;\n    }\n    has(key) {\n        return this.cache.has(key) || this._cache.has(key);\n    }\n    set(key, value) {\n        if (this.cache.has(key)) {\n            this.cache.set(key, value);\n        }\n        else {\n            this.update(key, value);\n        }\n        return this;\n    }\n    delete(key) {\n        if (this.cache.has(key)) {\n            return this.cache.delete(key);\n        }\n        if (this._cache.has(key)) {\n            return this._cache.delete(key);\n        }\n        return false;\n    }\n    update(key, value) {\n        this.cache.set(key, value);\n        if (this.cache.size >= this.maxSize) {\n            this._cache = this.cache;\n            this.cache = new Map();\n        }\n    }\n}\nexport class UnsupportedOperationError extends Error {\n    constructor(message) {\n        super(message ?? 'operation not supported');\n        this.name = this.constructor.name;\n        Error.captureStackTrace?.(this, this.constructor);\n    }\n}\nexport class OperationProcessingError extends Error {\n    constructor(message, options) {\n        super(message, options);\n        this.name = this.constructor.name;\n        Error.captureStackTrace?.(this, this.constructor);\n    }\n}\nconst OPE = OperationProcessingError;\nconst dpopNonces = new LRU(100);\nfunction isCryptoKey(key) {\n    return key instanceof CryptoKey;\n}\nfunction isPrivateKey(key) {\n    return isCryptoKey(key) && key.type === 'private';\n}\nfunction isPublicKey(key) {\n    return isCryptoKey(key) && key.type === 'public';\n}\nconst SUPPORTED_JWS_ALGS = [\n    'PS256',\n    'ES256',\n    'RS256',\n    'PS384',\n    'ES384',\n    'RS384',\n    'PS512',\n    'ES512',\n    'RS512',\n    'EdDSA',\n];\nfunction processDpopNonce(response) {\n    try {\n        const nonce = response.headers.get('dpop-nonce');\n        if (nonce) {\n            dpopNonces.set(new URL(response.url).origin, nonce);\n        }\n    }\n    catch { }\n    return response;\n}\nfunction normalizeTyp(value) {\n    return value.toLowerCase().replace(/^application\\//, '');\n}\nfunction isJsonObject(input) {\n    if (input === null || typeof input !== 'object' || Array.isArray(input)) {\n        return false;\n    }\n    return true;\n}\nfunction prepareHeaders(input) {\n    if (looseInstanceOf(input, Headers)) {\n        input = Object.fromEntries(input.entries());\n    }\n    const headers = new Headers(input);\n    if (USER_AGENT && !headers.has('user-agent')) {\n        headers.set('user-agent', USER_AGENT);\n    }\n    if (headers.has('authorization')) {\n        throw new TypeError('\"options.headers\" must not include the \"authorization\" header name');\n    }\n    if (headers.has('dpop')) {\n        throw new TypeError('\"options.headers\" must not include the \"dpop\" header name');\n    }\n    return headers;\n}\nfunction signal(value) {\n    if (typeof value === 'function') {\n        value = value();\n    }\n    if (!(value instanceof AbortSignal)) {\n        throw new TypeError('\"options.signal\" must return or be an instance of AbortSignal');\n    }\n    return value;\n}\nexport async function discoveryRequest(issuerIdentifier, options) {\n    if (!(issuerIdentifier instanceof URL)) {\n        throw new TypeError('\"issuerIdentifier\" must be an instance of URL');\n    }\n    if (issuerIdentifier.protocol !== 'https:' && issuerIdentifier.protocol !== 'http:') {\n        throw new TypeError('\"issuer.protocol\" must be \"https:\" or \"http:\"');\n    }\n    const url = new URL(issuerIdentifier.href);\n    switch (options?.algorithm) {\n        case undefined:\n        case 'oidc':\n            url.pathname = `${url.pathname}/.well-known/openid-configuration`.replace('//', '/');\n            break;\n        case 'oauth2':\n            if (url.pathname === '/') {\n                url.pathname = '.well-known/oauth-authorization-server';\n            }\n            else {\n                url.pathname = `.well-known/oauth-authorization-server/${url.pathname}`.replace('//', '/');\n            }\n            break;\n        default:\n            throw new TypeError('\"options.algorithm\" must be \"oidc\" (default), or \"oauth2\"');\n    }\n    const headers = prepareHeaders(options?.headers);\n    headers.set('accept', 'application/json');\n    return (options?.[customFetch] || fetch)(url.href, {\n        headers: Object.fromEntries(headers.entries()),\n        method: 'GET',\n        redirect: 'manual',\n        signal: options?.signal ? signal(options.signal) : null,\n    }).then(processDpopNonce);\n}\nfunction validateString(input) {\n    return typeof input === 'string' && input.length !== 0;\n}\nexport async function processDiscoveryResponse(expectedIssuerIdentifier, response) {\n    if (!(expectedIssuerIdentifier instanceof URL)) {\n        throw new TypeError('\"expectedIssuer\" must be an instance of URL');\n    }\n    if (!looseInstanceOf(response, Response)) {\n        throw new TypeError('\"response\" must be an instance of Response');\n    }\n    if (response.status !== 200) {\n        throw new OPE('\"response\" is not a conform Authorization Server Metadata response');\n    }\n    assertReadableResponse(response);\n    let json;\n    try {\n        json = await response.json();\n    }\n    catch (cause) {\n        throw new OPE('failed to parse \"response\" body as JSON', { cause });\n    }\n    if (!isJsonObject(json)) {\n        throw new OPE('\"response\" body must be a top level object');\n    }\n    if (!validateString(json.issuer)) {\n        throw new OPE('\"response\" body \"issuer\" property must be a non-empty string');\n    }\n    if (new URL(json.issuer).href !== expectedIssuerIdentifier.href) {\n        throw new OPE('\"response\" body \"issuer\" does not match \"expectedIssuer\"');\n    }\n    return json;\n}\nfunction randomBytes() {\n    return b64u(crypto.getRandomValues(new Uint8Array(32)));\n}\nexport function generateRandomCodeVerifier() {\n    return randomBytes();\n}\nexport function generateRandomState() {\n    return randomBytes();\n}\nexport function generateRandomNonce() {\n    return randomBytes();\n}\nexport async function calculatePKCECodeChallenge(codeVerifier) {\n    if (!validateString(codeVerifier)) {\n        throw new TypeError('\"codeVerifier\" must be a non-empty string');\n    }\n    return b64u(await crypto.subtle.digest('SHA-256', buf(codeVerifier)));\n}\nfunction getKeyAndKid(input) {\n    if (input instanceof CryptoKey) {\n        return { key: input };\n    }\n    if (!(input?.key instanceof CryptoKey)) {\n        return {};\n    }\n    if (input.kid !== undefined && !validateString(input.kid)) {\n        throw new TypeError('\"kid\" must be a non-empty string');\n    }\n    return { key: input.key, kid: input.kid };\n}\nfunction formUrlEncode(token) {\n    return encodeURIComponent(token).replace(/%20/g, '+');\n}\nfunction clientSecretBasic(clientId, clientSecret) {\n    const username = formUrlEncode(clientId);\n    const password = formUrlEncode(clientSecret);\n    const credentials = btoa(`${username}:${password}`);\n    return `Basic ${credentials}`;\n}\nfunction psAlg(key) {\n    switch (key.algorithm.hash.name) {\n        case 'SHA-256':\n            return 'PS256';\n        case 'SHA-384':\n            return 'PS384';\n        case 'SHA-512':\n            return 'PS512';\n        default:\n            throw new UnsupportedOperationError('unsupported RsaHashedKeyAlgorithm hash name');\n    }\n}\nfunction rsAlg(key) {\n    switch (key.algorithm.hash.name) {\n        case 'SHA-256':\n            return 'RS256';\n        case 'SHA-384':\n            return 'RS384';\n        case 'SHA-512':\n            return 'RS512';\n        default:\n            throw new UnsupportedOperationError('unsupported RsaHashedKeyAlgorithm hash name');\n    }\n}\nfunction esAlg(key) {\n    switch (key.algorithm.namedCurve) {\n        case 'P-256':\n            return 'ES256';\n        case 'P-384':\n            return 'ES384';\n        case 'P-521':\n            return 'ES512';\n        default:\n            throw new UnsupportedOperationError('unsupported EcKeyAlgorithm namedCurve');\n    }\n}\nfunction keyToJws(key) {\n    switch (key.algorithm.name) {\n        case 'RSA-PSS':\n            return psAlg(key);\n        case 'RSASSA-PKCS1-v1_5':\n            return rsAlg(key);\n        case 'ECDSA':\n            return esAlg(key);\n        case 'Ed25519':\n        case 'Ed448':\n            return 'EdDSA';\n        default:\n            throw new UnsupportedOperationError('unsupported CryptoKey algorithm name');\n    }\n}\nfunction getClockSkew(client) {\n    const skew = client?.[clockSkew];\n    return typeof skew === 'number' && Number.isFinite(skew) ? skew : 0;\n}\nfunction getClockTolerance(client) {\n    const tolerance = client?.[clockTolerance];\n    return typeof tolerance === 'number' && Number.isFinite(tolerance) && Math.sign(tolerance) !== -1\n        ? tolerance\n        : 30;\n}\nfunction epochTime() {\n    return Math.floor(Date.now() / 1000);\n}\nfunction clientAssertion(as, client) {\n    const now = epochTime() + getClockSkew(client);\n    return {\n        jti: randomBytes(),\n        aud: [as.issuer, as.token_endpoint],\n        exp: now + 60,\n        iat: now,\n        nbf: now,\n        iss: client.client_id,\n        sub: client.client_id,\n    };\n}\nasync function privateKeyJwt(as, client, key, kid) {\n    return jwt({\n        alg: keyToJws(key),\n        kid,\n    }, clientAssertion(as, client), key);\n}\nfunction assertAs(as) {\n    if (typeof as !== 'object' || as === null) {\n        throw new TypeError('\"as\" must be an object');\n    }\n    if (!validateString(as.issuer)) {\n        throw new TypeError('\"as.issuer\" property must be a non-empty string');\n    }\n    return true;\n}\nfunction assertClient(client) {\n    if (typeof client !== 'object' || client === null) {\n        throw new TypeError('\"client\" must be an object');\n    }\n    if (!validateString(client.client_id)) {\n        throw new TypeError('\"client.client_id\" property must be a non-empty string');\n    }\n    return true;\n}\nfunction assertClientSecret(clientSecret) {\n    if (!validateString(clientSecret)) {\n        throw new TypeError('\"client.client_secret\" property must be a non-empty string');\n    }\n    return clientSecret;\n}\nfunction assertNoClientPrivateKey(clientAuthMethod, clientPrivateKey) {\n    if (clientPrivateKey !== undefined) {\n        throw new TypeError(`\"options.clientPrivateKey\" property must not be provided when ${clientAuthMethod} client authentication method is used.`);\n    }\n}\nfunction assertNoClientSecret(clientAuthMethod, clientSecret) {\n    if (clientSecret !== undefined) {\n        throw new TypeError(`\"client.client_secret\" property must not be provided when ${clientAuthMethod} client authentication method is used.`);\n    }\n}\nasync function clientAuthentication(as, client, body, headers, clientPrivateKey) {\n    body.delete('client_secret');\n    body.delete('client_assertion_type');\n    body.delete('client_assertion');\n    switch (client.token_endpoint_auth_method) {\n        case undefined:\n        case 'client_secret_basic': {\n            assertNoClientPrivateKey('client_secret_basic', clientPrivateKey);\n            headers.set('authorization', clientSecretBasic(client.client_id, assertClientSecret(client.client_secret)));\n            break;\n        }\n        case 'client_secret_post': {\n            assertNoClientPrivateKey('client_secret_post', clientPrivateKey);\n            body.set('client_id', client.client_id);\n            body.set('client_secret', assertClientSecret(client.client_secret));\n            break;\n        }\n        case 'private_key_jwt': {\n            assertNoClientSecret('private_key_jwt', client.client_secret);\n            if (clientPrivateKey === undefined) {\n                throw new TypeError('\"options.clientPrivateKey\" must be provided when \"client.token_endpoint_auth_method\" is \"private_key_jwt\"');\n            }\n            const { key, kid } = getKeyAndKid(clientPrivateKey);\n            if (!isPrivateKey(key)) {\n                throw new TypeError('\"options.clientPrivateKey.key\" must be a private CryptoKey');\n            }\n            body.set('client_id', client.client_id);\n            body.set('client_assertion_type', 'urn:ietf:params:oauth:client-assertion-type:jwt-bearer');\n            body.set('client_assertion', await privateKeyJwt(as, client, key, kid));\n            break;\n        }\n        case 'tls_client_auth':\n        case 'self_signed_tls_client_auth':\n        case 'none': {\n            assertNoClientSecret(client.token_endpoint_auth_method, client.client_secret);\n            assertNoClientPrivateKey(client.token_endpoint_auth_method, clientPrivateKey);\n            body.set('client_id', client.client_id);\n            break;\n        }\n        default:\n            throw new UnsupportedOperationError('unsupported client token_endpoint_auth_method');\n    }\n}\nasync function jwt(header, claimsSet, key) {\n    if (!key.usages.includes('sign')) {\n        throw new TypeError('CryptoKey instances used for signing assertions must include \"sign\" in their \"usages\"');\n    }\n    const input = `${b64u(buf(JSON.stringify(header)))}.${b64u(buf(JSON.stringify(claimsSet)))}`;\n    const signature = b64u(await crypto.subtle.sign(keyToSubtle(key), key, buf(input)));\n    return `${input}.${signature}`;\n}\nexport async function issueRequestObject(as, client, parameters, privateKey) {\n    assertAs(as);\n    assertClient(client);\n    parameters = new URLSearchParams(parameters);\n    const { key, kid } = getKeyAndKid(privateKey);\n    if (!isPrivateKey(key)) {\n        throw new TypeError('\"privateKey.key\" must be a private CryptoKey');\n    }\n    parameters.set('client_id', client.client_id);\n    const now = epochTime() + getClockSkew(client);\n    const claims = {\n        ...Object.fromEntries(parameters.entries()),\n        jti: randomBytes(),\n        aud: as.issuer,\n        exp: now + 60,\n        iat: now,\n        nbf: now,\n        iss: client.client_id,\n    };\n    let resource;\n    if (parameters.has('resource') &&\n        (resource = parameters.getAll('resource')) &&\n        resource.length > 1) {\n        claims.resource = resource;\n    }\n    {\n        let value = parameters.get('max_age');\n        if (value !== null) {\n            claims.max_age = parseInt(value, 10);\n            if (!Number.isFinite(claims.max_age)) {\n                throw new OPE('\"max_age\" parameter must be a number');\n            }\n        }\n    }\n    {\n        let value = parameters.get('claims');\n        if (value !== null) {\n            try {\n                claims.claims = JSON.parse(value);\n            }\n            catch (cause) {\n                throw new OPE('failed to parse the \"claims\" parameter as JSON', { cause });\n            }\n            if (!isJsonObject(claims.claims)) {\n                throw new OPE('\"claims\" parameter must be a JSON with a top level object');\n            }\n        }\n    }\n    {\n        let value = parameters.get('authorization_details');\n        if (value !== null) {\n            try {\n                claims.authorization_details = JSON.parse(value);\n            }\n            catch (cause) {\n                throw new OPE('failed to parse the \"authorization_details\" parameter as JSON', { cause });\n            }\n            if (!Array.isArray(claims.authorization_details)) {\n                throw new OPE('\"authorization_details\" parameter must be a JSON with a top level array');\n            }\n        }\n    }\n    return jwt({\n        alg: keyToJws(key),\n        typ: 'oauth-authz-req+jwt',\n        kid,\n    }, claims, key);\n}\nasync function dpopProofJwt(headers, options, url, htm, clockSkew, accessToken) {\n    const { privateKey, publicKey, nonce = dpopNonces.get(url.origin) } = options;\n    if (!isPrivateKey(privateKey)) {\n        throw new TypeError('\"DPoP.privateKey\" must be a private CryptoKey');\n    }\n    if (!isPublicKey(publicKey)) {\n        throw new TypeError('\"DPoP.publicKey\" must be a public CryptoKey');\n    }\n    if (nonce !== undefined && !validateString(nonce)) {\n        throw new TypeError('\"DPoP.nonce\" must be a non-empty string or undefined');\n    }\n    if (!publicKey.extractable) {\n        throw new TypeError('\"DPoP.publicKey.extractable\" must be true');\n    }\n    const now = epochTime() + clockSkew;\n    const proof = await jwt({\n        alg: keyToJws(privateKey),\n        typ: 'dpop+jwt',\n        jwk: await publicJwk(publicKey),\n    }, {\n        iat: now,\n        jti: randomBytes(),\n        htm,\n        nonce,\n        htu: `${url.origin}${url.pathname}`,\n        ath: accessToken ? b64u(await crypto.subtle.digest('SHA-256', buf(accessToken))) : undefined,\n    }, privateKey);\n    headers.set('dpop', proof);\n}\nlet jwkCache;\nasync function getSetPublicJwkCache(key) {\n    const { kty, e, n, x, y, crv } = await crypto.subtle.exportKey('jwk', key);\n    const jwk = { kty, e, n, x, y, crv };\n    jwkCache.set(key, jwk);\n    return jwk;\n}\nasync function publicJwk(key) {\n    jwkCache || (jwkCache = new WeakMap());\n    return jwkCache.get(key) || getSetPublicJwkCache(key);\n}\nfunction validateEndpoint(value, endpoint, options) {\n    if (typeof value !== 'string') {\n        if (options?.[useMtlsAlias]) {\n            throw new TypeError(`\"as.mtls_endpoint_aliases.${endpoint}\" must be a string`);\n        }\n        throw new TypeError(`\"as.${endpoint}\" must be a string`);\n    }\n    return new URL(value);\n}\nfunction resolveEndpoint(as, endpoint, options) {\n    if (options?.[useMtlsAlias] && as.mtls_endpoint_aliases && endpoint in as.mtls_endpoint_aliases) {\n        return validateEndpoint(as.mtls_endpoint_aliases[endpoint], endpoint, options);\n    }\n    return validateEndpoint(as[endpoint], endpoint);\n}\nexport async function pushedAuthorizationRequest(as, client, parameters, options) {\n    assertAs(as);\n    assertClient(client);\n    const url = resolveEndpoint(as, 'pushed_authorization_request_endpoint', options);\n    const body = new URLSearchParams(parameters);\n    body.set('client_id', client.client_id);\n    const headers = prepareHeaders(options?.headers);\n    headers.set('accept', 'application/json');\n    if (options?.DPoP !== undefined) {\n        await dpopProofJwt(headers, options.DPoP, url, 'POST', getClockSkew(client));\n    }\n    return authenticatedRequest(as, client, 'POST', url, body, headers, options);\n}\nexport function isOAuth2Error(input) {\n    const value = input;\n    if (typeof value !== 'object' || Array.isArray(value) || value === null) {\n        return false;\n    }\n    return value.error !== undefined;\n}\nfunction unquote(value) {\n    if (value.length >= 2 && value[0] === '\"' && value[value.length - 1] === '\"') {\n        return value.slice(1, -1);\n    }\n    return value;\n}\nconst SPLIT_REGEXP = /((?:,|, )?[0-9a-zA-Z!#$%&'*+-.^_`|~]+=)/;\nconst SCHEMES_REGEXP = /(?:^|, ?)([0-9a-zA-Z!#$%&'*+\\-.^_`|~]+)(?=$|[ ,])/g;\nfunction wwwAuth(scheme, params) {\n    const arr = params.split(SPLIT_REGEXP).slice(1);\n    if (!arr.length) {\n        return { scheme: scheme.toLowerCase(), parameters: {} };\n    }\n    arr[arr.length - 1] = arr[arr.length - 1].replace(/,$/, '');\n    const parameters = {};\n    for (let i = 1; i < arr.length; i += 2) {\n        const idx = i;\n        if (arr[idx][0] === '\"') {\n            while (arr[idx].slice(-1) !== '\"' && ++i < arr.length) {\n                arr[idx] += arr[i];\n            }\n        }\n        const key = arr[idx - 1].replace(/^(?:, ?)|=$/g, '').toLowerCase();\n        parameters[key] = unquote(arr[idx]);\n    }\n    return {\n        scheme: scheme.toLowerCase(),\n        parameters,\n    };\n}\nexport function parseWwwAuthenticateChallenges(response) {\n    if (!looseInstanceOf(response, Response)) {\n        throw new TypeError('\"response\" must be an instance of Response');\n    }\n    const header = response.headers.get('www-authenticate');\n    if (header === null) {\n        return undefined;\n    }\n    const result = [];\n    for (const { 1: scheme, index } of header.matchAll(SCHEMES_REGEXP)) {\n        result.push([scheme, index]);\n    }\n    if (!result.length) {\n        return undefined;\n    }\n    const challenges = result.map(([scheme, indexOf], i, others) => {\n        const next = others[i + 1];\n        let parameters;\n        if (next) {\n            parameters = header.slice(indexOf, next[1]);\n        }\n        else {\n            parameters = header.slice(indexOf);\n        }\n        return wwwAuth(scheme, parameters);\n    });\n    return challenges;\n}\nexport async function processPushedAuthorizationResponse(as, client, response) {\n    assertAs(as);\n    assertClient(client);\n    if (!looseInstanceOf(response, Response)) {\n        throw new TypeError('\"response\" must be an instance of Response');\n    }\n    if (response.status !== 201) {\n        let err;\n        if ((err = await handleOAuthBodyError(response))) {\n            return err;\n        }\n        throw new OPE('\"response\" is not a conform Pushed Authorization Request Endpoint response');\n    }\n    assertReadableResponse(response);\n    let json;\n    try {\n        json = await response.json();\n    }\n    catch (cause) {\n        throw new OPE('failed to parse \"response\" body as JSON', { cause });\n    }\n    if (!isJsonObject(json)) {\n        throw new OPE('\"response\" body must be a top level object');\n    }\n    if (!validateString(json.request_uri)) {\n        throw new OPE('\"response\" body \"request_uri\" property must be a non-empty string');\n    }\n    if (typeof json.expires_in !== 'number' || json.expires_in <= 0) {\n        throw new OPE('\"response\" body \"expires_in\" property must be a positive number');\n    }\n    return json;\n}\nexport async function protectedResourceRequest(accessToken, method, url, headers, body, options) {\n    if (!validateString(accessToken)) {\n        throw new TypeError('\"accessToken\" must be a non-empty string');\n    }\n    if (!(url instanceof URL)) {\n        throw new TypeError('\"url\" must be an instance of URL');\n    }\n    headers = prepareHeaders(headers);\n    if (options?.DPoP === undefined) {\n        headers.set('authorization', `Bearer ${accessToken}`);\n    }\n    else {\n        await dpopProofJwt(headers, options.DPoP, url, 'GET', getClockSkew({ [clockSkew]: options?.[clockSkew] }), accessToken);\n        headers.set('authorization', `DPoP ${accessToken}`);\n    }\n    return (options?.[customFetch] || fetch)(url.href, {\n        body,\n        headers: Object.fromEntries(headers.entries()),\n        method,\n        redirect: 'manual',\n        signal: options?.signal ? signal(options.signal) : null,\n    }).then(processDpopNonce);\n}\nexport async function userInfoRequest(as, client, accessToken, options) {\n    assertAs(as);\n    assertClient(client);\n    const url = resolveEndpoint(as, 'userinfo_endpoint', options);\n    const headers = prepareHeaders(options?.headers);\n    if (client.userinfo_signed_response_alg) {\n        headers.set('accept', 'application/jwt');\n    }\n    else {\n        headers.set('accept', 'application/json');\n        headers.append('accept', 'application/jwt');\n    }\n    return protectedResourceRequest(accessToken, 'GET', url, headers, null, {\n        ...options,\n        [clockSkew]: getClockSkew(client),\n    });\n}\nlet jwksCache;\nasync function getPublicSigKeyFromIssuerJwksUri(as, options, header) {\n    const { alg, kid } = header;\n    checkSupportedJwsAlg(alg);\n    let jwks;\n    let age;\n    jwksCache || (jwksCache = new WeakMap());\n    if (jwksCache.has(as)) {\n        ;\n        ({ jwks, age } = jwksCache.get(as));\n        if (age >= 300) {\n            jwksCache.delete(as);\n            return getPublicSigKeyFromIssuerJwksUri(as, options, header);\n        }\n    }\n    else {\n        jwks = await jwksRequest(as, options).then(processJwksResponse);\n        age = 0;\n        jwksCache.set(as, {\n            jwks,\n            iat: epochTime(),\n            get age() {\n                return epochTime() - this.iat;\n            },\n        });\n    }\n    let kty;\n    switch (alg.slice(0, 2)) {\n        case 'RS':\n        case 'PS':\n            kty = 'RSA';\n            break;\n        case 'ES':\n            kty = 'EC';\n            break;\n        case 'Ed':\n            kty = 'OKP';\n            break;\n        default:\n            throw new UnsupportedOperationError();\n    }\n    const candidates = jwks.keys.filter((jwk) => {\n        if (jwk.kty !== kty) {\n            return false;\n        }\n        if (kid !== undefined && kid !== jwk.kid) {\n            return false;\n        }\n        if (jwk.alg !== undefined && alg !== jwk.alg) {\n            return false;\n        }\n        if (jwk.use !== undefined && jwk.use !== 'sig') {\n            return false;\n        }\n        if (jwk.key_ops?.includes('verify') === false) {\n            return false;\n        }\n        switch (true) {\n            case alg === 'ES256' && jwk.crv !== 'P-256':\n            case alg === 'ES384' && jwk.crv !== 'P-384':\n            case alg === 'ES512' && jwk.crv !== 'P-521':\n            case alg === 'EdDSA' && !(jwk.crv === 'Ed25519' || jwk.crv === 'Ed448'):\n                return false;\n        }\n        return true;\n    });\n    const { 0: jwk, length } = candidates;\n    if (!length) {\n        if (age >= 60) {\n            jwksCache.delete(as);\n            return getPublicSigKeyFromIssuerJwksUri(as, options, header);\n        }\n        throw new OPE('error when selecting a JWT verification key, no applicable keys found');\n    }\n    if (length !== 1) {\n        throw new OPE('error when selecting a JWT verification key, multiple applicable keys found, a \"kid\" JWT Header Parameter is required');\n    }\n    const key = await importJwk(alg, jwk);\n    if (key.type !== 'public') {\n        throw new OPE('jwks_uri must only contain public keys');\n    }\n    return key;\n}\nexport const skipSubjectCheck = Symbol();\nfunction getContentType(response) {\n    return response.headers.get('content-type')?.split(';')[0];\n}\nexport async function processUserInfoResponse(as, client, expectedSubject, response) {\n    assertAs(as);\n    assertClient(client);\n    if (!looseInstanceOf(response, Response)) {\n        throw new TypeError('\"response\" must be an instance of Response');\n    }\n    if (response.status !== 200) {\n        throw new OPE('\"response\" is not a conform UserInfo Endpoint response');\n    }\n    let json;\n    if (getContentType(response) === 'application/jwt') {\n        assertReadableResponse(response);\n        const { claims } = await validateJwt(await response.text(), checkSigningAlgorithm.bind(undefined, client.userinfo_signed_response_alg, as.userinfo_signing_alg_values_supported), noSignatureCheck, getClockSkew(client), getClockTolerance(client))\n            .then(validateOptionalAudience.bind(undefined, client.client_id))\n            .then(validateOptionalIssuer.bind(undefined, as.issuer));\n        json = claims;\n    }\n    else {\n        if (client.userinfo_signed_response_alg) {\n            throw new OPE('JWT UserInfo Response expected');\n        }\n        assertReadableResponse(response);\n        try {\n            json = await response.json();\n        }\n        catch (cause) {\n            throw new OPE('failed to parse \"response\" body as JSON', { cause });\n        }\n    }\n    if (!isJsonObject(json)) {\n        throw new OPE('\"response\" body must be a top level object');\n    }\n    if (!validateString(json.sub)) {\n        throw new OPE('\"response\" body \"sub\" property must be a non-empty string');\n    }\n    switch (expectedSubject) {\n        case skipSubjectCheck:\n            break;\n        default:\n            if (!validateString(expectedSubject)) {\n                throw new OPE('\"expectedSubject\" must be a non-empty string');\n            }\n            if (json.sub !== expectedSubject) {\n                throw new OPE('unexpected \"response\" body \"sub\" value');\n            }\n    }\n    return json;\n}\nasync function authenticatedRequest(as, client, method, url, body, headers, options) {\n    await clientAuthentication(as, client, body, headers, options?.clientPrivateKey);\n    headers.set('content-type', 'application/x-www-form-urlencoded;charset=UTF-8');\n    return (options?.[customFetch] || fetch)(url.href, {\n        body,\n        headers: Object.fromEntries(headers.entries()),\n        method,\n        redirect: 'manual',\n        signal: options?.signal ? signal(options.signal) : null,\n    }).then(processDpopNonce);\n}\nasync function tokenEndpointRequest(as, client, grantType, parameters, options) {\n    const url = resolveEndpoint(as, 'token_endpoint', options);\n    parameters.set('grant_type', grantType);\n    const headers = prepareHeaders(options?.headers);\n    headers.set('accept', 'application/json');\n    if (options?.DPoP !== undefined) {\n        await dpopProofJwt(headers, options.DPoP, url, 'POST', getClockSkew(client));\n    }\n    return authenticatedRequest(as, client, 'POST', url, parameters, headers, options);\n}\nexport async function refreshTokenGrantRequest(as, client, refreshToken, options) {\n    assertAs(as);\n    assertClient(client);\n    if (!validateString(refreshToken)) {\n        throw new TypeError('\"refreshToken\" must be a non-empty string');\n    }\n    const parameters = new URLSearchParams(options?.additionalParameters);\n    parameters.set('refresh_token', refreshToken);\n    return tokenEndpointRequest(as, client, 'refresh_token', parameters, options);\n}\nconst idTokenClaims = new WeakMap();\nexport function getValidatedIdTokenClaims(ref) {\n    if (!ref.id_token) {\n        return undefined;\n    }\n    const claims = idTokenClaims.get(ref);\n    if (!claims) {\n        throw new TypeError('\"ref\" was already garbage collected or did not resolve from the proper sources');\n    }\n    return claims;\n}\nasync function processGenericAccessTokenResponse(as, client, response, ignoreIdToken = false, ignoreRefreshToken = false) {\n    assertAs(as);\n    assertClient(client);\n    if (!looseInstanceOf(response, Response)) {\n        throw new TypeError('\"response\" must be an instance of Response');\n    }\n    if (response.status !== 200) {\n        let err;\n        if ((err = await handleOAuthBodyError(response))) {\n            return err;\n        }\n        throw new OPE('\"response\" is not a conform Token Endpoint response');\n    }\n    assertReadableResponse(response);\n    let json;\n    try {\n        json = await response.json();\n    }\n    catch (cause) {\n        throw new OPE('failed to parse \"response\" body as JSON', { cause });\n    }\n    if (!isJsonObject(json)) {\n        throw new OPE('\"response\" body must be a top level object');\n    }\n    if (!validateString(json.access_token)) {\n        throw new OPE('\"response\" body \"access_token\" property must be a non-empty string');\n    }\n    if (!validateString(json.token_type)) {\n        throw new OPE('\"response\" body \"token_type\" property must be a non-empty string');\n    }\n    json.token_type = json.token_type.toLowerCase();\n    if (json.token_type !== 'dpop' && json.token_type !== 'bearer') {\n        throw new UnsupportedOperationError('unsupported `token_type` value');\n    }\n    if (json.expires_in !== undefined &&\n        (typeof json.expires_in !== 'number' || json.expires_in <= 0)) {\n        throw new OPE('\"response\" body \"expires_in\" property must be a positive number');\n    }\n    if (!ignoreRefreshToken &&\n        json.refresh_token !== undefined &&\n        !validateString(json.refresh_token)) {\n        throw new OPE('\"response\" body \"refresh_token\" property must be a non-empty string');\n    }\n    if (json.scope !== undefined && typeof json.scope !== 'string') {\n        throw new OPE('\"response\" body \"scope\" property must be a string');\n    }\n    if (!ignoreIdToken) {\n        if (json.id_token !== undefined && !validateString(json.id_token)) {\n            throw new OPE('\"response\" body \"id_token\" property must be a non-empty string');\n        }\n        if (json.id_token) {\n            const { claims } = await validateJwt(json.id_token, checkSigningAlgorithm.bind(undefined, client.id_token_signed_response_alg, as.id_token_signing_alg_values_supported), noSignatureCheck, getClockSkew(client), getClockTolerance(client))\n                .then(validatePresence.bind(undefined, ['aud', 'exp', 'iat', 'iss', 'sub']))\n                .then(validateIssuer.bind(undefined, as.issuer))\n                .then(validateAudience.bind(undefined, client.client_id));\n            if (Array.isArray(claims.aud) && claims.aud.length !== 1 && claims.azp !== client.client_id) {\n                throw new OPE('unexpected ID Token \"azp\" (authorized party) claim value');\n            }\n            if (client.require_auth_time && typeof claims.auth_time !== 'number') {\n                throw new OPE('unexpected ID Token \"auth_time\" (authentication time) claim value');\n            }\n            idTokenClaims.set(json, claims);\n        }\n    }\n    return json;\n}\nexport async function processRefreshTokenResponse(as, client, response) {\n    return processGenericAccessTokenResponse(as, client, response);\n}\nfunction validateOptionalAudience(expected, result) {\n    if (result.claims.aud !== undefined) {\n        return validateAudience(expected, result);\n    }\n    return result;\n}\nfunction validateAudience(expected, result) {\n    if (Array.isArray(result.claims.aud)) {\n        if (!result.claims.aud.includes(expected)) {\n            throw new OPE('unexpected JWT \"aud\" (audience) claim value');\n        }\n    }\n    else if (result.claims.aud !== expected) {\n        throw new OPE('unexpected JWT \"aud\" (audience) claim value');\n    }\n    return result;\n}\nfunction validateOptionalIssuer(expected, result) {\n    if (result.claims.iss !== undefined) {\n        return validateIssuer(expected, result);\n    }\n    return result;\n}\nfunction validateIssuer(expected, result) {\n    if (result.claims.iss !== expected) {\n        throw new OPE('unexpected JWT \"iss\" (issuer) claim value');\n    }\n    return result;\n}\nconst branded = new WeakSet();\nfunction brand(searchParams) {\n    branded.add(searchParams);\n    return searchParams;\n}\nexport async function authorizationCodeGrantRequest(as, client, callbackParameters, redirectUri, codeVerifier, options) {\n    assertAs(as);\n    assertClient(client);\n    if (!branded.has(callbackParameters)) {\n        throw new TypeError('\"callbackParameters\" must be an instance of URLSearchParams obtained from \"validateAuthResponse()\", or \"validateJwtAuthResponse()');\n    }\n    if (!validateString(redirectUri)) {\n        throw new TypeError('\"redirectUri\" must be a non-empty string');\n    }\n    if (!validateString(codeVerifier)) {\n        throw new TypeError('\"codeVerifier\" must be a non-empty string');\n    }\n    const code = getURLSearchParameter(callbackParameters, 'code');\n    if (!code) {\n        throw new OPE('no authorization code in \"callbackParameters\"');\n    }\n    const parameters = new URLSearchParams(options?.additionalParameters);\n    parameters.set('redirect_uri', redirectUri);\n    parameters.set('code_verifier', codeVerifier);\n    parameters.set('code', code);\n    return tokenEndpointRequest(as, client, 'authorization_code', parameters, options);\n}\nconst jwtClaimNames = {\n    aud: 'audience',\n    c_hash: 'code hash',\n    client_id: 'client id',\n    exp: 'expiration time',\n    iat: 'issued at',\n    iss: 'issuer',\n    jti: 'jwt id',\n    nonce: 'nonce',\n    s_hash: 'state hash',\n    sub: 'subject',\n    ath: 'access token hash',\n    htm: 'http method',\n    htu: 'http uri',\n    cnf: 'confirmation',\n};\nfunction validatePresence(required, result) {\n    for (const claim of required) {\n        if (result.claims[claim] === undefined) {\n            throw new OPE(`JWT \"${claim}\" (${jwtClaimNames[claim]}) claim missing`);\n        }\n    }\n    return result;\n}\nexport const expectNoNonce = Symbol();\nexport const skipAuthTimeCheck = Symbol();\nexport async function processAuthorizationCodeOpenIDResponse(as, client, response, expectedNonce, maxAge) {\n    const result = await processGenericAccessTokenResponse(as, client, response);\n    if (isOAuth2Error(result)) {\n        return result;\n    }\n    if (!validateString(result.id_token)) {\n        throw new OPE('\"response\" body \"id_token\" property must be a non-empty string');\n    }\n    maxAge ?? (maxAge = client.default_max_age ?? skipAuthTimeCheck);\n    const claims = getValidatedIdTokenClaims(result);\n    if ((client.require_auth_time || maxAge !== skipAuthTimeCheck) &&\n        claims.auth_time === undefined) {\n        throw new OPE('ID Token \"auth_time\" (authentication time) claim missing');\n    }\n    if (maxAge !== skipAuthTimeCheck) {\n        if (typeof maxAge !== 'number' || maxAge < 0) {\n            throw new TypeError('\"options.max_age\" must be a non-negative number');\n        }\n        const now = epochTime() + getClockSkew(client);\n        const tolerance = getClockTolerance(client);\n        if (claims.auth_time + maxAge < now - tolerance) {\n            throw new OPE('too much time has elapsed since the last End-User authentication');\n        }\n    }\n    switch (expectedNonce) {\n        case undefined:\n        case expectNoNonce:\n            if (claims.nonce !== undefined) {\n                throw new OPE('unexpected ID Token \"nonce\" claim value');\n            }\n            break;\n        default:\n            if (!validateString(expectedNonce)) {\n                throw new TypeError('\"expectedNonce\" must be a non-empty string');\n            }\n            if (claims.nonce === undefined) {\n                throw new OPE('ID Token \"nonce\" claim missing');\n            }\n            if (claims.nonce !== expectedNonce) {\n                throw new OPE('unexpected ID Token \"nonce\" claim value');\n            }\n    }\n    return result;\n}\nexport async function processAuthorizationCodeOAuth2Response(as, client, response) {\n    const result = await processGenericAccessTokenResponse(as, client, response, true);\n    if (isOAuth2Error(result)) {\n        return result;\n    }\n    if (result.id_token !== undefined) {\n        if (typeof result.id_token === 'string' && result.id_token.length) {\n            throw new OPE('Unexpected ID Token returned, use processAuthorizationCodeOpenIDResponse() for OpenID Connect callback processing');\n        }\n        delete result.id_token;\n    }\n    return result;\n}\nfunction checkJwtType(expected, result) {\n    if (typeof result.header.typ !== 'string' || normalizeTyp(result.header.typ) !== expected) {\n        throw new OPE('unexpected JWT \"typ\" header parameter value');\n    }\n    return result;\n}\nexport async function clientCredentialsGrantRequest(as, client, parameters, options) {\n    assertAs(as);\n    assertClient(client);\n    return tokenEndpointRequest(as, client, 'client_credentials', new URLSearchParams(parameters), options);\n}\nexport async function processClientCredentialsResponse(as, client, response) {\n    const result = await processGenericAccessTokenResponse(as, client, response, true, true);\n    if (isOAuth2Error(result)) {\n        return result;\n    }\n    return result;\n}\nexport async function revocationRequest(as, client, token, options) {\n    assertAs(as);\n    assertClient(client);\n    if (!validateString(token)) {\n        throw new TypeError('\"token\" must be a non-empty string');\n    }\n    const url = resolveEndpoint(as, 'revocation_endpoint', options);\n    const body = new URLSearchParams(options?.additionalParameters);\n    body.set('token', token);\n    const headers = prepareHeaders(options?.headers);\n    headers.delete('accept');\n    return authenticatedRequest(as, client, 'POST', url, body, headers, options);\n}\nexport async function processRevocationResponse(response) {\n    if (!looseInstanceOf(response, Response)) {\n        throw new TypeError('\"response\" must be an instance of Response');\n    }\n    if (response.status !== 200) {\n        let err;\n        if ((err = await handleOAuthBodyError(response))) {\n            return err;\n        }\n        throw new OPE('\"response\" is not a conform Revocation Endpoint response');\n    }\n    return undefined;\n}\nfunction assertReadableResponse(response) {\n    if (response.bodyUsed) {\n        throw new TypeError('\"response\" body has been used already');\n    }\n}\nexport async function introspectionRequest(as, client, token, options) {\n    assertAs(as);\n    assertClient(client);\n    if (!validateString(token)) {\n        throw new TypeError('\"token\" must be a non-empty string');\n    }\n    const url = resolveEndpoint(as, 'introspection_endpoint', options);\n    const body = new URLSearchParams(options?.additionalParameters);\n    body.set('token', token);\n    const headers = prepareHeaders(options?.headers);\n    if (options?.requestJwtResponse ?? client.introspection_signed_response_alg) {\n        headers.set('accept', 'application/token-introspection+jwt');\n    }\n    else {\n        headers.set('accept', 'application/json');\n    }\n    return authenticatedRequest(as, client, 'POST', url, body, headers, options);\n}\nexport async function processIntrospectionResponse(as, client, response) {\n    assertAs(as);\n    assertClient(client);\n    if (!looseInstanceOf(response, Response)) {\n        throw new TypeError('\"response\" must be an instance of Response');\n    }\n    if (response.status !== 200) {\n        let err;\n        if ((err = await handleOAuthBodyError(response))) {\n            return err;\n        }\n        throw new OPE('\"response\" is not a conform Introspection Endpoint response');\n    }\n    let json;\n    if (getContentType(response) === 'application/token-introspection+jwt') {\n        assertReadableResponse(response);\n        const { claims } = await validateJwt(await response.text(), checkSigningAlgorithm.bind(undefined, client.introspection_signed_response_alg, as.introspection_signing_alg_values_supported), noSignatureCheck, getClockSkew(client), getClockTolerance(client))\n            .then(checkJwtType.bind(undefined, 'token-introspection+jwt'))\n            .then(validatePresence.bind(undefined, ['aud', 'iat', 'iss']))\n            .then(validateIssuer.bind(undefined, as.issuer))\n            .then(validateAudience.bind(undefined, client.client_id));\n        json = claims.token_introspection;\n        if (!isJsonObject(json)) {\n            throw new OPE('JWT \"token_introspection\" claim must be a JSON object');\n        }\n    }\n    else {\n        assertReadableResponse(response);\n        try {\n            json = await response.json();\n        }\n        catch (cause) {\n            throw new OPE('failed to parse \"response\" body as JSON', { cause });\n        }\n        if (!isJsonObject(json)) {\n            throw new OPE('\"response\" body must be a top level object');\n        }\n    }\n    if (typeof json.active !== 'boolean') {\n        throw new OPE('\"response\" body \"active\" property must be a boolean');\n    }\n    return json;\n}\nasync function jwksRequest(as, options) {\n    assertAs(as);\n    const url = resolveEndpoint(as, 'jwks_uri');\n    const headers = prepareHeaders(options?.headers);\n    headers.set('accept', 'application/json');\n    headers.append('accept', 'application/jwk-set+json');\n    return (options?.[customFetch] || fetch)(url.href, {\n        headers: Object.fromEntries(headers.entries()),\n        method: 'GET',\n        redirect: 'manual',\n        signal: options?.signal ? signal(options.signal) : null,\n    }).then(processDpopNonce);\n}\nasync function processJwksResponse(response) {\n    if (!looseInstanceOf(response, Response)) {\n        throw new TypeError('\"response\" must be an instance of Response');\n    }\n    if (response.status !== 200) {\n        throw new OPE('\"response\" is not a conform JSON Web Key Set response');\n    }\n    assertReadableResponse(response);\n    let json;\n    try {\n        json = await response.json();\n    }\n    catch (cause) {\n        throw new OPE('failed to parse \"response\" body as JSON', { cause });\n    }\n    if (!isJsonObject(json)) {\n        throw new OPE('\"response\" body must be a top level object');\n    }\n    if (!Array.isArray(json.keys)) {\n        throw new OPE('\"response\" body \"keys\" property must be an array');\n    }\n    if (!Array.prototype.every.call(json.keys, isJsonObject)) {\n        throw new OPE('\"response\" body \"keys\" property members must be JWK formatted objects');\n    }\n    return json;\n}\nasync function handleOAuthBodyError(response) {\n    if (response.status > 399 && response.status < 500) {\n        assertReadableResponse(response);\n        try {\n            const json = await response.json();\n            if (isJsonObject(json) && typeof json.error === 'string' && json.error.length) {\n                if (json.error_description !== undefined && typeof json.error_description !== 'string') {\n                    delete json.error_description;\n                }\n                if (json.error_uri !== undefined && typeof json.error_uri !== 'string') {\n                    delete json.error_uri;\n                }\n                if (json.algs !== undefined && typeof json.algs !== 'string') {\n                    delete json.algs;\n                }\n                if (json.scope !== undefined && typeof json.scope !== 'string') {\n                    delete json.scope;\n                }\n                return json;\n            }\n        }\n        catch { }\n    }\n    return undefined;\n}\nfunction checkSupportedJwsAlg(alg) {\n    if (!SUPPORTED_JWS_ALGS.includes(alg)) {\n        throw new UnsupportedOperationError('unsupported JWS \"alg\" identifier');\n    }\n    return alg;\n}\nfunction checkRsaKeyAlgorithm(algorithm) {\n    if (typeof algorithm.modulusLength !== 'number' || algorithm.modulusLength < 2048) {\n        throw new OPE(`${algorithm.name} modulusLength must be at least 2048 bits`);\n    }\n}\nfunction ecdsaHashName(namedCurve) {\n    switch (namedCurve) {\n        case 'P-256':\n            return 'SHA-256';\n        case 'P-384':\n            return 'SHA-384';\n        case 'P-521':\n            return 'SHA-512';\n        default:\n            throw new UnsupportedOperationError();\n    }\n}\nfunction keyToSubtle(key) {\n    switch (key.algorithm.name) {\n        case 'ECDSA':\n            return {\n                name: key.algorithm.name,\n                hash: ecdsaHashName(key.algorithm.namedCurve),\n            };\n        case 'RSA-PSS': {\n            checkRsaKeyAlgorithm(key.algorithm);\n            switch (key.algorithm.hash.name) {\n                case 'SHA-256':\n                case 'SHA-384':\n                case 'SHA-512':\n                    return {\n                        name: key.algorithm.name,\n                        saltLength: parseInt(key.algorithm.hash.name.slice(-3), 10) >> 3,\n                    };\n                default:\n                    throw new UnsupportedOperationError();\n            }\n        }\n        case 'RSASSA-PKCS1-v1_5':\n            checkRsaKeyAlgorithm(key.algorithm);\n            return key.algorithm.name;\n        case 'Ed448':\n        case 'Ed25519':\n            return key.algorithm.name;\n    }\n    throw new UnsupportedOperationError();\n}\nconst noSignatureCheck = Symbol();\nasync function validateJwt(jws, checkAlg, getKey, clockSkew, clockTolerance) {\n    const { 0: protectedHeader, 1: payload, 2: encodedSignature, length } = jws.split('.');\n    if (length === 5) {\n        throw new UnsupportedOperationError('JWE structure JWTs are not supported');\n    }\n    if (length !== 3) {\n        throw new OPE('Invalid JWT');\n    }\n    let header;\n    try {\n        header = JSON.parse(buf(b64u(protectedHeader)));\n    }\n    catch (cause) {\n        throw new OPE('failed to parse JWT Header body as base64url encoded JSON', { cause });\n    }\n    if (!isJsonObject(header)) {\n        throw new OPE('JWT Header must be a top level object');\n    }\n    checkAlg(header);\n    if (header.crit !== undefined) {\n        throw new OPE('unexpected JWT \"crit\" header parameter');\n    }\n    const signature = b64u(encodedSignature);\n    let key;\n    if (getKey !== noSignatureCheck) {\n        key = await getKey(header);\n        const input = `${protectedHeader}.${payload}`;\n        const verified = await crypto.subtle.verify(keyToSubtle(key), key, signature, buf(input));\n        if (!verified) {\n            throw new OPE('JWT signature verification failed');\n        }\n    }\n    let claims;\n    try {\n        claims = JSON.parse(buf(b64u(payload)));\n    }\n    catch (cause) {\n        throw new OPE('failed to parse JWT Payload body as base64url encoded JSON', { cause });\n    }\n    if (!isJsonObject(claims)) {\n        throw new OPE('JWT Payload must be a top level object');\n    }\n    const now = epochTime() + clockSkew;\n    if (claims.exp !== undefined) {\n        if (typeof claims.exp !== 'number') {\n            throw new OPE('unexpected JWT \"exp\" (expiration time) claim type');\n        }\n        if (claims.exp <= now - clockTolerance) {\n            throw new OPE('unexpected JWT \"exp\" (expiration time) claim value, timestamp is <= now()');\n        }\n    }\n    if (claims.iat !== undefined) {\n        if (typeof claims.iat !== 'number') {\n            throw new OPE('unexpected JWT \"iat\" (issued at) claim type');\n        }\n    }\n    if (claims.iss !== undefined) {\n        if (typeof claims.iss !== 'string') {\n            throw new OPE('unexpected JWT \"iss\" (issuer) claim type');\n        }\n    }\n    if (claims.nbf !== undefined) {\n        if (typeof claims.nbf !== 'number') {\n            throw new OPE('unexpected JWT \"nbf\" (not before) claim type');\n        }\n        if (claims.nbf > now + clockTolerance) {\n            throw new OPE('unexpected JWT \"nbf\" (not before) claim value, timestamp is > now()');\n        }\n    }\n    if (claims.aud !== undefined) {\n        if (typeof claims.aud !== 'string' && !Array.isArray(claims.aud)) {\n            throw new OPE('unexpected JWT \"aud\" (audience) claim type');\n        }\n    }\n    return { header, claims, signature, key };\n}\nexport async function validateJwtAuthResponse(as, client, parameters, expectedState, options) {\n    assertAs(as);\n    assertClient(client);\n    if (parameters instanceof URL) {\n        parameters = parameters.searchParams;\n    }\n    if (!(parameters instanceof URLSearchParams)) {\n        throw new TypeError('\"parameters\" must be an instance of URLSearchParams, or URL');\n    }\n    const response = getURLSearchParameter(parameters, 'response');\n    if (!response) {\n        throw new OPE('\"parameters\" does not contain a JARM response');\n    }\n    if (typeof as.jwks_uri !== 'string') {\n        throw new TypeError('\"as.jwks_uri\" must be a string');\n    }\n    const { claims } = await validateJwt(response, checkSigningAlgorithm.bind(undefined, client.authorization_signed_response_alg, as.authorization_signing_alg_values_supported), getPublicSigKeyFromIssuerJwksUri.bind(undefined, as, options), getClockSkew(client), getClockTolerance(client))\n        .then(validatePresence.bind(undefined, ['aud', 'exp', 'iss']))\n        .then(validateIssuer.bind(undefined, as.issuer))\n        .then(validateAudience.bind(undefined, client.client_id));\n    const result = new URLSearchParams();\n    for (const [key, value] of Object.entries(claims)) {\n        if (typeof value === 'string' && key !== 'aud') {\n            result.set(key, value);\n        }\n    }\n    return validateAuthResponse(as, client, result, expectedState);\n}\nasync function idTokenHash(alg, data, key) {\n    let algorithm;\n    switch (alg) {\n        case 'RS256':\n        case 'PS256':\n        case 'ES256':\n            algorithm = 'SHA-256';\n            break;\n        case 'RS384':\n        case 'PS384':\n        case 'ES384':\n            algorithm = 'SHA-384';\n            break;\n        case 'RS512':\n        case 'PS512':\n        case 'ES512':\n            algorithm = 'SHA-512';\n            break;\n        case 'EdDSA':\n            if (key.algorithm.name === 'Ed25519') {\n                algorithm = 'SHA-512';\n                break;\n            }\n            throw new UnsupportedOperationError();\n        default:\n            throw new UnsupportedOperationError();\n    }\n    const digest = await crypto.subtle.digest(algorithm, buf(data));\n    return b64u(digest.slice(0, digest.byteLength / 2));\n}\nasync function idTokenHashMatches(data, actual, alg, key) {\n    const expected = await idTokenHash(alg, data, key);\n    return actual === expected;\n}\nexport async function validateDetachedSignatureResponse(as, client, parameters, expectedNonce, expectedState, maxAge, options) {\n    assertAs(as);\n    assertClient(client);\n    if (parameters instanceof URL) {\n        if (!parameters.hash.length) {\n            throw new TypeError('\"parameters\" as an instance of URL must contain a hash (fragment) with the Authorization Response parameters');\n        }\n        parameters = new URLSearchParams(parameters.hash.slice(1));\n    }\n    if (!(parameters instanceof URLSearchParams)) {\n        throw new TypeError('\"parameters\" must be an instance of URLSearchParams');\n    }\n    parameters = new URLSearchParams(parameters);\n    const id_token = getURLSearchParameter(parameters, 'id_token');\n    parameters.delete('id_token');\n    switch (expectedState) {\n        case undefined:\n        case expectNoState:\n            break;\n        default:\n            if (!validateString(expectedState)) {\n                throw new TypeError('\"expectedState\" must be a non-empty string');\n            }\n    }\n    const result = validateAuthResponse({\n        ...as,\n        authorization_response_iss_parameter_supported: false,\n    }, client, parameters, expectedState);\n    if (isOAuth2Error(result)) {\n        return result;\n    }\n    if (!id_token) {\n        throw new OPE('\"parameters\" does not contain an ID Token');\n    }\n    const code = getURLSearchParameter(parameters, 'code');\n    if (!code) {\n        throw new OPE('\"parameters\" does not contain an Authorization Code');\n    }\n    if (typeof as.jwks_uri !== 'string') {\n        throw new TypeError('\"as.jwks_uri\" must be a string');\n    }\n    const requiredClaims = [\n        'aud',\n        'exp',\n        'iat',\n        'iss',\n        'sub',\n        'nonce',\n        'c_hash',\n    ];\n    if (typeof expectedState === 'string') {\n        requiredClaims.push('s_hash');\n    }\n    const { claims, header, key } = await validateJwt(id_token, checkSigningAlgorithm.bind(undefined, client.id_token_signed_response_alg, as.id_token_signing_alg_values_supported), getPublicSigKeyFromIssuerJwksUri.bind(undefined, as, options), getClockSkew(client), getClockTolerance(client))\n        .then(validatePresence.bind(undefined, requiredClaims))\n        .then(validateIssuer.bind(undefined, as.issuer))\n        .then(validateAudience.bind(undefined, client.client_id));\n    const clockSkew = getClockSkew(client);\n    const now = epochTime() + clockSkew;\n    if (claims.iat < now - 3600) {\n        throw new OPE('unexpected JWT \"iat\" (issued at) claim value, it is too far in the past');\n    }\n    if (typeof claims.c_hash !== 'string' ||\n        (await idTokenHashMatches(code, claims.c_hash, header.alg, key)) !== true) {\n        throw new OPE('invalid ID Token \"c_hash\" (code hash) claim value');\n    }\n    if (claims.s_hash !== undefined && typeof expectedState !== 'string') {\n        throw new OPE('could not verify ID Token \"s_hash\" (state hash) claim value');\n    }\n    if (typeof expectedState === 'string' &&\n        (typeof claims.s_hash !== 'string' ||\n            (await idTokenHashMatches(expectedState, claims.s_hash, header.alg, key)) !== true)) {\n        throw new OPE('invalid ID Token \"s_hash\" (state hash) claim value');\n    }\n    if (client.require_auth_time !== undefined && typeof claims.auth_time !== 'number') {\n        throw new OPE('unexpected ID Token \"auth_time\" (authentication time) claim value');\n    }\n    maxAge ?? (maxAge = client.default_max_age ?? skipAuthTimeCheck);\n    if ((client.require_auth_time || maxAge !== skipAuthTimeCheck) &&\n        claims.auth_time === undefined) {\n        throw new OPE('ID Token \"auth_time\" (authentication time) claim missing');\n    }\n    if (maxAge !== skipAuthTimeCheck) {\n        if (typeof maxAge !== 'number' || maxAge < 0) {\n            throw new TypeError('\"options.max_age\" must be a non-negative number');\n        }\n        const now = epochTime() + getClockSkew(client);\n        const tolerance = getClockTolerance(client);\n        if (claims.auth_time + maxAge < now - tolerance) {\n            throw new OPE('too much time has elapsed since the last End-User authentication');\n        }\n    }\n    if (!validateString(expectedNonce)) {\n        throw new TypeError('\"expectedNonce\" must be a non-empty string');\n    }\n    if (claims.nonce !== expectedNonce) {\n        throw new OPE('unexpected ID Token \"nonce\" claim value');\n    }\n    if (Array.isArray(claims.aud) && claims.aud.length !== 1 && claims.azp !== client.client_id) {\n        throw new OPE('unexpected ID Token \"azp\" (authorized party) claim value');\n    }\n    return result;\n}\nfunction checkSigningAlgorithm(client, issuer, header) {\n    if (client !== undefined) {\n        if (header.alg !== client) {\n            throw new OPE('unexpected JWT \"alg\" header parameter');\n        }\n        return;\n    }\n    if (Array.isArray(issuer)) {\n        if (!issuer.includes(header.alg)) {\n            throw new OPE('unexpected JWT \"alg\" header parameter');\n        }\n        return;\n    }\n    if (header.alg !== 'RS256') {\n        throw new OPE('unexpected JWT \"alg\" header parameter');\n    }\n}\nfunction getURLSearchParameter(parameters, name) {\n    const { 0: value, length } = parameters.getAll(name);\n    if (length > 1) {\n        throw new OPE(`\"${name}\" parameter must be provided only once`);\n    }\n    return value;\n}\nexport const skipStateCheck = Symbol();\nexport const expectNoState = Symbol();\nexport function validateAuthResponse(as, client, parameters, expectedState) {\n    assertAs(as);\n    assertClient(client);\n    if (parameters instanceof URL) {\n        parameters = parameters.searchParams;\n    }\n    if (!(parameters instanceof URLSearchParams)) {\n        throw new TypeError('\"parameters\" must be an instance of URLSearchParams, or URL');\n    }\n    if (getURLSearchParameter(parameters, 'response')) {\n        throw new OPE('\"parameters\" contains a JARM response, use validateJwtAuthResponse() instead of validateAuthResponse()');\n    }\n    const iss = getURLSearchParameter(parameters, 'iss');\n    const state = getURLSearchParameter(parameters, 'state');\n    if (!iss && as.authorization_response_iss_parameter_supported) {\n        throw new OPE('response parameter \"iss\" (issuer) missing');\n    }\n    if (iss && iss !== as.issuer) {\n        throw new OPE('unexpected \"iss\" (issuer) response parameter value');\n    }\n    switch (expectedState) {\n        case undefined:\n        case expectNoState:\n            if (state !== undefined) {\n                throw new OPE('unexpected \"state\" response parameter encountered');\n            }\n            break;\n        case skipStateCheck:\n            break;\n        default:\n            if (!validateString(expectedState)) {\n                throw new OPE('\"expectedState\" must be a non-empty string');\n            }\n            if (state === undefined) {\n                throw new OPE('response parameter \"state\" missing');\n            }\n            if (state !== expectedState) {\n                throw new OPE('unexpected \"state\" response parameter value');\n            }\n    }\n    const error = getURLSearchParameter(parameters, 'error');\n    if (error) {\n        return {\n            error,\n            error_description: getURLSearchParameter(parameters, 'error_description'),\n            error_uri: getURLSearchParameter(parameters, 'error_uri'),\n        };\n    }\n    const id_token = getURLSearchParameter(parameters, 'id_token');\n    const token = getURLSearchParameter(parameters, 'token');\n    if (id_token !== undefined || token !== undefined) {\n        throw new UnsupportedOperationError('implicit and hybrid flows are not supported');\n    }\n    return brand(new URLSearchParams(parameters));\n}\nfunction algToSubtle(alg, crv) {\n    switch (alg) {\n        case 'PS256':\n        case 'PS384':\n        case 'PS512':\n            return { name: 'RSA-PSS', hash: `SHA-${alg.slice(-3)}` };\n        case 'RS256':\n        case 'RS384':\n        case 'RS512':\n            return { name: 'RSASSA-PKCS1-v1_5', hash: `SHA-${alg.slice(-3)}` };\n        case 'ES256':\n        case 'ES384':\n            return { name: 'ECDSA', namedCurve: `P-${alg.slice(-3)}` };\n        case 'ES512':\n            return { name: 'ECDSA', namedCurve: 'P-521' };\n        case 'EdDSA': {\n            switch (crv) {\n                case 'Ed25519':\n                case 'Ed448':\n                    return crv;\n                default:\n                    throw new UnsupportedOperationError();\n            }\n        }\n        default:\n            throw new UnsupportedOperationError();\n    }\n}\nasync function importJwk(alg, jwk) {\n    const { ext, key_ops, use, ...key } = jwk;\n    return crypto.subtle.importKey('jwk', key, algToSubtle(alg, jwk.crv), true, ['verify']);\n}\nexport async function deviceAuthorizationRequest(as, client, parameters, options) {\n    assertAs(as);\n    assertClient(client);\n    const url = resolveEndpoint(as, 'device_authorization_endpoint', options);\n    const body = new URLSearchParams(parameters);\n    body.set('client_id', client.client_id);\n    const headers = prepareHeaders(options?.headers);\n    headers.set('accept', 'application/json');\n    return authenticatedRequest(as, client, 'POST', url, body, headers, options);\n}\nexport async function processDeviceAuthorizationResponse(as, client, response) {\n    assertAs(as);\n    assertClient(client);\n    if (!looseInstanceOf(response, Response)) {\n        throw new TypeError('\"response\" must be an instance of Response');\n    }\n    if (response.status !== 200) {\n        let err;\n        if ((err = await handleOAuthBodyError(response))) {\n            return err;\n        }\n        throw new OPE('\"response\" is not a conform Device Authorization Endpoint response');\n    }\n    assertReadableResponse(response);\n    let json;\n    try {\n        json = await response.json();\n    }\n    catch (cause) {\n        throw new OPE('failed to parse \"response\" body as JSON', { cause });\n    }\n    if (!isJsonObject(json)) {\n        throw new OPE('\"response\" body must be a top level object');\n    }\n    if (!validateString(json.device_code)) {\n        throw new OPE('\"response\" body \"device_code\" property must be a non-empty string');\n    }\n    if (!validateString(json.user_code)) {\n        throw new OPE('\"response\" body \"user_code\" property must be a non-empty string');\n    }\n    if (!validateString(json.verification_uri)) {\n        throw new OPE('\"response\" body \"verification_uri\" property must be a non-empty string');\n    }\n    if (typeof json.expires_in !== 'number' || json.expires_in <= 0) {\n        throw new OPE('\"response\" body \"expires_in\" property must be a positive number');\n    }\n    if (json.verification_uri_complete !== undefined &&\n        !validateString(json.verification_uri_complete)) {\n        throw new OPE('\"response\" body \"verification_uri_complete\" property must be a non-empty string');\n    }\n    if (json.interval !== undefined && (typeof json.interval !== 'number' || json.interval <= 0)) {\n        throw new OPE('\"response\" body \"interval\" property must be a positive number');\n    }\n    return json;\n}\nexport async function deviceCodeGrantRequest(as, client, deviceCode, options) {\n    assertAs(as);\n    assertClient(client);\n    if (!validateString(deviceCode)) {\n        throw new TypeError('\"deviceCode\" must be a non-empty string');\n    }\n    const parameters = new URLSearchParams(options?.additionalParameters);\n    parameters.set('device_code', deviceCode);\n    return tokenEndpointRequest(as, client, 'urn:ietf:params:oauth:grant-type:device_code', parameters, options);\n}\nexport async function processDeviceCodeResponse(as, client, response) {\n    return processGenericAccessTokenResponse(as, client, response);\n}\nexport async function generateKeyPair(alg, options) {\n    if (!validateString(alg)) {\n        throw new TypeError('\"alg\" must be a non-empty string');\n    }\n    const algorithm = algToSubtle(alg, alg === 'EdDSA' ? options?.crv ?? 'Ed25519' : undefined);\n    if (alg.startsWith('PS') || alg.startsWith('RS')) {\n        Object.assign(algorithm, {\n            modulusLength: options?.modulusLength ?? 2048,\n            publicExponent: new Uint8Array([0x01, 0x00, 0x01]),\n        });\n    }\n    return (crypto.subtle.generateKey(algorithm, options?.extractable ?? false, ['sign', 'verify']));\n}\nfunction normalizeHtu(htu) {\n    const url = new URL(htu);\n    url.search = '';\n    url.hash = '';\n    return url.href;\n}\nasync function validateDPoP(as, request, accessToken, accessTokenClaims, options) {\n    const header = request.headers.get('dpop');\n    if (header === null) {\n        throw new OPE('operation indicated DPoP use but the request has no DPoP HTTP Header');\n    }\n    if (request.headers.get('authorization')?.toLowerCase().startsWith('dpop ') === false) {\n        throw new OPE(`operation indicated DPoP use but the request's Authorization HTTP Header scheme is not DPoP`);\n    }\n    if (typeof accessTokenClaims.cnf?.jkt !== 'string') {\n        throw new OPE('operation indicated DPoP use but the JWT Access Token has no jkt confirmation claim');\n    }\n    const clockSkew = getClockSkew(options);\n    const proof = await validateJwt(header, checkSigningAlgorithm.bind(undefined, undefined, as?.dpop_signing_alg_values_supported || SUPPORTED_JWS_ALGS), async ({ jwk, alg }) => {\n        if (!jwk) {\n            throw new OPE('DPoP Proof is missing the jwk header parameter');\n        }\n        const key = await importJwk(alg, jwk);\n        if (key.type !== 'public') {\n            throw new OPE('DPoP Proof jwk header parameter must contain a public key');\n        }\n        return key;\n    }, clockSkew, getClockTolerance(options))\n        .then(checkJwtType.bind(undefined, 'dpop+jwt'))\n        .then(validatePresence.bind(undefined, ['iat', 'jti', 'ath', 'htm', 'htu']));\n    const now = epochTime() + clockSkew;\n    const diff = Math.abs(now - proof.claims.iat);\n    if (diff > 300) {\n        throw new OPE('DPoP Proof iat is not recent enough');\n    }\n    if (proof.claims.htm !== request.method) {\n        throw new OPE('DPoP Proof htm mismatch');\n    }\n    if (typeof proof.claims.htu !== 'string' ||\n        normalizeHtu(proof.claims.htu) !== normalizeHtu(request.url)) {\n        throw new OPE('DPoP Proof htu mismatch');\n    }\n    {\n        const expected = b64u(await crypto.subtle.digest('SHA-256', encoder.encode(accessToken)));\n        if (proof.claims.ath !== expected) {\n            throw new OPE('DPoP Proof ath mismatch');\n        }\n    }\n    {\n        let components;\n        switch (proof.header.jwk.kty) {\n            case 'EC':\n                components = {\n                    crv: proof.header.jwk.crv,\n                    kty: proof.header.jwk.kty,\n                    x: proof.header.jwk.x,\n                    y: proof.header.jwk.y,\n                };\n                break;\n            case 'OKP':\n                components = {\n                    crv: proof.header.jwk.crv,\n                    kty: proof.header.jwk.kty,\n                    x: proof.header.jwk.x,\n                };\n                break;\n            case 'RSA':\n                components = {\n                    e: proof.header.jwk.e,\n                    kty: proof.header.jwk.kty,\n                    n: proof.header.jwk.n,\n                };\n                break;\n            default:\n                throw new UnsupportedOperationError();\n        }\n        const expected = b64u(await crypto.subtle.digest('SHA-256', encoder.encode(JSON.stringify(components))));\n        if (accessTokenClaims.cnf.jkt !== expected) {\n            throw new OPE('JWT Access Token confirmation mismatch');\n        }\n    }\n}\nexport async function validateJwtAccessToken(as, request, expectedAudience, options) {\n    assertAs(as);\n    if (!looseInstanceOf(request, Request)) {\n        throw new TypeError('\"request\" must be an instance of Request');\n    }\n    if (!validateString(expectedAudience)) {\n        throw new OPE('\"expectedAudience\" must be a non-empty string');\n    }\n    const authorization = request.headers.get('authorization');\n    if (authorization === null) {\n        throw new OPE('\"request\" is missing an Authorization HTTP Header');\n    }\n    let { 0: scheme, 1: accessToken, length } = authorization.split(' ');\n    scheme = scheme.toLowerCase();\n    switch (scheme) {\n        case 'dpop':\n        case 'bearer':\n            break;\n        default:\n            throw new UnsupportedOperationError('unsupported Authorization HTTP Header scheme');\n    }\n    if (length !== 2) {\n        throw new OPE('invalid Authorization HTTP Header format');\n    }\n    const requiredClaims = [\n        'iss',\n        'exp',\n        'aud',\n        'sub',\n        'iat',\n        'jti',\n        'client_id',\n    ];\n    if (options?.requireDPoP || scheme === 'dpop' || request.headers.has('dpop')) {\n        requiredClaims.push('cnf');\n    }\n    const { claims } = await validateJwt(accessToken, checkSigningAlgorithm.bind(undefined, undefined, SUPPORTED_JWS_ALGS), getPublicSigKeyFromIssuerJwksUri.bind(undefined, as, options), getClockSkew(options), getClockTolerance(options))\n        .then(checkJwtType.bind(undefined, 'at+jwt'))\n        .then(validatePresence.bind(undefined, requiredClaims))\n        .then(validateIssuer.bind(undefined, as.issuer))\n        .then(validateAudience.bind(undefined, expectedAudience));\n    for (const claim of ['client_id', 'jti', 'sub']) {\n        if (typeof claims[claim] !== 'string') {\n            throw new OPE(`unexpected JWT \"${claim}\" claim type`);\n        }\n    }\n    if ('cnf' in claims) {\n        if (!isJsonObject(claims.cnf)) {\n            throw new OPE('unexpected JWT \"cnf\" (confirmation) claim value');\n        }\n        const { 0: cnf, length } = Object.keys(claims.cnf);\n        if (length) {\n            if (length !== 1) {\n                throw new UnsupportedOperationError('multiple confirmation claims are not supported');\n            }\n            if (cnf !== 'jkt') {\n                throw new UnsupportedOperationError('unsupported JWT Confirmation method');\n            }\n        }\n    }\n    if (options?.requireDPoP ||\n        scheme === 'dpop' ||\n        claims.cnf?.jkt !== undefined ||\n        request.headers.has('dpop')) {\n        await validateDPoP(as, request, accessToken, claims, options);\n    }\n    return claims;\n}\nexport const experimentalCustomFetch = customFetch;\nexport const experimental_customFetch = customFetch;\nexport const experimentalUseMtlsAlias = useMtlsAlias;\nexport const experimental_useMtlsAlias = useMtlsAlias;\nexport const experimental_validateDetachedSignatureResponse = validateDetachedSignatureResponse;\nexport const experimental_validateJwtAccessToken = validateJwtAccessToken;\n", "import * as oauth from 'oauth4webapi';\n\nimport * as yup from 'yup';\nimport { KnownError, KnownErrors } from '../known-errors';\nimport { AccessToken, InternalSession, RefreshToken } from '../sessions';\nimport { generateSecureRandomString } from '../utils/crypto';\nimport { StackAssertionError, throwErr } from '../utils/errors';\nimport { globalVar } from '../utils/globals';\nimport { HTTP_METHODS, HttpMethod } from '../utils/http';\nimport { ReadonlyJson } from '../utils/json';\nimport { filterUndefined, filterUndefinedOrNull } from '../utils/objects';\nimport { AuthenticationResponseJSON, PublicKeyCredentialCreationOptionsJSON, PublicKeyCredentialRequestOptionsJSON, RegistrationResponseJSON } from '../utils/passkey';\nimport { wait } from '../utils/promises';\nimport { Result } from \"../utils/results\";\nimport { deindent } from '../utils/strings';\nimport { ContactChannelsCrud } from './crud/contact-channels';\nimport { CurrentUserCrud } from './crud/current-user';\nimport { ConnectedAccountAccessTokenCrud } from './crud/oauth';\nimport { TeamApiKeysCrud, UserApiKeysCrud, teamApiKeysCreateInputSchema, teamApiKeysCreateOutputSchema, userApiKeysCreateInputSchema, userApiKeysCreateOutputSchema } from './crud/project-api-keys';\nimport { ProjectPermissionsCrud } from './crud/project-permissions';\nimport { AdminUserProjectsCrud, ClientProjectsCrud } from './crud/projects';\nimport { SessionsCrud } from './crud/sessions';\nimport { TeamInvitationCrud } from './crud/team-invitation';\nimport { TeamMemberProfilesCrud } from './crud/team-member-profiles';\nimport { TeamPermissionsCrud } from './crud/team-permissions';\nimport { TeamsCrud } from './crud/teams';\n\nexport type ClientInterfaceOptions = {\n  clientVersion: string,\n  // This is a function instead of a string because it might be different based on the environment (for example client vs server)\n  getBaseUrl: () => string,\n  extraRequestHeaders: Record<string, string>,\n  projectId: string,\n  prepareRequest?: () => Promise<void>,\n} & ({\n  publishableClientKey: string,\n} | {\n  projectOwnerSession: InternalSession,\n});\n\nexport class StackClientInterface {\n  constructor(public readonly options: ClientInterfaceOptions) {\n    // nothing here\n  }\n\n  get projectId() {\n    return this.options.projectId;\n  }\n\n  getApiUrl() {\n    return this.options.getBaseUrl() + \"/api/v1\";\n  }\n\n  public async runNetworkDiagnostics(session?: InternalSession | null, requestType?: \"client\" | \"server\" | \"admin\") {\n    const tryRequest = async (cb: () => Promise<void>) => {\n      try {\n        await cb();\n        return \"OK\";\n      } catch (e) {\n        return `${e}`;\n      }\n    };\n    const cfTrace = await tryRequest(async () => {\n      const res = await fetch(\"https://1.1.1.1/cdn-cgi/trace\");\n      if (!res.ok) {\n        throw new Error(`${res.status} ${res.statusText}: ${await res.text()}`);\n      }\n    });\n    const apiRoot = session !== undefined && requestType !== undefined ? await tryRequest(async () => {\n      const res = await this.sendClientRequestInner(\"/\", {}, session!, requestType);\n      if (res.status === \"error\") {\n        throw res.error;\n      }\n    }) : \"Not tested\";\n    const baseUrlBackend = await tryRequest(async () => {\n      const res = await fetch(new URL(\"/health\", this.getApiUrl()));\n      if (!res.ok) {\n        throw new Error(`${res.status} ${res.statusText}: ${await res.text()}`);\n      }\n    });\n    const prodDashboard = await tryRequest(async () => {\n      const res = await fetch(\"https://app.stack-auth.com/health\");\n      if (!res.ok) {\n        throw new Error(`${res.status} ${res.statusText}: ${await res.text()}`);\n      }\n    });\n    const prodBackend = await tryRequest(async () => {\n      const res = await fetch(\"https://api.stack-auth.com/health\");\n      if (!res.ok) {\n        throw new Error(`${res.status} ${res.statusText}: ${await res.text()}`);\n      }\n    });\n    return {\n      \"navigator?.onLine\": globalVar.navigator?.onLine,\n      cfTrace,\n      apiRoot,\n      baseUrlBackend,\n      prodDashboard,\n      prodBackend,\n    };\n  }\n\n  protected async _createNetworkError(cause: Error, session?: InternalSession | null, requestType?: \"client\" | \"server\" | \"admin\") {\n    return new Error(deindent`\n      Stack Auth is unable to connect to the server. Please check your internet connection and try again.\n      \n      If the problem persists, please contact support and provide a screenshot of your entire browser console.\n\n      ${cause}\n      \n      ${JSON.stringify(await this.runNetworkDiagnostics(session, requestType), null, 2)}\n    `, { cause: cause });\n  }\n\n  protected async _networkRetry<T>(cb: () => Promise<Result<T, any>>, session?: InternalSession | null, requestType?: \"client\" | \"server\" | \"admin\"): Promise<T> {\n    const retriedResult = await Result.retry(\n      cb,\n      5,\n      { exponentialDelayBase: 1000 },\n    );\n\n    // try to diagnose the error for the user\n    if (retriedResult.status === \"error\") {\n      if (globalVar.navigator && !globalVar.navigator.onLine) {\n        throw new Error(\"Failed to send Stack network request. It seems like you are offline, please check your internet connection and try again. This is not an error with Stack Auth. (window.navigator.onLine is falsy)\", { cause: retriedResult.error });\n      }\n      throw await this._createNetworkError(retriedResult.error, session, requestType);\n    }\n    return retriedResult.data;\n  }\n\n  protected async _networkRetryException<T>(cb: () => Promise<T>, session?: InternalSession | null, requestType?: \"client\" | \"server\" | \"admin\"): Promise<T> {\n    return await this._networkRetry(async () => await Result.fromThrowingAsync(cb), session, requestType);\n  }\n\n  public async fetchNewAccessToken(refreshToken: RefreshToken) {\n    if (!('publishableClientKey' in this.options)) {\n      // TODO support it\n      throw new Error(\"Admin session token is currently not supported for fetching new access token. Did you try to log in on a StackApp initiated with the admin session?\");\n    }\n\n    const as = {\n      issuer: this.options.getBaseUrl(),\n      algorithm: 'oauth2',\n      token_endpoint: this.getApiUrl() + '/auth/oauth/token',\n    };\n    const client: oauth.Client = {\n      client_id: this.projectId,\n      client_secret: this.options.publishableClientKey,\n      token_endpoint_auth_method: 'client_secret_post',\n    };\n\n    const rawResponse = await this._networkRetryException(\n      async () => await oauth.refreshTokenGrantRequest(\n        as,\n        client,\n        refreshToken.token,\n      )\n    );\n    const response = await this._processResponse(rawResponse);\n\n    if (response.status === \"error\") {\n      const error = response.error;\n      if (KnownErrors.RefreshTokenError.isInstance(error)) {\n        return null;\n      }\n      throw error;\n    }\n\n    if (!response.data.ok) {\n      const body = await response.data.text();\n      throw new Error(`Failed to send refresh token request: ${response.status} ${body}`);\n    }\n\n    const result = await oauth.processRefreshTokenResponse(as, client, response.data);\n    if (oauth.isOAuth2Error(result)) {\n      // TODO Handle OAuth 2.0 response body error\n      throw new StackAssertionError(\"OAuth error\", { result });\n    }\n\n    if (!result.access_token) {\n      throw new StackAssertionError(\"Access token not found in token endpoint response, this is weird!\");\n    }\n\n    return new AccessToken(result.access_token);\n  }\n\n  public async sendClientRequest(\n    path: string,\n    requestOptions: RequestInit,\n    session: InternalSession | null,\n    requestType: \"client\" | \"server\" | \"admin\" = \"client\",\n  ) {\n    session ??= this.createSession({\n      refreshToken: null,\n    });\n\n\n    return await this._networkRetry(\n      () => this.sendClientRequestInner(path, requestOptions, session!, requestType),\n      session,\n      requestType,\n    );\n  }\n\n  public createSession(options: Omit<ConstructorParameters<typeof InternalSession>[0], \"refreshAccessTokenCallback\">): InternalSession {\n    const session = new InternalSession({\n      refreshAccessTokenCallback: async (refreshToken) => await this.fetchNewAccessToken(refreshToken),\n      ...options,\n    });\n    return session;\n  }\n\n  protected async sendClientRequestAndCatchKnownError<E extends typeof KnownErrors[keyof KnownErrors]>(\n    path: string,\n    requestOptions: RequestInit,\n    tokenStoreOrNull: InternalSession | null,\n    errorsToCatch: readonly E[],\n  ): Promise<Result<\n    Response & {\n      usedTokens: {\n        accessToken: AccessToken,\n        refreshToken: RefreshToken | null,\n      } | null,\n    },\n    InstanceType<E>\n  >> {\n    try {\n      return Result.ok(await this.sendClientRequest(path, requestOptions, tokenStoreOrNull));\n    } catch (e) {\n      for (const errorType of errorsToCatch) {\n        if (errorType.isInstance(e)) {\n          return Result.error(e as InstanceType<E>);\n        }\n      }\n      throw e;\n    }\n  }\n\n  private async sendClientRequestInner(\n    path: string,\n    options: RequestInit,\n    session: InternalSession,\n    requestType: \"client\" | \"server\" | \"admin\",\n  ): Promise<Result<Response & {\n    usedTokens: {\n      accessToken: AccessToken,\n      refreshToken: RefreshToken | null,\n    } | null,\n  }>> {\n    /**\n     * `tokenObj === null` means the session is invalid/not logged in\n     */\n    let tokenObj = await session.getOrFetchLikelyValidTokens(20_000);\n\n    let adminSession = \"projectOwnerSession\" in this.options ? this.options.projectOwnerSession : null;\n    let adminTokenObj = adminSession ? await adminSession.getOrFetchLikelyValidTokens(20_000) : null;\n\n    // all requests should be dynamic to prevent Next.js caching\n    await this.options.prepareRequest?.();\n\n    let url = this.getApiUrl() + path;\n    if (url.endsWith(\"/\")) {\n      url = url.slice(0, -1);\n    }\n    const params: RequestInit = {\n      /**\n       * This fetch may be cross-origin, in which case we don't want to send cookies of the\n       * original origin (this is the default behavior of `credentials`).\n       *\n       * To help debugging, also omit cookies on same-origin, so we don't accidentally\n       * implement reliance on cookies anywhere.\n       *\n       * However, Cloudflare Workers don't actually support `credentials`, so we only set it\n       * if Cloudflare-exclusive globals are not detected. https://github.com/cloudflare/workers-sdk/issues/2514\n       */\n      ...(\"WebSocketPair\" in globalVar ? {} : {\n        credentials: \"omit\",\n      }),\n      ...options,\n      headers: {\n        \"X-Stack-Override-Error-Status\": \"true\",\n        \"X-Stack-Project-Id\": this.projectId,\n        \"X-Stack-Access-Type\": requestType,\n        \"X-Stack-Client-Version\": this.options.clientVersion,\n        ...(tokenObj ? {\n          \"X-Stack-Access-Token\": tokenObj.accessToken.token,\n        } : {}),\n        ...(tokenObj?.refreshToken ? {\n          \"X-Stack-Refresh-Token\": tokenObj.refreshToken.token,\n        } : {}),\n        ...('publishableClientKey' in this.options ? {\n          \"X-Stack-Publishable-Client-Key\": this.options.publishableClientKey,\n        } : {}),\n        ...(adminTokenObj ? {\n          \"X-Stack-Admin-Access-Token\": adminTokenObj.accessToken.token,\n        } : {}),\n        /**\n         * Next.js until v15 would cache fetch requests by default, and forcefully disabling it was nearly impossible.\n         *\n         * This header is used to change the cache key and hence always disable it, because we do our own caching.\n         *\n         * When we drop support for Next.js <15, we may be able to remove this header, but please make sure that this is\n         * the case (I haven't actually tested.)\n         */\n        \"X-Stack-Random-Nonce\": generateSecureRandomString(),\n        // don't show a warning when proxying the API through ngrok (only relevant if the API url is an ngrok site)\n        'ngrok-skip-browser-warning': 'true',\n        ...this.options.extraRequestHeaders,\n        ...options.headers,\n      },\n      /**\n       * Cloudflare Workers does not support cache, so don't pass it there\n       */\n      ...(\"WebSocketPair\" in globalVar ? {} : {\n        cache: \"no-store\",\n      }),\n    };\n\n    let rawRes;\n    try {\n      rawRes = await fetch(url, params);\n    } catch (e) {\n      if (e instanceof TypeError) {\n        // Likely to be a network error. Retry if the request is idempotent, throw network error otherwise.\n        if (HTTP_METHODS[(params.method ?? \"GET\") as HttpMethod].idempotent) {\n          return Result.error(e);\n        } else {\n          throw await this._createNetworkError(e, session, requestType);\n        }\n      }\n      throw e;\n    }\n\n    const processedRes = await this._processResponse(rawRes);\n    if (processedRes.status === \"error\") {\n      // If the access token is invalid, reset it and retry\n      if (KnownErrors.InvalidAccessToken.isInstance(processedRes.error)) {\n        if (!tokenObj) {\n          throw new StackAssertionError(\"Received invalid access token, but session is not logged in\", { tokenObj, processedRes });\n        }\n        session.markAccessTokenExpired(tokenObj.accessToken);\n        return Result.error(processedRes.error);\n      }\n\n      // Same for the admin access token\n      // TODO HACK: Some of the backend hasn't been ported to use the new error codes, so if we have project owner tokens we need to check for ApiKeyNotFound too. Once the migration to smartRouteHandlers is complete, we can check for InvalidAdminAccessToken only.\n      if (adminSession && (KnownErrors.InvalidAdminAccessToken.isInstance(processedRes.error) || KnownErrors.ApiKeyNotFound.isInstance(processedRes.error))) {\n        if (!adminTokenObj) {\n          throw new StackAssertionError(\"Received invalid admin access token, but admin session is not logged in\", { adminTokenObj, processedRes });\n        }\n        adminSession.markAccessTokenExpired(adminTokenObj.accessToken);\n        return Result.error(processedRes.error);\n      }\n\n      // Known errors are client side errors, so except for the ones above they should not be retried\n      // Hence, throw instead of returning an error\n      throw processedRes.error;\n    }\n\n\n    const res = Object.assign(processedRes.data, {\n      usedTokens: tokenObj,\n    });\n    if (res.ok) {\n      return Result.ok(res);\n    } else if (res.status === 429) {\n      // Rate limited, so retry if we can\n      const retryAfter = res.headers.get(\"Retry-After\");\n      if (retryAfter !== null) {\n        console.log(`Rate limited while sending request to ${url}. Will retry after ${retryAfter} seconds...`);\n        await wait(Number(retryAfter) * 1000);\n        return Result.error(new Error(`Rate limited, retrying after ${retryAfter} seconds`));\n      }\n      console.log(`Rate limited while sending request to ${url}, no retry-after header received. Retrying...`);\n      return Result.error(new Error(\"Rate limited, no retry-after header received\"));\n    } else {\n      const error = await res.text();\n\n      const errorObj = new StackAssertionError(`Failed to send request to ${url}: ${res.status} ${error}`, { request: params, res, path });\n\n      if (res.status === 508 && error.includes(\"INFINITE_LOOP_DETECTED\")) {\n        // Some Vercel deployments seem to have an odd infinite loop bug. In that case, retry.\n        // See: https://github.com/stack-auth/stack-auth/issues/319\n        return Result.error(errorObj);\n      }\n\n      // Do not retry, throw error instead of returning one\n      throw errorObj;\n    }\n  }\n\n  private async _processResponse(rawRes: Response): Promise<Result<Response, KnownError>> {\n    let res = rawRes;\n    if (rawRes.headers.has(\"x-stack-actual-status\")) {\n      const actualStatus = Number(rawRes.headers.get(\"x-stack-actual-status\"));\n      res = new Response(rawRes.body, {\n        status: actualStatus,\n        statusText: rawRes.statusText,\n        headers: rawRes.headers,\n      });\n    }\n\n    // Handle known errors\n    if (res.headers.has(\"x-stack-known-error\")) {\n      const errorJson = await res.json();\n      if (res.headers.get(\"x-stack-known-error\") !== errorJson.code) {\n        throw new StackAssertionError(\"Mismatch between x-stack-known-error header and error code in body; the server's response is invalid\");\n      }\n      const error = KnownError.fromJson(errorJson);\n      return Result.error(error);\n    }\n\n    return Result.ok(res);\n  }\n\n  public async checkFeatureSupport(options: { featureName?: string } & ReadonlyJson): Promise<never> {\n    const res = await this.sendClientRequest(\"/check-feature-support\", {\n      method: \"POST\",\n      headers: {\n        \"Content-Type\": \"application/json\",\n      },\n      body: JSON.stringify(options),\n    }, null);\n\n    throw new StackAssertionError(await res.text());\n  }\n\n  async sendForgotPasswordEmail(\n    email: string,\n    callbackUrl: string,\n  ): Promise<Result<undefined, KnownErrors[\"UserNotFound\"]>> {\n    const res = await this.sendClientRequestAndCatchKnownError(\n      \"/auth/password/send-reset-code\",\n      {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\"\n        },\n        body: JSON.stringify({\n          email,\n          callback_url: callbackUrl,\n        }),\n      },\n      null,\n      [KnownErrors.UserNotFound],\n    );\n\n    if (res.status === \"error\") {\n      return Result.error(res.error);\n    } else {\n      return Result.ok(undefined);\n    }\n  }\n\n  async sendVerificationEmail(\n    email: string,\n    callbackUrl: string,\n    session: InternalSession\n  ): Promise<KnownErrors[\"EmailAlreadyVerified\"] | undefined> {\n    const res = await this.sendClientRequestAndCatchKnownError(\n      \"/contact-channels/send-verification-code\",\n      {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\"\n        },\n        body: JSON.stringify({\n          email,\n          callback_url: callbackUrl,\n        }),\n      },\n      session,\n      [KnownErrors.EmailAlreadyVerified]\n    );\n\n    if (res.status === \"error\") {\n      return res.error;\n    }\n  }\n\n  async sendMagicLinkEmail(\n    email: string,\n    callbackUrl: string,\n  ): Promise<Result<{ nonce: string }, KnownErrors[\"RedirectUrlNotWhitelisted\"]>> {\n    const res = await this.sendClientRequestAndCatchKnownError(\n      \"/auth/otp/send-sign-in-code\",\n      {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\"\n        },\n        body: JSON.stringify({\n          email,\n          callback_url: callbackUrl,\n        }),\n      },\n      null,\n      [KnownErrors.RedirectUrlNotWhitelisted]\n    );\n\n    if (res.status === \"error\") {\n      return Result.error(res.error);\n    } else {\n      return Result.ok(await res.data.json());\n    }\n  }\n\n  async resetPassword(\n    options: { code: string } & ({ password: string } | { onlyVerifyCode: true })\n  ): Promise<Result<undefined, KnownErrors[\"VerificationCodeError\"]>> {\n    const res = await this.sendClientRequestAndCatchKnownError(\n      \"onlyVerifyCode\" in options ? \"/auth/password/reset/check-code\" : \"/auth/password/reset\",\n      {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\"\n        },\n        body: JSON.stringify({\n          code: options.code,\n          ...(\"password\" in options ? { password: options.password } : {}),\n        }),\n      },\n      null,\n      [KnownErrors.VerificationCodeError]\n    );\n\n    if (res.status === \"error\") {\n      return Result.error(res.error);\n    } else {\n      return Result.ok(undefined);\n    }\n  }\n\n  async updatePassword(\n    options: { oldPassword: string, newPassword: string },\n    session: InternalSession\n  ): Promise<KnownErrors[\"PasswordConfirmationMismatch\"] | KnownErrors[\"PasswordRequirementsNotMet\"] | undefined> {\n    const res = await this.sendClientRequestAndCatchKnownError(\n      \"/auth/password/update\",\n      {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\"\n        },\n        body: JSON.stringify({\n          old_password: options.oldPassword,\n          new_password: options.newPassword,\n        }),\n      },\n      session,\n      [KnownErrors.PasswordConfirmationMismatch, KnownErrors.PasswordRequirementsNotMet]\n    );\n\n    if (res.status === \"error\") {\n      return res.error;\n    }\n  }\n\n  async setPassword(\n    options: { password: string },\n    session: InternalSession\n  ): Promise<KnownErrors[\"PasswordRequirementsNotMet\"] | undefined> {\n    const res = await this.sendClientRequestAndCatchKnownError(\n      \"/auth/password/set\",\n      {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\"\n        },\n        body: JSON.stringify(options),\n      },\n      session,\n      [KnownErrors.PasswordRequirementsNotMet]\n    );\n\n    if (res.status === \"error\") {\n      return res.error;\n    }\n  }\n\n  async verifyPasswordResetCode(code: string): Promise<Result<undefined, KnownErrors[\"VerificationCodeError\"]>> {\n    const res = await this.resetPassword({ code, onlyVerifyCode: true });\n    if (res.status === \"error\") {\n      return Result.error(res.error);\n    } else {\n      return Result.ok(undefined);\n    }\n  }\n\n  async verifyEmail(code: string): Promise<Result<undefined, KnownErrors[\"VerificationCodeError\"]>> {\n    const res = await this.sendClientRequestAndCatchKnownError(\n      \"/contact-channels/verify\",\n      {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\"\n        },\n        body: JSON.stringify({\n          code,\n        }),\n      },\n      null,\n      [KnownErrors.VerificationCodeError]\n    );\n\n    if (res.status === \"error\") {\n      return Result.error(res.error);\n    } else {\n      return Result.ok(undefined);\n    }\n  }\n\n  async initiatePasskeyRegistration(\n    options: {},\n    session: InternalSession\n  ): Promise<Result<{ options_json: PublicKeyCredentialCreationOptionsJSON, code: string }, KnownErrors[]>> {\n    const res = await this.sendClientRequestAndCatchKnownError(\n      \"/auth/passkey/initiate-passkey-registration\",\n      {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\"\n        },\n        body: JSON.stringify(options),\n      },\n      session,\n      []\n    );\n\n    if (res.status === \"error\") {\n      return Result.error(res.error);\n    }\n\n    return Result.ok(await res.data.json());\n  }\n\n  async registerPasskey(\n    options: { credential: RegistrationResponseJSON, code: string },\n    session: InternalSession\n  ): Promise<Result<undefined, KnownErrors[\"PasskeyRegistrationFailed\"]>> {\n    const res = await this.sendClientRequestAndCatchKnownError(\n      \"/auth/passkey/register\",\n      {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\"\n        },\n        body: JSON.stringify(options),\n      },\n      session,\n      [KnownErrors.PasskeyRegistrationFailed]\n    );\n    if (res.status === \"error\") {\n      return Result.error(res.error);\n    }\n    return Result.ok(undefined);\n  }\n\n  async initiatePasskeyAuthentication(\n    options: {\n    },\n    session: InternalSession\n  ): Promise<Result<{ options_json: PublicKeyCredentialRequestOptionsJSON, code: string }, KnownErrors[]>> {\n    const res = await this.sendClientRequestAndCatchKnownError(\n      \"/auth/passkey/initiate-passkey-authentication\",\n      {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\"\n        },\n        body: JSON.stringify(options),\n      },\n      session,\n      []\n    );\n\n    if (res.status === \"error\") {\n      return Result.error(res.error);\n    }\n\n    return Result.ok(await res.data.json());\n  }\n\n  async sendTeamInvitation(options: {\n    email: string,\n    teamId: string,\n    callbackUrl: string,\n    session: InternalSession,\n  }): Promise<void> {\n    await this.sendClientRequest(\n      \"/team-invitations/send-code\",\n      {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\"\n        },\n        body: JSON.stringify({\n          email: options.email,\n          team_id: options.teamId,\n          callback_url: options.callbackUrl,\n        }),\n      },\n      options.session,\n    );\n  }\n\n  async acceptTeamInvitation<T extends 'use' | 'details' | 'check'>(options: {\n    code: string,\n    session: InternalSession,\n    type: T,\n  }): Promise<Result<T extends 'details' ? { team_display_name: string } : undefined, KnownErrors[\"VerificationCodeError\"]>> {\n    const res = await this.sendClientRequestAndCatchKnownError(\n      options.type === 'check' ?\n        \"/team-invitations/accept/check-code\" :\n        options.type === 'details' ?\n          \"/team-invitations/accept/details\" :\n          \"/team-invitations/accept\",\n      {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\"\n        },\n        body: JSON.stringify({\n          code: options.code,\n        }),\n      },\n      options.session,\n      [KnownErrors.VerificationCodeError]\n    );\n\n    if (res.status === \"error\") {\n      return Result.error(res.error);\n    } else {\n      return Result.ok(await res.data.json());\n    }\n  }\n\n  async totpMfa(\n    attemptCode: string,\n    totp: string,\n    session: InternalSession\n  ) {\n    const res = await this.sendClientRequest(\"/auth/mfa/sign-in\", {\n      method: \"POST\",\n      headers: {\n        \"Content-Type\": \"application/json\"\n      },\n      body: JSON.stringify({\n        code: attemptCode,\n        type: \"totp\",\n        totp: totp,\n      }),\n    }, session);\n\n    const result = await res.json();\n    return {\n      accessToken: result.access_token,\n      refreshToken: result.refresh_token,\n      newUser: result.is_new_user,\n    };\n  }\n\n  async signInWithCredential(\n    email: string,\n    password: string,\n    session: InternalSession\n  ): Promise<Result<{ accessToken: string, refreshToken: string }, KnownErrors[\"EmailPasswordMismatch\"]>> {\n    const res = await this.sendClientRequestAndCatchKnownError(\n      \"/auth/password/sign-in\",\n      {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\"\n        },\n        body: JSON.stringify({\n          email,\n          password,\n        }),\n      },\n      session,\n      [KnownErrors.EmailPasswordMismatch]\n    );\n\n    if (res.status === \"error\") {\n      return Result.error(res.error);\n    }\n\n    const result = await res.data.json();\n    return Result.ok({\n      accessToken: result.access_token,\n      refreshToken: result.refresh_token,\n    });\n  }\n\n  async signUpWithCredential(\n    email: string,\n    password: string,\n    emailVerificationRedirectUrl: string,\n    session: InternalSession,\n  ): Promise<Result<{ accessToken: string, refreshToken: string }, KnownErrors[\"UserWithEmailAlreadyExists\"] | KnownErrors[\"PasswordRequirementsNotMet\"]>> {\n    const res = await this.sendClientRequestAndCatchKnownError(\n      \"/auth/password/sign-up\",\n      {\n        headers: {\n          \"Content-Type\": \"application/json\"\n        },\n        method: \"POST\",\n        body: JSON.stringify({\n          email,\n          password,\n          verification_callback_url: emailVerificationRedirectUrl,\n        }),\n      },\n      session,\n      [KnownErrors.UserWithEmailAlreadyExists, KnownErrors.PasswordRequirementsNotMet]\n    );\n\n    if (res.status === \"error\") {\n      return Result.error(res.error);\n    }\n\n    const result = await res.data.json();\n    return Result.ok({\n      accessToken: result.access_token,\n      refreshToken: result.refresh_token,\n    });\n  }\n\n  async signUpAnonymously(session: InternalSession): Promise<Result<{ accessToken: string, refreshToken: string }, never>> {\n    const res = await this.sendClientRequestAndCatchKnownError(\n      \"/auth/anonymous/sign-up\",\n      {\n        method: \"POST\",\n      },\n      session,\n      [],\n    );\n\n    if (res.status === \"error\") {\n      return Result.error(res.error);\n    }\n\n    const result = await res.data.json();\n    return Result.ok({\n      accessToken: result.access_token,\n      refreshToken: result.refresh_token,\n    });\n  }\n\n  async signInWithMagicLink(code: string): Promise<Result<{ newUser: boolean, accessToken: string, refreshToken: string }, KnownErrors[\"VerificationCodeError\"]>> {\n    const res = await this.sendClientRequestAndCatchKnownError(\n      \"/auth/otp/sign-in\",\n      {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\"\n        },\n        body: JSON.stringify({\n          code,\n        }),\n      },\n      null,\n      [KnownErrors.VerificationCodeError]\n    );\n\n    if (res.status === \"error\") {\n      return Result.error(res.error);\n    }\n\n    const result = await res.data.json();\n    return Result.ok({\n      accessToken: result.access_token,\n      refreshToken: result.refresh_token,\n      newUser: result.is_new_user,\n    });\n  }\n\n  async signInWithPasskey(body: { authentication_response: AuthenticationResponseJSON, code: string }): Promise<Result<{accessToken: string, refreshToken: string }, KnownErrors[\"PasskeyAuthenticationFailed\"]>> {\n    const res = await this.sendClientRequestAndCatchKnownError(\n      \"/auth/passkey/sign-in\",\n      {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\"\n        },\n        body: JSON.stringify(body),\n      },\n      null,\n      [KnownErrors.PasskeyAuthenticationFailed]\n    );\n\n    if (res.status === \"error\") {\n      return Result.error(res.error);\n    }\n\n    const result = await res.data.json();\n    return Result.ok({\n      accessToken: result.access_token,\n      refreshToken: result.refresh_token,\n    });\n  }\n\n  async getOAuthUrl(\n    options: {\n      provider: string,\n      redirectUrl: string,\n      errorRedirectUrl: string,\n      afterCallbackRedirectUrl?: string,\n      codeChallenge: string,\n      state: string,\n      type: \"authenticate\" | \"link\",\n      providerScope?: string,\n    } & ({ type: \"authenticate\" } | { type: \"link\", session: InternalSession })\n  ): Promise<string> {\n    const updatedRedirectUrl = new URL(options.redirectUrl);\n    for (const key of [\"code\", \"state\"]) {\n      if (updatedRedirectUrl.searchParams.has(key)) {\n        console.warn(\"Redirect URL already contains \" + key + \" parameter, removing it as it will be overwritten by the OAuth callback\");\n      }\n      updatedRedirectUrl.searchParams.delete(key);\n    }\n\n    if (!('publishableClientKey' in this.options)) {\n      // TODO fix\n      throw new Error(\"Admin session token is currently not supported for OAuth\");\n    }\n    const url = new URL(this.getApiUrl() + \"/auth/oauth/authorize/\" + options.provider.toLowerCase());\n    url.searchParams.set(\"client_id\", this.projectId);\n    url.searchParams.set(\"client_secret\", this.options.publishableClientKey);\n    url.searchParams.set(\"redirect_uri\", updatedRedirectUrl.toString());\n    url.searchParams.set(\"scope\", \"legacy\");\n    url.searchParams.set(\"state\", options.state);\n    url.searchParams.set(\"grant_type\", \"authorization_code\");\n    url.searchParams.set(\"code_challenge\", options.codeChallenge);\n    url.searchParams.set(\"code_challenge_method\", \"S256\");\n    url.searchParams.set(\"response_type\", \"code\");\n    url.searchParams.set(\"type\", options.type);\n    url.searchParams.set(\"error_redirect_url\", options.errorRedirectUrl);\n\n    if (options.afterCallbackRedirectUrl) {\n      url.searchParams.set(\"after_callback_redirect_url\", options.afterCallbackRedirectUrl);\n    }\n\n    if (options.type === \"link\") {\n      const tokens = await options.session.getOrFetchLikelyValidTokens(20_000);\n      url.searchParams.set(\"token\", tokens?.accessToken.token || \"\");\n\n      if (options.providerScope) {\n        url.searchParams.set(\"provider_scope\", options.providerScope);\n      }\n    }\n\n    return url.toString();\n  }\n\n  async callOAuthCallback(options: {\n    oauthParams: URLSearchParams,\n    redirectUri: string,\n    codeVerifier: string,\n    state: string,\n  }): Promise<{ newUser: boolean, afterCallbackRedirectUrl?: string, accessToken: string, refreshToken: string }> {\n    if (!('publishableClientKey' in this.options)) {\n      // TODO fix\n      throw new Error(\"Admin session token is currently not supported for OAuth\");\n    }\n    const as = {\n      issuer: this.options.getBaseUrl(),\n      algorithm: 'oauth2',\n      token_endpoint: this.getApiUrl() + '/auth/oauth/token',\n    };\n    const client: oauth.Client = {\n      client_id: this.projectId,\n      client_secret: this.options.publishableClientKey,\n      token_endpoint_auth_method: 'client_secret_post',\n    };\n    const params = await this._networkRetryException(\n      async () => oauth.validateAuthResponse(as, client, options.oauthParams, options.state),\n    );\n    if (oauth.isOAuth2Error(params)) {\n      throw new StackAssertionError(\"Error validating outer OAuth response\", { params }); // Handle OAuth 2.0 redirect error\n    }\n    const response = await oauth.authorizationCodeGrantRequest(\n      as,\n      client,\n      params,\n      options.redirectUri,\n      options.codeVerifier,\n    );\n\n    const result = await oauth.processAuthorizationCodeOAuth2Response(as, client, response);\n    if (oauth.isOAuth2Error(result)) {\n      if (\"code\" in result && result.code === \"MULTI_FACTOR_AUTHENTICATION_REQUIRED\") {\n        throw new KnownErrors.MultiFactorAuthenticationRequired((result as any).details.attempt_code);\n      }\n      // TODO Handle OAuth 2.0 response body error\n      throw new StackAssertionError(\"Outer OAuth error during authorization code response\", { result });\n    }\n    return {\n      newUser: result.is_new_user as boolean,\n      afterCallbackRedirectUrl: result.after_callback_redirect_url as string | undefined,\n      accessToken: result.access_token,\n      refreshToken: result.refresh_token ?? throwErr(\"Refresh token not found in outer OAuth response\"),\n    };\n  }\n\n  async signOut(session: InternalSession): Promise<void> {\n    const tokenObj = await session.getOrFetchLikelyValidTokens(20_000);\n    if (tokenObj) {\n      const resOrError = await this.sendClientRequestAndCatchKnownError(\n        \"/auth/sessions/current\",\n        {\n          method: \"DELETE\",\n          headers: {\n            \"Content-Type\": \"application/json\"\n          },\n          body: JSON.stringify({}),\n        },\n        session,\n        [KnownErrors.RefreshTokenError]\n      );\n      if (resOrError.status === \"error\") {\n        if (KnownErrors.RefreshTokenError.isInstance(resOrError.error)) {\n          // refresh token was already invalid, just continue like nothing happened\n        } else {\n          // this should never happen\n          throw new StackAssertionError(\"Unexpected error\", { error: resOrError.error });\n        }\n      } else {\n        // user was signed out successfully, all good\n      }\n    }\n    session.markInvalid();\n  }\n\n  async getClientUserByToken(session: InternalSession): Promise<CurrentUserCrud[\"Client\"][\"Read\"] | null> {\n    const responseOrError = await this.sendClientRequestAndCatchKnownError(\n      \"/users/me\",\n      {},\n      session,\n      [KnownErrors.CannotGetOwnUserWithoutUser],\n    );\n    if (responseOrError.status === \"error\") {\n      if (KnownErrors.CannotGetOwnUserWithoutUser.isInstance(responseOrError.error)) {\n        return null;\n      } else {\n        throw new StackAssertionError(\"Unexpected uncaught error\", { cause: responseOrError.error });\n      }\n    }\n    const response = responseOrError.data;\n    const user: CurrentUserCrud[\"Client\"][\"Read\"] = await response.json();\n    if (!(user as any)) throw new StackAssertionError(\"User endpoint returned null; this should never happen\");\n    return user;\n  }\n\n  async listTeamInvitations(\n    options: {\n      teamId: string,\n    },\n    session: InternalSession,\n  ): Promise<TeamInvitationCrud['Client']['Read'][]> {\n    const response = await this.sendClientRequest(\n      \"/team-invitations?\" + new URLSearchParams({ team_id: options.teamId }),\n      {},\n      session,\n    );\n    const result = await response.json() as TeamInvitationCrud['Client']['List'];\n    return result.items;\n  }\n\n  async revokeTeamInvitation(\n    invitationId: string,\n    teamId: string,\n    session: InternalSession,\n  ) {\n    await this.sendClientRequest(\n      `/team-invitations/${invitationId}?team_id=${teamId}`,\n      { method: \"DELETE\" },\n      session,\n    );\n  }\n\n  async listTeamMemberProfiles(\n    options: {\n      teamId?: string,\n      userId?: string,\n    },\n    session: InternalSession,\n  ): Promise<TeamMemberProfilesCrud['Client']['Read'][]> {\n    const response = await this.sendClientRequest(\n      \"/team-member-profiles?\" + new URLSearchParams(filterUndefined({\n        team_id: options.teamId,\n        user_id: options.userId,\n      })),\n      {},\n      session,\n    );\n    const result = await response.json() as TeamMemberProfilesCrud['Client']['List'];\n    return result.items;\n  }\n\n  async getTeamMemberProfile(\n    options: {\n      teamId: string,\n      userId: string,\n    },\n    session: InternalSession,\n  ): Promise<TeamMemberProfilesCrud['Client']['Read']> {\n    const response = await this.sendClientRequest(\n      `/team-member-profiles/${options.teamId}/${options.userId}`,\n      {},\n      session,\n    );\n    return await response.json();\n  }\n\n  async leaveTeam(\n    teamId: string,\n    session: InternalSession,\n  ) {\n    await this.sendClientRequest(\n      `/team-memberships/${teamId}/me`,\n      {\n        method: \"DELETE\",\n        headers: {\n          \"content-type\": \"application/json\",\n        },\n        body: JSON.stringify({}),\n      },\n      session,\n    );\n  }\n\n  async updateTeamMemberProfile(\n    options: {\n      teamId: string,\n      userId: string,\n      profile: TeamMemberProfilesCrud['Client']['Update'],\n    },\n    session: InternalSession,\n  ) {\n    await this.sendClientRequest(\n      `/team-member-profiles/${options.teamId}/${options.userId}`,\n      {\n        method: \"PATCH\",\n        headers: {\n          \"content-type\": \"application/json\",\n        },\n        body: JSON.stringify(options.profile),\n      },\n      session,\n    );\n  }\n\n  async updateTeam(\n    options: {\n      teamId: string,\n      data: TeamsCrud['Client']['Update'],\n    },\n    session: InternalSession,\n  ) {\n    await this.sendClientRequest(\n      `/teams/${options.teamId}`,\n      {\n        method: \"PATCH\",\n        headers: {\n          \"content-type\": \"application/json\",\n        },\n        body: JSON.stringify(options.data),\n      },\n      session,\n    );\n  }\n\n  async listCurrentUserTeamPermissions(\n    options: {\n      teamId: string,\n      recursive: boolean,\n    },\n    session: InternalSession\n  ): Promise<TeamPermissionsCrud['Client']['Read'][]> {\n    const response = await this.sendClientRequest(\n      `/team-permissions?team_id=${options.teamId}&user_id=me&recursive=${options.recursive}`,\n      {},\n      session,\n    );\n    const result = await response.json() as TeamPermissionsCrud['Client']['List'];\n    return result.items;\n  }\n\n  async listCurrentUserProjectPermissions(\n    options: {\n      recursive: boolean,\n    },\n    session: InternalSession\n  ): Promise<ProjectPermissionsCrud['Client']['Read'][]> {\n    const response = await this.sendClientRequest(\n      `/project-permissions?user_id=me&recursive=${options.recursive}`,\n      {},\n      session,\n    );\n    const result = await response.json() as ProjectPermissionsCrud['Client']['List'];\n    return result.items;\n  }\n\n  async listCurrentUserTeams(session: InternalSession): Promise<TeamsCrud[\"Client\"][\"Read\"][]> {\n    const response = await this.sendClientRequest(\n      \"/teams?user_id=me\",\n      {},\n      session,\n    );\n    const result = await response.json() as TeamsCrud[\"Client\"][\"List\"];\n    return result.items;\n  }\n\n  async getClientProject(): Promise<Result<ClientProjectsCrud['Client']['Read'], KnownErrors[\"ProjectNotFound\"]>> {\n    const responseOrError = await this.sendClientRequestAndCatchKnownError(\"/projects/current\", {}, null, [KnownErrors.ProjectNotFound]);\n    if (responseOrError.status === \"error\") {\n      return Result.error(responseOrError.error);\n    }\n    const response = responseOrError.data;\n    const project: ClientProjectsCrud['Client']['Read'] = await response.json();\n    return Result.ok(project);\n  }\n\n  async updateClientUser(update: CurrentUserCrud[\"Client\"][\"Update\"], session: InternalSession) {\n    await this.sendClientRequest(\n      \"/users/me\",\n      {\n        method: \"PATCH\",\n        headers: {\n          \"content-type\": \"application/json\",\n        },\n        body: JSON.stringify(update),\n      },\n      session,\n    );\n  }\n\n  async listProjects(session: InternalSession): Promise<AdminUserProjectsCrud['Client']['Read'][]> {\n    const response = await this.sendClientRequest(\"/internal/projects\", {}, session);\n    if (!response.ok) {\n      throw new Error(\"Failed to list projects: \" + response.status + \" \" + (await response.text()));\n    }\n\n    const json = await response.json() as AdminUserProjectsCrud['Client']['List'];\n    return json.items;\n  }\n\n  async createProject(\n    project: AdminUserProjectsCrud['Client']['Create'],\n    session: InternalSession,\n  ): Promise<AdminUserProjectsCrud['Client']['Read']> {\n    const fetchResponse = await this.sendClientRequest(\n      \"/internal/projects\",\n      {\n        method: \"POST\",\n        headers: {\n          \"content-type\": \"application/json\",\n        },\n        body: JSON.stringify(project),\n      },\n      session,\n    );\n    if (!fetchResponse.ok) {\n      throw new Error(\"Failed to create project: \" + fetchResponse.status + \" \" + (await fetchResponse.text()));\n    }\n\n    const json = await fetchResponse.json();\n    return json;\n  }\n\n  async createProviderAccessToken(\n    provider: string,\n    scope: string,\n    session: InternalSession,\n  ): Promise<ConnectedAccountAccessTokenCrud['Client']['Read']> {\n    const response = await this.sendClientRequest(\n      `/connected-accounts/me/${provider}/access-token`,\n      {\n        method: \"POST\",\n        headers: {\n          \"content-type\": \"application/json\",\n        },\n        body: JSON.stringify({ scope }),\n      },\n      session,\n    );\n    return await response.json();\n  }\n\n  async createClientTeam(\n    data: TeamsCrud['Client']['Create'],\n    session: InternalSession,\n  ): Promise<TeamsCrud['Client']['Read']> {\n    const response = await this.sendClientRequest(\n      \"/teams\",\n      {\n        method: \"POST\",\n        headers: {\n          \"content-type\": \"application/json\",\n        },\n        body: JSON.stringify(data),\n      },\n      session,\n    );\n    return await response.json();\n  }\n\n  async deleteTeam(\n    teamId: string,\n    session: InternalSession,\n  ) {\n    await this.sendClientRequest(\n      `/teams/${teamId}`,\n      {\n        method: \"DELETE\",\n      },\n      session,\n    );\n  }\n\n  async deleteCurrentUser(session: InternalSession) {\n    await this.sendClientRequest(\n      \"/users/me\",\n      {\n        method: \"DELETE\",\n      },\n      session,\n    );\n  }\n\n  async createClientContactChannel(\n    data: ContactChannelsCrud['Client']['Create'],\n    session: InternalSession,\n  ): Promise<ContactChannelsCrud['Client']['Read']> {\n    const response = await this.sendClientRequest(\n      \"/contact-channels\",\n      {\n        method: \"POST\",\n        headers: {\n          \"content-type\": \"application/json\",\n        },\n        body: JSON.stringify(data),\n      },\n      session,\n    );\n    return await response.json();\n  }\n\n  async updateClientContactChannel(\n    id: string,\n    data: ContactChannelsCrud['Client']['Update'],\n    session: InternalSession,\n  ): Promise<ContactChannelsCrud['Client']['Read']> {\n    const response = await this.sendClientRequest(\n      `/contact-channels/me/${id}`,\n      {\n        method: \"PATCH\",\n        headers: {\n          \"content-type\": \"application/json\",\n        },\n        body: JSON.stringify(data),\n      },\n      session,\n    );\n    return await response.json();\n  }\n\n  async deleteClientContactChannel(\n    id: string,\n    session: InternalSession,\n  ): Promise<void> {\n    await this.sendClientRequest(\n      `/contact-channels/me/${id}`,\n      {\n        method: \"DELETE\",\n      },\n      session,\n    );\n  }\n\n  async deleteSession(\n    sessionId: string,\n    session: InternalSession,\n  ): Promise<void> {\n    await this.sendClientRequest(\n      `/auth/sessions/${sessionId}?user_id=me`,\n      {\n        method: \"DELETE\",\n      },\n      session,\n    );\n  }\n\n  async listSessions(\n    session: InternalSession,\n  ): Promise<SessionsCrud['Client']['List']> {\n    const response = await this.sendClientRequest(\n      \"/auth/sessions?user_id=me\",\n      {\n        method: \"GET\",\n      },\n      session,\n    );\n    return await response.json();\n  }\n\n\n  async listClientContactChannels(\n    session: InternalSession,\n  ): Promise<ContactChannelsCrud['Client']['Read'][]> {\n    const response = await this.sendClientRequest(\n      \"/contact-channels?user_id=me\",\n      {\n        method: \"GET\",\n      },\n      session,\n    );\n    const json = await response.json() as ContactChannelsCrud['Client']['List'];\n    return json.items;\n  }\n\n  async sendCurrentUserContactChannelVerificationEmail(\n    contactChannelId: string,\n    callbackUrl: string,\n    session: InternalSession,\n  ): Promise<Result<undefined, KnownErrors[\"EmailAlreadyVerified\"]>> {\n    const responseOrError = await this.sendClientRequestAndCatchKnownError(\n      `/contact-channels/me/${contactChannelId}/send-verification-code`,\n      {\n        method: \"POST\",\n        headers: {\n          \"content-type\": \"application/json\",\n        },\n        body: JSON.stringify({ callback_url: callbackUrl }),\n      },\n      session,\n      [KnownErrors.EmailAlreadyVerified]\n    );\n\n    if (responseOrError.status === \"error\") {\n      return Result.error(responseOrError.error);\n    }\n    return Result.ok(undefined);\n  }\n\n  async cliLogin(\n    loginCode: string,\n    refreshToken: string,\n    session: InternalSession\n  ): Promise<Result<undefined, KnownErrors[\"SchemaError\"]>> {\n    const responseOrError = await this.sendClientRequestAndCatchKnownError(\n      \"/auth/cli/complete\",\n      {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\"\n        },\n        body: JSON.stringify({\n          login_code: loginCode,\n          refresh_token: refreshToken,\n        }),\n      },\n      session,\n      [KnownErrors.SchemaError]\n    );\n\n    if (responseOrError.status === \"error\") {\n      return Result.error(responseOrError.error);\n    }\n    return Result.ok(undefined);\n  }\n\n  private async _getApiKeyRequestInfo(options: { user_id: string | null } | { team_id: string }) {\n    if (\"user_id\" in options && \"team_id\" in options) {\n      throw new StackAssertionError(\"Cannot specify both user_id and team_id in _getApiKeyRequestInfo\");\n    }\n\n    return {\n      endpoint: \"team_id\" in options ? \"/team-api-keys\" : \"/user-api-keys\",\n      queryParams: new URLSearchParams(filterUndefinedOrNull(options)),\n    };\n  }\n\n  // API Keys CRUD operations\n  listProjectApiKeys(options: { user_id: string }, session: InternalSession | null, requestType: \"client\" | \"server\" | \"admin\"): Promise<UserApiKeysCrud['Client']['Read'][]>;\n  listProjectApiKeys(options: { team_id: string }, session: InternalSession | null, requestType: \"client\" | \"server\" | \"admin\"): Promise<TeamApiKeysCrud['Client']['Read'][]>;\n  listProjectApiKeys(options: { user_id: string } | { team_id: string }, session: InternalSession | null, requestType: \"client\" | \"server\" | \"admin\"): Promise<(UserApiKeysCrud['Client']['Read'] | TeamApiKeysCrud['Client']['Read'])[]>;\n  async listProjectApiKeys(\n    options: { user_id: string } | { team_id: string },\n    session: InternalSession | null,\n    requestType: \"client\" | \"server\" | \"admin\",\n  ): Promise<(UserApiKeysCrud['Client']['Read'] | TeamApiKeysCrud['Client']['Read'])[]> {\n    const sendRequest = (requestType === \"client\" ? this.sendClientRequest : (this as any).sendServerRequest as never).bind(this);\n    const { endpoint, queryParams } = await this._getApiKeyRequestInfo(options);\n\n    const response = await sendRequest(\n      `${endpoint}?${queryParams.toString()}`,\n      {\n        method: \"GET\",\n      },\n      session,\n      requestType,\n    );\n    const json = await response.json();\n    return json.items;\n  }\n\n  createProjectApiKey(data: yup.InferType<typeof userApiKeysCreateInputSchema>, session: InternalSession | null, requestType: \"client\" | \"server\" | \"admin\"): Promise<yup.InferType<typeof userApiKeysCreateOutputSchema>>;\n  createProjectApiKey(data: yup.InferType<typeof teamApiKeysCreateInputSchema>, session: InternalSession | null, requestType: \"client\" | \"server\" | \"admin\"): Promise<yup.InferType<typeof teamApiKeysCreateOutputSchema>>;\n  createProjectApiKey(data: yup.InferType<typeof userApiKeysCreateInputSchema> | yup.InferType<typeof teamApiKeysCreateInputSchema>, session: InternalSession | null, requestType: \"client\" | \"server\" | \"admin\"): Promise<yup.InferType<typeof userApiKeysCreateOutputSchema> | yup.InferType<typeof teamApiKeysCreateOutputSchema>>;\n  async createProjectApiKey(\n    data: yup.InferType<typeof userApiKeysCreateInputSchema> | yup.InferType<typeof teamApiKeysCreateInputSchema>,\n    session: InternalSession | null,\n    requestType: \"client\" | \"server\" | \"admin\",\n  ): Promise<yup.InferType<typeof userApiKeysCreateOutputSchema> | yup.InferType<typeof teamApiKeysCreateOutputSchema>> {\n    const sendRequest = (requestType === \"client\" ? this.sendClientRequest : (this as any).sendServerRequest as never).bind(this);\n    const { endpoint } = await this._getApiKeyRequestInfo(data);\n\n    const response = await sendRequest(\n      `${endpoint}`,\n      {\n        method: \"POST\",\n        headers: {\n          \"content-type\": \"application/json\",\n        },\n        body: JSON.stringify(data),\n      },\n      session,\n      requestType,\n    );\n    return await response.json();\n  }\n\n  getProjectApiKey(options: { user_id: string | null }, keyId: string, session: InternalSession | null, requestType: \"client\" | \"server\" | \"admin\"): Promise<UserApiKeysCrud['Client']['Read']>;\n  getProjectApiKey(options: { team_id: string }, keyId: string, session: InternalSession | null, requestType: \"client\" | \"server\" | \"admin\"): Promise<TeamApiKeysCrud['Client']['Read']>;\n  getProjectApiKey(options: { user_id: string | null } | { team_id: string }, keyId: string, session: InternalSession | null, requestType: \"client\" | \"server\" | \"admin\"): Promise<UserApiKeysCrud['Client']['Read'] | TeamApiKeysCrud['Client']['Read']>;\n  async getProjectApiKey(\n    options: { user_id: string | null } | { team_id: string },\n    keyId: string,\n    session: InternalSession | null,\n    requestType: \"client\" | \"server\" | \"admin\",\n  ): Promise<UserApiKeysCrud['Client']['Read'] | TeamApiKeysCrud['Client']['Read']> {\n    const sendRequest = (requestType === \"client\" ? this.sendClientRequest : (this as any).sendServerRequest as never).bind(this);\n    const { endpoint, queryParams } = await this._getApiKeyRequestInfo(options);\n\n    const response = await sendRequest(\n      `${endpoint}/${keyId}?${queryParams.toString()}`,\n      {\n        method: \"GET\",\n      },\n      session,\n      requestType,\n    );\n    return await response.json();\n  }\n\n  updateProjectApiKey(options: { user_id: string }, keyId: string, data: UserApiKeysCrud['Client']['Update'], session: InternalSession | null, requestType: \"client\" | \"server\" | \"admin\"): Promise<UserApiKeysCrud['Client']['Read']>;\n  updateProjectApiKey(options: { team_id: string }, keyId: string, data: TeamApiKeysCrud['Client']['Update'], session: InternalSession | null, requestType: \"client\" | \"server\" | \"admin\"): Promise<TeamApiKeysCrud['Client']['Read']>;\n  updateProjectApiKey(options: { user_id: string } | { team_id: string }, keyId: string, data: UserApiKeysCrud['Client']['Update'] | TeamApiKeysCrud['Client']['Update'], session: InternalSession | null, requestType: \"client\" | \"server\" | \"admin\"): Promise<UserApiKeysCrud['Client']['Read'] | TeamApiKeysCrud['Client']['Read']>;\n  async updateProjectApiKey(\n    options: { user_id: string } | { team_id: string },\n    keyId: string,\n    data: UserApiKeysCrud['Client']['Update'] | TeamApiKeysCrud['Client']['Update'],\n    session: InternalSession | null,\n    requestType: \"client\" | \"server\" | \"admin\",\n  ): Promise<UserApiKeysCrud['Client']['Read'] | TeamApiKeysCrud['Client']['Read']> {\n    const sendRequest = (requestType === \"client\" ? this.sendClientRequest : (this as any).sendServerRequest as never).bind(this);\n    const { endpoint, queryParams } = await this._getApiKeyRequestInfo(options);\n\n    const response = await sendRequest(\n      `${endpoint}/${keyId}?${queryParams.toString()}`,\n      {\n        method: \"PATCH\",\n        headers: {\n          \"content-type\": \"application/json\",\n        },\n        body: JSON.stringify(data),\n      },\n      session,\n      requestType,\n    );\n    return await response.json();\n  }\n\n  checkProjectApiKey(type: \"user\", apiKey: string, session: InternalSession | null, requestType: \"client\" | \"server\" | \"admin\"): Promise<UserApiKeysCrud['Client']['Read'] | null>;\n  checkProjectApiKey(type: \"team\", apiKey: string, session: InternalSession | null, requestType: \"client\" | \"server\" | \"admin\"): Promise<TeamApiKeysCrud['Client']['Read'] | null>;\n  checkProjectApiKey(type: \"user\" | \"team\", apiKey: string, session: InternalSession | null, requestType: \"client\" | \"server\" | \"admin\"): Promise<UserApiKeysCrud['Client']['Read'] | TeamApiKeysCrud['Client']['Read'] | null>;\n  async checkProjectApiKey(type: \"user\" | \"team\", apiKey: string, session: InternalSession | null, requestType: \"client\" | \"server\" | \"admin\"): Promise<UserApiKeysCrud['Client']['Read'] | TeamApiKeysCrud['Client']['Read'] | null> {\n    const sendRequest = (requestType === \"client\" ? this.sendClientRequestAndCatchKnownError : (this as any).sendServerRequestAndCatchKnownError as never).bind(this);\n    const result = await sendRequest(\n      `/${type}-api-keys/check`,\n      {\n        method: \"POST\",\n        headers: {\n          \"content-type\": \"application/json\",\n        },\n        body: JSON.stringify({ api_key: apiKey }),\n      },\n      session,\n      [KnownErrors.ApiKeyNotValid]\n    );\n    if (result.status === \"error\") {\n      return null;\n    }\n    return await result.data.json();\n  }\n}\n\n", "import { KnownErrors } from \"../known-errors\";\nimport { AccessToken, InternalSession, RefreshToken } from \"../sessions\";\nimport { StackAssertionError } from \"../utils/errors\";\nimport { filterUndefined } from \"../utils/objects\";\nimport { Result } from \"../utils/results\";\nimport { urlString } from \"../utils/urls\";\nimport {\n  ClientInterfaceOptions,\n  StackClientInterface\n} from \"./clientInterface\";\nimport { ContactChannelsCrud } from \"./crud/contact-channels\";\nimport { CurrentUserCrud } from \"./crud/current-user\";\nimport { ConnectedAccountAccessTokenCrud } from \"./crud/oauth\";\nimport { ProjectPermissionsCrud } from \"./crud/project-permissions\";\nimport { SessionsCrud } from \"./crud/sessions\";\nimport { TeamInvitationCrud } from \"./crud/team-invitation\";\nimport { TeamMemberProfilesCrud } from \"./crud/team-member-profiles\";\nimport { TeamMembershipsCrud } from \"./crud/team-memberships\";\nimport { TeamPermissionsCrud } from \"./crud/team-permissions\";\nimport { TeamsCrud } from \"./crud/teams\";\nimport { UsersCrud } from \"./crud/users\";\n\nexport type ServerAuthApplicationOptions = (\n  & ClientInterfaceOptions\n  & (\n    | {\n      readonly secretServerKey: string,\n    }\n    | {\n      readonly projectOwnerSession: InternalSession,\n    }\n  )\n);\n\nexport class StackServerInterface extends StackClientInterface {\n  constructor(public override options: ServerAuthApplicationOptions) {\n    super(options);\n  }\n\n  protected async sendServerRequest(path: string, options: RequestInit, session: InternalSession | null, requestType: \"server\" | \"admin\" = \"server\") {\n    return await this.sendClientRequest(\n      path,\n      {\n        ...options,\n        headers: {\n          \"x-stack-secret-server-key\": \"secretServerKey\" in this.options ? this.options.secretServerKey : \"\",\n          ...options.headers,\n        },\n      },\n      session,\n      requestType,\n    );\n  }\n\n  protected async sendServerRequestAndCatchKnownError<E extends typeof KnownErrors[keyof KnownErrors]>(\n    path: string,\n    requestOptions: RequestInit,\n    tokenStoreOrNull: InternalSession | null,\n    errorsToCatch: readonly E[],\n  ): Promise<Result<\n    Response & {\n      usedTokens: {\n        accessToken: AccessToken,\n        refreshToken: RefreshToken | null,\n      } | null,\n    },\n    InstanceType<E>\n  >> {\n    try {\n      return Result.ok(await this.sendServerRequest(path, requestOptions, tokenStoreOrNull));\n    } catch (e) {\n      for (const errorType of errorsToCatch) {\n        if (errorType.isInstance(e)) {\n          return Result.error(e as InstanceType<E>);\n        }\n      }\n      throw e;\n    }\n  }\n\n  async createServerUser(data: UsersCrud['Server']['Create']): Promise<UsersCrud['Server']['Read']> {\n    const response = await this.sendServerRequest(\n      \"/users\",\n      {\n        method: \"POST\",\n        headers: {\n          \"content-type\": \"application/json\",\n        },\n        body: JSON.stringify(data),\n      },\n      null,\n    );\n    return await response.json();\n  }\n\n  async getServerUserByToken(session: InternalSession): Promise<CurrentUserCrud['Server']['Read'] | null> {\n    const responseOrError = await this.sendServerRequestAndCatchKnownError(\n      \"/users/me\",\n      {},\n      session,\n      [KnownErrors.CannotGetOwnUserWithoutUser],\n    );\n    if (responseOrError.status === \"error\") {\n      if (KnownErrors.CannotGetOwnUserWithoutUser.isInstance(responseOrError.error)) {\n        return null;\n      } else {\n        throw new StackAssertionError(\"Unexpected uncaught error\", { cause: responseOrError.error });\n      }\n    }\n    const response = responseOrError.data;\n    const user: CurrentUserCrud['Server']['Read'] = await response.json();\n    if (!(user as any)) throw new StackAssertionError(\"User endpoint returned null; this should never happen\");\n    return user;\n  }\n\n  async getServerUserById(userId: string): Promise<Result<UsersCrud['Server']['Read']>> {\n    const responseOrError = await this.sendServerRequestAndCatchKnownError(\n      urlString`/users/${userId}`,\n      {},\n      null,\n      [KnownErrors.UserNotFound],\n    );\n    if (responseOrError.status === \"error\") {\n      return Result.error(responseOrError.error);\n    }\n    const user: UsersCrud['Server']['Read'] = await responseOrError.data.json();\n    return Result.ok(user);\n  }\n\n  async listServerTeamInvitations(options: {\n    teamId: string,\n  }): Promise<TeamInvitationCrud['Server']['Read'][]> {\n    const response = await this.sendServerRequest(\n      urlString`/team-invitations?team_id=${options.teamId}`,\n      {},\n      null,\n    );\n    const result = await response.json() as TeamInvitationCrud['Server']['List'];\n    return result.items;\n  }\n\n  async revokeServerTeamInvitation(invitationId: string, teamId: string) {\n    await this.sendServerRequest(\n      urlString`/team-invitations/${invitationId}?team_id=${teamId}`,\n      { method: \"DELETE\" },\n      null,\n    );\n  }\n\n  async listServerTeamMemberProfiles(\n    options: {\n      teamId: string,\n    },\n  ): Promise<TeamMemberProfilesCrud['Server']['Read'][]> {\n    const response = await this.sendServerRequest(\n      urlString`/team-member-profiles?team_id=${options.teamId}`,\n      {},\n      null,\n    );\n    const result = await response.json() as TeamMemberProfilesCrud['Server']['List'];\n    return result.items;\n  }\n\n  async getServerTeamMemberProfile(\n    options: {\n      teamId: string,\n      userId: string,\n    },\n  ): Promise<TeamMemberProfilesCrud['Client']['Read']> {\n    const response = await this.sendServerRequest(\n      urlString`/team-member-profiles/${options.teamId}/${options.userId}`,\n      {},\n      null,\n    );\n    return await response.json();\n  }\n\n  async listServerTeamPermissions(\n    options: {\n      userId?: string,\n      teamId?: string,\n      recursive: boolean,\n    },\n    session: InternalSession | null,\n  ): Promise<TeamPermissionsCrud['Server']['Read'][]> {\n    const response = await this.sendServerRequest(\n      `/team-permissions?${new URLSearchParams(filterUndefined({\n        user_id: options.userId,\n        team_id: options.teamId,\n        recursive: options.recursive.toString(),\n      }))}`,\n      {},\n      session,\n    );\n    const result = await response.json() as TeamPermissionsCrud['Server']['List'];\n    return result.items;\n  }\n\n  async listServerProjectPermissions(\n    options: {\n      userId?: string,\n      recursive: boolean,\n    },\n    session: InternalSession | null,\n  ): Promise<ProjectPermissionsCrud['Server']['Read'][]> {\n    const response = await this.sendServerRequest(\n      `/project-permissions?${new URLSearchParams(filterUndefined({\n        user_id: options.userId,\n        recursive: options.recursive.toString(),\n      }))}`,\n      {},\n      session,\n    );\n    const result = await response.json() as ProjectPermissionsCrud['Server']['List'];\n    return result.items;\n  }\n\n  async listServerUsers(options: {\n    cursor?: string,\n    limit?: number,\n    orderBy?: 'signedUpAt',\n    desc?: boolean,\n    query?: string,\n  }): Promise<UsersCrud['Server']['List']> {\n    const searchParams = new URLSearchParams(filterUndefined({\n      cursor: options.cursor,\n      limit: options.limit?.toString(),\n      desc: options.desc?.toString(),\n      ...options.orderBy ? {\n        order_by: {\n          signedUpAt: \"signed_up_at\",\n        }[options.orderBy],\n      } : {},\n      ...options.query ? {\n        query: options.query,\n      } : {},\n    }));\n    const response = await this.sendServerRequest(\"/users?\" + searchParams.toString(), {}, null);\n    return await response.json();\n  }\n\n  async listServerTeams(options?: {\n    userId?: string,\n  }): Promise<TeamsCrud['Server']['Read'][]> {\n    const response = await this.sendServerRequest(\n      `/teams?${new URLSearchParams(filterUndefined({\n        user_id: options?.userId,\n      }))}`,\n      {},\n      null\n    );\n    const result = await response.json() as TeamsCrud['Server']['List'];\n    return result.items;\n  }\n\n  async getServerTeam(teamId: string): Promise<TeamsCrud['Server']['Read']> {\n    const response = await this.sendServerRequest(\n      `/teams/${teamId}`,\n      {},\n      null\n    );\n    return await response.json();\n  }\n\n  async listServerTeamUsers(teamId: string): Promise<UsersCrud['Server']['Read'][]> {\n    const response = await this.sendServerRequest(`/users?team_id=${teamId}`, {}, null);\n    const result = await response.json() as UsersCrud['Server']['List'];\n    return result.items;\n  }\n\n  /* when passing a session, the user will be added to the team */\n  async createServerTeam(data: TeamsCrud['Server']['Create']): Promise<TeamsCrud['Server']['Read']> {\n    const response = await this.sendServerRequest(\n      \"/teams\",\n      {\n        method: \"POST\",\n        headers: {\n          \"content-type\": \"application/json\",\n        },\n        body: JSON.stringify(data),\n      },\n      null\n    );\n    return await response.json();\n  }\n\n  async updateServerTeam(teamId: string, data: TeamsCrud['Server']['Update']): Promise<TeamsCrud['Server']['Read']> {\n    const response = await this.sendServerRequest(\n      urlString`/teams/${teamId}`,\n      {\n        method: \"PATCH\",\n        headers: {\n          \"content-type\": \"application/json\",\n        },\n        body: JSON.stringify(data),\n      },\n      null,\n    );\n    return await response.json();\n  }\n\n  async deleteServerTeam(teamId: string): Promise<void> {\n    await this.sendServerRequest(\n      urlString`/teams/${teamId}`,\n      { method: \"DELETE\" },\n      null,\n    );\n  }\n\n  async addServerUserToTeam(options: {\n    userId: string,\n    teamId: string,\n  }): Promise<TeamMembershipsCrud['Server']['Read']> {\n    const response = await this.sendServerRequest(\n      urlString`/team-memberships/${options.teamId}/${options.userId}`,\n      {\n        method: \"POST\",\n        headers: {\n          \"content-type\": \"application/json\",\n        },\n        body: JSON.stringify({}),\n      },\n      null,\n    );\n    return await response.json();\n  }\n\n  async removeServerUserFromTeam(options: {\n    userId: string,\n    teamId: string,\n  }) {\n    await this.sendServerRequest(\n      urlString`/team-memberships/${options.teamId}/${options.userId}`,\n      {\n        method: \"DELETE\",\n        headers: {\n          \"content-type\": \"application/json\",\n        },\n        body: JSON.stringify({}),\n      },\n      null,\n    );\n  }\n\n  async updateServerUser(userId: string, update: UsersCrud['Server']['Update']): Promise<UsersCrud['Server']['Read']> {\n    const response = await this.sendServerRequest(\n      urlString`/users/${userId}`,\n      {\n        method: \"PATCH\",\n        headers: {\n          \"content-type\": \"application/json\",\n        },\n        body: JSON.stringify(update),\n      },\n      null,\n    );\n    return await response.json();\n  }\n\n  async createServerProviderAccessToken(\n    userId: string,\n    provider: string,\n    scope: string,\n  ): Promise<ConnectedAccountAccessTokenCrud['Server']['Read']> {\n    const response = await this.sendServerRequest(\n      urlString`/connected-accounts/${userId}/${provider}/access-token`,\n      {\n        method: \"POST\",\n        headers: {\n          \"content-type\": \"application/json\",\n        },\n        body: JSON.stringify({ scope }),\n      },\n      null,\n    );\n    return await response.json();\n  }\n\n  async createServerUserSession(userId: string, expiresInMillis: number, isImpersonation: boolean): Promise<{ accessToken: string, refreshToken: string }> {\n    const response = await this.sendServerRequest(\n      \"/auth/sessions\",\n      {\n        method: \"POST\",\n        headers: {\n          \"content-type\": \"application/json\",\n        },\n        body: JSON.stringify({\n          user_id: userId,\n          expires_in_millis: expiresInMillis,\n          is_impersonation: isImpersonation,\n        }),\n      },\n      null,\n    );\n    const result = await response.json();\n    return {\n      accessToken: result.access_token,\n      refreshToken: result.refresh_token,\n    };\n  }\n\n  async leaveServerTeam(\n    options: {\n      teamId: string,\n      userId: string,\n    },\n  ) {\n    await this.sendClientRequest(\n      urlString`/team-memberships/${options.teamId}/${options.userId}`,\n      {\n        method: \"DELETE\",\n        headers: {\n          \"content-type\": \"application/json\",\n        },\n        body: JSON.stringify({}),\n      },\n      null,\n    );\n  }\n\n  async updateServerTeamMemberProfile(options: {\n    teamId: string,\n    userId: string,\n    profile: TeamMemberProfilesCrud['Server']['Update'],\n  }) {\n    await this.sendServerRequest(\n      urlString`/team-member-profiles/${options.teamId}/${options.userId}`,\n      {\n        method: \"PATCH\",\n        headers: {\n          \"content-type\": \"application/json\",\n        },\n        body: JSON.stringify(options.profile),\n      },\n      null,\n    );\n  }\n\n  async grantServerTeamUserPermission(teamId: string, userId: string, permissionId: string) {\n    await this.sendServerRequest(\n      urlString`/team-permissions/${teamId}/${userId}/${permissionId}`,\n      {\n        method: \"POST\",\n        headers: {\n          \"content-type\": \"application/json\",\n        },\n        body: JSON.stringify({}),\n      },\n      null,\n    );\n  }\n\n  async grantServerProjectPermission(userId: string, permissionId: string) {\n    await this.sendServerRequest(\n      urlString`/project-permissions/${userId}/${permissionId}`,\n      {\n        method: \"POST\",\n        headers: {\n          \"content-type\": \"application/json\",\n        },\n        body: JSON.stringify({}),\n      },\n      null,\n    );\n  }\n\n  async revokeServerTeamUserPermission(teamId: string, userId: string, permissionId: string) {\n    await this.sendServerRequest(\n      urlString`/team-permissions/${teamId}/${userId}/${permissionId}`,\n      {\n        method: \"DELETE\",\n        headers: {\n          \"content-type\": \"application/json\",\n        },\n        body: JSON.stringify({}),\n      },\n      null,\n    );\n  }\n\n  async revokeServerProjectPermission(userId: string, permissionId: string) {\n    await this.sendServerRequest(\n      urlString`/project-permissions/${userId}/${permissionId}`,\n      {\n        method: \"DELETE\",\n        headers: {\n          \"content-type\": \"application/json\",\n        },\n        body: JSON.stringify({}),\n      },\n      null,\n    );\n  }\n\n  async deleteServerUser(userId: string) {\n    await this.sendServerRequest(\n      urlString`/users/${userId}`,\n      {\n        method: \"DELETE\",\n        headers: {\n          \"content-type\": \"application/json\",\n        },\n        body: JSON.stringify({}),\n      },\n      null,\n    );\n  }\n\n  async createServerContactChannel(\n    data: ContactChannelsCrud['Server']['Create'],\n  ): Promise<ContactChannelsCrud['Server']['Read']> {\n    const response = await this.sendServerRequest(\n      \"/contact-channels\",\n      {\n        method: \"POST\",\n        headers: {\n          \"content-type\": \"application/json\",\n        },\n        body: JSON.stringify(data),\n      },\n      null,\n    );\n    return await response.json();\n  }\n\n  async updateServerContactChannel(\n    userId: string,\n    contactChannelId: string,\n    data: ContactChannelsCrud['Server']['Update'],\n  ): Promise<ContactChannelsCrud['Server']['Read']> {\n    const response = await this.sendServerRequest(\n      urlString`/contact-channels/${userId}/${contactChannelId}`,\n      {\n        method: \"PATCH\",\n        headers: {\n          \"content-type\": \"application/json\",\n        },\n        body: JSON.stringify(data),\n      },\n      null,\n    );\n    return await response.json();\n  }\n\n  async deleteServerContactChannel(\n    userId: string,\n    contactChannelId: string,\n  ): Promise<void> {\n    await this.sendServerRequest(\n      urlString`/contact-channels/${userId}/${contactChannelId}`,\n      {\n        method: \"DELETE\",\n      },\n      null,\n    );\n  }\n\n  async listServerContactChannels(\n    userId: string,\n  ): Promise<ContactChannelsCrud['Server']['Read'][]> {\n    const response = await this.sendServerRequest(\n      urlString`/contact-channels?user_id=${userId}`,\n      {\n        method: \"GET\",\n      },\n      null,\n    );\n    const json = await response.json() as ContactChannelsCrud['Server']['List'];\n    return json.items;\n  }\n\n  async sendServerContactChannelVerificationEmail(\n    userId: string,\n    contactChannelId: string,\n    callbackUrl: string,\n  ): Promise<void> {\n    await this.sendServerRequest(\n      urlString`/contact-channels/${userId}/${contactChannelId}/send-verification-code`,\n      {\n        method: \"POST\",\n        headers: {\n          \"content-type\": \"application/json\",\n        },\n        body: JSON.stringify({ callback_url: callbackUrl }),\n      },\n      null,\n    );\n  }\n\n\n  async listServerSessions(userId: string): Promise<SessionsCrud['Server']['Read'][]> {\n    const response = await this.sendServerRequest(\n      urlString`/auth/sessions?user_id=${userId}`,\n      {\n        method: \"GET\",\n      },\n      null,\n    );\n    return await response.json();\n  }\n\n  async deleteServerSession(sessionId: string) {\n    await this.sendServerRequest(\n      urlString`/auth/sessions/${sessionId}`,\n      {\n        method: \"DELETE\",\n      },\n      null,\n    );\n  }\n\n\n  async sendServerTeamInvitation(options: {\n    email: string,\n    teamId: string,\n    callbackUrl: string,\n  }): Promise<void> {\n    await this.sendServerRequest(\n      \"/team-invitations/send-code\",\n      {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\"\n        },\n        body: JSON.stringify({\n          email: options.email,\n          team_id: options.teamId,\n          callback_url: options.callbackUrl,\n        }),\n      },\n      null,\n    );\n  }\n\n  async updatePassword(\n    options: { oldPassword: string, newPassword: string },\n  ): Promise<KnownErrors[\"PasswordConfirmationMismatch\"] | KnownErrors[\"PasswordRequirementsNotMet\"] | undefined> {\n    const res = await this.sendServerRequestAndCatchKnownError(\n      \"/auth/password/update\",\n      {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\"\n        },\n        body: JSON.stringify({\n          old_password: options.oldPassword,\n          new_password: options.newPassword,\n        }),\n      },\n      null,\n      [KnownErrors.PasswordConfirmationMismatch, KnownErrors.PasswordRequirementsNotMet]\n    );\n\n    if (res.status === \"error\") {\n      return res.error;\n    }\n  }\n}\n", "import { InternalSession } from \"../sessions\";\nimport { EmailTemplateCrud, EmailTemplateType } from \"./crud/email-templates\";\nimport { InternalEmailsCrud } from \"./crud/emails\";\nimport { InternalApiKeysCrud } from \"./crud/internal-api-keys\";\nimport { ProjectPermissionDefinitionsCrud } from \"./crud/project-permissions\";\nimport { ProjectsCrud } from \"./crud/projects\";\nimport { SvixTokenCrud } from \"./crud/svix-token\";\nimport { TeamPermissionDefinitionsCrud } from \"./crud/team-permissions\";\nimport { ServerAuthApplicationOptions, StackServerInterface } from \"./serverInterface\";\n\nexport type AdminAuthApplicationOptions = ServerAuthApplicationOptions &(\n  | {\n    superSecretAdminKey: string,\n  }\n  | {\n    projectOwnerSession: InternalSession,\n  }\n);\n\nexport type InternalApiKeyCreateCrudRequest = {\n  has_publishable_client_key: boolean,\n  has_secret_server_key: boolean,\n  has_super_secret_admin_key: boolean,\n  expires_at_millis: number,\n  description: string,\n};\n\nexport type InternalApiKeyCreateCrudResponse = InternalApiKeysCrud[\"Admin\"][\"Read\"] & {\n  publishable_client_key?: string,\n  secret_server_key?: string,\n  super_secret_admin_key?: string,\n};\n\nexport class StackAdminInterface extends StackServerInterface {\n  constructor(public readonly options: AdminAuthApplicationOptions) {\n    super(options);\n  }\n\n  public async sendAdminRequest(path: string, options: RequestInit, session: InternalSession | null, requestType: \"admin\" = \"admin\") {\n    return await this.sendServerRequest(\n      path,\n      {\n        ...options,\n        headers: {\n          \"x-stack-super-secret-admin-key\": \"superSecretAdminKey\" in this.options ? this.options.superSecretAdminKey : \"\",\n          ...options.headers,\n        },\n      },\n      session,\n      requestType,\n    );\n  }\n\n  async getProject(): Promise<ProjectsCrud[\"Admin\"][\"Read\"]> {\n    const response = await this.sendAdminRequest(\n      \"/internal/projects/current\",\n      {\n        method: \"GET\",\n      },\n      null,\n    );\n    return await response.json();\n  }\n\n  async updateProject(update: ProjectsCrud[\"Admin\"][\"Update\"]): Promise<ProjectsCrud[\"Admin\"][\"Read\"]> {\n    const response = await this.sendAdminRequest(\n      \"/internal/projects/current\",\n      {\n        method: \"PATCH\",\n        headers: {\n          \"content-type\": \"application/json\",\n        },\n        body: JSON.stringify(update),\n      },\n      null,\n    );\n    return await response.json();\n  }\n\n  async createInternalApiKey(\n    options: InternalApiKeyCreateCrudRequest,\n  ): Promise<InternalApiKeyCreateCrudResponse> {\n    const response = await this.sendAdminRequest(\n      \"/internal/api-keys\",\n      {\n        method: \"POST\",\n        headers: {\n          \"content-type\": \"application/json\",\n        },\n        body: JSON.stringify(options),\n      },\n      null,\n    );\n    return await response.json();\n  }\n\n  async listInternalApiKeys(): Promise<InternalApiKeysCrud[\"Admin\"][\"Read\"][]> {\n    const response = await this.sendAdminRequest(\"/internal/api-keys\", {}, null);\n    const result = await response.json() as InternalApiKeysCrud[\"Admin\"][\"List\"];\n    return result.items;\n  }\n\n  async revokeInternalApiKeyById(id: string) {\n    await this.sendAdminRequest(\n      `/internal/api-keys/${id}`, {\n        method: \"PATCH\",\n        headers: {\n          \"content-type\": \"application/json\",\n        },\n        body: JSON.stringify({\n          revoked: true,\n        }),\n      },\n      null,\n    );\n  }\n\n  async getInternalApiKey(id: string, session: InternalSession): Promise<InternalApiKeysCrud[\"Admin\"][\"Read\"]> {\n    const response = await this.sendAdminRequest(`/internal/api-keys/${id}`, {}, session);\n    return await response.json();\n  }\n\n  async listEmailTemplates(): Promise<EmailTemplateCrud['Admin']['Read'][]> {\n    const response = await this.sendAdminRequest(`/email-templates`, {}, null);\n    const result = await response.json() as EmailTemplateCrud['Admin']['List'];\n    return result.items;\n  }\n\n  async updateEmailTemplate(type: EmailTemplateType, data: EmailTemplateCrud['Admin']['Update']): Promise<EmailTemplateCrud['Admin']['Read']> {\n    const result = await this.sendAdminRequest(\n      `/email-templates/${type}`,\n      {\n        method: \"PATCH\",\n        headers: {\n          \"content-type\": \"application/json\",\n        },\n        body: JSON.stringify(data),\n      },\n      null,\n    );\n    return await result.json();\n  }\n\n  async resetEmailTemplate(type: EmailTemplateType): Promise<void> {\n    await this.sendAdminRequest(\n      `/email-templates/${type}`,\n      { method: \"DELETE\" },\n      null\n    );\n  }\n\n  // Team permission definitions methods\n  async listTeamPermissionDefinitions(): Promise<TeamPermissionDefinitionsCrud['Admin']['Read'][]> {\n    const response = await this.sendAdminRequest(`/team-permission-definitions`, {}, null);\n    const result = await response.json() as TeamPermissionDefinitionsCrud['Admin']['List'];\n    return result.items;\n  }\n\n  async createTeamPermissionDefinition(data: TeamPermissionDefinitionsCrud['Admin']['Create']): Promise<TeamPermissionDefinitionsCrud['Admin']['Read']> {\n    const response = await this.sendAdminRequest(\n      \"/team-permission-definitions\",\n      {\n        method: \"POST\",\n        headers: {\n          \"content-type\": \"application/json\",\n        },\n        body: JSON.stringify(data),\n      },\n      null,\n    );\n    return await response.json();\n  }\n\n  async updateTeamPermissionDefinition(permissionId: string, data: TeamPermissionDefinitionsCrud['Admin']['Update']): Promise<TeamPermissionDefinitionsCrud['Admin']['Read']> {\n    const response = await this.sendAdminRequest(\n      `/team-permission-definitions/${permissionId}`,\n      {\n        method: \"PATCH\",\n        headers: {\n          \"content-type\": \"application/json\",\n        },\n        body: JSON.stringify(data),\n      },\n      null,\n    );\n    return await response.json();\n  }\n\n  async deleteTeamPermissionDefinition(permissionId: string): Promise<void> {\n    await this.sendAdminRequest(\n      `/team-permission-definitions/${permissionId}`,\n      { method: \"DELETE\" },\n      null,\n    );\n  }\n\n  async listProjectPermissionDefinitions(): Promise<ProjectPermissionDefinitionsCrud['Admin']['Read'][]> {\n    const response = await this.sendAdminRequest(`/project-permission-definitions`, {}, null);\n    const result = await response.json() as ProjectPermissionDefinitionsCrud['Admin']['List'];\n    return result.items;\n  }\n\n  async createProjectPermissionDefinition(data: ProjectPermissionDefinitionsCrud['Admin']['Create']): Promise<ProjectPermissionDefinitionsCrud['Admin']['Read']> {\n    const response = await this.sendAdminRequest(\n      \"/project-permission-definitions\",\n      {\n        method: \"POST\",\n        headers: {\n          \"content-type\": \"application/json\",\n        },\n        body: JSON.stringify(data),\n      },\n      null,\n    );\n    return await response.json();\n  }\n\n  async updateProjectPermissionDefinition(permissionId: string, data: ProjectPermissionDefinitionsCrud['Admin']['Update']): Promise<ProjectPermissionDefinitionsCrud['Admin']['Read']> {\n    const response = await this.sendAdminRequest(\n      `/project-permission-definitions/${permissionId}`,\n      {\n        method: \"PATCH\",\n        headers: {\n          \"content-type\": \"application/json\",\n        },\n        body: JSON.stringify(data),\n      },\n      null,\n    );\n    return await response.json();\n  }\n\n  async deleteProjectPermissionDefinition(permissionId: string): Promise<void> {\n    await this.sendAdminRequest(\n      `/project-permission-definitions/${permissionId}`,\n      { method: \"DELETE\" },\n      null,\n    );\n  }\n\n  async getSvixToken(): Promise<SvixTokenCrud[\"Admin\"][\"Read\"]> {\n    const response = await this.sendAdminRequest(\n      \"/webhooks/svix-token\",\n      {\n        method: \"POST\",\n        headers: {\n          \"content-type\": \"application/json\",\n        },\n        body: JSON.stringify({}),\n      },\n      null,\n    );\n    return await response.json();\n  }\n\n  async deleteProject(): Promise<void> {\n    await this.sendAdminRequest(\n      \"/internal/projects/current\",\n      {\n        method: \"DELETE\",\n      },\n      null,\n    );\n  }\n\n  async getMetrics(): Promise<any> {\n    const response = await this.sendAdminRequest(\n      \"/internal/metrics\",\n      {\n        method: \"GET\",\n      },\n      null,\n    );\n    return await response.json();\n  }\n\n  async sendTestEmail(data: {\n    recipient_email: string,\n    email_config: {\n      host: string,\n      port: number,\n      username: string,\n      password: string,\n      sender_email: string,\n      sender_name: string,\n    },\n  }): Promise<{ success: boolean, error_message?: string }> {\n    const response = await this.sendAdminRequest(`/internal/send-test-email`, {\n      method: \"POST\",\n      headers: {\n        \"content-type\": \"application/json\",\n      },\n      body: JSON.stringify(data),\n    }, null);\n    return await response.json();\n  }\n\n  async listSentEmails(): Promise<InternalEmailsCrud[\"Admin\"][\"List\"]> {\n    const response = await this.sendAdminRequest(\"/internal/emails\", {\n      method: \"GET\",\n    }, null);\n    return await response.json();\n  }\n}\n", "import { KnownError } from \"..\";\nimport { StackAssertionError, captureError, concatStacktraces } from \"./errors\";\nimport { DependenciesMap } from \"./maps\";\nimport { Result } from \"./results\";\nimport { generateUuid } from \"./uuids\";\n\nexport type ReactPromise<T> = Promise<T> & (\n  | { status: \"rejected\", reason: unknown }\n  | { status: \"fulfilled\", value: T }\n  | { status: \"pending\" }\n);\n\ntype Resolve<T> = (value: T) => void;\ntype Reject = (reason: unknown) => void;\nexport function createPromise<T>(callback: (resolve: Resolve<T>, reject: Reject) => void): ReactPromise<T> {\n  let status = \"pending\" as \"fulfilled\" | \"rejected\" | \"pending\";\n  let valueOrReason: T | unknown | undefined = undefined;\n  let resolve: Resolve<T> | null = null;\n  let reject: Reject | null = null;\n  const promise = new Promise<T>((res, rej) => {\n    resolve = (value) => {\n      if (status !== \"pending\") return;\n      status = \"fulfilled\";\n      valueOrReason = value;\n      res(value);\n    };\n    reject = (reason) => {\n      if (status !== \"pending\") return;\n      status = \"rejected\";\n      valueOrReason = reason;\n      rej(reason);\n    };\n  });\n\n  callback(resolve!, reject!);\n  return Object.assign(promise, {\n    status: status,\n    ...status === \"fulfilled\" ? { value: valueOrReason as T } : {},\n    ...status === \"rejected\" ? { reason: valueOrReason } : {},\n  } as any);\n}\nundefined?.test(\"createPromise\", async ({ expect }) => {\n  // Test resolved promise\n  const resolvedPromise = createPromise<number>((resolve) => {\n    resolve(42);\n  });\n  expect(resolvedPromise.status).toBe(\"fulfilled\");\n  expect((resolvedPromise as any).value).toBe(42);\n  expect(await resolvedPromise).toBe(42);\n\n  // Test rejected promise\n  const error = new Error(\"Test error\");\n  const rejectedPromise = createPromise<number>((_, reject) => {\n    reject(error);\n  });\n  expect(rejectedPromise.status).toBe(\"rejected\");\n  expect((rejectedPromise as any).reason).toBe(error);\n  await expect(rejectedPromise).rejects.toBe(error);\n\n  // Test pending promise\n  const pendingPromise = createPromise<number>(() => {\n    // Do nothing, leave it pending\n  });\n  expect(pendingPromise.status).toBe(\"pending\");\n  expect((pendingPromise as any).value).toBeUndefined();\n  expect((pendingPromise as any).reason).toBeUndefined();\n\n  // Test that resolving after already resolved does nothing\n  let resolveCount = 0;\n  const multiResolvePromise = createPromise<number>((resolve) => {\n    resolve(1);\n    resolveCount++;\n    resolve(2);\n    resolveCount++;\n  });\n  expect(resolveCount).toBe(2); // Both resolve calls executed\n  expect(multiResolvePromise.status).toBe(\"fulfilled\");\n  expect((multiResolvePromise as any).value).toBe(1); // Only first resolve took effect\n  expect(await multiResolvePromise).toBe(1);\n});\n\nlet resolvedCache: DependenciesMap<[unknown], ReactPromise<unknown>> | null = null;\n/**\n * Like Promise.resolve(...), but also adds the status and value properties for use with React's `use` hook, and caches\n * the value so that invoking `resolved` twice returns the same promise.\n */\nexport function resolved<T>(value: T): ReactPromise<T> {\n  resolvedCache ??= new DependenciesMap<[unknown], ReactPromise<unknown>>();\n  if (resolvedCache.has([value])) {\n    return resolvedCache.get([value]) as ReactPromise<T>;\n  }\n\n  const res = Object.assign(Promise.resolve(value), {\n    status: \"fulfilled\",\n    value,\n  } as const);\n  resolvedCache.set([value], res);\n  return res;\n}\nundefined?.test(\"resolved\", async ({ expect }) => {\n  // Test with primitive value\n  const promise1 = resolved(42);\n  expect(promise1.status).toBe(\"fulfilled\");\n  // Need to use type assertion since value is only available when status is \"fulfilled\"\n  expect((promise1 as { value: number }).value).toBe(42);\n  expect(await promise1).toBe(42);\n\n  // Test with object value\n  const obj = { test: true };\n  const promise2 = resolved(obj);\n  expect(promise2.status).toBe(\"fulfilled\");\n  expect((promise2 as { value: typeof obj }).value).toBe(obj);\n  expect(await promise2).toBe(obj);\n\n  // Test caching (same reference for same value)\n  const promise3 = resolved(42);\n  expect(promise3).toBe(promise1); // Same reference due to caching\n\n  // Test with different value (different reference)\n  const promise4 = resolved(43);\n  expect(promise4).not.toBe(promise1);\n});\n\nlet rejectedCache: DependenciesMap<[unknown], ReactPromise<unknown>> | null = null;\n/**\n * Like Promise.reject(...), but also adds the status and value properties for use with React's `use` hook, and caches\n * the value so that invoking `rejected` twice returns the same promise.\n */\nexport function rejected<T>(reason: unknown): ReactPromise<T> {\n  rejectedCache ??= new DependenciesMap<[unknown], ReactPromise<unknown>>();\n  if (rejectedCache.has([reason])) {\n    return rejectedCache.get([reason]) as ReactPromise<T>;\n  }\n\n  const promise = Promise.reject(reason);\n  ignoreUnhandledRejection(promise);\n  const res = Object.assign(promise, {\n    status: \"rejected\",\n    reason: reason,\n  } as const);\n  rejectedCache.set([reason], res);\n  return res;\n}\nundefined?.test(\"rejected\", ({ expect }) => {\n  // Test with error object\n  const error = new Error(\"Test error\");\n  const promise1 = rejected<number>(error);\n  expect(promise1.status).toBe(\"rejected\");\n  // Need to use type assertion since reason is only available when status is \"rejected\"\n  expect((promise1 as { reason: Error }).reason).toBe(error);\n\n  // Test with string reason\n  const promise2 = rejected<string>(\"error message\");\n  expect(promise2.status).toBe(\"rejected\");\n  expect((promise2 as { reason: string }).reason).toBe(\"error message\");\n\n  // Test caching (same reference for same reason)\n  const promise3 = rejected<number>(error);\n  expect(promise3).toBe(promise1); // Same reference due to caching\n\n  // Test with different reason (different reference)\n  const differentError = new Error(\"Different error\");\n  const promise4 = rejected<number>(differentError);\n  expect(promise4).not.toBe(promise1);\n\n  // Note: We're not using await expect(promise).rejects to avoid unhandled rejections\n});\n\n// We'll skip the rejection test for pending() since it's causing unhandled rejections\n// The function is already well tested through other tests like rejected() and createPromise()\n\n\nconst neverResolvePromise = pending(new Promise<never>(() => {}));\nexport function neverResolve(): ReactPromise<never> {\n  return neverResolvePromise;\n}\nundefined?.test(\"neverResolve\", ({ expect }) => {\n  const promise = neverResolve();\n  expect(promise.status).toBe(\"pending\");\n  expect((promise as any).value).toBeUndefined();\n  expect((promise as any).reason).toBeUndefined();\n\n  // Test that multiple calls return the same promise\n  const promise2 = neverResolve();\n  expect(promise2).toBe(promise);\n});\n\nexport function pending<T>(promise: Promise<T>, options: { disableErrorWrapping?: boolean } = {}): ReactPromise<T> {\n  const res = promise.then(\n    value => {\n      res.status = \"fulfilled\";\n      (res as any).value = value;\n      return value;\n    },\n    actualReason => {\n      res.status = \"rejected\";\n      (res as any).reason = actualReason;\n      throw actualReason;\n    },\n  ) as ReactPromise<T>;\n  res.status = \"pending\";\n  return res;\n}\nundefined?.test(\"pending\", async ({ expect }) => {\n  // Test with a promise that resolves\n  const resolvePromise = Promise.resolve(42);\n  const pendingPromise = pending(resolvePromise);\n\n  // Initially it should be pending\n  expect(pendingPromise.status).toBe(\"pending\");\n\n  // After resolution, it should be fulfilled\n  await resolvePromise;\n  // Need to wait a tick for the then handler to execute\n  await new Promise(resolve => setTimeout(resolve, 0));\n  expect(pendingPromise.status).toBe(\"fulfilled\");\n  expect((pendingPromise as { value: number }).value).toBe(42);\n\n  // For the rejection test, we'll use a separate test to avoid unhandled rejections\n});\n\n/**\n * Should be used to wrap Promises that are not immediately awaited, so they don't throw an unhandled promise rejection\n * error.\n *\n * Vercel kills serverless functions on unhandled promise rejection errors, so this is important.\n */\nexport function ignoreUnhandledRejection<T extends Promise<any>>(promise: T): void {\n  promise.catch(() => {});\n}\nundefined?.test(\"ignoreUnhandledRejection\", async ({ expect }) => {\n  // Test with a promise that resolves\n  const resolvePromise = Promise.resolve(42);\n  ignoreUnhandledRejection(resolvePromise);\n  expect(await resolvePromise).toBe(42); // Should still resolve to the same value\n\n  // Test with a promise that rejects\n  // The promise should still reject, but the rejection is caught internally\n  // so it doesn't cause an unhandled rejection error\n  const error = new Error(\"Test error\");\n  const rejectPromise = Promise.reject(error);\n  ignoreUnhandledRejection(rejectPromise);\n  await expect(rejectPromise).rejects.toBe(error);\n});\n\nexport async function wait(ms: number) {\n  if (!Number.isFinite(ms) || ms < 0) {\n    throw new StackAssertionError(`wait() requires a non-negative integer number of milliseconds to wait. (found: ${ms}ms)`);\n  }\n  if (ms >= 2**31) {\n    throw new StackAssertionError(\"The maximum timeout for wait() is 2147483647ms (2**31 - 1). (found: ${ms}ms)\");\n  }\n  return await new Promise<void>(resolve => setTimeout(resolve, ms));\n}\nundefined?.test(\"wait\", async ({ expect }) => {\n  // Test with valid input\n  const start = Date.now();\n  await wait(10);\n  const elapsed = Date.now() - start;\n  expect(elapsed).toBeGreaterThanOrEqual(5); // Allow some flexibility in timing\n\n  // Test with zero\n  await expect(wait(0)).resolves.toBeUndefined();\n\n  // Test with negative number\n  await expect(wait(-10)).rejects.toThrow(\"wait() requires a non-negative integer\");\n\n  // Test with non-finite number\n  await expect(wait(NaN)).rejects.toThrow(\"wait() requires a non-negative integer\");\n  await expect(wait(Infinity)).rejects.toThrow(\"wait() requires a non-negative integer\");\n\n  // Test with too large number\n  await expect(wait(2**31)).rejects.toThrow(\"The maximum timeout for wait()\");\n});\n\nexport async function waitUntil(date: Date) {\n  return await wait(date.getTime() - Date.now());\n}\nundefined?.test(\"waitUntil\", async ({ expect }) => {\n  // Test with future date\n  const futureDate = new Date(Date.now() + 10);\n  const start = Date.now();\n  await waitUntil(futureDate);\n  const elapsed = Date.now() - start;\n  expect(elapsed).toBeGreaterThanOrEqual(5); // Allow some flexibility in timing\n\n  // Test with past date - this will throw because wait() requires non-negative time\n  // We need to verify it throws the correct error\n  try {\n    await waitUntil(new Date(Date.now() - 1000));\n    expect.fail(\"Should have thrown an error\");\n  } catch (error) {\n    expect(error).toBeInstanceOf(StackAssertionError);\n    expect((error as Error).message).toContain(\"wait() requires a non-negative integer\");\n  }\n});\n\nexport function runAsynchronouslyWithAlert(...args: Parameters<typeof runAsynchronously>) {\n  return runAsynchronously(\n    args[0],\n    {\n      ...args[1],\n      onError: error => {\n        if (KnownError.isKnownError(error) && typeof process !== \"undefined\" && (process.env.NODE_ENV as any)?.includes(\"production\")) {\n          alert(error.message);\n        } else {\n          alert(`An unhandled error occurred. Please ${process.env.NODE_ENV === \"development\" ? `check the browser console for the full error.` : \"report this to the developer.\"}\\n\\n${error}`);\n        }\n        args[1]?.onError?.(error);\n      },\n    },\n    ...args.slice(2) as [],\n  );\n}\nundefined?.test(\"runAsynchronouslyWithAlert\", ({ expect }) => {\n  // Simple test to verify the function calls runAsynchronously\n  // We can't easily test the alert functionality without mocking\n  const testFn = () => Promise.resolve(\"test\");\n  const testOptions = { noErrorLogging: true };\n\n  // Just verify it doesn't throw\n  expect(() => runAsynchronouslyWithAlert(testFn, testOptions)).not.toThrow();\n\n  // We can't easily test the error handling without mocking, so we'll\n  // just verify the function exists and can be called\n  expect(typeof runAsynchronouslyWithAlert).toBe(\"function\");\n});\n\nexport function runAsynchronously(\n  promiseOrFunc: void | Promise<unknown> | (() => void | Promise<unknown>) | undefined,\n  options: {\n    noErrorLogging?: boolean,\n    onError?: (error: Error) => void,\n  } = {},\n): void {\n  if (typeof promiseOrFunc === \"function\") {\n    promiseOrFunc = promiseOrFunc();\n  }\n  const duringError = new Error();\n  promiseOrFunc?.catch(error => {\n    options.onError?.(error);\n    const newError = new StackAssertionError(\n      \"Uncaught error in asynchronous function: \" + error.toString(),\n      { cause: error },\n    );\n    concatStacktraces(newError, duringError);\n    if (!options.noErrorLogging) {\n      captureError(\"runAsynchronously\", newError);\n    }\n  });\n}\nundefined?.test(\"runAsynchronously\", ({ expect }) => {\n  // Simple test to verify the function exists and can be called\n  const testFn = () => Promise.resolve(\"test\");\n\n  // Just verify it doesn't throw\n  expect(() => runAsynchronously(testFn)).not.toThrow();\n  expect(() => runAsynchronously(Promise.resolve(\"test\"))).not.toThrow();\n  expect(() => runAsynchronously(undefined)).not.toThrow();\n\n  // We can't easily test the error handling without mocking, so we'll\n  // just verify the function exists and can be called with options\n  expect(() => runAsynchronously(testFn, { noErrorLogging: true })).not.toThrow();\n  expect(() => runAsynchronously(testFn, { onError: () => {} })).not.toThrow();\n});\n\n\nclass TimeoutError extends Error {\n  constructor(public readonly ms: number) {\n    super(`Timeout after ${ms}ms`);\n    this.name = \"TimeoutError\";\n  }\n}\n\nexport async function timeout<T>(promise: Promise<T>, ms: number): Promise<Result<T, TimeoutError>> {\n  return await Promise.race([\n    promise.then(value => Result.ok(value)),\n    wait(ms).then(() => Result.error(new TimeoutError(ms))),\n  ]);\n}\nundefined?.test(\"timeout\", async ({ expect }) => {\n  // Test with a promise that resolves quickly\n  const fastPromise = Promise.resolve(42);\n  const fastResult = await timeout(fastPromise, 100);\n  expect(fastResult.status).toBe(\"ok\");\n  if (fastResult.status === \"ok\") {\n    expect(fastResult.data).toBe(42);\n  }\n\n  // Test with a promise that takes longer than the timeout\n  const slowPromise = new Promise(resolve => setTimeout(() => resolve(\"too late\"), 50));\n  const slowResult = await timeout(slowPromise, 10);\n  expect(slowResult.status).toBe(\"error\");\n  if (slowResult.status === \"error\") {\n    expect(slowResult.error).toBeInstanceOf(TimeoutError);\n    expect((slowResult.error as TimeoutError).ms).toBe(10);\n  }\n});\n\nexport async function timeoutThrow<T>(promise: Promise<T>, ms: number): Promise<T> {\n  return Result.orThrow(await timeout(promise, ms));\n}\nundefined?.test(\"timeoutThrow\", async ({ expect }) => {\n  // Test with a promise that resolves quickly\n  const fastPromise = Promise.resolve(42);\n  const fastResult = await timeoutThrow(fastPromise, 100);\n  expect(fastResult).toBe(42);\n\n  // Test with a promise that takes longer than the timeout\n  const slowPromise = new Promise(resolve => setTimeout(() => resolve(\"too late\"), 50));\n  await expect(timeoutThrow(slowPromise, 10)).rejects.toThrow(\"Timeout after 10ms\");\n  await expect(timeoutThrow(slowPromise, 10)).rejects.toBeInstanceOf(TimeoutError);\n});\n\n\nexport type RateLimitOptions = {\n  /**\n   * The number of requests to process in parallel. Currently only 1 is supported.\n   */\n  concurrency: 1,\n\n  /**\n   * If true, multiple requests waiting at the same time will be reduced to just one. Default is false.\n   */\n  batchCalls?: boolean,\n\n  /**\n   * Waits for throttleMs since the start of last request before starting the next request. Default is 0.\n   */\n  throttleMs?: number,\n\n  /**\n   * Waits for gapMs since the end of last request before starting the next request. Default is 0.\n   */\n  gapMs?: number,\n\n  /**\n   * Waits until there have been no new requests for debounceMs before starting a new request. Default is 0.\n   */\n  debounceMs?: number,\n};\n\nexport function rateLimited<T>(\n  func: () => Promise<T>,\n  options: RateLimitOptions,\n): () => Promise<T> {\n  let waitUntil = performance.now();\n  let queue: [(t: T) => void, (e: unknown) => void][] = [];\n  let addedToQueueCallbacks = new Map<string, () => void>;\n\n  const next = async () => {\n    while (true) {\n      if (waitUntil > performance.now()) {\n        await wait(Math.max(1, waitUntil - performance.now() + 1));\n      } else if (queue.length === 0) {\n        const uuid = generateUuid();\n        await new Promise<void>(resolve => {\n          addedToQueueCallbacks.set(uuid, resolve);\n        });\n        addedToQueueCallbacks.delete(uuid);\n      } else {\n        break;\n      }\n    }\n    const nextFuncs = options.batchCalls ? queue.splice(0, queue.length) : [queue.shift()!];\n\n    const start = performance.now();\n    const value = await Result.fromPromise(func());\n    const end = performance.now();\n\n    waitUntil = Math.max(\n      waitUntil,\n      start + (options.throttleMs ?? 0),\n      end + (options.gapMs ?? 0),\n    );\n\n    for (const nextFunc of nextFuncs) {\n      if (value.status === \"ok\") {\n        nextFunc[0](value.data);\n      } else {\n        nextFunc[1](value.error);\n      }\n    }\n  };\n\n  runAsynchronously(async () => {\n    while (true) {\n      await next();\n    }\n  });\n\n  return () => {\n    return new Promise<T>((resolve, reject) => {\n      waitUntil = Math.max(\n        waitUntil,\n        performance.now() + (options.debounceMs ?? 0),\n      );\n      queue.push([resolve, reject]);\n      addedToQueueCallbacks.forEach(cb => cb());\n    });\n  };\n}\n\nexport function throttled<T, A extends any[]>(func: (...args: A) => Promise<T>, delayMs: number): (...args: A) => Promise<T> {\n  let timeout: ReturnType<typeof setTimeout> | null = null;\n  let nextAvailable: Promise<T> | null = null;\n  return async (...args) => {\n    while (nextAvailable !== null) {\n      await nextAvailable;\n    }\n    nextAvailable = new Promise<T>(resolve => {\n      timeout = setTimeout(() => {\n        nextAvailable = null;\n        resolve(func(...args));\n      }, delayMs);\n    });\n    return await nextAvailable;\n  };\n}\n", "import { wait } from \"./promises\";\nimport { deindent, nicify } from \"./strings\";\n\nexport type Result<T, E = unknown> =\n  | {\n    status: \"ok\",\n    data: T,\n  }\n  | {\n    status: \"error\",\n    error: E,\n  };\n\nexport type AsyncResult<T, E = unknown, P = void> =\n  | Result<T, E>\n  | (\n    & {\n      status: \"pending\",\n    }\n    & {\n      progress: P,\n    }\n  );\n\n\nexport const Result = {\n  fromThrowing,\n  fromThrowingAsync,\n  fromPromise: promiseToResult,\n  ok<T>(data: T): Result<T, never> & { status: \"ok\" } {\n    return {\n      status: \"ok\",\n      data,\n    };\n  },\n  error<E>(error: E): Result<never, E> & { status: \"error\" } {\n    return {\n      status: \"error\",\n      error,\n    };\n  },\n  map: mapResult,\n  or: <T, E, U>(result: Result<T, E>, fallback: U): T | U => {\n    return result.status === \"ok\" ? result.data : fallback;\n  },\n  orThrow: <T, E>(result: Result<T, E>): T => {\n    if (result.status === \"error\") {\n      throw result.error;\n    }\n    return result.data;\n  },\n  orThrowAsync: async <T, E>(result: Promise<Result<T, E>>): Promise<T> => {\n    return Result.orThrow(await result);\n  },\n  retry,\n};\nundefined?.test(\"Result.ok and Result.error\", ({ expect }) => {\n  // Test Result.ok\n  const okResult = Result.ok(42);\n  expect(okResult.status).toBe(\"ok\");\n  expect(okResult.data).toBe(42);\n\n  // Test Result.error\n  const error = new Error(\"Test error\");\n  const errorResult = Result.error(error);\n  expect(errorResult.status).toBe(\"error\");\n  expect(errorResult.error).toBe(error);\n});\n\nundefined?.test(\"Result.or\", ({ expect }) => {\n  // Test with ok result\n  const okResult: Result<number, string> = { status: \"ok\", data: 42 };\n  expect(Result.or(okResult, 0)).toBe(42);\n\n  // Test with error result\n  const errorResult: Result<number, string> = { status: \"error\", error: \"error message\" };\n  expect(Result.or(errorResult, 0)).toBe(0);\n});\n\nundefined?.test(\"Result.orThrow\", ({ expect }) => {\n  // Test with ok result\n  const okResult: Result<number, Error> = { status: \"ok\", data: 42 };\n  expect(Result.orThrow(okResult)).toBe(42);\n\n  // Test with error result\n  const error = new Error(\"Test error\");\n  const errorResult: Result<number, Error> = { status: \"error\", error };\n  expect(() => Result.orThrow(errorResult)).toThrow(error);\n});\n\nundefined?.test(\"Result.orThrowAsync\", async ({ expect }) => {\n  // Test with ok result\n  const okPromise = Promise.resolve({ status: \"ok\", data: 42 } as Result<number, Error>);\n  expect(await Result.orThrowAsync(okPromise)).toBe(42);\n\n  // Test with error result\n  const error = new Error(\"Test error\");\n  const errorPromise = Promise.resolve({ status: \"error\", error } as Result<number, Error>);\n  await expect(Result.orThrowAsync(errorPromise)).rejects.toThrow(error);\n});\n\nexport const AsyncResult = {\n  fromThrowing,\n  fromPromise: promiseToResult,\n  ok: Result.ok,\n  error: Result.error,\n  pending,\n  map: mapResult,\n  or: <T, E, P, U>(result: AsyncResult<T, E, P>, fallback: U): T | U => {\n    if (result.status === \"pending\") {\n      return fallback;\n    }\n    return Result.or(result, fallback);\n  },\n  orThrow: <T, E, P>(result: AsyncResult<T, E, P>): T => {\n    if (result.status === \"pending\") {\n      throw new Error(\"Result still pending\");\n    }\n    return Result.orThrow(result);\n  },\n  retry,\n};\nundefined?.test(\"AsyncResult.or\", ({ expect }) => {\n  // Test with ok result\n  const okResult: AsyncResult<number, string> = { status: \"ok\", data: 42 };\n  expect(AsyncResult.or(okResult, 0)).toBe(42);\n\n  // Test with error result\n  const errorResult: AsyncResult<number, string> = { status: \"error\", error: \"error message\" };\n  expect(AsyncResult.or(errorResult, 0)).toBe(0);\n\n  // Test with pending result\n  const pendingResult: AsyncResult<number, string> = { status: \"pending\", progress: undefined };\n  expect(AsyncResult.or(pendingResult, 0)).toBe(0);\n});\n\nundefined?.test(\"AsyncResult.orThrow\", ({ expect }) => {\n  // Test with ok result\n  const okResult: AsyncResult<number, Error> = { status: \"ok\", data: 42 };\n  expect(AsyncResult.orThrow(okResult)).toBe(42);\n\n  // Test with error result\n  const error = new Error(\"Test error\");\n  const errorResult: AsyncResult<number, Error> = { status: \"error\", error };\n  expect(() => AsyncResult.orThrow(errorResult)).toThrow(error);\n\n  // Test with pending result\n  const pendingResult: AsyncResult<number, Error> = { status: \"pending\", progress: undefined };\n  expect(() => AsyncResult.orThrow(pendingResult)).toThrow(\"Result still pending\");\n});\n\nfunction pending(): AsyncResult<never, never, void> & { status: \"pending\" };\nfunction pending<P>(progress: P): AsyncResult<never, never, P> & { status: \"pending\" };\nfunction pending<P>(progress?: P): AsyncResult<never, never, P> & { status: \"pending\" } {\n  return {\n    status: \"pending\",\n    progress: progress!,\n  };\n}\nundefined?.test(\"pending\", ({ expect }) => {\n  // Test without progress\n  const pendingResult = pending();\n  expect(pendingResult.status).toBe(\"pending\");\n  expect(pendingResult.progress).toBe(undefined);\n\n  // Test with progress\n  const progressValue = { loaded: 50, total: 100 };\n  const pendingWithProgress = pending(progressValue);\n  expect(pendingWithProgress.status).toBe(\"pending\");\n  expect(pendingWithProgress.progress).toBe(progressValue);\n});\n\nasync function promiseToResult<T>(promise: Promise<T>): Promise<Result<T>> {\n  try {\n    const value = await promise;\n    return Result.ok(value);\n  } catch (error) {\n    return Result.error(error);\n  }\n}\nundefined?.test(\"promiseToResult\", async ({ expect }) => {\n  // Test with resolved promise\n  const resolvedPromise = Promise.resolve(42);\n  const resolvedResult = await promiseToResult(resolvedPromise);\n  expect(resolvedResult.status).toBe(\"ok\");\n  if (resolvedResult.status === \"ok\") {\n    expect(resolvedResult.data).toBe(42);\n  }\n\n  // Test with rejected promise\n  const error = new Error(\"Test error\");\n  const rejectedPromise = Promise.reject(error);\n  const rejectedResult = await promiseToResult(rejectedPromise);\n  expect(rejectedResult.status).toBe(\"error\");\n  if (rejectedResult.status === \"error\") {\n    expect(rejectedResult.error).toBe(error);\n  }\n});\n\nfunction fromThrowing<T>(fn: () => T): Result<T, unknown> {\n  try {\n    return Result.ok(fn());\n  } catch (error) {\n    return Result.error(error);\n  }\n}\nundefined?.test(\"fromThrowing\", ({ expect }) => {\n  // Test with function that succeeds\n  const successFn = () => 42;\n  const successResult = fromThrowing(successFn);\n  expect(successResult.status).toBe(\"ok\");\n  if (successResult.status === \"ok\") {\n    expect(successResult.data).toBe(42);\n  }\n\n  // Test with function that throws\n  const error = new Error(\"Test error\");\n  const errorFn = () => {\n    throw error;\n  };\n  const errorResult = fromThrowing(errorFn);\n  expect(errorResult.status).toBe(\"error\");\n  if (errorResult.status === \"error\") {\n    expect(errorResult.error).toBe(error);\n  }\n});\n\nasync function fromThrowingAsync<T>(fn: () => Promise<T>): Promise<Result<T, unknown>> {\n  try {\n    return Result.ok(await fn());\n  } catch (error) {\n    return Result.error(error);\n  }\n}\nundefined?.test(\"fromThrowingAsync\", async ({ expect }) => {\n  // Test with async function that succeeds\n  const successFn = async () => 42;\n  const successResult = await fromThrowingAsync(successFn);\n  expect(successResult.status).toBe(\"ok\");\n  if (successResult.status === \"ok\") {\n    expect(successResult.data).toBe(42);\n  }\n\n  // Test with async function that throws\n  const error = new Error(\"Test error\");\n  const errorFn = async () => {\n    throw error;\n  };\n  const errorResult = await fromThrowingAsync(errorFn);\n  expect(errorResult.status).toBe(\"error\");\n  if (errorResult.status === \"error\") {\n    expect(errorResult.error).toBe(error);\n  }\n});\n\nfunction mapResult<T, U, E = unknown, P = unknown>(result: Result<T, E>, fn: (data: T) => U): Result<U, E>;\nfunction mapResult<T, U, E = unknown, P = unknown>(result: AsyncResult<T, E, P>, fn: (data: T) => U): AsyncResult<U, E, P>;\nfunction mapResult<T, U, E = unknown, P = unknown>(result: AsyncResult<T, E, P>, fn: (data: T) => U): AsyncResult<U, E, P> {\n  if (result.status === \"error\") return {\n    status: \"error\",\n    error: result.error,\n  };\n  if (result.status === \"pending\") return {\n    status: \"pending\",\n    ...\"progress\" in result ? { progress: result.progress } : {},\n  } as any;\n\n  return Result.ok(fn(result.data));\n}\nundefined?.test(\"mapResult\", ({ expect }) => {\n  // Test with ok result\n  const okResult: Result<number, string> = { status: \"ok\", data: 42 };\n  const mappedOk = mapResult(okResult, (n: number) => n * 2);\n  expect(mappedOk.status).toBe(\"ok\");\n  if (mappedOk.status === \"ok\") {\n    expect(mappedOk.data).toBe(84);\n  }\n\n  // Test with error result\n  const errorResult: Result<number, string> = { status: \"error\", error: \"error message\" };\n  const mappedError = mapResult(errorResult, (n: number) => n * 2);\n  expect(mappedError.status).toBe(\"error\");\n  if (mappedError.status === \"error\") {\n    expect(mappedError.error).toBe(\"error message\");\n  }\n\n  // Test with pending result (no progress)\n  const pendingResult: AsyncResult<number, string, void> = { status: \"pending\", progress: undefined };\n  const mappedPending = mapResult(pendingResult, (n: number) => n * 2);\n  expect(mappedPending.status).toBe(\"pending\");\n\n  // Test with pending result (with progress)\n  const progressValue = { loaded: 50, total: 100 };\n  const pendingWithProgress: AsyncResult<number, string, typeof progressValue> = {\n    status: \"pending\",\n    progress: progressValue\n  };\n  const mappedPendingWithProgress = mapResult(pendingWithProgress, (n: number) => n * 2);\n  expect(mappedPendingWithProgress.status).toBe(\"pending\");\n  if (mappedPendingWithProgress.status === \"pending\") {\n    expect(mappedPendingWithProgress.progress).toBe(progressValue);\n  }\n});\n\n\nclass RetryError extends AggregateError {\n  constructor(public readonly errors: unknown[]) {\n    const strings = errors.map(e => nicify(e));\n    const isAllSame = strings.length > 1 && strings.every(s => s === strings[0]);\n    super(\n      errors,\n      deindent`\n      Error after ${errors.length} attempts.\n      \n      ${isAllSame ? deindent`\n        Attempts 1-${errors.length}:\n          ${strings[0]}\n      ` : strings.map((s, i) => deindent`\n          Attempt ${i + 1}:\n            ${s}\n        `).join(\"\\n\\n\")}\n      `,\n      { cause: errors[errors.length - 1] }\n    );\n    this.name = \"RetryError\";\n  }\n\n  get attempts() {\n    return this.errors.length;\n  }\n}\nRetryError.prototype.name = \"RetryError\";\n\nundefined?.test(\"RetryError\", ({ expect }) => {\n  // Test with single error\n  const singleError = new Error(\"Single error\");\n  const retryErrorSingle = new RetryError([singleError]);\n  expect(retryErrorSingle.name).toBe(\"RetryError\");\n  expect(retryErrorSingle.errors).toEqual([singleError]);\n  expect(retryErrorSingle.attempts).toBe(1);\n  expect(retryErrorSingle.cause).toBe(singleError);\n  expect(retryErrorSingle.message).toContain(\"Error after 1 attempts\");\n\n  // Test with multiple different errors\n  const error1 = new Error(\"Error 1\");\n  const error2 = new Error(\"Error 2\");\n  const retryErrorMultiple = new RetryError([error1, error2]);\n  expect(retryErrorMultiple.name).toBe(\"RetryError\");\n  expect(retryErrorMultiple.errors).toEqual([error1, error2]);\n  expect(retryErrorMultiple.attempts).toBe(2);\n  expect(retryErrorMultiple.cause).toBe(error2);\n  expect(retryErrorMultiple.message).toContain(\"Error after 2 attempts\");\n  expect(retryErrorMultiple.message).toContain(\"Attempt 1\");\n  expect(retryErrorMultiple.message).toContain(\"Attempt 2\");\n\n  // Test with multiple identical errors\n  const sameError = new Error(\"Same error\");\n  const retryErrorSame = new RetryError([sameError, sameError]);\n  expect(retryErrorSame.name).toBe(\"RetryError\");\n  expect(retryErrorSame.errors).toEqual([sameError, sameError]);\n  expect(retryErrorSame.attempts).toBe(2);\n  expect(retryErrorSame.cause).toBe(sameError);\n  expect(retryErrorSame.message).toContain(\"Error after 2 attempts\");\n  expect(retryErrorSame.message).toContain(\"Attempts 1-2\");\n});\n\nasync function retry<T>(\n  fn: (attemptIndex: number) => Result<T> | Promise<Result<T>>,\n  totalAttempts: number,\n  { exponentialDelayBase = 1000 } = {},\n): Promise<Result<T, RetryError> & { attempts: number }> {\n  const errors: unknown[] = [];\n  for (let i = 0; i < totalAttempts; i++) {\n    const res = await fn(i);\n    if (res.status === \"ok\") {\n      return Object.assign(Result.ok(res.data), { attempts: i + 1 });\n    } else {\n      errors.push(res.error);\n      if (i < totalAttempts - 1) {\n        await wait((Math.random() + 0.5) * exponentialDelayBase * (2 ** i));\n      }\n    }\n  }\n  return Object.assign(Result.error(new RetryError(errors)), { attempts: totalAttempts });\n}\nundefined?.test(\"retry\", async ({ expect }) => {\n  // Test successful on first attempt\n  const successFn = async () => Result.ok(\"success\");\n  const successResult = await retry(successFn, 3, { exponentialDelayBase: 0 });\n    expect(successResult).toEqual({ status: \"ok\", data: \"success\", attempts: 1 });\n\n    // Test successful after failures\n    let attemptCount = 0;\n    const eventualSuccessFn = async () => {\n      return ++attemptCount < 2 ? Result.error(new Error(`Attempt ${attemptCount} failed`))\n        : Result.ok(\"eventual success\");\n    };\n    const eventualSuccessResult = await retry(eventualSuccessFn, 3, { exponentialDelayBase: 0 });\n    expect(eventualSuccessResult).toEqual({ status: \"ok\", data: \"eventual success\", attempts: 2 });\n\n    // Test all attempts fail\n    const errors = [new Error(\"Error 1\"), new Error(\"Error 2\"), new Error(\"Error 3\")];\n    const allFailFn = async (attempt: number) => {\n      return Result.error(errors[attempt]);\n    };\n    const allFailResult = await retry(allFailFn, 3, { exponentialDelayBase: 0 });\n    expect(allFailResult).toEqual({ status: \"error\", error: expect.any(RetryError), attempts: 3 });\n    const retryError = (allFailResult as any).error as RetryError;\n    expect(retryError.errors).toEqual(errors);\n    expect(retryError.attempts).toBe(3);\n});\n", "import { Result } from \"./results\";\n\nexport class WeakRefIfAvailable<T extends object> {\n  private readonly _ref: { deref: () => T | undefined };\n\n  constructor(value: T) {\n    if (typeof WeakRef === \"undefined\") {\n      this._ref = { deref: () => value };\n    } else {\n      this._ref = new WeakRef<T>(value);\n    }\n  }\n\n  deref(): T | undefined {\n    return this._ref.deref();\n  }\n}\nundefined?.test(\"WeakRefIfAvailable\", ({ expect }) => {\n  // Test with an object\n  const obj = { id: 1, name: \"test\" };\n  const weakRef = new WeakRefIfAvailable(obj);\n\n  // Test deref returns the original object\n  expect(weakRef.deref()).toBe(obj);\n\n  // Test with a different object\n  const obj2 = { id: 2, name: \"test2\" };\n  const weakRef2 = new WeakRefIfAvailable(obj2);\n  expect(weakRef2.deref()).toBe(obj2);\n  expect(weakRef2.deref()).not.toBe(obj);\n\n  // We can't easily test garbage collection in this environment,\n  // but we can verify the basic functionality works\n});\n\n\n/**\n * A WeakMap-like object that can be iterated over.\n *\n * Note that it relies on WeakRef, and always falls back to the regular Map behavior (ie. no GC) in browsers that don't support it.\n */\nexport class IterableWeakMap<K extends object, V> {\n  private readonly _weakMap: WeakMap<K & WeakKey, { value: V, keyRef: WeakRefIfAvailable<K & WeakKey> }>;\n  private readonly _keyRefs: Set<WeakRefIfAvailable<K & WeakKey>>;\n\n  constructor(entries?: readonly (readonly [K, V])[] | null) {\n    const mappedEntries = entries?.map((e) => [e[0], { value: e[1], keyRef: new WeakRefIfAvailable(e[0]) }] as const);\n    this._weakMap = new WeakMap(mappedEntries ?? []);\n    this._keyRefs = new Set(mappedEntries?.map((e) => e[1].keyRef) ?? []);\n  }\n\n  get(key: K): V | undefined {\n    return this._weakMap.get(key)?.value;\n  }\n\n  set(key: K, value: V): this {\n    const existing = this._weakMap.get(key);\n    const updated = { value, keyRef: existing?.keyRef ?? new WeakRefIfAvailable(key) };\n    this._weakMap.set(key, updated);\n    this._keyRefs.add(updated.keyRef);\n    return this;\n  }\n\n  delete(key: K): boolean {\n    const res = this._weakMap.get(key);\n    if (res) {\n      this._weakMap.delete(key);\n      this._keyRefs.delete(res.keyRef);\n      return true;\n    }\n    return false;\n  }\n\n  has(key: K): boolean {\n    return this._weakMap.has(key) && this._keyRefs.has(this._weakMap.get(key)!.keyRef);\n  }\n\n  *[Symbol.iterator](): IterableIterator<[K, V]> {\n    for (const keyRef of this._keyRefs) {\n      const key = keyRef.deref();\n      const existing = key ? this._weakMap.get(key) : undefined;\n      if (!key) {\n        // This can happen if the key was GCed. Remove it so the next iteration is faster.\n        this._keyRefs.delete(keyRef);\n      } else if (existing) {\n        yield [key, existing.value];\n      }\n    }\n  }\n\n  [Symbol.toStringTag] = \"IterableWeakMap\";\n}\nundefined?.test(\"IterableWeakMap\", ({ expect }) => {\n  // Test basic functionality\n  const map = new IterableWeakMap<{ id: number }, string>();\n\n  // Create object keys\n  const obj1 = { id: 1 };\n  const obj2 = { id: 2 };\n\n  // Test set and get\n  map.set(obj1, \"value1\");\n  expect(map.get(obj1)).toBe(\"value1\");\n\n  // Test has\n  expect(map.has(obj1)).toBe(true);\n  expect(map.has(obj2)).toBe(false);\n  expect(map.has({ id: 1 })).toBe(false); // Different object with same content\n\n  // Test with multiple keys\n  map.set(obj2, \"value2\");\n  expect(map.get(obj2)).toBe(\"value2\");\n  expect(map.get(obj1)).toBe(\"value1\"); // Original still exists\n\n  // Test delete\n  expect(map.delete(obj1)).toBe(true);\n  expect(map.has(obj1)).toBe(false);\n  expect(map.get(obj1)).toBeUndefined();\n  expect(map.has(obj2)).toBe(true); // Other key still exists\n\n  // Test delete non-existent key\n  expect(map.delete({ id: 3 })).toBe(false);\n\n  // Test iteration\n  const iterMap = new IterableWeakMap<{ id: number }, number>();\n  const iterObj1 = { id: 1 };\n  const iterObj2 = { id: 2 };\n  const iterObj3 = { id: 3 };\n\n  iterMap.set(iterObj1, 1);\n  iterMap.set(iterObj2, 2);\n  iterMap.set(iterObj3, 3);\n\n  const entries = Array.from(iterMap);\n  expect(entries.length).toBe(3);\n\n  // Find entries by their values since we can't directly compare objects in the array\n  const values = entries.map(entry => entry[1]);\n  expect(values).toContain(1);\n  expect(values).toContain(2);\n  expect(values).toContain(3);\n\n  // Test constructor with entries\n  const initialEntries: [{ id: number }, string][] = [\n    [{ id: 4 }, \"initial1\"],\n    [{ id: 5 }, \"initial2\"]\n  ];\n  const mapWithEntries = new IterableWeakMap(initialEntries);\n\n  // We can't directly access the initial entries since they're different object references\n  // But we can verify the map has the correct number of entries\n  const entriesFromConstructor = Array.from(mapWithEntries);\n  expect(entriesFromConstructor.length).toBe(2);\n});\n\n/**\n * A map that is a IterableWeakMap for object keys and a regular Map for primitive keys. Also provides iteration over both\n * object and primitive keys.\n *\n * Note that, just like IterableWeakMap, older browsers without support for WeakRef will use a regular Map for object keys.\n */\nexport class MaybeWeakMap<K, V> {\n  private readonly _primitiveMap: Map<K, V>;\n  private readonly _weakMap: IterableWeakMap<K & WeakKey, V>;\n\n  constructor(entries?: readonly (readonly [K, V])[] | null) {\n    const entriesArray = [...entries ?? []];\n    this._primitiveMap = new Map(entriesArray.filter((e) => !this._isAllowedInWeakMap(e[0])));\n    this._weakMap = new IterableWeakMap(entriesArray.filter((e): e is [K & WeakKey, V] => this._isAllowedInWeakMap(e[0])));\n  }\n\n  private _isAllowedInWeakMap(key: K): key is K & WeakKey {\n    return (typeof key === \"object\" && key !== null) || (typeof key === \"symbol\" && Symbol.keyFor(key) === undefined);\n  }\n\n  get(key: K): V | undefined {\n    if (this._isAllowedInWeakMap(key)) {\n      return this._weakMap.get(key);\n    } else {\n      return this._primitiveMap.get(key);\n    }\n  }\n\n  set(key: K, value: V): this {\n    if (this._isAllowedInWeakMap(key)) {\n      this._weakMap.set(key, value);\n    } else {\n      this._primitiveMap.set(key, value);\n    }\n    return this;\n  }\n\n  delete(key: K): boolean {\n    if (this._isAllowedInWeakMap(key)) {\n      return this._weakMap.delete(key);\n    } else {\n      return this._primitiveMap.delete(key);\n    }\n  }\n\n  has(key: K): boolean {\n    if (this._isAllowedInWeakMap(key)) {\n      return this._weakMap.has(key);\n    } else {\n      return this._primitiveMap.has(key);\n    }\n  }\n\n  *[Symbol.iterator](): IterableIterator<[K, V]> {\n    yield* this._primitiveMap;\n    yield* this._weakMap;\n  }\n\n  [Symbol.toStringTag] = \"MaybeWeakMap\";\n}\nundefined?.test(\"MaybeWeakMap\", ({ expect }) => {\n  // Test with primitive keys\n  const map = new MaybeWeakMap<string | object, number>();\n\n  // Test with string keys\n  map.set(\"key1\", 1);\n  map.set(\"key2\", 2);\n  expect(map.get(\"key1\")).toBe(1);\n  expect(map.get(\"key2\")).toBe(2);\n  expect(map.has(\"key1\")).toBe(true);\n  expect(map.has(\"nonexistent\")).toBe(false);\n\n  // Test with object keys\n  const obj1 = { id: 1 };\n  const obj2 = { id: 2 };\n  map.set(obj1, 3);\n  map.set(obj2, 4);\n  expect(map.get(obj1)).toBe(3);\n  expect(map.get(obj2)).toBe(4);\n  expect(map.has(obj1)).toBe(true);\n\n  // Test delete with primitive key\n  expect(map.delete(\"key1\")).toBe(true);\n  expect(map.has(\"key1\")).toBe(false);\n  expect(map.delete(\"nonexistent\")).toBe(false);\n\n  // Test delete with object key\n  expect(map.delete(obj1)).toBe(true);\n  expect(map.has(obj1)).toBe(false);\n\n  // Test iteration\n  const entries = Array.from(map);\n  expect(entries.length).toBe(2);\n  expect(entries).toContainEqual([\"key2\", 2]);\n  expect(entries).toContainEqual([obj2, 4]);\n\n  // Test constructor with entries\n  const initialEntries: [string | object, number][] = [\n    [\"initial1\", 10],\n    [{ id: 3 }, 20]\n  ];\n  const mapWithEntries = new MaybeWeakMap(initialEntries);\n  expect(mapWithEntries.get(\"initial1\")).toBe(10);\n  expect(mapWithEntries.get(initialEntries[1][0])).toBe(20);\n});\n\n\ntype DependenciesMapInner<V> = (\n  & { map: MaybeWeakMap<unknown, DependenciesMapInner<V>> }\n  & (\n    | { hasValue: true, value: V }\n    | { hasValue: false, value: undefined }\n  )\n);\n\n/**\n * A map that stores values indexed by an array of keys. If the keys are objects and the environment supports WeakRefs,\n * they are stored in a WeakMap.\n */\nexport class DependenciesMap<K extends any[], V> {\n  private _inner: DependenciesMapInner<V> = { map: new MaybeWeakMap(), hasValue: false, value: undefined };\n\n  private _valueToResult(inner: DependenciesMapInner<V>): Result<V, void> {\n    if (inner.hasValue) {\n      return Result.ok(inner.value);\n    } else {\n      return Result.error(undefined);\n    }\n  }\n\n\n  private _unwrapFromInner(dependencies: any[], inner: DependenciesMapInner<V>): Result<V, void> {\n    if ((dependencies.length === 0)) {\n      return this._valueToResult(inner);\n    } else {\n      const [key, ...rest] = dependencies;\n      const newInner = inner.map.get(key);\n      if (!newInner) {\n        return Result.error(undefined);\n      }\n      return this._unwrapFromInner(rest, newInner);\n    }\n  }\n\n  private _setInInner(dependencies: any[], value: Result<V, void>, inner: DependenciesMapInner<V>): Result<V, void> {\n    if (dependencies.length === 0) {\n      const res = this._valueToResult(inner);\n      if (value.status === \"ok\") {\n        inner.hasValue = true;\n        inner.value = value.data;\n      } else {\n        inner.hasValue = false;\n        inner.value = undefined;\n      }\n      return res;\n    } else {\n      const [key, ...rest] = dependencies;\n      let newInner = inner.map.get(key);\n      if (!newInner) {\n        inner.map.set(key, newInner = { map: new MaybeWeakMap(), hasValue: false, value: undefined });\n      }\n      return this._setInInner(rest, value, newInner);\n    }\n  }\n\n  private *_iterateInner(dependencies: any[], inner: DependenciesMapInner<V>): IterableIterator<[K, V]> {\n    if (inner.hasValue) {\n      yield [dependencies as K, inner.value];\n    }\n    for (const [key, value] of inner.map) {\n      yield* this._iterateInner([...dependencies, key], value);\n    }\n  }\n\n  get(dependencies: K): V | undefined {\n    return Result.or(this._unwrapFromInner(dependencies, this._inner), undefined);\n  }\n\n  set(dependencies: K, value: V): this {\n    this._setInInner(dependencies, Result.ok(value), this._inner);\n    return this;\n  }\n\n  delete(dependencies: K): boolean {\n    return this._setInInner(dependencies, Result.error(undefined), this._inner).status === \"ok\";\n  }\n\n  has(dependencies: K): boolean {\n    return this._unwrapFromInner(dependencies, this._inner).status === \"ok\";\n  }\n\n  clear(): void {\n    this._inner = { map: new MaybeWeakMap(), hasValue: false, value: undefined };\n  }\n\n  *[Symbol.iterator](): IterableIterator<[K, V]> {\n    yield* this._iterateInner([], this._inner);\n  }\n\n  [Symbol.toStringTag] = \"DependenciesMap\";\n}\nundefined?.test(\"DependenciesMap\", ({ expect }) => {\n  // Test basic functionality\n  const map = new DependenciesMap<[string, number], string>();\n\n  // Test set and get\n  map.set([\"key\", 1], \"value1\");\n  expect(map.get([\"key\", 1])).toBe(\"value1\");\n\n  // Test has\n  expect(map.has([\"key\", 1])).toBe(true);\n  expect(map.has([\"key\", 2])).toBe(false);\n\n  // Test with different dependencies\n  map.set([\"key\", 2], \"value2\");\n  expect(map.get([\"key\", 2])).toBe(\"value2\");\n  expect(map.get([\"key\", 1])).toBe(\"value1\"); // Original still exists\n\n  // Test delete\n  expect(map.delete([\"key\", 1])).toBe(true);\n  expect(map.has([\"key\", 1])).toBe(false);\n  expect(map.get([\"key\", 1])).toBeUndefined();\n  expect(map.has([\"key\", 2])).toBe(true); // Other key still exists\n\n  // Test delete non-existent key\n  expect(map.delete([\"nonexistent\", 1])).toBe(false);\n\n  // Test clear\n  map.clear();\n  expect(map.has([\"key\", 2])).toBe(false);\n\n  // Test with object keys\n  const objMap = new DependenciesMap<[object, number], string>();\n  const obj1 = { id: 1 };\n  const obj2 = { id: 2 };\n  objMap.set([obj1, 1], \"object1\");\n  objMap.set([obj2, 2], \"object2\");\n  expect(objMap.get([obj1, 1])).toBe(\"object1\");\n  expect(objMap.get([obj2, 2])).toBe(\"object2\");\n\n  // Test iteration\n  const iterMap = new DependenciesMap<[string], number>();\n  iterMap.set([\"a\"], 1);\n  iterMap.set([\"b\"], 2);\n  iterMap.set([\"c\"], 3);\n\n  const entries = Array.from(iterMap);\n  expect(entries.length).toBe(3);\n  expect(entries).toContainEqual([[\"a\"], 1]);\n  expect(entries).toContainEqual([[\"b\"], 2]);\n  expect(entries).toContainEqual([[\"c\"], 3]);\n});\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAO,oBAAQ;AACR,IAAM,cAAc,CAAC,QAAQ,eAAe;;;ACA5C,IAAM,UAAU,IAAI,YAAY;AAChC,IAAM,UAAU,IAAI,YAAY;AACvC,IAAM,YAAY,KAAK;;;ACFhB,IAAM,eAAe,CAAC,UAAU;AACnC,MAAI,YAAY;AAChB,MAAI,OAAO,cAAc,UAAU;AAC/B,gBAAY,QAAQ,OAAO,SAAS;AAAA,EACxC;AACA,QAAMA,cAAa;AACnB,QAAM,MAAM,CAAC;AACb,WAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAKA,aAAY;AACnD,QAAI,KAAK,OAAO,aAAa,MAAM,MAAM,UAAU,SAAS,GAAG,IAAIA,WAAU,CAAC,CAAC;AAAA,EACnF;AACA,SAAO,KAAK,IAAI,KAAK,EAAE,CAAC;AAC5B;AACO,IAAM,SAAS,CAAC,UAAU;AAC7B,SAAO,aAAa,KAAK,EAAE,QAAQ,MAAM,EAAE,EAAE,QAAQ,OAAO,GAAG,EAAE,QAAQ,OAAO,GAAG;AACvF;AACO,IAAMC,gBAAe,CAAC,YAAY;AACrC,QAAM,SAAS,KAAK,OAAO;AAC3B,QAAM,QAAQ,IAAI,WAAW,OAAO,MAAM;AAC1C,WAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACpC,UAAM,CAAC,IAAI,OAAO,WAAW,CAAC;AAAA,EAClC;AACA,SAAO;AACX;AACO,IAAM,SAAS,CAAC,UAAU;AAC7B,MAAI,UAAU;AACd,MAAI,mBAAmB,YAAY;AAC/B,cAAU,QAAQ,OAAO,OAAO;AAAA,EACpC;AACA,YAAU,QAAQ,QAAQ,MAAM,GAAG,EAAE,QAAQ,MAAM,GAAG,EAAE,QAAQ,OAAO,EAAE;AACzE,MAAI;AACA,WAAOA,cAAa,OAAO;AAAA,EAC/B,QACM;AACF,UAAM,IAAI,UAAU,mDAAmD;AAAA,EAC3E;AACJ;;;ACpCA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAO,IAAM,YAAN,cAAwB,MAAM;AAAA,EACjC,YAAYC,UAAS,SAAS;AADlC,QAAAC;AAEQ,UAAMD,UAAS,OAAO;AACtB,SAAK,OAAO;AACZ,SAAK,OAAO,KAAK,YAAY;AAC7B,KAAAC,MAAA,MAAM,sBAAN,gBAAAA,IAAA,YAA0B,MAAM,KAAK;AAAA,EACzC;AACJ;AACA,UAAU,OAAO;AACV,IAAM,2BAAN,cAAuC,UAAU;AAAA,EACpD,YAAYD,UAAS,SAAS,QAAQ,eAAe,SAAS,eAAe;AACzE,UAAMA,UAAS,EAAE,OAAO,EAAE,OAAO,QAAQ,QAAQ,EAAE,CAAC;AACpD,SAAK,OAAO;AACZ,SAAK,QAAQ;AACb,SAAK,SAAS;AACd,SAAK,UAAU;AAAA,EACnB;AACJ;AACA,yBAAyB,OAAO;AACzB,IAAM,aAAN,cAAyB,UAAU;AAAA,EACtC,YAAYA,UAAS,SAAS,QAAQ,eAAe,SAAS,eAAe;AACzE,UAAMA,UAAS,EAAE,OAAO,EAAE,OAAO,QAAQ,QAAQ,EAAE,CAAC;AACpD,SAAK,OAAO;AACZ,SAAK,QAAQ;AACb,SAAK,SAAS;AACd,SAAK,UAAU;AAAA,EACnB;AACJ;AACA,WAAW,OAAO;AACX,IAAM,oBAAN,cAAgC,UAAU;AAAA,EAC7C,cAAc;AACV,UAAM,GAAG,SAAS;AAClB,SAAK,OAAO;AAAA,EAChB;AACJ;AACA,kBAAkB,OAAO;AAClB,IAAM,mBAAN,cAA+B,UAAU;AAAA,EAC5C,cAAc;AACV,UAAM,GAAG,SAAS;AAClB,SAAK,OAAO;AAAA,EAChB;AACJ;AACA,iBAAiB,OAAO;AACjB,IAAM,sBAAN,cAAkC,UAAU;AAAA,EAC/C,YAAYA,WAAU,+BAA+B,SAAS;AAC1D,UAAMA,UAAS,OAAO;AACtB,SAAK,OAAO;AAAA,EAChB;AACJ;AACA,oBAAoB,OAAO;AACpB,IAAM,aAAN,cAAyB,UAAU;AAAA,EACtC,cAAc;AACV,UAAM,GAAG,SAAS;AAClB,SAAK,OAAO;AAAA,EAChB;AACJ;AACA,WAAW,OAAO;AACX,IAAM,aAAN,cAAyB,UAAU;AAAA,EACtC,cAAc;AACV,UAAM,GAAG,SAAS;AAClB,SAAK,OAAO;AAAA,EAChB;AACJ;AACA,WAAW,OAAO;AACX,IAAM,aAAN,cAAyB,UAAU;AAAA,EACtC,cAAc;AACV,UAAM,GAAG,SAAS;AAClB,SAAK,OAAO;AAAA,EAChB;AACJ;AACA,WAAW,OAAO;AACX,IAAM,aAAN,cAAyB,UAAU;AAAA,EACtC,cAAc;AACV,UAAM,GAAG,SAAS;AAClB,SAAK,OAAO;AAAA,EAChB;AACJ;AACA,WAAW,OAAO;AACX,IAAM,cAAN,cAA0B,UAAU;AAAA,EACvC,cAAc;AACV,UAAM,GAAG,SAAS;AAClB,SAAK,OAAO;AAAA,EAChB;AACJ;AACA,YAAY,OAAO;AACZ,IAAM,oBAAN,cAAgC,UAAU;AAAA,EAC7C,YAAYA,WAAU,mDAAmD,SAAS;AAC9E,UAAMA,UAAS,OAAO;AACtB,SAAK,OAAO;AAAA,EAChB;AACJ;AACA,kBAAkB,OAAO;AAClB,IAAM,2BAAN,cAAuC,UAAU;AAAA,EACpD,YAAYA,WAAU,wDAAwD,SAAS;AACnF,UAAMA,UAAS,OAAO;AACtB,SAAK,OAAO;AAAA,EAChB;AACJ;AAEA,yBAAyB,OAAO;AACzB,IAAM,cAAN,cAA0B,UAAU;AAAA,EACvC,YAAYE,WAAU,qBAAqB,SAAS;AAChD,UAAMA,UAAS,OAAO;AACtB,SAAK,OAAO;AAAA,EAChB;AACJ;AACA,YAAY,OAAO;AACZ,IAAM,iCAAN,cAA6C,UAAU;AAAA,EAC1D,YAAYA,WAAU,iCAAiC,SAAS;AAC5D,UAAMA,UAAS,OAAO;AACtB,SAAK,OAAO;AAAA,EAChB;AACJ;AACA,+BAA+B,OAAO;;;AChHtC,IAAO,iBAAQ,kBAAO,gBAAgB,KAAK,iBAAM;;;ACDjD,SAAS,QAAQ,KAAK,WAAWC,QAAO;AAAxC,MAAAC;AACI,EAAAD,SAAQA,OAAM,OAAO,OAAO;AAC5B,MAAIA,OAAM,SAAS,GAAG;AAClB,UAAM,OAAOA,OAAM,IAAI;AACvB,WAAO,eAAeA,OAAM,KAAK,IAAI,CAAC,QAAQ,IAAI;AAAA,EACtD,WACSA,OAAM,WAAW,GAAG;AACzB,WAAO,eAAeA,OAAM,CAAC,CAAC,OAAOA,OAAM,CAAC,CAAC;AAAA,EACjD,OACK;AACD,WAAO,WAAWA,OAAM,CAAC,CAAC;AAAA,EAC9B;AACA,MAAI,UAAU,MAAM;AAChB,WAAO,aAAa,MAAM;AAAA,EAC9B,WACS,OAAO,WAAW,cAAc,OAAO,MAAM;AAClD,WAAO,sBAAsB,OAAO,IAAI;AAAA,EAC5C,WACS,OAAO,WAAW,YAAY,UAAU,MAAM;AACnD,SAAIC,MAAA,OAAO,gBAAP,gBAAAA,IAAoB,MAAM;AAC1B,aAAO,4BAA4B,OAAO,YAAY,IAAI;AAAA,IAC9D;AAAA,EACJ;AACA,SAAO;AACX;AAIO,SAAS,QAAQ,KAAK,WAAWC,QAAO;AAC3C,SAAO,QAAQ,eAAe,GAAG,uBAAuB,QAAQ,GAAGA,MAAK;AAC5E;;;AC7BA,IAAO,sBAAQ,CAAC,QAAQ;AACpB,MAAI,YAAY,GAAG,GAAG;AAClB,WAAO;AAAA,EACX;AACA,UAAO,2BAAM,OAAO,kBAAiB;AACzC;AACO,IAAM,QAAQ,CAAC,WAAW;;;ACPjC,SAAS,aAAa,OAAO;AACzB,SAAO,OAAO,UAAU,YAAY,UAAU;AAClD;AACe,SAAR,SAA0B,OAAO;AACpC,MAAI,CAAC,aAAa,KAAK,KAAK,OAAO,UAAU,SAAS,KAAK,KAAK,MAAM,mBAAmB;AACrF,WAAO;AAAA,EACX;AACA,MAAI,OAAO,eAAe,KAAK,MAAM,MAAM;AACvC,WAAO;AAAA,EACX;AACA,MAAI,QAAQ;AACZ,SAAO,OAAO,eAAe,KAAK,MAAM,MAAM;AAC1C,YAAQ,OAAO,eAAe,KAAK;AAAA,EACvC;AACA,SAAO,OAAO,eAAe,KAAK,MAAM;AAC5C;;;ACdO,SAAS,MAAM,KAAK;AACvB,SAAO,SAAS,GAAG,KAAK,OAAO,IAAI,QAAQ;AAC/C;AACO,SAAS,aAAa,KAAK;AAC9B,SAAO,IAAI,QAAQ,SAAS,OAAO,IAAI,MAAM;AACjD;AACO,SAAS,YAAY,KAAK;AAC7B,SAAO,IAAI,QAAQ,SAAS,OAAO,IAAI,MAAM;AACjD;AACO,SAAS,YAAY,KAAK;AAC7B,SAAO,MAAM,GAAG,KAAK,IAAI,QAAQ,SAAS,OAAO,IAAI,MAAM;AAC/D;;;ACTA,IAAM,MAAM,CAAC,QAAQ,2BAAM,OAAO;AAClC,IAAM,eAAe,CAAC,KAAK,KAAK,UAAU;AAJ1C,MAAAC,KAAAC;AAKI,MAAI,IAAI,QAAQ,UAAa,IAAI,QAAQ,OAAO;AAC5C,UAAM,IAAI,UAAU,kEAAkE;AAAA,EAC1F;AACA,MAAI,IAAI,YAAY,YAAaA,OAAAD,MAAA,IAAI,SAAQ,aAAZ,gBAAAC,IAAA,KAAAD,KAAuB,YAAW,MAAM;AACrE,UAAM,IAAI,UAAU,yEAAyE,KAAK,EAAE;AAAA,EACxG;AACA,MAAI,IAAI,QAAQ,UAAa,IAAI,QAAQ,KAAK;AAC1C,UAAM,IAAI,UAAU,gEAAgE,GAAG,EAAE;AAAA,EAC7F;AACA,SAAO;AACX;AACA,IAAM,qBAAqB,CAAC,KAAK,KAAK,OAAO,aAAa;AACtD,MAAI,eAAe;AACf;AACJ,MAAI,YAAgB,MAAM,GAAG,GAAG;AAC5B,QAAQ,YAAY,GAAG,KAAK,aAAa,KAAK,KAAK,KAAK;AACpD;AACJ,UAAM,IAAI,UAAU,yHAAyH;AAAA,EACjJ;AACA,MAAI,CAAC,oBAAU,GAAG,GAAG;AACjB,UAAM,IAAI,UAAU,QAAgB,KAAK,KAAK,GAAG,OAAO,cAAc,WAAW,iBAAiB,IAAI,CAAC;AAAA,EAC3G;AACA,MAAI,IAAI,SAAS,UAAU;AACvB,UAAM,IAAI,UAAU,GAAG,IAAI,GAAG,CAAC,8DAA8D;AAAA,EACjG;AACJ;AACA,IAAM,sBAAsB,CAAC,KAAK,KAAK,OAAO,aAAa;AACvD,MAAI,YAAgB,MAAM,GAAG,GAAG;AAC5B,YAAQ,OAAO;AAAA,MACX,KAAK;AACD,YAAQ,aAAa,GAAG,KAAK,aAAa,KAAK,KAAK,KAAK;AACrD;AACJ,cAAM,IAAI,UAAU,kDAAkD;AAAA,MAC1E,KAAK;AACD,YAAQ,YAAY,GAAG,KAAK,aAAa,KAAK,KAAK,KAAK;AACpD;AACJ,cAAM,IAAI,UAAU,iDAAiD;AAAA,IAC7E;AAAA,EACJ;AACA,MAAI,CAAC,oBAAU,GAAG,GAAG;AACjB,UAAM,IAAI,UAAU,QAAgB,KAAK,KAAK,GAAG,OAAO,WAAW,iBAAiB,IAAI,CAAC;AAAA,EAC7F;AACA,MAAI,IAAI,SAAS,UAAU;AACvB,UAAM,IAAI,UAAU,GAAG,IAAI,GAAG,CAAC,mEAAmE;AAAA,EACtG;AACA,MAAI,UAAU,UAAU,IAAI,SAAS,UAAU;AAC3C,UAAM,IAAI,UAAU,GAAG,IAAI,GAAG,CAAC,uEAAuE;AAAA,EAC1G;AACA,MAAI,UAAU,aAAa,IAAI,SAAS,UAAU;AAC9C,UAAM,IAAI,UAAU,GAAG,IAAI,GAAG,CAAC,0EAA0E;AAAA,EAC7G;AACA,MAAI,IAAI,aAAa,UAAU,YAAY,IAAI,SAAS,WAAW;AAC/D,UAAM,IAAI,UAAU,GAAG,IAAI,GAAG,CAAC,wEAAwE;AAAA,EAC3G;AACA,MAAI,IAAI,aAAa,UAAU,aAAa,IAAI,SAAS,WAAW;AAChE,UAAM,IAAI,UAAU,GAAG,IAAI,GAAG,CAAC,yEAAyE;AAAA,EAC5G;AACJ;AACA,SAAS,aAAa,UAAU,KAAK,KAAK,OAAO;AAC7C,QAAM,YAAY,IAAI,WAAW,IAAI,KACjC,QAAQ,SACR,IAAI,WAAW,OAAO,KACtB,qBAAqB,KAAK,GAAG;AACjC,MAAI,WAAW;AACX,uBAAmB,KAAK,KAAK,OAAO,QAAQ;AAAA,EAChD,OACK;AACD,wBAAoB,KAAK,KAAK,OAAO,QAAQ;AAAA,EACjD;AACJ;AACA,IAAO,yBAAQ,aAAa,KAAK,QAAW,KAAK;AAC1C,IAAM,sBAAsB,aAAa,KAAK,QAAW,IAAI;;;AC5E7D,IAAM,cAAc,OAAO;;;ACAlC,IAAM,SAAS;AACf,IAAM,OAAO,SAAS;AACtB,IAAM,MAAM,OAAO;AACnB,IAAM,OAAO,MAAM;AACnB,IAAM,OAAO,MAAM;;;ACKnB,IAAI;AATJ;AAUA,IAAI,OAAO,cAAc,eAAe,GAAC,qBAAU,cAAV,mBAAqB,eAArB,4BAAkC,kBAAiB;AACxF,QAAM,OAAO;AACb,QAAM,UAAU;AAChB,eAAa,GAAG,IAAI,IAAI,OAAO;AACnC;AACO,IAAM,YAAY,OAAO;;;ACfhC,IAAAE,qBAAA;AAAA,SAAAA,oBAAA;AAAA,gBAAAC;AAAA,EAAA,cAAAC;AAAA;AACO,IAAMC,UAAmB;AACzB,IAAMC,UAAmB;;;ACEzB,SAAS,UAAUC,MAAK;AAC3B,MAAI,OAAOA,SAAQ;AACf,UAAM,IAAI,WAAW,+DAA+D;AACxF,QAAM,EAAE,GAAG,SAAS,OAAO,IAAIA,KAAI,MAAM,GAAG;AAC5C,MAAI,WAAW;AACX,UAAM,IAAI,WAAW,0DAA0D;AACnF,MAAI,WAAW;AACX,UAAM,IAAI,WAAW,aAAa;AACtC,MAAI,CAAC;AACD,UAAM,IAAI,WAAW,6BAA6B;AACtD,MAAI;AACJ,MAAI;AACA,cAAUC,QAAU,OAAO;AAAA,EAC/B,QACM;AACF,UAAM,IAAI,WAAW,wCAAwC;AAAA,EACjE;AACA,MAAI;AACJ,MAAI;AACA,aAAS,KAAK,MAAM,QAAQ,OAAO,OAAO,CAAC;AAAA,EAC/C,QACM;AACF,UAAM,IAAI,WAAW,6CAA6C;AAAA,EACtE;AACA,MAAI,CAAC,SAAS,MAAM;AAChB,UAAM,IAAI,WAAW,wBAAwB;AACjD,SAAO;AACX;;;AC/BA,IAAM,YAAY,IAAI,MAAM,qDAAqD;AACjF,IAAM,mBAAmB,IAAI,MAAM,sBAAsB;AACzD,IAAM,aAAa,IAAI,MAAM,2BAA2B;AAExD,IAAI,cAAoD,SAAU,SAAS,YAAY,GAAG,WAAW;AACjG,WAAS,MAAM,OAAO;AAAE,WAAO,iBAAiB,IAAI,QAAQ,IAAI,EAAE,SAAU,SAAS;AAAE,cAAQ,KAAK;AAAA,IAAG,CAAC;AAAA,EAAG;AAC3G,SAAO,KAAK,MAAM,IAAI,UAAU,SAAU,SAAS,QAAQ;AACvD,aAAS,UAAU,OAAO;AAAE,UAAI;AAAE,aAAK,UAAU,KAAK,KAAK,CAAC;AAAA,MAAG,SAAS,GAAG;AAAE,eAAO,CAAC;AAAA,MAAG;AAAA,IAAE;AAC1F,aAASC,UAAS,OAAO;AAAE,UAAI;AAAE,aAAK,UAAU,OAAO,EAAE,KAAK,CAAC;AAAA,MAAG,SAAS,GAAG;AAAE,eAAO,CAAC;AAAA,MAAG;AAAA,IAAE;AAC7F,aAAS,KAAK,QAAQ;AAAE,aAAO,OAAO,QAAQ,OAAO,KAAK,IAAI,MAAM,OAAO,KAAK,EAAE,KAAK,WAAWA,SAAQ;AAAA,IAAG;AAC7G,UAAM,YAAY,UAAU,MAAM,SAAS,cAAc,CAAC,CAAC,GAAG,KAAK,CAAC;AAAA,EACxE,CAAC;AACL;AACA,IAAM,YAAN,MAAgB;AAAA,EACZ,YAAY,QAAQ,eAAe,YAAY;AAC3C,SAAK,SAAS;AACd,SAAK,eAAe;AACpB,SAAK,SAAS,CAAC;AACf,SAAK,mBAAmB,CAAC;AAAA,EAC7B;AAAA,EACA,QAAQ,SAAS,GAAG,WAAW,GAAG;AAC9B,QAAI,UAAU;AACV,YAAM,IAAI,MAAM,kBAAkB,MAAM,oBAAoB;AAChE,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACpC,YAAM,OAAO,EAAE,SAAS,QAAQ,QAAQ,SAAS;AACjD,YAAM,IAAI,iBAAiB,KAAK,QAAQ,CAAC,UAAU,YAAY,MAAM,QAAQ;AAC7E,UAAI,MAAM,MAAM,UAAU,KAAK,QAAQ;AAEnC,aAAK,cAAc,IAAI;AAAA,MAC3B,OACK;AACD,aAAK,OAAO,OAAO,IAAI,GAAG,GAAG,IAAI;AAAA,MACrC;AAAA,IACJ,CAAC;AAAA,EACL;AAAA,EACA,aAAa,YAAY;AACrB,WAAO,YAAY,MAAM,WAAW,QAAQ,WAAW,UAAU,SAAS,GAAG,WAAW,GAAG;AACvF,YAAM,CAAC,OAAO,OAAO,IAAI,MAAM,KAAK,QAAQ,QAAQ,QAAQ;AAC5D,UAAI;AACA,eAAO,MAAM,SAAS,KAAK;AAAA,MAC/B,UACA;AACI,gBAAQ;AAAA,MACZ;AAAA,IACJ,CAAC;AAAA,EACL;AAAA,EACA,cAAc,SAAS,GAAG,WAAW,GAAG;AACpC,QAAI,UAAU;AACV,YAAM,IAAI,MAAM,kBAAkB,MAAM,oBAAoB;AAChE,QAAI,KAAK,sBAAsB,QAAQ,QAAQ,GAAG;AAC9C,aAAO,QAAQ,QAAQ;AAAA,IAC3B,OACK;AACD,aAAO,IAAI,QAAQ,CAAC,YAAY;AAC5B,YAAI,CAAC,KAAK,iBAAiB,SAAS,CAAC;AACjC,eAAK,iBAAiB,SAAS,CAAC,IAAI,CAAC;AACzC,qBAAa,KAAK,iBAAiB,SAAS,CAAC,GAAG,EAAE,SAAS,SAAS,CAAC;AAAA,MACzE,CAAC;AAAA,IACL;AAAA,EACJ;AAAA,EACA,WAAW;AACP,WAAO,KAAK,UAAU;AAAA,EAC1B;AAAA,EACA,WAAW;AACP,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,SAAS,OAAO;AACZ,SAAK,SAAS;AACd,SAAK,eAAe;AAAA,EACxB;AAAA,EACA,QAAQ,SAAS,GAAG;AAChB,QAAI,UAAU;AACV,YAAM,IAAI,MAAM,kBAAkB,MAAM,oBAAoB;AAChE,SAAK,UAAU;AACf,SAAK,eAAe;AAAA,EACxB;AAAA,EACA,SAAS;AACL,SAAK,OAAO,QAAQ,CAAC,UAAU,MAAM,OAAO,KAAK,YAAY,CAAC;AAC9D,SAAK,SAAS,CAAC;AAAA,EACnB;AAAA,EACA,iBAAiB;AACb,SAAK,oBAAoB;AACzB,WAAO,KAAK,OAAO,SAAS,KAAK,KAAK,OAAO,CAAC,EAAE,UAAU,KAAK,QAAQ;AACnE,WAAK,cAAc,KAAK,OAAO,MAAM,CAAC;AACtC,WAAK,oBAAoB;AAAA,IAC7B;AAAA,EACJ;AAAA,EACA,cAAc,MAAM;AAChB,UAAM,gBAAgB,KAAK;AAC3B,SAAK,UAAU,KAAK;AACpB,SAAK,QAAQ,CAAC,eAAe,KAAK,aAAa,KAAK,MAAM,CAAC,CAAC;AAAA,EAChE;AAAA,EACA,aAAa,QAAQ;AACjB,QAAI,SAAS;AACb,WAAO,MAAM;AACT,UAAI;AACA;AACJ,eAAS;AACT,WAAK,QAAQ,MAAM;AAAA,IACvB;AAAA,EACJ;AAAA,EACA,sBAAsB;AAClB,QAAI,KAAK,OAAO,WAAW,GAAG;AAC1B,eAAS,SAAS,KAAK,QAAQ,SAAS,GAAG,UAAU;AACjD,cAAM,UAAU,KAAK,iBAAiB,SAAS,CAAC;AAChD,YAAI,CAAC;AACD;AACJ,gBAAQ,QAAQ,CAAC,WAAW,OAAO,QAAQ,CAAC;AAC5C,aAAK,iBAAiB,SAAS,CAAC,IAAI,CAAC;AAAA,MACzC;AAAA,IACJ,OACK;AACD,YAAM,iBAAiB,KAAK,OAAO,CAAC,EAAE;AACtC,eAAS,SAAS,KAAK,QAAQ,SAAS,GAAG,UAAU;AACjD,cAAM,UAAU,KAAK,iBAAiB,SAAS,CAAC;AAChD,YAAI,CAAC;AACD;AACJ,cAAM,IAAI,QAAQ,UAAU,CAAC,WAAW,OAAO,YAAY,cAAc;AACzE,SAAC,MAAM,KAAK,UAAU,QAAQ,OAAO,GAAG,CAAC,GACpC,QAAS,YAAU,OAAO,QAAQ,CAAE;AAAA,MAC7C;AAAA,IACJ;AAAA,EACJ;AAAA,EACA,sBAAsB,QAAQ,UAAU;AACpC,YAAQ,KAAK,OAAO,WAAW,KAAK,KAAK,OAAO,CAAC,EAAE,WAAW,aAC1D,UAAU,KAAK;AAAA,EACvB;AACJ;AACA,SAAS,aAAa,GAAG,GAAG;AACxB,QAAM,IAAI,iBAAiB,GAAG,CAAC,UAAU,EAAE,YAAY,MAAM,QAAQ;AACrE,IAAE,OAAO,IAAI,GAAG,GAAG,CAAC;AACxB;AACA,SAAS,iBAAiB,GAAG,WAAW;AACpC,WAAS,IAAI,EAAE,SAAS,GAAG,KAAK,GAAG,KAAK;AACpC,QAAI,UAAU,EAAE,CAAC,CAAC,GAAG;AACjB,aAAO;AAAA,IACX;AAAA,EACJ;AACA,SAAO;AACX;;;ACvIO,IAAM,gBAAN,MAAoB;EAKzB,cAAc;AACZ,SAAK,YAAY,IAAI,UAAU,CAAC;AAChC,SAAK,UAAU;AACf,SAAK,eAAe,IAAI,UAAU,CAAC;EACrC;EAEA,MAAM,aAAgB,UAAuC;AAC3D,UAAM,KAAK,iBAAiB;AAC5B,QAAI;AACF,aAAO,MAAM,SAAS;IACxB,UAAA;AACE,YAAM,KAAK,iBAAiB;IAC9B;EACF;EAEA,MAAM,cAAiB,UAAuC;AAC5D,UAAM,KAAK,kBAAkB;AAC7B,QAAI;AACF,aAAO,MAAM,SAAS;IACxB,UAAA;AACE,YAAM,KAAK,kBAAkB;IAC/B;EACF;EAEA,MAAc,mBAAkC;AAE9C,UAAM,KAAK,aAAa,QAAQ;AAChC,QAAI;AACF,WAAK,WAAW;AAEhB,UAAI,KAAK,YAAY,GAAG;AACtB,cAAM,KAAK,UAAU,QAAQ;MAC/B;IACF,UAAA;AACE,WAAK,aAAa,QAAQ;IAC5B;EACF;EAEA,MAAc,mBAAkC;AAE9C,UAAM,KAAK,aAAa,QAAQ;AAChC,QAAI;AACF,WAAK,WAAW;AAEhB,UAAI,KAAK,YAAY,GAAG;AACtB,aAAK,UAAU,QAAQ;MACzB;IACF,UAAA;AACE,WAAK,aAAa,QAAQ;IAC5B;EACF;EAEA,MAAc,oBAAmC;AAE/C,UAAM,KAAK,UAAU,QAAQ;EAC/B;EAEA,MAAc,oBAAmC;AAE/C,SAAK,UAAU,QAAQ;EACzB;AACF;;;AC/CO,IAAM,QAAN,MAA2C;EAGhD,YACU,QACR;AADQ,SAAA,SAAA;AAHV,SAAiB,aAAyE,oBAAI,IAAI;EAI/F;EAEH,MAAS;AACP,WAAO,KAAK;EACd;EAEA,IAAI,OAAgB;AAClB,UAAM,WAAW,KAAK;AACtB,SAAK,SAAS;AACd,SAAK,WAAW,QAAQ,CAAC,aAAa,SAAS,OAAO,QAAQ,CAAC;EACjE;EAEA,OAAO,SAA6B;AAClC,UAAM,QAAQ,QAAQ,KAAK,MAAM;AACjC,SAAK,IAAI,KAAK;AACd,WAAO;EACT;EAEA,SAAS,UAAoF;AAC3F,UAAM,OAAO,aAAa;AAC1B,SAAK,WAAW,IAAI,MAAM,QAAQ;AAClC,WAAO;MACL,aAAa,MAAM;AACjB,aAAK,WAAW,OAAO,IAAI;MAC7B;IACF;EACF;EAEA,WAAW,UAAoF;AAC7F,UAAM,EAAE,YAAY,IAAI,KAAK,SAAS,IAAI,SAAS;AACjD,kBAAY;AACZ,eAAS,GAAG,IAAI;IAClB,CAAC;AACD,WAAO,EAAE,YAAY;EACvB;AACF;AAEO,IAAM,YAAY,IAAI,cAAc;AAGpC,IAAM,aAAN,MAAM,YAA+C;EAa1D,eAAe,MAAgB;AAX/B,SAAQ,qBAAoC;AAE5C,SAAQ,cAAc;AAEtB,SAAiB,0BAA0B,oBAAI,IAAwC;AAEvF,SAAiB,aAA4D,oBAAI,IAAI;AAErF,SAAQ,iBAAiB;AACzB,SAAQ,wBAAwB;AAG9B,QAAI,KAAK,WAAW,GAAG;AACrB,WAAK,eAAe;IACtB,OAAO;AACL,WAAK,eAAe;AACpB,WAAK,qBAAqB,KAAK,CAAC;IAClC;EACF;EAEA,cAAuB;AACrB,WAAO,KAAK;EACd;EAEA,aAAsB;AACpB,WAAO,KAAK;EACd;EAEA,MAAM;AACJ,QAAI,KAAK,WAAW,GAAG;AACrB,aAAO,YAAY,MAAM,KAAK,eAAe;IAC/C,WAAW,KAAK,YAAY,GAAG;AAC7B,aAAO,YAAY,GAAG,KAAK,kBAAuB;IACpD,OAAO;AACL,aAAO,YAAY,QAAQ;IAC7B;EACF;EAEA,YAA6B;AAC3B,UAAM,OAAO,aAAa;AAC1B,QAAI,KAAK,WAAW,GAAG;AACrB,aAAO,SAAS,KAAK,eAAe;IACtC,WAAW,KAAK,YAAY,GAAG;AAC7B,aAAO,SAAS,KAAK,kBAAuB;IAC9C;AACA,UAAM,UAAU,IAAI,QAAW,CAAC,SAAS,WAAW;AAClD,WAAK,WAAW,CAAC,UAAU;AACzB,gBAAQ,KAAK;MACf,CAAC;AACD,WAAK,wBAAwB,IAAI,MAAM,MAAM;IAC/C,CAAC;AACD,UAAM,cAAc,QAAQ,QAAQ,MAAM;AACxC,WAAK,wBAAwB,OAAO,IAAI;IAC1C,CAAC;AACD,WAAO,QAAQ,WAAW;EAC5B;EAEA,aAAa,QAAmB,YAAoB;AAClD,UAAM,WAAW,KAAK,IAAI;AAC1B,UAAM,WAAW,KAAK;AACtB,QAAI,aAAa,KAAK,uBAAuB;AAC3C,cAAQ,OAAO,QAAQ;QACrB,KAAK,MAAM;AACT,cAAI,CAAC,KAAK,gBAAgB,KAAK,eAAe,KAAK,uBAAuB,OAAO,MAAM;AACrF,iBAAK,wBAAwB;AAC7B,iBAAK,eAAe;AACpB,iBAAK,cAAc;AACnB,iBAAK,qBAAqB,OAAO;AACjC,iBAAK,kBAAkB;AACvB,iBAAK,WAAW,QAAQ,CAAC,aAAa,SAAS;cAC7C,OAAO,KAAK,IAAI;cAChB;cACA,aAAa;YACf,CAAC,CAAC;AACF,mBAAO;UACT;AACA,iBAAO;QACT;QACA,KAAK,SAAS;AACZ,eAAK,wBAAwB;AAC7B,eAAK,eAAe;AACpB,eAAK,cAAc;AACnB,eAAK,kBAAkB,OAAO;AAC9B,eAAK,wBAAwB,QAAQ,CAAC,WAAW,OAAO,OAAO,KAAK,CAAC;AACrE,eAAK,WAAW,QAAQ,CAAC,aAAa,SAAS;YAC7C,OAAO,KAAK,IAAI;YAChB;YACA,aAAa;UACf,CAAC,CAAC;AACF,iBAAO;QACT;MACF;IACF;AACA,WAAO;EACT;EAEA,IAAI,OAAgB;AAClB,SAAK,aAAa,OAAO,GAAG,KAAK,GAAG,EAAE,KAAK,cAAc;EAC3D;EAEA,OAAO,SAAyC;AAC9C,UAAM,QAAQ,QAAQ,KAAK,kBAAkB;AAC7C,SAAK,IAAI,KAAK;AACd,WAAO;EACT;EAEA,MAAM,SAAS,SAAuC;AACpD,WAAO,MAAM,UAAU,aAAa,YAAY;AAC9C,YAAM,aAAa,EAAE,KAAK;AAC1B,YAAM,SAAS,MAAM,OAAO,YAAY,OAAO;AAC/C,aAAO,KAAK,aAAa,QAAQ,UAAU;IAC7C,CAAC;EACH;EAEA,iBAAuB;AACrB,SAAK,wBAAwB,EAAE,KAAK;AACpC,SAAK,eAAe;AACpB,SAAK,cAAc;AACnB,SAAK,kBAAkB;EACzB;EAEA,YAAY,OAAsB;AAChC,SAAK,aAAa,OAAO,MAAM,KAAK,GAAG,EAAE,KAAK,cAAc;EAC9D;EAEA,IAAO,QAAwC;AAC7C,UAAM,QAAQ,IAAI,YAAc;AAChC,SAAK,SAAS,CAAC,UAAU;AACvB,YAAM,IAAI,OAAO,KAAK,CAAC;IACzB,CAAC;AACD,WAAO;EACT;EAEA,SAAS,UAAoF;AAC3F,WAAO,KAAK,cAAc,CAAC,EAAE,OAAO,YAAY,MAAM;AACpD,UAAI,MAAM,WAAW,MAAM;AACzB,iBAAS,MAAM,MAAM,WAAW;MAClC;IACF,CAAC;EACH;EAEA,cAAc,UAAyE;AACrF,UAAM,OAAO,aAAa;AAC1B,SAAK,WAAW,IAAI,MAAM,QAAQ;AAClC,WAAO;MACL,aAAa,MAAM;AACjB,aAAK,WAAW,OAAO,IAAI;MAC7B;IACF;EACF;EAEA,WAAW,UAAoF;AAC7F,UAAM,EAAE,YAAY,IAAI,KAAK,SAAS,IAAI,SAAS;AACjD,kBAAY;AACZ,eAAS,GAAG,IAAI;IAClB,CAAC;AACD,WAAO,EAAE,YAAY;EACvB;EAEA,gBAAgB,UAAyE;AACvF,UAAM,EAAE,YAAY,IAAI,KAAK,cAAc,IAAI,SAAS;AACtD,kBAAY;AACZ,eAAS,GAAG,IAAI;IAClB,CAAC;AACD,WAAO,EAAE,YAAY;EACvB;AACF;;;ACxOO,IAAM,cAAN,MAAkB;EACvB,YACkB,OAChB;AADgB,SAAA,QAAA;AAEhB,QAAI,UAAU,aAAa;AACzB,YAAM,IAAI,oBAAoB,sHAAsH;IACtJ;EACF;EAEA,IAAI,UAAU;AACZ,WAAY,UAAU,KAAK,KAAK;EAClC;EAEA,IAAI,YAAkB;AACpB,UAAM,EAAE,IAAI,IAAI,KAAK;AACrB,QAAI,QAAQ,OAAW,QAAO,oBAAI,KAAK,MAAgB;AACvD,WAAO,IAAI,KAAK,MAAM,GAAI;EAC5B;;;;EAKA,IAAI,kBAA0B;AAC5B,WAAO,KAAK,IAAI,GAAG,KAAK,UAAU,QAAQ,IAAI,KAAK,IAAI,CAAC;EAC1D;EAEA,YAAqB;AACnB,WAAO,KAAK,mBAAmB;EACjC;AACF;AAEO,IAAM,eAAN,MAAmB;EACxB,YACkB,OAChB;AADgB,SAAA,QAAA;AAEhB,QAAI,UAAU,aAAa;AACzB,YAAM,IAAI,oBAAoB,uHAAuH;IACvJ;EACF;AACF;AAOO,IAAM,kBAAN,MAAM,iBAAgB;EA4B3B,YAA6B,UAI1B;AAJ0B,SAAA,WAAA;AAJ7B,SAAQ,oBAAoB,IAAI,MAAe,KAAK;AAEpD,SAAQ,kBAAsD;AAO5D,SAAK,eAAe,IAAI,MAAM,SAAS,cAAc,IAAI,YAAY,SAAS,WAAW,IAAI,IAAI;AACjG,SAAK,gBAAgB,SAAS,eAAe,IAAI,aAAa,SAAS,YAAY,IAAI;AACvF,QAAI,SAAS,gBAAgB,QAAQ,SAAS,iBAAiB,MAAM;AAEnE,WAAK,kBAAkB,IAAI,IAAI;IACjC;AACA,SAAK,aAAa,iBAAgB,oBAAoB,EAAE,aAAa,SAAS,eAAe,MAAM,cAAc,SAAS,aAAa,CAAC;EAC1I;EAEA,OAAO,oBAAoB,UAAgF;AACzG,QAAI,SAAS,cAAc;AACzB,aAAO,WAAW,SAAS,YAAY;IACzC,WAAW,SAAS,aAAa;AAC/B,aAAO,UAAU,SAAS,WAAW;IACvC,OAAO;AACL,aAAO;IACT;EACF;EAEA,qBAAqB;AACnB,WAAO,KAAK,kBAAkB,IAAI;EACpC;;;;EAKA,cAAc;AACZ,SAAK,aAAa,IAAI,IAAI;AAC1B,SAAK,kBAAkB,IAAI,IAAI;EACjC;EAEA,aAAa,UAAmD;AAC9D,WAAO,KAAK,kBAAkB,SAAS,MAAM,SAAS,CAAC;EACzD;;;;;;;;EASA,MAAM,4BAA4B,0BAAmH;AACnJ,QAAI,4BAA4B,KAAQ;AACtC,YAAM,IAAI,MAAM,gCAAgC,wBAAwB,0EAA0E;IACpJ;AAEA,UAAM,cAAc,KAAK,6CAA6C;AACtE,QAAI,CAAC,eAAe,YAAY,kBAAkB,0BAA0B;AAC1E,YAAM,YAAY,MAAM,KAAK,eAAe;AAC5C,YAAM,kBAAkB,uCAAW,YAAY;AAC/C,UAAI,mBAAmB,kBAAkB,0BAA0B;AACjE,cAAM,IAAI,oBAAoB,gCAAgC,wBAAwB,uEAAuE,eAAe,KAAK;MACnL;AACA,aAAO;IACT;AACA,WAAO,EAAE,aAAa,cAAc,KAAK,cAAc;EACzD;;;;;;;;;;EAWA,MAAM,iBAAkG;AACtG,UAAM,cAAc,MAAM,KAAK,4BAA4B;AAC3D,WAAO,cAAc,EAAE,aAAa,cAAc,KAAK,cAAc,IAAI;EAC3E;EAEA,uBAAuB,aAA0B;AAE/C,QAAI,KAAK,aAAa,IAAI,MAAM,aAAa;AAC3C,WAAK,aAAa,IAAI,IAAI;IAC5B;EACF;;;;EAKA,oBAAoB,UAAqF;AACvG,WAAO,KAAK,aAAa,SAAS,QAAQ;EAC5C;;;;EAKQ,+CAAmE;AACzE,QAAI,CAAC,KAAK,cAAe,QAAO;AAChC,QAAI,KAAK,mBAAmB,EAAG,QAAO;AAEtC,UAAM,cAAc,KAAK,aAAa,IAAI;AAC1C,QAAI,eAAe,CAAC,YAAY,UAAU,EAAG,QAAO;AAEpD,WAAO;EACT;;;;;;EAOA,MAAc,8BAA2D;AACvE,QAAI,CAAC,KAAK,cAAe,QAAO;AAChC,QAAI,KAAK,kBAAkB,IAAI,EAAG,QAAO;AAEzC,QAAI,CAAC,KAAK,iBAAiB;AACzB,WAAK,6BAA6B,KAAK,aAAa;IACtD;AACA,WAAO,MAAM,KAAK;EACpB;EAEQ,6BAA6B,cAA4B;AAC/D,QAAI,iBAA8C,KAAK,SAAS,2BAA2B,YAAY,EAAE,KAAK,CAAC,gBAAgB;AAC7H,UAAI,mBAAmB,KAAK,iBAAiB;AAC3C,aAAK,kBAAkB;AACvB,aAAK,aAAa,IAAI,WAAW;AACjC,YAAI,CAAC,aAAa;AAChB,eAAK,YAAY;QACnB;MACF;AACA,aAAO;IACT,CAAC;AACD,SAAK,kBAAkB;EACzB;AACF;;;ACjNO,IAAM,eAAe;EAC1B,OAAO;IACL,MAAM;IACN,YAAY;EACd;EACA,QAAQ;IACN,MAAM;IACN,YAAY;EACd;EACA,OAAO;IACL,MAAM;IACN,YAAY;EACd;EACA,UAAU;IACR,MAAM;IACN,YAAY;EACd;EACA,SAAS;IACP,MAAM;IACN,YAAY;EACd;EACA,WAAW;IACT,MAAM;IACN,YAAY;EACd;EACA,QAAQ;IACN,MAAM;IACN,YAAY;EACd;EACA,SAAS;IACP,MAAM;IACN,YAAY;EACd;EACA,WAAW;IACT,MAAM;IACN,YAAY;EACd;AACF;AAGO,SAAS,+BAA+B,OAAwC;AACrF,QAAM,CAAC,MAAM,SAAS,GAAG,IAAI,IAAI,MAAM,MAAM,GAAG;AAChD,MAAI,KAAK,SAAS,EAAG,QAAO;AAC5B,MAAI,CAAC,QAAS,QAAO;AACrB,MAAI,SAAS,QAAS,QAAO;AAC7B,MAAI,CAAC,SAAS,OAAO,EAAG,QAAO;AAC/B,QAAM,UAAU,IAAI,YAAY,EAAE,OAAO,aAAa,OAAO,CAAC;AAC9D,QAAM,QAAQ,QAAQ,MAAM,GAAG;AAC/B,SAAO,CAAC,MAAM,CAAC,GAAG,MAAM,MAAM,CAAC,EAAE,KAAK,GAAG,CAAC;AAC5C;;;ACnDA,IAAIC;;AACJ,IAAI,OAAO,cAAc,eAAe,GAACC,OAAAC,MAAA,UAAU,cAAV,gBAAAA,IAAqB,eAArB,gBAAAD,IAAA,KAAAC,KAAkC,kBAAiB;AACxF,QAAM,OAAO;AACb,QAAM,UAAU;AAChB,EAAAF,cAAa,GAAG,IAAI,IAAI,OAAO;AACnC;AACA,SAAS,gBAAgB,OAAO,UAAU;AACtC,MAAI,SAAS,MAAM;AACf,WAAO;EACX;AACA,MAAI;AACA,WAAQ,iBAAiB,YACrB,OAAO,eAAe,KAAK,EAAE,OAAO,WAAW,MAAM,SAAS,UAAU,OAAO,WAAW;EAClG,QACM;AACF,WAAO;EACX;AACJ;AACO,IAAM,YAAY,OAAO;AACzB,IAAM,iBAAiB,OAAO;AAC9B,IAAM,cAAc,OAAO;AAC3B,IAAM,eAAe,OAAO;AACnC,IAAMG,WAAU,IAAI,YAAY;AAChC,IAAMC,WAAU,IAAI,YAAY;AAChC,SAAS,IAAI,OAAO;AAChB,MAAI,OAAO,UAAU,UAAU;AAC3B,WAAOD,SAAQ,OAAO,KAAK;EAC/B;AACA,SAAOC,SAAQ,OAAO,KAAK;AAC/B;AACA,IAAM,aAAa;AACnB,SAAS,gBAAgB,OAAO;AAC5B,MAAI,iBAAiB,aAAa;AAC9B,YAAQ,IAAI,WAAW,KAAK;EAChC;AACA,QAAM,MAAM,CAAC;AACb,WAAS,IAAI,GAAG,IAAI,MAAM,YAAY,KAAK,YAAY;AACnD,QAAI,KAAK,OAAO,aAAa,MAAM,MAAM,MAAM,SAAS,GAAG,IAAI,UAAU,CAAC,CAAC;EAC/E;AACA,SAAO,KAAK,IAAI,KAAK,EAAE,CAAC,EAAE,QAAQ,MAAM,EAAE,EAAE,QAAQ,OAAO,GAAG,EAAE,QAAQ,OAAO,GAAG;AACtF;AACA,SAAS,gBAAgB,OAAO;AAC5B,MAAI;AACA,UAAM,SAAS,KAAK,MAAM,QAAQ,MAAM,GAAG,EAAE,QAAQ,MAAM,GAAG,EAAE,QAAQ,OAAO,EAAE,CAAC;AAClF,UAAM,QAAQ,IAAI,WAAW,OAAO,MAAM;AAC1C,aAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACpC,YAAM,CAAC,IAAI,OAAO,WAAW,CAAC;IAClC;AACA,WAAO;EACX,SACO,OAAO;AACV,UAAM,IAAI,IAAI,qDAAqD,EAAE,MAAM,CAAC;EAChF;AACJ;AACA,SAAS,KAAK,OAAO;AACjB,MAAI,OAAO,UAAU,UAAU;AAC3B,WAAO,gBAAgB,KAAK;EAChC;AACA,SAAO,gBAAgB,KAAK;AAChC;AACA,IAAM,MAAN,MAAU;EACN,YAAY,SAAS;AACjB,SAAK,QAAQ,oBAAI,IAAI;AACrB,SAAK,SAAS,oBAAI,IAAI;AACtB,SAAK,UAAU;EACnB;EACA,IAAI,KAAK;AACL,QAAI,IAAI,KAAK,MAAM,IAAI,GAAG;AAC1B,QAAI,GAAG;AACH,aAAO;IACX;AACA,QAAK,IAAI,KAAK,OAAO,IAAI,GAAG,GAAI;AAC5B,WAAK,OAAO,KAAK,CAAC;AAClB,aAAO;IACX;AACA,WAAO;EACX;EACA,IAAI,KAAK;AACL,WAAO,KAAK,MAAM,IAAI,GAAG,KAAK,KAAK,OAAO,IAAI,GAAG;EACrD;EACA,IAAI,KAAK,OAAO;AACZ,QAAI,KAAK,MAAM,IAAI,GAAG,GAAG;AACrB,WAAK,MAAM,IAAI,KAAK,KAAK;IAC7B,OACK;AACD,WAAK,OAAO,KAAK,KAAK;IAC1B;AACA,WAAO;EACX;EACA,OAAO,KAAK;AACR,QAAI,KAAK,MAAM,IAAI,GAAG,GAAG;AACrB,aAAO,KAAK,MAAM,OAAO,GAAG;IAChC;AACA,QAAI,KAAK,OAAO,IAAI,GAAG,GAAG;AACtB,aAAO,KAAK,OAAO,OAAO,GAAG;IACjC;AACA,WAAO;EACX;EACA,OAAO,KAAK,OAAO;AACf,SAAK,MAAM,IAAI,KAAK,KAAK;AACzB,QAAI,KAAK,MAAM,QAAQ,KAAK,SAAS;AACjC,WAAK,SAAS,KAAK;AACnB,WAAK,QAAQ,oBAAI,IAAI;IACzB;EACJ;AACJ;AACO,IAAM,4BAAN,cAAwC,MAAM;EACjD,YAAYC,UAAS;;AACjB,UAAMA,YAAW,yBAAyB;AAC1C,SAAK,OAAO,KAAK,YAAY;AAC7B,KAAAH,MAAA,MAAM,sBAAN,gBAAAA,IAAA,YAA0B,MAAM,KAAK;EACzC;AACJ;AACO,IAAM,2BAAN,cAAuC,MAAM;EAChD,YAAYG,UAAS,SAAS;;AAC1B,UAAMA,UAAS,OAAO;AACtB,SAAK,OAAO,KAAK,YAAY;AAC7B,KAAAH,MAAA,MAAM,sBAAN,gBAAAA,IAAA,YAA0B,MAAM,KAAK;EACzC;AACJ;AACA,IAAM,MAAM;AACZ,IAAM,aAAa,IAAI,IAAI,GAAG;AAC9B,SAASI,aAAY,KAAK;AACtB,SAAO,eAAe;AAC1B;AACA,SAAS,aAAa,KAAK;AACvB,SAAOA,aAAY,GAAG,KAAK,IAAI,SAAS;AAC5C;AACA,SAAS,YAAY,KAAK;AACtB,SAAOA,aAAY,GAAG,KAAK,IAAI,SAAS;AAC5C;AAaA,SAAS,iBAAiB,UAAU;AAChC,MAAI;AACA,UAAM,QAAQ,SAAS,QAAQ,IAAI,YAAY;AAC/C,QAAI,OAAO;AACP,iBAAW,IAAI,IAAI,IAAI,SAAS,GAAG,EAAE,QAAQ,KAAK;IACtD;EACJ,QACM;EAAE;AACR,SAAO;AACX;AAIA,SAAS,aAAa,OAAO;AACzB,MAAI,UAAU,QAAQ,OAAO,UAAU,YAAY,MAAM,QAAQ,KAAK,GAAG;AACrE,WAAO;EACX;AACA,SAAO;AACX;AACA,SAAS,eAAe,OAAO;AAC3B,MAAI,gBAAgB,OAAO,OAAO,GAAG;AACjC,YAAQ,OAAO,YAAY,MAAM,QAAQ,CAAC;EAC9C;AACA,QAAM,UAAU,IAAI,QAAQ,KAAK;AACjC,MAAIN,eAAc,CAAC,QAAQ,IAAI,YAAY,GAAG;AAC1C,YAAQ,IAAI,cAAcA,WAAU;EACxC;AACA,MAAI,QAAQ,IAAI,eAAe,GAAG;AAC9B,UAAM,IAAI,UAAU,oEAAoE;EAC5F;AACA,MAAI,QAAQ,IAAI,MAAM,GAAG;AACrB,UAAM,IAAI,UAAU,2DAA2D;EACnF;AACA,SAAO;AACX;AACA,SAAS,OAAO,OAAO;AACnB,MAAI,OAAO,UAAU,YAAY;AAC7B,YAAQ,MAAM;EAClB;AACA,MAAI,EAAE,iBAAiB,cAAc;AACjC,UAAM,IAAI,UAAU,+DAA+D;EACvF;AACA,SAAO;AACX;AAkCA,SAAS,eAAe,OAAO;AAC3B,SAAO,OAAO,UAAU,YAAY,MAAM,WAAW;AACzD;AA8BA,SAAS,cAAc;AACnB,SAAO,KAAK,OAAO,gBAAgB,IAAI,WAAW,EAAE,CAAC,CAAC;AAC1D;AAgBA,SAAS,aAAa,OAAO;AACzB,MAAI,iBAAiB,WAAW;AAC5B,WAAO,EAAE,KAAK,MAAM;EACxB;AACA,MAAI,GAAE,+BAAO,gBAAe,YAAY;AACpC,WAAO,CAAC;EACZ;AACA,MAAI,MAAM,QAAQ,UAAa,CAAC,eAAe,MAAM,GAAG,GAAG;AACvD,UAAM,IAAI,UAAU,kCAAkC;EAC1D;AACA,SAAO,EAAE,KAAK,MAAM,KAAK,KAAK,MAAM,IAAI;AAC5C;AACA,SAAS,cAAc,OAAO;AAC1B,SAAO,mBAAmB,KAAK,EAAE,QAAQ,QAAQ,GAAG;AACxD;AACA,SAAS,kBAAkB,UAAU,cAAc;AAC/C,QAAM,WAAW,cAAc,QAAQ;AACvC,QAAM,WAAW,cAAc,YAAY;AAC3C,QAAM,cAAc,KAAK,GAAG,QAAQ,IAAI,QAAQ,EAAE;AAClD,SAAO,SAAS,WAAW;AAC/B;AACA,SAAS,MAAM,KAAK;AAChB,UAAQ,IAAI,UAAU,KAAK,MAAM;IAC7B,KAAK;AACD,aAAO;IACX,KAAK;AACD,aAAO;IACX,KAAK;AACD,aAAO;IACX;AACI,YAAM,IAAI,0BAA0B,6CAA6C;EACzF;AACJ;AACA,SAAS,MAAM,KAAK;AAChB,UAAQ,IAAI,UAAU,KAAK,MAAM;IAC7B,KAAK;AACD,aAAO;IACX,KAAK;AACD,aAAO;IACX,KAAK;AACD,aAAO;IACX;AACI,YAAM,IAAI,0BAA0B,6CAA6C;EACzF;AACJ;AACA,SAAS,MAAM,KAAK;AAChB,UAAQ,IAAI,UAAU,YAAY;IAC9B,KAAK;AACD,aAAO;IACX,KAAK;AACD,aAAO;IACX,KAAK;AACD,aAAO;IACX;AACI,YAAM,IAAI,0BAA0B,uCAAuC;EACnF;AACJ;AACA,SAAS,SAAS,KAAK;AACnB,UAAQ,IAAI,UAAU,MAAM;IACxB,KAAK;AACD,aAAO,MAAM,GAAG;IACpB,KAAK;AACD,aAAO,MAAM,GAAG;IACpB,KAAK;AACD,aAAO,MAAM,GAAG;IACpB,KAAK;IACL,KAAK;AACD,aAAO;IACX;AACI,YAAM,IAAI,0BAA0B,sCAAsC;EAClF;AACJ;AACA,SAAS,aAAa,QAAQ;AAC1B,QAAM,OAAO,iCAAS;AACtB,SAAO,OAAO,SAAS,YAAY,OAAO,SAAS,IAAI,IAAI,OAAO;AACtE;AACA,SAAS,kBAAkB,QAAQ;AAC/B,QAAM,YAAY,iCAAS;AAC3B,SAAO,OAAO,cAAc,YAAY,OAAO,SAAS,SAAS,KAAK,KAAK,KAAK,SAAS,MAAM,KACzF,YACA;AACV;AACA,SAAS,YAAY;AACjB,SAAO,KAAK,MAAM,KAAK,IAAI,IAAI,GAAI;AACvC;AACA,SAAS,gBAAgB,IAAI,QAAQ;AACjC,QAAM,MAAM,UAAU,IAAI,aAAa,MAAM;AAC7C,SAAO;IACH,KAAK,YAAY;IACjB,KAAK,CAAC,GAAG,QAAQ,GAAG,cAAc;IAClC,KAAK,MAAM;IACX,KAAK;IACL,KAAK;IACL,KAAK,OAAO;IACZ,KAAK,OAAO;EAChB;AACJ;AACA,eAAe,cAAc,IAAI,QAAQ,KAAK,KAAK;AAC/C,SAAO,IAAI;IACP,KAAK,SAAS,GAAG;IACjB;EACJ,GAAG,gBAAgB,IAAI,MAAM,GAAG,GAAG;AACvC;AACA,SAAS,SAAS,IAAI;AAClB,MAAI,OAAO,OAAO,YAAY,OAAO,MAAM;AACvC,UAAM,IAAI,UAAU,wBAAwB;EAChD;AACA,MAAI,CAAC,eAAe,GAAG,MAAM,GAAG;AAC5B,UAAM,IAAI,UAAU,iDAAiD;EACzE;AACA,SAAO;AACX;AACA,SAAS,aAAa,QAAQ;AAC1B,MAAI,OAAO,WAAW,YAAY,WAAW,MAAM;AAC/C,UAAM,IAAI,UAAU,4BAA4B;EACpD;AACA,MAAI,CAAC,eAAe,OAAO,SAAS,GAAG;AACnC,UAAM,IAAI,UAAU,wDAAwD;EAChF;AACA,SAAO;AACX;AACA,SAAS,mBAAmB,cAAc;AACtC,MAAI,CAAC,eAAe,YAAY,GAAG;AAC/B,UAAM,IAAI,UAAU,4DAA4D;EACpF;AACA,SAAO;AACX;AACA,SAAS,yBAAyB,kBAAkB,kBAAkB;AAClE,MAAI,qBAAqB,QAAW;AAChC,UAAM,IAAI,UAAU,iEAAiE,gBAAgB,wCAAwC;EACjJ;AACJ;AACA,SAAS,qBAAqB,kBAAkB,cAAc;AAC1D,MAAI,iBAAiB,QAAW;AAC5B,UAAM,IAAI,UAAU,6DAA6D,gBAAgB,wCAAwC;EAC7I;AACJ;AACA,eAAe,qBAAqB,IAAI,QAAQ,MAAM,SAAS,kBAAkB;AAC7E,OAAK,OAAO,eAAe;AAC3B,OAAK,OAAO,uBAAuB;AACnC,OAAK,OAAO,kBAAkB;AAC9B,UAAQ,OAAO,4BAA4B;IACvC,KAAK;IACL,KAAK,uBAAuB;AACxB,+BAAyB,uBAAuB,gBAAgB;AAChE,cAAQ,IAAI,iBAAiB,kBAAkB,OAAO,WAAW,mBAAmB,OAAO,aAAa,CAAC,CAAC;AAC1G;IACJ;IACA,KAAK,sBAAsB;AACvB,+BAAyB,sBAAsB,gBAAgB;AAC/D,WAAK,IAAI,aAAa,OAAO,SAAS;AACtC,WAAK,IAAI,iBAAiB,mBAAmB,OAAO,aAAa,CAAC;AAClE;IACJ;IACA,KAAK,mBAAmB;AACpB,2BAAqB,mBAAmB,OAAO,aAAa;AAC5D,UAAI,qBAAqB,QAAW;AAChC,cAAM,IAAI,UAAU,2GAA2G;MACnI;AACA,YAAM,EAAE,KAAK,IAAI,IAAI,aAAa,gBAAgB;AAClD,UAAI,CAAC,aAAa,GAAG,GAAG;AACpB,cAAM,IAAI,UAAU,4DAA4D;MACpF;AACA,WAAK,IAAI,aAAa,OAAO,SAAS;AACtC,WAAK,IAAI,yBAAyB,wDAAwD;AAC1F,WAAK,IAAI,oBAAoB,MAAM,cAAc,IAAI,QAAQ,KAAK,GAAG,CAAC;AACtE;IACJ;IACA,KAAK;IACL,KAAK;IACL,KAAK,QAAQ;AACT,2BAAqB,OAAO,4BAA4B,OAAO,aAAa;AAC5E,+BAAyB,OAAO,4BAA4B,gBAAgB;AAC5E,WAAK,IAAI,aAAa,OAAO,SAAS;AACtC;IACJ;IACA;AACI,YAAM,IAAI,0BAA0B,+CAA+C;EAC3F;AACJ;AACA,eAAe,IAAI,QAAQ,WAAW,KAAK;AACvC,MAAI,CAAC,IAAI,OAAO,SAAS,MAAM,GAAG;AAC9B,UAAM,IAAI,UAAU,uFAAuF;EAC/G;AACA,QAAM,QAAQ,GAAG,KAAK,IAAI,KAAK,UAAU,MAAM,CAAC,CAAC,CAAC,IAAI,KAAK,IAAI,KAAK,UAAU,SAAS,CAAC,CAAC,CAAC;AAC1F,QAAM,YAAY,KAAK,MAAM,OAAO,OAAO,KAAK,YAAY,GAAG,GAAG,KAAK,IAAI,KAAK,CAAC,CAAC;AAClF,SAAO,GAAG,KAAK,IAAI,SAAS;AAChC;AAqEA,eAAe,aAAa,SAAS,SAAS,KAAK,KAAKO,YAAW,aAAa;AAC5E,QAAM,EAAE,YAAY,WAAW,QAAQ,WAAW,IAAI,IAAI,MAAM,EAAE,IAAI;AACtE,MAAI,CAAC,aAAa,UAAU,GAAG;AAC3B,UAAM,IAAI,UAAU,+CAA+C;EACvE;AACA,MAAI,CAAC,YAAY,SAAS,GAAG;AACzB,UAAM,IAAI,UAAU,6CAA6C;EACrE;AACA,MAAI,UAAU,UAAa,CAAC,eAAe,KAAK,GAAG;AAC/C,UAAM,IAAI,UAAU,sDAAsD;EAC9E;AACA,MAAI,CAAC,UAAU,aAAa;AACxB,UAAM,IAAI,UAAU,2CAA2C;EACnE;AACA,QAAM,MAAM,UAAU,IAAIA;AAC1B,QAAM,QAAQ,MAAM,IAAI;IACpB,KAAK,SAAS,UAAU;IACxB,KAAK;IACL,KAAK,MAAM,UAAU,SAAS;EAClC,GAAG;IACC,KAAK;IACL,KAAK,YAAY;IACjB;IACA;IACA,KAAK,GAAG,IAAI,MAAM,GAAG,IAAI,QAAQ;IACjC,KAAK,cAAc,KAAK,MAAM,OAAO,OAAO,OAAO,WAAW,IAAI,WAAW,CAAC,CAAC,IAAI;EACvF,GAAG,UAAU;AACb,UAAQ,IAAI,QAAQ,KAAK;AAC7B;AACA,IAAI;AACJ,eAAe,qBAAqB,KAAK;AACrC,QAAM,EAAE,KAAK,GAAG,GAAG,GAAG,GAAG,IAAI,IAAI,MAAM,OAAO,OAAO,UAAU,OAAO,GAAG;AACzE,QAAM,MAAM,EAAE,KAAK,GAAG,GAAG,GAAG,GAAG,IAAI;AACnC,WAAS,IAAI,KAAK,GAAG;AACrB,SAAO;AACX;AACA,eAAe,UAAU,KAAK;AAC1B,eAAa,WAAW,oBAAI,QAAQ;AACpC,SAAO,SAAS,IAAI,GAAG,KAAK,qBAAqB,GAAG;AACxD;AACA,SAAS,iBAAiB,OAAO,UAAU,SAAS;AAChD,MAAI,OAAO,UAAU,UAAU;AAC3B,QAAI,mCAAU,eAAe;AACzB,YAAM,IAAI,UAAU,6BAA6B,QAAQ,oBAAoB;IACjF;AACA,UAAM,IAAI,UAAU,OAAO,QAAQ,oBAAoB;EAC3D;AACA,SAAO,IAAI,IAAI,KAAK;AACxB;AACA,SAAS,gBAAgB,IAAI,UAAU,SAAS;AAC5C,OAAI,mCAAU,kBAAiB,GAAG,yBAAyB,YAAY,GAAG,uBAAuB;AAC7F,WAAO,iBAAiB,GAAG,sBAAsB,QAAQ,GAAG,UAAU,OAAO;EACjF;AACA,SAAO,iBAAiB,GAAG,QAAQ,GAAG,QAAQ;AAClD;AAcO,SAAS,cAAc,OAAO;AACjC,QAAM,QAAQ;AACd,MAAI,OAAO,UAAU,YAAY,MAAM,QAAQ,KAAK,KAAK,UAAU,MAAM;AACrE,WAAO;EACX;AACA,SAAO,MAAM,UAAU;AAC3B;AAsNO,IAAM,mBAAmB,OAAO;AAoDvC,eAAe,qBAAqB,IAAI,QAAQ,QAAQ,KAAK,MAAM,SAAS,SAAS;AACjF,QAAM,qBAAqB,IAAI,QAAQ,MAAM,SAAS,mCAAS,gBAAgB;AAC/E,UAAQ,IAAI,gBAAgB,iDAAiD;AAC7E,WAAQ,mCAAU,iBAAgB,OAAO,IAAI,MAAM;IAC/C;IACA,SAAS,OAAO,YAAY,QAAQ,QAAQ,CAAC;IAC7C;IACA,UAAU;IACV,SAAQ,mCAAS,UAAS,OAAO,QAAQ,MAAM,IAAI;EACvD,CAAC,EAAE,KAAK,gBAAgB;AAC5B;AACA,eAAe,qBAAqB,IAAI,QAAQ,WAAW,YAAY,SAAS;AAC5E,QAAM,MAAM,gBAAgB,IAAI,kBAAkB,OAAO;AACzD,aAAW,IAAI,cAAc,SAAS;AACtC,QAAM,UAAU,eAAe,mCAAS,OAAO;AAC/C,UAAQ,IAAI,UAAU,kBAAkB;AACxC,OAAI,mCAAS,UAAS,QAAW;AAC7B,UAAM,aAAa,SAAS,QAAQ,MAAM,KAAK,QAAQ,aAAa,MAAM,CAAC;EAC/E;AACA,SAAO,qBAAqB,IAAI,QAAQ,QAAQ,KAAK,YAAY,SAAS,OAAO;AACrF;AACA,eAAsB,yBAAyB,IAAI,QAAQ,cAAc,SAAS;AAC9E,WAAS,EAAE;AACX,eAAa,MAAM;AACnB,MAAI,CAAC,eAAe,YAAY,GAAG;AAC/B,UAAM,IAAI,UAAU,2CAA2C;EACnE;AACA,QAAM,aAAa,IAAI,gBAAgB,mCAAS,oBAAoB;AACpE,aAAW,IAAI,iBAAiB,YAAY;AAC5C,SAAO,qBAAqB,IAAI,QAAQ,iBAAiB,YAAY,OAAO;AAChF;AACA,IAAM,gBAAgB,oBAAI,QAAQ;AAWlC,eAAe,kCAAkC,IAAI,QAAQ,UAAU,gBAAgB,OAAO,qBAAqB,OAAO;AACtH,WAAS,EAAE;AACX,eAAa,MAAM;AACnB,MAAI,CAAC,gBAAgB,UAAU,QAAQ,GAAG;AACtC,UAAM,IAAI,UAAU,4CAA4C;EACpE;AACA,MAAI,SAAS,WAAW,KAAK;AACzB,QAAI;AACJ,QAAK,MAAM,MAAM,qBAAqB,QAAQ,GAAI;AAC9C,aAAO;IACX;AACA,UAAM,IAAI,IAAI,qDAAqD;EACvE;AACA,yBAAuB,QAAQ;AAC/B,MAAI;AACJ,MAAI;AACA,WAAO,MAAM,SAAS,KAAK;EAC/B,SACO,OAAO;AACV,UAAM,IAAI,IAAI,2CAA2C,EAAE,MAAM,CAAC;EACtE;AACA,MAAI,CAAC,aAAa,IAAI,GAAG;AACrB,UAAM,IAAI,IAAI,4CAA4C;EAC9D;AACA,MAAI,CAAC,eAAe,KAAK,YAAY,GAAG;AACpC,UAAM,IAAI,IAAI,oEAAoE;EACtF;AACA,MAAI,CAAC,eAAe,KAAK,UAAU,GAAG;AAClC,UAAM,IAAI,IAAI,kEAAkE;EACpF;AACA,OAAK,aAAa,KAAK,WAAW,YAAY;AAC9C,MAAI,KAAK,eAAe,UAAU,KAAK,eAAe,UAAU;AAC5D,UAAM,IAAI,0BAA0B,gCAAgC;EACxE;AACA,MAAI,KAAK,eAAe,WACnB,OAAO,KAAK,eAAe,YAAY,KAAK,cAAc,IAAI;AAC/D,UAAM,IAAI,IAAI,iEAAiE;EACnF;AACA,MAAI,CAAC,sBACD,KAAK,kBAAkB,UACvB,CAAC,eAAe,KAAK,aAAa,GAAG;AACrC,UAAM,IAAI,IAAI,qEAAqE;EACvF;AACA,MAAI,KAAK,UAAU,UAAa,OAAO,KAAK,UAAU,UAAU;AAC5D,UAAM,IAAI,IAAI,mDAAmD;EACrE;AACA,MAAI,CAAC,eAAe;AAChB,QAAI,KAAK,aAAa,UAAa,CAAC,eAAe,KAAK,QAAQ,GAAG;AAC/D,YAAM,IAAI,IAAI,gEAAgE;IAClF;AACA,QAAI,KAAK,UAAU;AACf,YAAM,EAAE,OAAO,IAAI,MAAM,YAAY,KAAK,UAAU,sBAAsB,KAAK,QAAW,OAAO,8BAA8B,GAAG,qCAAqC,GAAG,kBAAkB,aAAa,MAAM,GAAG,kBAAkB,MAAM,CAAC,EACtO,KAAK,iBAAiB,KAAK,QAAW,CAAC,OAAO,OAAO,OAAO,OAAO,KAAK,CAAC,CAAC,EAC1E,KAAK,eAAe,KAAK,QAAW,GAAG,MAAM,CAAC,EAC9C,KAAK,iBAAiB,KAAK,QAAW,OAAO,SAAS,CAAC;AAC5D,UAAI,MAAM,QAAQ,OAAO,GAAG,KAAK,OAAO,IAAI,WAAW,KAAK,OAAO,QAAQ,OAAO,WAAW;AACzF,cAAM,IAAI,IAAI,0DAA0D;MAC5E;AACA,UAAI,OAAO,qBAAqB,OAAO,OAAO,cAAc,UAAU;AAClE,cAAM,IAAI,IAAI,mEAAmE;MACrF;AACA,oBAAc,IAAI,MAAM,MAAM;IAClC;EACJ;AACA,SAAO;AACX;AACA,eAAsB,4BAA4B,IAAI,QAAQ,UAAU;AACpE,SAAO,kCAAkC,IAAI,QAAQ,QAAQ;AACjE;AAOA,SAAS,iBAAiB,UAAU,QAAQ;AACxC,MAAI,MAAM,QAAQ,OAAO,OAAO,GAAG,GAAG;AAClC,QAAI,CAAC,OAAO,OAAO,IAAI,SAAS,QAAQ,GAAG;AACvC,YAAM,IAAI,IAAI,6CAA6C;IAC/D;EACJ,WACS,OAAO,OAAO,QAAQ,UAAU;AACrC,UAAM,IAAI,IAAI,6CAA6C;EAC/D;AACA,SAAO;AACX;AAOA,SAAS,eAAe,UAAU,QAAQ;AACtC,MAAI,OAAO,OAAO,QAAQ,UAAU;AAChC,UAAM,IAAI,IAAI,2CAA2C;EAC7D;AACA,SAAO;AACX;AACA,IAAM,UAAU,oBAAI,QAAQ;AAC5B,SAAS,MAAM,cAAc;AACzB,UAAQ,IAAI,YAAY;AACxB,SAAO;AACX;AACA,eAAsB,8BAA8B,IAAI,QAAQ,oBAAoB,aAAa,cAAc,SAAS;AACpH,WAAS,EAAE;AACX,eAAa,MAAM;AACnB,MAAI,CAAC,QAAQ,IAAI,kBAAkB,GAAG;AAClC,UAAM,IAAI,UAAU,mIAAmI;EAC3J;AACA,MAAI,CAAC,eAAe,WAAW,GAAG;AAC9B,UAAM,IAAI,UAAU,0CAA0C;EAClE;AACA,MAAI,CAAC,eAAe,YAAY,GAAG;AAC/B,UAAM,IAAI,UAAU,2CAA2C;EACnE;AACA,QAAM,OAAO,sBAAsB,oBAAoB,MAAM;AAC7D,MAAI,CAAC,MAAM;AACP,UAAM,IAAI,IAAI,+CAA+C;EACjE;AACA,QAAM,aAAa,IAAI,gBAAgB,mCAAS,oBAAoB;AACpE,aAAW,IAAI,gBAAgB,WAAW;AAC1C,aAAW,IAAI,iBAAiB,YAAY;AAC5C,aAAW,IAAI,QAAQ,IAAI;AAC3B,SAAO,qBAAqB,IAAI,QAAQ,sBAAsB,YAAY,OAAO;AACrF;AACA,IAAM,gBAAgB;EAClB,KAAK;EACL,QAAQ;EACR,WAAW;EACX,KAAK;EACL,KAAK;EACL,KAAK;EACL,KAAK;EACL,OAAO;EACP,QAAQ;EACR,KAAK;EACL,KAAK;EACL,KAAK;EACL,KAAK;EACL,KAAK;AACT;AACA,SAAS,iBAAiB,UAAU,QAAQ;AACxC,aAAW,SAAS,UAAU;AAC1B,QAAI,OAAO,OAAO,KAAK,MAAM,QAAW;AACpC,YAAM,IAAI,IAAI,QAAQ,KAAK,MAAM,cAAc,KAAK,CAAC,iBAAiB;IAC1E;EACJ;AACA,SAAO;AACX;AACO,IAAM,gBAAgB,OAAO;AAC7B,IAAM,oBAAoB,OAAO;AA6CxC,eAAsB,uCAAuC,IAAI,QAAQ,UAAU;AAC/E,QAAM,SAAS,MAAM,kCAAkC,IAAI,QAAQ,UAAU,IAAI;AACjF,MAAI,cAAc,MAAM,GAAG;AACvB,WAAO;EACX;AACA,MAAI,OAAO,aAAa,QAAW;AAC/B,QAAI,OAAO,OAAO,aAAa,YAAY,OAAO,SAAS,QAAQ;AAC/D,YAAM,IAAI,IAAI,mHAAmH;IACrI;AACA,WAAO,OAAO;EAClB;AACA,SAAO;AACX;AA6CA,SAAS,uBAAuB,UAAU;AACtC,MAAI,SAAS,UAAU;AACnB,UAAM,IAAI,UAAU,uCAAuC;EAC/D;AACJ;AAqGA,eAAe,qBAAqB,UAAU;AAC1C,MAAI,SAAS,SAAS,OAAO,SAAS,SAAS,KAAK;AAChD,2BAAuB,QAAQ;AAC/B,QAAI;AACA,YAAM,OAAO,MAAM,SAAS,KAAK;AACjC,UAAI,aAAa,IAAI,KAAK,OAAO,KAAK,UAAU,YAAY,KAAK,MAAM,QAAQ;AAC3E,YAAI,KAAK,sBAAsB,UAAa,OAAO,KAAK,sBAAsB,UAAU;AACpF,iBAAO,KAAK;QAChB;AACA,YAAI,KAAK,cAAc,UAAa,OAAO,KAAK,cAAc,UAAU;AACpE,iBAAO,KAAK;QAChB;AACA,YAAI,KAAK,SAAS,UAAa,OAAO,KAAK,SAAS,UAAU;AAC1D,iBAAO,KAAK;QAChB;AACA,YAAI,KAAK,UAAU,UAAa,OAAO,KAAK,UAAU,UAAU;AAC5D,iBAAO,KAAK;QAChB;AACA,eAAO;MACX;IACJ,QACM;IAAE;EACZ;AACA,SAAO;AACX;AAOA,SAAS,qBAAqB,WAAW;AACrC,MAAI,OAAO,UAAU,kBAAkB,YAAY,UAAU,gBAAgB,MAAM;AAC/E,UAAM,IAAI,IAAI,GAAG,UAAU,IAAI,2CAA2C;EAC9E;AACJ;AACA,SAAS,cAAc,YAAY;AAC/B,UAAQ,YAAY;IAChB,KAAK;AACD,aAAO;IACX,KAAK;AACD,aAAO;IACX,KAAK;AACD,aAAO;IACX;AACI,YAAM,IAAI,0BAA0B;EAC5C;AACJ;AACA,SAAS,YAAY,KAAK;AACtB,UAAQ,IAAI,UAAU,MAAM;IACxB,KAAK;AACD,aAAO;QACH,MAAM,IAAI,UAAU;QACpB,MAAM,cAAc,IAAI,UAAU,UAAU;MAChD;IACJ,KAAK,WAAW;AACZ,2BAAqB,IAAI,SAAS;AAClC,cAAQ,IAAI,UAAU,KAAK,MAAM;QAC7B,KAAK;QACL,KAAK;QACL,KAAK;AACD,iBAAO;YACH,MAAM,IAAI,UAAU;YACpB,YAAY,SAAS,IAAI,UAAU,KAAK,KAAK,MAAM,EAAE,GAAG,EAAE,KAAK;UACnE;QACJ;AACI,gBAAM,IAAI,0BAA0B;MAC5C;IACJ;IACA,KAAK;AACD,2BAAqB,IAAI,SAAS;AAClC,aAAO,IAAI,UAAU;IACzB,KAAK;IACL,KAAK;AACD,aAAO,IAAI,UAAU;EAC7B;AACA,QAAM,IAAI,0BAA0B;AACxC;AACA,IAAM,mBAAmB,OAAO;AAChC,eAAe,YAAY,KAAK,UAAU,QAAQA,YAAWC,iBAAgB;AACzE,QAAM,EAAE,GAAG,iBAAiB,GAAG,SAAS,GAAG,kBAAkB,OAAO,IAAI,IAAI,MAAM,GAAG;AACrF,MAAI,WAAW,GAAG;AACd,UAAM,IAAI,0BAA0B,sCAAsC;EAC9E;AACA,MAAI,WAAW,GAAG;AACd,UAAM,IAAI,IAAI,aAAa;EAC/B;AACA,MAAI;AACJ,MAAI;AACA,aAAS,KAAK,MAAM,IAAI,KAAK,eAAe,CAAC,CAAC;EAClD,SACO,OAAO;AACV,UAAM,IAAI,IAAI,6DAA6D,EAAE,MAAM,CAAC;EACxF;AACA,MAAI,CAAC,aAAa,MAAM,GAAG;AACvB,UAAM,IAAI,IAAI,uCAAuC;EACzD;AACA,WAAS,MAAM;AACf,MAAI,OAAO,SAAS,QAAW;AAC3B,UAAM,IAAI,IAAI,wCAAwC;EAC1D;AACA,QAAM,YAAY,KAAK,gBAAgB;AACvC,MAAI;AACJ,MAAI,WAAW,kBAAkB;AAC7B,UAAM,MAAM,OAAO,MAAM;AACzB,UAAM,QAAQ,GAAG,eAAe,IAAI,OAAO;AAC3C,UAAM,WAAW,MAAM,OAAO,OAAO,OAAO,YAAY,GAAG,GAAG,KAAK,WAAW,IAAI,KAAK,CAAC;AACxF,QAAI,CAAC,UAAU;AACX,YAAM,IAAI,IAAI,mCAAmC;IACrD;EACJ;AACA,MAAI;AACJ,MAAI;AACA,aAAS,KAAK,MAAM,IAAI,KAAK,OAAO,CAAC,CAAC;EAC1C,SACO,OAAO;AACV,UAAM,IAAI,IAAI,8DAA8D,EAAE,MAAM,CAAC;EACzF;AACA,MAAI,CAAC,aAAa,MAAM,GAAG;AACvB,UAAM,IAAI,IAAI,wCAAwC;EAC1D;AACA,QAAM,MAAM,UAAU,IAAID;AAC1B,MAAI,OAAO,QAAQ,QAAW;AAC1B,QAAI,OAAO,OAAO,QAAQ,UAAU;AAChC,YAAM,IAAI,IAAI,mDAAmD;IACrE;AACA,QAAI,OAAO,OAAO,MAAMC,iBAAgB;AACpC,YAAM,IAAI,IAAI,2EAA2E;IAC7F;EACJ;AACA,MAAI,OAAO,QAAQ,QAAW;AAC1B,QAAI,OAAO,OAAO,QAAQ,UAAU;AAChC,YAAM,IAAI,IAAI,6CAA6C;IAC/D;EACJ;AACA,MAAI,OAAO,QAAQ,QAAW;AAC1B,QAAI,OAAO,OAAO,QAAQ,UAAU;AAChC,YAAM,IAAI,IAAI,0CAA0C;IAC5D;EACJ;AACA,MAAI,OAAO,QAAQ,QAAW;AAC1B,QAAI,OAAO,OAAO,QAAQ,UAAU;AAChC,YAAM,IAAI,IAAI,8CAA8C;IAChE;AACA,QAAI,OAAO,MAAM,MAAMA,iBAAgB;AACnC,YAAM,IAAI,IAAI,qEAAqE;IACvF;EACJ;AACA,MAAI,OAAO,QAAQ,QAAW;AAC1B,QAAI,OAAO,OAAO,QAAQ,YAAY,CAAC,MAAM,QAAQ,OAAO,GAAG,GAAG;AAC9D,YAAM,IAAI,IAAI,4CAA4C;IAC9D;EACJ;AACA,SAAO,EAAE,QAAQ,QAAQ,WAAW,IAAI;AAC5C;AAsKA,SAAS,sBAAsB,QAAQ,QAAQ,QAAQ;AACnD,MAAI,WAAW,QAAW;AACtB,QAAI,OAAO,QAAQ,QAAQ;AACvB,YAAM,IAAI,IAAI,uCAAuC;IACzD;AACA;EACJ;AACA,MAAI,MAAM,QAAQ,MAAM,GAAG;AACvB,QAAI,CAAC,OAAO,SAAS,OAAO,GAAG,GAAG;AAC9B,YAAM,IAAI,IAAI,uCAAuC;IACzD;AACA;EACJ;AACA,MAAI,OAAO,QAAQ,SAAS;AACxB,UAAM,IAAI,IAAI,uCAAuC;EACzD;AACJ;AACA,SAAS,sBAAsB,YAAY,MAAM;AAC7C,QAAM,EAAE,GAAG,OAAO,OAAO,IAAI,WAAW,OAAO,IAAI;AACnD,MAAI,SAAS,GAAG;AACZ,UAAM,IAAI,IAAI,IAAI,IAAI,wCAAwC;EAClE;AACA,SAAO;AACX;AACO,IAAM,iBAAiB,OAAO;AAC9B,IAAM,gBAAgB,OAAO;AAC7B,SAAS,qBAAqB,IAAI,QAAQ,YAAY,eAAe;AACxE,WAAS,EAAE;AACX,eAAa,MAAM;AACnB,MAAI,sBAAsB,KAAK;AAC3B,iBAAa,WAAW;EAC5B;AACA,MAAI,EAAE,sBAAsB,kBAAkB;AAC1C,UAAM,IAAI,UAAU,6DAA6D;EACrF;AACA,MAAI,sBAAsB,YAAY,UAAU,GAAG;AAC/C,UAAM,IAAI,IAAI,wGAAwG;EAC1H;AACA,QAAM,MAAM,sBAAsB,YAAY,KAAK;AACnD,QAAM,QAAQ,sBAAsB,YAAY,OAAO;AACvD,MAAI,CAAC,OAAO,GAAG,gDAAgD;AAC3D,UAAM,IAAI,IAAI,2CAA2C;EAC7D;AACA,MAAI,OAAO,QAAQ,GAAG,QAAQ;AAC1B,UAAM,IAAI,IAAI,oDAAoD;EACtE;AACA,UAAQ,eAAe;IACnB,KAAK;IACL,KAAK;AACD,UAAI,UAAU,QAAW;AACrB,cAAM,IAAI,IAAI,mDAAmD;MACrE;AACA;IACJ,KAAK;AACD;IACJ;AACI,UAAI,CAAC,eAAe,aAAa,GAAG;AAChC,cAAM,IAAI,IAAI,4CAA4C;MAC9D;AACA,UAAI,UAAU,QAAW;AACrB,cAAM,IAAI,IAAI,oCAAoC;MACtD;AACA,UAAI,UAAU,eAAe;AACzB,cAAM,IAAI,IAAI,6CAA6C;MAC/D;EACR;AACA,QAAM,QAAQ,sBAAsB,YAAY,OAAO;AACvD,MAAI,OAAO;AACP,WAAO;MACH;MACA,mBAAmB,sBAAsB,YAAY,mBAAmB;MACxE,WAAW,sBAAsB,YAAY,WAAW;IAC5D;EACJ;AACA,QAAM,WAAW,sBAAsB,YAAY,UAAU;AAC7D,QAAM,QAAQ,sBAAsB,YAAY,OAAO;AACvD,MAAI,aAAa,UAAa,UAAU,QAAW;AAC/C,UAAM,IAAI,0BAA0B,6CAA6C;EACrF;AACA,SAAO,MAAM,IAAI,gBAAgB,UAAU,CAAC;AAChD;ACzlDO,IAAM,uBAAN,MAA2B;EAChC,YAA4B,SAAiC;AAAjC,SAAA,UAAA;EAE5B;EAEA,IAAI,YAAY;AACd,WAAO,KAAK,QAAQ;EACtB;EAEA,YAAY;AACV,WAAO,KAAK,QAAQ,WAAW,IAAI;EACrC;EAEA,MAAa,sBAAsB,SAAkC,aAA6C;;AAChH,UAAM,aAAa,OAAO,OAA4B;AACpD,UAAI;AACF,cAAM,GAAG;AACT,eAAO;MACT,SAAS,GAAG;AACV,eAAO,GAAG,CAAC;MACb;IACF;AACA,UAAM,UAAU,MAAM,WAAW,YAAY;AAC3C,YAAM,MAAM,MAAM,MAAM,+BAA+B;AACvD,UAAI,CAAC,IAAI,IAAI;AACX,cAAM,IAAI,MAAM,GAAG,IAAI,MAAM,IAAI,IAAI,UAAU,KAAK,MAAM,IAAI,KAAK,CAAC,EAAE;MACxE;IACF,CAAC;AACD,UAAM,UAAU,YAAY,UAAa,gBAAgB,SAAY,MAAM,WAAW,YAAY;AAChG,YAAM,MAAM,MAAM,KAAK,uBAAuB,KAAK,CAAC,GAAG,SAAU,WAAW;AAC5E,UAAI,IAAI,WAAW,SAAS;AAC1B,cAAM,IAAI;MACZ;IACF,CAAC,IAAI;AACL,UAAM,iBAAiB,MAAM,WAAW,YAAY;AAClD,YAAM,MAAM,MAAM,MAAM,IAAI,IAAI,WAAW,KAAK,UAAU,CAAC,CAAC;AAC5D,UAAI,CAAC,IAAI,IAAI;AACX,cAAM,IAAI,MAAM,GAAG,IAAI,MAAM,IAAI,IAAI,UAAU,KAAK,MAAM,IAAI,KAAK,CAAC,EAAE;MACxE;IACF,CAAC;AACD,UAAM,gBAAgB,MAAM,WAAW,YAAY;AACjD,YAAM,MAAM,MAAM,MAAM,mCAAmC;AAC3D,UAAI,CAAC,IAAI,IAAI;AACX,cAAM,IAAI,MAAM,GAAG,IAAI,MAAM,IAAI,IAAI,UAAU,KAAK,MAAM,IAAI,KAAK,CAAC,EAAE;MACxE;IACF,CAAC;AACD,UAAM,cAAc,MAAM,WAAW,YAAY;AAC/C,YAAM,MAAM,MAAM,MAAM,mCAAmC;AAC3D,UAAI,CAAC,IAAI,IAAI;AACX,cAAM,IAAI,MAAM,GAAG,IAAI,MAAM,IAAI,IAAI,UAAU,KAAK,MAAM,IAAI,KAAK,CAAC,EAAE;MACxE;IACF,CAAC;AACD,WAAO;MACL,sBAAqBN,MAAA,UAAU,cAAV,gBAAAA,IAAqB;MAC1C;MACA;MACA;MACA;MACA;IACF;EACF;EAEA,MAAgB,oBAAoB,OAAc,SAAkC,aAA6C;AAC/H,WAAO,IAAI,MAAM;;;;;QAKb,KAAK;;QAEL,KAAK,UAAU,MAAM,KAAK,sBAAsB,SAAS,WAAW,GAAG,MAAM,CAAC,CAAC;OAChF,EAAE,MAAa,CAAC;EACrB;EAEA,MAAgB,cAAiB,IAAmC,SAAkC,aAAyD;AAC7J,UAAM,gBAAgB,MAAM,OAAO;MACjC;MACA;MACA,EAAE,sBAAsB,IAAK;IAC/B;AAGA,QAAI,cAAc,WAAW,SAAS;AACpC,UAAI,UAAU,aAAa,CAAC,UAAU,UAAU,QAAQ;AACtD,cAAM,IAAI,MAAM,sMAAsM,EAAE,OAAO,cAAc,MAAM,CAAC;MACtP;AACA,YAAM,MAAM,KAAK,oBAAoB,cAAc,OAAO,SAAS,WAAW;IAChF;AACA,WAAO,cAAc;EACvB;EAEA,MAAgB,uBAA0B,IAAsB,SAAkC,aAAyD;AACzJ,WAAO,MAAM,KAAK,cAAc,YAAY,MAAM,OAAO,kBAAkB,EAAE,GAAG,SAAS,WAAW;EACtG;EAEA,MAAa,oBAAoB,cAA4B;AAC3D,QAAI,EAAE,0BAA0B,KAAK,UAAU;AAE7C,YAAM,IAAI,MAAM,qJAAqJ;IACvK;AAEA,UAAM,KAAK;MACT,QAAQ,KAAK,QAAQ,WAAW;MAChC,WAAW;MACX,gBAAgB,KAAK,UAAU,IAAI;IACrC;AACA,UAAM,SAAuB;MAC3B,WAAW,KAAK;MAChB,eAAe,KAAK,QAAQ;MAC5B,4BAA4B;IAC9B;AAEA,UAAM,cAAc,MAAM,KAAK;MAC7B,YAAY,MAAY;QACtB;QACA;QACA,aAAa;MACf;IACF;AACA,UAAM,WAAW,MAAM,KAAK,iBAAiB,WAAW;AAExD,QAAI,SAAS,WAAW,SAAS;AAC/B,YAAM,QAAQ,SAAS;AACvB,UAAI,YAAY,kBAAkB,WAAW,KAAK,GAAG;AACnD,eAAO;MACT;AACA,YAAM;IACR;AAEA,QAAI,CAAC,SAAS,KAAK,IAAI;AACrB,YAAM,OAAO,MAAM,SAAS,KAAK,KAAK;AACtC,YAAM,IAAI,MAAM,yCAAyC,SAAS,MAAM,IAAI,IAAI,EAAE;IACpF;AAEA,UAAM,SAAS,MAAY,4BAA4B,IAAI,QAAQ,SAAS,IAAI;AAChF,QAAU,cAAc,MAAM,GAAG;AAE/B,YAAM,IAAI,oBAAoB,eAAe,EAAE,OAAO,CAAC;IACzD;AAEA,QAAI,CAAC,OAAO,cAAc;AACxB,YAAM,IAAI,oBAAoB,mEAAmE;IACnG;AAEA,WAAO,IAAI,YAAY,OAAO,YAAY;EAC5C;EAEA,MAAa,kBACX,MACA,gBACA,SACA,cAA6C,UAC7C;AACA,0BAAY,KAAK,cAAc;MAC7B,cAAc;IAChB,CAAC;AAGD,WAAO,MAAM,KAAK;MAChB,MAAM,KAAK,uBAAuB,MAAM,gBAAgB,SAAU,WAAW;MAC7E;MACA;IACF;EACF;EAEO,cAAc,SAAgH;AACnI,UAAM,UAAU,IAAI,gBAAgB;MAClC,4BAA4B,OAAO,iBAAiB,MAAM,KAAK,oBAAoB,YAAY;MAC/F,GAAG;IACL,CAAC;AACD,WAAO;EACT;EAEA,MAAgB,oCACd,MACA,gBACA,kBACA,eASC;AACD,QAAI;AACF,aAAO,OAAO,GAAG,MAAM,KAAK,kBAAkB,MAAM,gBAAgB,gBAAgB,CAAC;IACvF,SAAS,GAAG;AACV,iBAAW,aAAa,eAAe;AACrC,YAAI,UAAU,WAAW,CAAC,GAAG;AAC3B,iBAAO,OAAO,MAAM,CAAoB;QAC1C;MACF;AACA,YAAM;IACR;EACF;EAEA,MAAc,uBACZ,MACA,SACA,SACA,aAME;;AAIF,QAAI,WAAW,MAAM,QAAQ,4BAA4B,GAAM;AAE/D,QAAI,eAAe,yBAAyB,KAAK,UAAU,KAAK,QAAQ,sBAAsB;AAC9F,QAAI,gBAAgB,eAAe,MAAM,aAAa,4BAA4B,GAAM,IAAI;AAG5F,YAAMD,OAAAC,MAAA,KAAK,SAAQ,mBAAb,gBAAAD,IAAA,KAAAC;AAEN,QAAI,MAAM,KAAK,UAAU,IAAI;AAC7B,QAAI,IAAI,SAAS,GAAG,GAAG;AACrB,YAAM,IAAI,MAAM,GAAG,EAAE;IACvB;AACA,UAAM,SAAsB;;;;;;;;;;;MAW1B,GAAI,mBAAmB,YAAY,CAAC,IAAI;QACtC,aAAa;MACf;MACA,GAAG;MACH,SAAS;QACP,iCAAiC;QACjC,sBAAsB,KAAK;QAC3B,uBAAuB;QACvB,0BAA0B,KAAK,QAAQ;QACvC,GAAI,WAAW;UACb,wBAAwB,SAAS,YAAY;QAC/C,IAAI,CAAC;QACL,IAAI,qCAAU,gBAAe;UAC3B,yBAAyB,SAAS,aAAa;QACjD,IAAI,CAAC;QACL,GAAI,0BAA0B,KAAK,UAAU;UAC3C,kCAAkC,KAAK,QAAQ;QACjD,IAAI,CAAC;QACL,GAAI,gBAAgB;UAClB,8BAA8B,cAAc,YAAY;QAC1D,IAAI,CAAC;;;;;;;;;QASL,wBAAwB,2BAA2B;;QAEnD,8BAA8B;QAC9B,GAAG,KAAK,QAAQ;QAChB,GAAG,QAAQ;MACb;;;;MAIA,GAAI,mBAAmB,YAAY,CAAC,IAAI;QACtC,OAAO;MACT;IACF;AAEA,QAAI;AACJ,QAAI;AACF,eAAS,MAAM,MAAM,KAAK,MAAM;IAClC,SAAS,GAAG;AACV,UAAI,aAAa,WAAW;AAE1B,YAAI,aAAc,OAAO,UAAU,KAAoB,EAAE,YAAY;AACnE,iBAAO,OAAO,MAAM,CAAC;QACvB,OAAO;AACL,gBAAM,MAAM,KAAK,oBAAoB,GAAG,SAAS,WAAW;QAC9D;MACF;AACA,YAAM;IACR;AAEA,UAAM,eAAe,MAAM,KAAK,iBAAiB,MAAM;AACvD,QAAI,aAAa,WAAW,SAAS;AAEnC,UAAI,YAAY,mBAAmB,WAAW,aAAa,KAAK,GAAG;AACjE,YAAI,CAAC,UAAU;AACb,gBAAM,IAAI,oBAAoB,+DAA+D,EAAE,UAAU,aAAa,CAAC;QACzH;AACA,gBAAQ,uBAAuB,SAAS,WAAW;AACnD,eAAO,OAAO,MAAM,aAAa,KAAK;MACxC;AAIA,UAAI,iBAAiB,YAAY,wBAAwB,WAAW,aAAa,KAAK,KAAK,YAAY,eAAe,WAAW,aAAa,KAAK,IAAI;AACrJ,YAAI,CAAC,eAAe;AAClB,gBAAM,IAAI,oBAAoB,2EAA2E,EAAE,eAAe,aAAa,CAAC;QAC1I;AACA,qBAAa,uBAAuB,cAAc,WAAW;AAC7D,eAAO,OAAO,MAAM,aAAa,KAAK;MACxC;AAIA,YAAM,aAAa;IACrB;AAGA,UAAM,MAAM,OAAO,OAAO,aAAa,MAAM;MAC3C,YAAY;IACd,CAAC;AACD,QAAI,IAAI,IAAI;AACV,aAAO,OAAO,GAAG,GAAG;IACtB,WAAW,IAAI,WAAW,KAAK;AAE7B,YAAM,aAAa,IAAI,QAAQ,IAAI,aAAa;AAChD,UAAI,eAAe,MAAM;AACvB,gBAAQ,IAAI,yCAAyC,GAAG,sBAAsB,UAAU,aAAa;AACrG,cAAM,KAAK,OAAO,UAAU,IAAI,GAAI;AACpC,eAAO,OAAO,MAAM,IAAI,MAAM,gCAAgC,UAAU,UAAU,CAAC;MACrF;AACA,cAAQ,IAAI,yCAAyC,GAAG,+CAA+C;AACvG,aAAO,OAAO,MAAM,IAAI,MAAM,8CAA8C,CAAC;IAC/E,OAAO;AACL,YAAM,QAAQ,MAAM,IAAI,KAAK;AAE7B,YAAM,WAAW,IAAI,oBAAoB,6BAA6B,GAAG,KAAK,IAAI,MAAM,IAAI,KAAK,IAAI,EAAE,SAAS,QAAQ,KAAK,KAAK,CAAC;AAEnI,UAAI,IAAI,WAAW,OAAO,MAAM,SAAS,wBAAwB,GAAG;AAGlE,eAAO,OAAO,MAAM,QAAQ;MAC9B;AAGA,YAAM;IACR;EACF;EAEA,MAAc,iBAAiB,QAAyD;AACtF,QAAI,MAAM;AACV,QAAI,OAAO,QAAQ,IAAI,uBAAuB,GAAG;AAC/C,YAAM,eAAe,OAAO,OAAO,QAAQ,IAAI,uBAAuB,CAAC;AACvE,YAAM,IAAI,SAAS,OAAO,MAAM;QAC9B,QAAQ;QACR,YAAY,OAAO;QACnB,SAAS,OAAO;MAClB,CAAC;IACH;AAGA,QAAI,IAAI,QAAQ,IAAI,qBAAqB,GAAG;AAC1C,YAAM,YAAY,MAAM,IAAI,KAAK;AACjC,UAAI,IAAI,QAAQ,IAAI,qBAAqB,MAAM,UAAU,MAAM;AAC7D,cAAM,IAAI,oBAAoB,sGAAsG;MACtI;AACA,YAAM,QAAQ,WAAW,SAAS,SAAS;AAC3C,aAAO,OAAO,MAAM,KAAK;IAC3B;AAEA,WAAO,OAAO,GAAG,GAAG;EACtB;EAEA,MAAa,oBAAoB,SAAkE;AACjG,UAAM,MAAM,MAAM,KAAK,kBAAkB,0BAA0B;MACjE,QAAQ;MACR,SAAS;QACP,gBAAgB;MAClB;MACA,MAAM,KAAK,UAAU,OAAO;IAC9B,GAAG,IAAI;AAEP,UAAM,IAAI,oBAAoB,MAAM,IAAI,KAAK,CAAC;EAChD;EAEA,MAAM,wBACJ,OACA,aACyD;AACzD,UAAM,MAAM,MAAM,KAAK;MACrB;MACA;QACE,QAAQ;QACR,SAAS;UACP,gBAAgB;QAClB;QACA,MAAM,KAAK,UAAU;UACnB;UACA,cAAc;QAChB,CAAC;MACH;MACA;MACA,CAAC,YAAY,YAAY;IAC3B;AAEA,QAAI,IAAI,WAAW,SAAS;AAC1B,aAAO,OAAO,MAAM,IAAI,KAAK;IAC/B,OAAO;AACL,aAAO,OAAO,GAAG,MAAS;IAC5B;EACF;EAEA,MAAM,sBACJ,OACA,aACA,SAC0D;AAC1D,UAAM,MAAM,MAAM,KAAK;MACrB;MACA;QACE,QAAQ;QACR,SAAS;UACP,gBAAgB;QAClB;QACA,MAAM,KAAK,UAAU;UACnB;UACA,cAAc;QAChB,CAAC;MACH;MACA;MACA,CAAC,YAAY,oBAAoB;IACnC;AAEA,QAAI,IAAI,WAAW,SAAS;AAC1B,aAAO,IAAI;IACb;EACF;EAEA,MAAM,mBACJ,OACA,aAC8E;AAC9E,UAAM,MAAM,MAAM,KAAK;MACrB;MACA;QACE,QAAQ;QACR,SAAS;UACP,gBAAgB;QAClB;QACA,MAAM,KAAK,UAAU;UACnB;UACA,cAAc;QAChB,CAAC;MACH;MACA;MACA,CAAC,YAAY,yBAAyB;IACxC;AAEA,QAAI,IAAI,WAAW,SAAS;AAC1B,aAAO,OAAO,MAAM,IAAI,KAAK;IAC/B,OAAO;AACL,aAAO,OAAO,GAAG,MAAM,IAAI,KAAK,KAAK,CAAC;IACxC;EACF;EAEA,MAAM,cACJ,SACkE;AAClE,UAAM,MAAM,MAAM,KAAK;MACrB,oBAAoB,UAAU,oCAAoC;MAClE;QACE,QAAQ;QACR,SAAS;UACP,gBAAgB;QAClB;QACA,MAAM,KAAK,UAAU;UACnB,MAAM,QAAQ;UACd,GAAI,cAAc,UAAU,EAAE,UAAU,QAAQ,SAAS,IAAI,CAAC;QAChE,CAAC;MACH;MACA;MACA,CAAC,YAAY,qBAAqB;IACpC;AAEA,QAAI,IAAI,WAAW,SAAS;AAC1B,aAAO,OAAO,MAAM,IAAI,KAAK;IAC/B,OAAO;AACL,aAAO,OAAO,GAAG,MAAS;IAC5B;EACF;EAEA,MAAM,eACJ,SACA,SAC8G;AAC9G,UAAM,MAAM,MAAM,KAAK;MACrB;MACA;QACE,QAAQ;QACR,SAAS;UACP,gBAAgB;QAClB;QACA,MAAM,KAAK,UAAU;UACnB,cAAc,QAAQ;UACtB,cAAc,QAAQ;QACxB,CAAC;MACH;MACA;MACA,CAAC,YAAY,8BAA8B,YAAY,0BAA0B;IACnF;AAEA,QAAI,IAAI,WAAW,SAAS;AAC1B,aAAO,IAAI;IACb;EACF;EAEA,MAAM,YACJ,SACA,SACgE;AAChE,UAAM,MAAM,MAAM,KAAK;MACrB;MACA;QACE,QAAQ;QACR,SAAS;UACP,gBAAgB;QAClB;QACA,MAAM,KAAK,UAAU,OAAO;MAC9B;MACA;MACA,CAAC,YAAY,0BAA0B;IACzC;AAEA,QAAI,IAAI,WAAW,SAAS;AAC1B,aAAO,IAAI;IACb;EACF;EAEA,MAAM,wBAAwB,MAAgF;AAC5G,UAAM,MAAM,MAAM,KAAK,cAAc,EAAE,MAAM,gBAAgB,KAAK,CAAC;AACnE,QAAI,IAAI,WAAW,SAAS;AAC1B,aAAO,OAAO,MAAM,IAAI,KAAK;IAC/B,OAAO;AACL,aAAO,OAAO,GAAG,MAAS;IAC5B;EACF;EAEA,MAAM,YAAY,MAAgF;AAChG,UAAM,MAAM,MAAM,KAAK;MACrB;MACA;QACE,QAAQ;QACR,SAAS;UACP,gBAAgB;QAClB;QACA,MAAM,KAAK,UAAU;UACnB;QACF,CAAC;MACH;MACA;MACA,CAAC,YAAY,qBAAqB;IACpC;AAEA,QAAI,IAAI,WAAW,SAAS;AAC1B,aAAO,OAAO,MAAM,IAAI,KAAK;IAC/B,OAAO;AACL,aAAO,OAAO,GAAG,MAAS;IAC5B;EACF;EAEA,MAAM,4BACJ,SACA,SACwG;AACxG,UAAM,MAAM,MAAM,KAAK;MACrB;MACA;QACE,QAAQ;QACR,SAAS;UACP,gBAAgB;QAClB;QACA,MAAM,KAAK,UAAU,OAAO;MAC9B;MACA;MACA,CAAC;IACH;AAEA,QAAI,IAAI,WAAW,SAAS;AAC1B,aAAO,OAAO,MAAM,IAAI,KAAK;IAC/B;AAEA,WAAO,OAAO,GAAG,MAAM,IAAI,KAAK,KAAK,CAAC;EACxC;EAEA,MAAM,gBACJ,SACA,SACsE;AACtE,UAAM,MAAM,MAAM,KAAK;MACrB;MACA;QACE,QAAQ;QACR,SAAS;UACP,gBAAgB;QAClB;QACA,MAAM,KAAK,UAAU,OAAO;MAC9B;MACA;MACA,CAAC,YAAY,yBAAyB;IACxC;AACA,QAAI,IAAI,WAAW,SAAS;AAC1B,aAAO,OAAO,MAAM,IAAI,KAAK;IAC/B;AACA,WAAO,OAAO,GAAG,MAAS;EAC5B;EAEA,MAAM,8BACJ,SAEA,SACuG;AACvG,UAAM,MAAM,MAAM,KAAK;MACrB;MACA;QACE,QAAQ;QACR,SAAS;UACP,gBAAgB;QAClB;QACA,MAAM,KAAK,UAAU,OAAO;MAC9B;MACA;MACA,CAAC;IACH;AAEA,QAAI,IAAI,WAAW,SAAS;AAC1B,aAAO,OAAO,MAAM,IAAI,KAAK;IAC/B;AAEA,WAAO,OAAO,GAAG,MAAM,IAAI,KAAK,KAAK,CAAC;EACxC;EAEA,MAAM,mBAAmB,SAKP;AAChB,UAAM,KAAK;MACT;MACA;QACE,QAAQ;QACR,SAAS;UACP,gBAAgB;QAClB;QACA,MAAM,KAAK,UAAU;UACnB,OAAO,QAAQ;UACf,SAAS,QAAQ;UACjB,cAAc,QAAQ;QACxB,CAAC;MACH;MACA,QAAQ;IACV;EACF;EAEA,MAAM,qBAA4D,SAIyD;AACzH,UAAM,MAAM,MAAM,KAAK;MACrB,QAAQ,SAAS,UACf,wCACA,QAAQ,SAAS,YACf,qCACA;MACJ;QACE,QAAQ;QACR,SAAS;UACP,gBAAgB;QAClB;QACA,MAAM,KAAK,UAAU;UACnB,MAAM,QAAQ;QAChB,CAAC;MACH;MACA,QAAQ;MACR,CAAC,YAAY,qBAAqB;IACpC;AAEA,QAAI,IAAI,WAAW,SAAS;AAC1B,aAAO,OAAO,MAAM,IAAI,KAAK;IAC/B,OAAO;AACL,aAAO,OAAO,GAAG,MAAM,IAAI,KAAK,KAAK,CAAC;IACxC;EACF;EAEA,MAAM,QACJ,aACA,MACA,SACA;AACA,UAAM,MAAM,MAAM,KAAK,kBAAkB,qBAAqB;MAC5D,QAAQ;MACR,SAAS;QACP,gBAAgB;MAClB;MACA,MAAM,KAAK,UAAU;QACnB,MAAM;QACN,MAAM;QACN;MACF,CAAC;IACH,GAAG,OAAO;AAEV,UAAM,SAAS,MAAM,IAAI,KAAK;AAC9B,WAAO;MACL,aAAa,OAAO;MACpB,cAAc,OAAO;MACrB,SAAS,OAAO;IAClB;EACF;EAEA,MAAM,qBACJ,OACA,UACA,SACsG;AACtG,UAAM,MAAM,MAAM,KAAK;MACrB;MACA;QACE,QAAQ;QACR,SAAS;UACP,gBAAgB;QAClB;QACA,MAAM,KAAK,UAAU;UACnB;UACA;QACF,CAAC;MACH;MACA;MACA,CAAC,YAAY,qBAAqB;IACpC;AAEA,QAAI,IAAI,WAAW,SAAS;AAC1B,aAAO,OAAO,MAAM,IAAI,KAAK;IAC/B;AAEA,UAAM,SAAS,MAAM,IAAI,KAAK,KAAK;AACnC,WAAO,OAAO,GAAG;MACf,aAAa,OAAO;MACpB,cAAc,OAAO;IACvB,CAAC;EACH;EAEA,MAAM,qBACJ,OACA,UACA,8BACA,SACuJ;AACvJ,UAAM,MAAM,MAAM,KAAK;MACrB;MACA;QACE,SAAS;UACP,gBAAgB;QAClB;QACA,QAAQ;QACR,MAAM,KAAK,UAAU;UACnB;UACA;UACA,2BAA2B;QAC7B,CAAC;MACH;MACA;MACA,CAAC,YAAY,4BAA4B,YAAY,0BAA0B;IACjF;AAEA,QAAI,IAAI,WAAW,SAAS;AAC1B,aAAO,OAAO,MAAM,IAAI,KAAK;IAC/B;AAEA,UAAM,SAAS,MAAM,IAAI,KAAK,KAAK;AACnC,WAAO,OAAO,GAAG;MACf,aAAa,OAAO;MACpB,cAAc,OAAO;IACvB,CAAC;EACH;EAEA,MAAM,kBAAkB,SAAiG;AACvH,UAAM,MAAM,MAAM,KAAK;MACrB;MACA;QACE,QAAQ;MACV;MACA;MACA,CAAC;IACH;AAEA,QAAI,IAAI,WAAW,SAAS;AAC1B,aAAO,OAAO,MAAM,IAAI,KAAK;IAC/B;AAEA,UAAM,SAAS,MAAM,IAAI,KAAK,KAAK;AACnC,WAAO,OAAO,GAAG;MACf,aAAa,OAAO;MACpB,cAAc,OAAO;IACvB,CAAC;EACH;EAEA,MAAM,oBAAoB,MAAsI;AAC9J,UAAM,MAAM,MAAM,KAAK;MACrB;MACA;QACE,QAAQ;QACR,SAAS;UACP,gBAAgB;QAClB;QACA,MAAM,KAAK,UAAU;UACnB;QACF,CAAC;MACH;MACA;MACA,CAAC,YAAY,qBAAqB;IACpC;AAEA,QAAI,IAAI,WAAW,SAAS;AAC1B,aAAO,OAAO,MAAM,IAAI,KAAK;IAC/B;AAEA,UAAM,SAAS,MAAM,IAAI,KAAK,KAAK;AACnC,WAAO,OAAO,GAAG;MACf,aAAa,OAAO;MACpB,cAAc,OAAO;MACrB,SAAS,OAAO;IAClB,CAAC;EACH;EAEA,MAAM,kBAAkB,MAAwL;AAC9M,UAAM,MAAM,MAAM,KAAK;MACrB;MACA;QACE,QAAQ;QACR,SAAS;UACP,gBAAgB;QAClB;QACA,MAAM,KAAK,UAAU,IAAI;MAC3B;MACA;MACA,CAAC,YAAY,2BAA2B;IAC1C;AAEA,QAAI,IAAI,WAAW,SAAS;AAC1B,aAAO,OAAO,MAAM,IAAI,KAAK;IAC/B;AAEA,UAAM,SAAS,MAAM,IAAI,KAAK,KAAK;AACnC,WAAO,OAAO,GAAG;MACf,aAAa,OAAO;MACpB,cAAc,OAAO;IACvB,CAAC;EACH;EAEA,MAAM,YACJ,SAUiB;AACjB,UAAM,qBAAqB,IAAI,IAAI,QAAQ,WAAW;AACtD,eAAW,OAAO,CAAC,QAAQ,OAAO,GAAG;AACnC,UAAI,mBAAmB,aAAa,IAAI,GAAG,GAAG;AAC5C,gBAAQ,KAAK,mCAAmC,MAAM,yEAAyE;MACjI;AACA,yBAAmB,aAAa,OAAO,GAAG;IAC5C;AAEA,QAAI,EAAE,0BAA0B,KAAK,UAAU;AAE7C,YAAM,IAAI,MAAM,0DAA0D;IAC5E;AACA,UAAM,MAAM,IAAI,IAAI,KAAK,UAAU,IAAI,2BAA2B,QAAQ,SAAS,YAAY,CAAC;AAChG,QAAI,aAAa,IAAI,aAAa,KAAK,SAAS;AAChD,QAAI,aAAa,IAAI,iBAAiB,KAAK,QAAQ,oBAAoB;AACvE,QAAI,aAAa,IAAI,gBAAgB,mBAAmB,SAAS,CAAC;AAClE,QAAI,aAAa,IAAI,SAAS,QAAQ;AACtC,QAAI,aAAa,IAAI,SAAS,QAAQ,KAAK;AAC3C,QAAI,aAAa,IAAI,cAAc,oBAAoB;AACvD,QAAI,aAAa,IAAI,kBAAkB,QAAQ,aAAa;AAC5D,QAAI,aAAa,IAAI,yBAAyB,MAAM;AACpD,QAAI,aAAa,IAAI,iBAAiB,MAAM;AAC5C,QAAI,aAAa,IAAI,QAAQ,QAAQ,IAAI;AACzC,QAAI,aAAa,IAAI,sBAAsB,QAAQ,gBAAgB;AAEnE,QAAI,QAAQ,0BAA0B;AACpC,UAAI,aAAa,IAAI,+BAA+B,QAAQ,wBAAwB;IACtF;AAEA,QAAI,QAAQ,SAAS,QAAQ;AAC3B,YAAM,SAAS,MAAM,QAAQ,QAAQ,4BAA4B,GAAM;AACvE,UAAI,aAAa,IAAI,UAAS,iCAAQ,YAAY,UAAS,EAAE;AAE7D,UAAI,QAAQ,eAAe;AACzB,YAAI,aAAa,IAAI,kBAAkB,QAAQ,aAAa;MAC9D;IACF;AAEA,WAAO,IAAI,SAAS;EACtB;EAEA,MAAM,kBAAkB,SAKwF;AAC9G,QAAI,EAAE,0BAA0B,KAAK,UAAU;AAE7C,YAAM,IAAI,MAAM,0DAA0D;IAC5E;AACA,UAAM,KAAK;MACT,QAAQ,KAAK,QAAQ,WAAW;MAChC,WAAW;MACX,gBAAgB,KAAK,UAAU,IAAI;IACrC;AACA,UAAM,SAAuB;MAC3B,WAAW,KAAK;MAChB,eAAe,KAAK,QAAQ;MAC5B,4BAA4B;IAC9B;AACA,UAAM,SAAS,MAAM,KAAK;MACxB,YAAkB,qBAAqB,IAAI,QAAQ,QAAQ,aAAa,QAAQ,KAAK;IACvF;AACA,QAAU,cAAc,MAAM,GAAG;AAC/B,YAAM,IAAI,oBAAoB,yCAAyC,EAAE,OAAO,CAAC;IACnF;AACA,UAAM,WAAW,MAAY;MAC3B;MACA;MACA;MACA,QAAQ;MACR,QAAQ;IACV;AAEA,UAAM,SAAS,MAAY,uCAAuC,IAAI,QAAQ,QAAQ;AACtF,QAAU,cAAc,MAAM,GAAG;AAC/B,UAAI,UAAU,UAAU,OAAO,SAAS,wCAAwC;AAC9E,cAAM,IAAI,YAAY,kCAAmC,OAAe,QAAQ,YAAY;MAC9F;AAEA,YAAM,IAAI,oBAAoB,wDAAwD,EAAE,OAAO,CAAC;IAClG;AACA,WAAO;MACL,SAAS,OAAO;MAChB,0BAA0B,OAAO;MACjC,aAAa,OAAO;MACpB,cAAc,OAAO,iBAAiB,SAAS,iDAAiD;IAClG;EACF;EAEA,MAAM,QAAQ,SAAyC;AACrD,UAAM,WAAW,MAAM,QAAQ,4BAA4B,GAAM;AACjE,QAAI,UAAU;AACZ,YAAM,aAAa,MAAM,KAAK;QAC5B;QACA;UACE,QAAQ;UACR,SAAS;YACP,gBAAgB;UAClB;UACA,MAAM,KAAK,UAAU,CAAC,CAAC;QACzB;QACA;QACA,CAAC,YAAY,iBAAiB;MAChC;AACA,UAAI,WAAW,WAAW,SAAS;AACjC,YAAI,YAAY,kBAAkB,WAAW,WAAW,KAAK,GAAG;QAEhE,OAAO;AAEL,gBAAM,IAAI,oBAAoB,oBAAoB,EAAE,OAAO,WAAW,MAAM,CAAC;QAC/E;MACF,OAAO;MAEP;IACF;AACA,YAAQ,YAAY;EACtB;EAEA,MAAM,qBAAqB,SAA6E;AACtG,UAAM,kBAAkB,MAAM,KAAK;MACjC;MACA,CAAC;MACD;MACA,CAAC,YAAY,2BAA2B;IAC1C;AACA,QAAI,gBAAgB,WAAW,SAAS;AACtC,UAAI,YAAY,4BAA4B,WAAW,gBAAgB,KAAK,GAAG;AAC7E,eAAO;MACT,OAAO;AACL,cAAM,IAAI,oBAAoB,6BAA6B,EAAE,OAAO,gBAAgB,MAAM,CAAC;MAC7F;IACF;AACA,UAAM,WAAW,gBAAgB;AACjC,UAAM,OAA0C,MAAM,SAAS,KAAK;AACpE,QAAI,CAAE,KAAc,OAAM,IAAI,oBAAoB,uDAAuD;AACzG,WAAO;EACT;EAEA,MAAM,oBACJ,SAGA,SACiD;AACjD,UAAM,WAAW,MAAM,KAAK;MAC1B,uBAAuB,IAAI,gBAAgB,EAAE,SAAS,QAAQ,OAAO,CAAC;MACtE,CAAC;MACD;IACF;AACA,UAAM,SAAS,MAAM,SAAS,KAAK;AACnC,WAAO,OAAO;EAChB;EAEA,MAAM,qBACJ,cACA,QACA,SACA;AACA,UAAM,KAAK;MACT,qBAAqB,YAAY,YAAY,MAAM;MACnD,EAAE,QAAQ,SAAS;MACnB;IACF;EACF;EAEA,MAAM,uBACJ,SAIA,SACqD;AACrD,UAAM,WAAW,MAAM,KAAK;MAC1B,2BAA2B,IAAI,gBAAgB,gBAAgB;QAC7D,SAAS,QAAQ;QACjB,SAAS,QAAQ;MACnB,CAAC,CAAC;MACF,CAAC;MACD;IACF;AACA,UAAM,SAAS,MAAM,SAAS,KAAK;AACnC,WAAO,OAAO;EAChB;EAEA,MAAM,qBACJ,SAIA,SACmD;AACnD,UAAM,WAAW,MAAM,KAAK;MAC1B,yBAAyB,QAAQ,MAAM,IAAI,QAAQ,MAAM;MACzD,CAAC;MACD;IACF;AACA,WAAO,MAAM,SAAS,KAAK;EAC7B;EAEA,MAAM,UACJ,QACA,SACA;AACA,UAAM,KAAK;MACT,qBAAqB,MAAM;MAC3B;QACE,QAAQ;QACR,SAAS;UACP,gBAAgB;QAClB;QACA,MAAM,KAAK,UAAU,CAAC,CAAC;MACzB;MACA;IACF;EACF;EAEA,MAAM,wBACJ,SAKA,SACA;AACA,UAAM,KAAK;MACT,yBAAyB,QAAQ,MAAM,IAAI,QAAQ,MAAM;MACzD;QACE,QAAQ;QACR,SAAS;UACP,gBAAgB;QAClB;QACA,MAAM,KAAK,UAAU,QAAQ,OAAO;MACtC;MACA;IACF;EACF;EAEA,MAAM,WACJ,SAIA,SACA;AACA,UAAM,KAAK;MACT,UAAU,QAAQ,MAAM;MACxB;QACE,QAAQ;QACR,SAAS;UACP,gBAAgB;QAClB;QACA,MAAM,KAAK,UAAU,QAAQ,IAAI;MACnC;MACA;IACF;EACF;EAEA,MAAM,+BACJ,SAIA,SACkD;AAClD,UAAM,WAAW,MAAM,KAAK;MAC1B,6BAA6B,QAAQ,MAAM,yBAAyB,QAAQ,SAAS;MACrF,CAAC;MACD;IACF;AACA,UAAM,SAAS,MAAM,SAAS,KAAK;AACnC,WAAO,OAAO;EAChB;EAEA,MAAM,kCACJ,SAGA,SACqD;AACrD,UAAM,WAAW,MAAM,KAAK;MAC1B,6CAA6C,QAAQ,SAAS;MAC9D,CAAC;MACD;IACF;AACA,UAAM,SAAS,MAAM,SAAS,KAAK;AACnC,WAAO,OAAO;EAChB;EAEA,MAAM,qBAAqB,SAAkE;AAC3F,UAAM,WAAW,MAAM,KAAK;MAC1B;MACA,CAAC;MACD;IACF;AACA,UAAM,SAAS,MAAM,SAAS,KAAK;AACnC,WAAO,OAAO;EAChB;EAEA,MAAM,mBAA0G;AAC9G,UAAM,kBAAkB,MAAM,KAAK,oCAAoC,qBAAqB,CAAC,GAAG,MAAM,CAAC,YAAY,eAAe,CAAC;AACnI,QAAI,gBAAgB,WAAW,SAAS;AACtC,aAAO,OAAO,MAAM,gBAAgB,KAAK;IAC3C;AACA,UAAM,WAAW,gBAAgB;AACjC,UAAM,UAAgD,MAAM,SAAS,KAAK;AAC1E,WAAO,OAAO,GAAG,OAAO;EAC1B;EAEA,MAAM,iBAAiB,QAA6C,SAA0B;AAC5F,UAAM,KAAK;MACT;MACA;QACE,QAAQ;QACR,SAAS;UACP,gBAAgB;QAClB;QACA,MAAM,KAAK,UAAU,MAAM;MAC7B;MACA;IACF;EACF;EAEA,MAAM,aAAa,SAA8E;AAC/F,UAAM,WAAW,MAAM,KAAK,kBAAkB,sBAAsB,CAAC,GAAG,OAAO;AAC/E,QAAI,CAAC,SAAS,IAAI;AAChB,YAAM,IAAI,MAAM,8BAA8B,SAAS,SAAS,MAAO,MAAM,SAAS,KAAK,CAAE;IAC/F;AAEA,UAAM,OAAO,MAAM,SAAS,KAAK;AACjC,WAAO,KAAK;EACd;EAEA,MAAM,cACJ,SACA,SACkD;AAClD,UAAM,gBAAgB,MAAM,KAAK;MAC/B;MACA;QACE,QAAQ;QACR,SAAS;UACP,gBAAgB;QAClB;QACA,MAAM,KAAK,UAAU,OAAO;MAC9B;MACA;IACF;AACA,QAAI,CAAC,cAAc,IAAI;AACrB,YAAM,IAAI,MAAM,+BAA+B,cAAc,SAAS,MAAO,MAAM,cAAc,KAAK,CAAE;IAC1G;AAEA,UAAM,OAAO,MAAM,cAAc,KAAK;AACtC,WAAO;EACT;EAEA,MAAM,0BACJ,UACA,OACA,SAC4D;AAC5D,UAAM,WAAW,MAAM,KAAK;MAC1B,0BAA0B,QAAQ;MAClC;QACE,QAAQ;QACR,SAAS;UACP,gBAAgB;QAClB;QACA,MAAM,KAAK,UAAU,EAAE,MAAM,CAAC;MAChC;MACA;IACF;AACA,WAAO,MAAM,SAAS,KAAK;EAC7B;EAEA,MAAM,iBACJ,MACA,SACsC;AACtC,UAAM,WAAW,MAAM,KAAK;MAC1B;MACA;QACE,QAAQ;QACR,SAAS;UACP,gBAAgB;QAClB;QACA,MAAM,KAAK,UAAU,IAAI;MAC3B;MACA;IACF;AACA,WAAO,MAAM,SAAS,KAAK;EAC7B;EAEA,MAAM,WACJ,QACA,SACA;AACA,UAAM,KAAK;MACT,UAAU,MAAM;MAChB;QACE,QAAQ;MACV;MACA;IACF;EACF;EAEA,MAAM,kBAAkB,SAA0B;AAChD,UAAM,KAAK;MACT;MACA;QACE,QAAQ;MACV;MACA;IACF;EACF;EAEA,MAAM,2BACJ,MACA,SACgD;AAChD,UAAM,WAAW,MAAM,KAAK;MAC1B;MACA;QACE,QAAQ;QACR,SAAS;UACP,gBAAgB;QAClB;QACA,MAAM,KAAK,UAAU,IAAI;MAC3B;MACA;IACF;AACA,WAAO,MAAM,SAAS,KAAK;EAC7B;EAEA,MAAM,2BACJ,IACA,MACA,SACgD;AAChD,UAAM,WAAW,MAAM,KAAK;MAC1B,wBAAwB,EAAE;MAC1B;QACE,QAAQ;QACR,SAAS;UACP,gBAAgB;QAClB;QACA,MAAM,KAAK,UAAU,IAAI;MAC3B;MACA;IACF;AACA,WAAO,MAAM,SAAS,KAAK;EAC7B;EAEA,MAAM,2BACJ,IACA,SACe;AACf,UAAM,KAAK;MACT,wBAAwB,EAAE;MAC1B;QACE,QAAQ;MACV;MACA;IACF;EACF;EAEA,MAAM,cACJ,WACA,SACe;AACf,UAAM,KAAK;MACT,kBAAkB,SAAS;MAC3B;QACE,QAAQ;MACV;MACA;IACF;EACF;EAEA,MAAM,aACJ,SACyC;AACzC,UAAM,WAAW,MAAM,KAAK;MAC1B;MACA;QACE,QAAQ;MACV;MACA;IACF;AACA,WAAO,MAAM,SAAS,KAAK;EAC7B;EAGA,MAAM,0BACJ,SACkD;AAClD,UAAM,WAAW,MAAM,KAAK;MAC1B;MACA;QACE,QAAQ;MACV;MACA;IACF;AACA,UAAM,OAAO,MAAM,SAAS,KAAK;AACjC,WAAO,KAAK;EACd;EAEA,MAAM,+CACJ,kBACA,aACA,SACiE;AACjE,UAAM,kBAAkB,MAAM,KAAK;MACjC,wBAAwB,gBAAgB;MACxC;QACE,QAAQ;QACR,SAAS;UACP,gBAAgB;QAClB;QACA,MAAM,KAAK,UAAU,EAAE,cAAc,YAAY,CAAC;MACpD;MACA;MACA,CAAC,YAAY,oBAAoB;IACnC;AAEA,QAAI,gBAAgB,WAAW,SAAS;AACtC,aAAO,OAAO,MAAM,gBAAgB,KAAK;IAC3C;AACA,WAAO,OAAO,GAAG,MAAS;EAC5B;EAEA,MAAM,SACJ,WACA,cACA,SACwD;AACxD,UAAM,kBAAkB,MAAM,KAAK;MACjC;MACA;QACE,QAAQ;QACR,SAAS;UACP,gBAAgB;QAClB;QACA,MAAM,KAAK,UAAU;UACnB,YAAY;UACZ,eAAe;QACjB,CAAC;MACH;MACA;MACA,CAAC,YAAY,WAAW;IAC1B;AAEA,QAAI,gBAAgB,WAAW,SAAS;AACtC,aAAO,OAAO,MAAM,gBAAgB,KAAK;IAC3C;AACA,WAAO,OAAO,GAAG,MAAS;EAC5B;EAEA,MAAc,sBAAsB,SAA2D;AAC7F,QAAI,aAAa,WAAW,aAAa,SAAS;AAChD,YAAM,IAAI,oBAAoB,kEAAkE;IAClG;AAEA,WAAO;MACL,UAAU,aAAa,UAAU,mBAAmB;MACpD,aAAa,IAAI,gBAAgB,sBAAsB,OAAO,CAAC;IACjE;EACF;EAMA,MAAM,mBACJ,SACA,SACA,aACoF;AACpF,UAAM,eAAe,gBAAgB,WAAW,KAAK,oBAAqB,KAAa,mBAA4B,KAAK,IAAI;AAC5H,UAAM,EAAE,UAAU,YAAY,IAAI,MAAM,KAAK,sBAAsB,OAAO;AAE1E,UAAM,WAAW,MAAM;MACrB,GAAG,QAAQ,IAAI,YAAY,SAAS,CAAC;MACrC;QACE,QAAQ;MACV;MACA;MACA;IACF;AACA,UAAM,OAAO,MAAM,SAAS,KAAK;AACjC,WAAO,KAAK;EACd;EAKA,MAAM,oBACJ,MACA,SACA,aACoH;AACpH,UAAM,eAAe,gBAAgB,WAAW,KAAK,oBAAqB,KAAa,mBAA4B,KAAK,IAAI;AAC5H,UAAM,EAAE,SAAS,IAAI,MAAM,KAAK,sBAAsB,IAAI;AAE1D,UAAM,WAAW,MAAM;MACrB,GAAG,QAAQ;MACX;QACE,QAAQ;QACR,SAAS;UACP,gBAAgB;QAClB;QACA,MAAM,KAAK,UAAU,IAAI;MAC3B;MACA;MACA;IACF;AACA,WAAO,MAAM,SAAS,KAAK;EAC7B;EAKA,MAAM,iBACJ,SACA,OACA,SACA,aACgF;AAChF,UAAM,eAAe,gBAAgB,WAAW,KAAK,oBAAqB,KAAa,mBAA4B,KAAK,IAAI;AAC5H,UAAM,EAAE,UAAU,YAAY,IAAI,MAAM,KAAK,sBAAsB,OAAO;AAE1E,UAAM,WAAW,MAAM;MACrB,GAAG,QAAQ,IAAI,KAAK,IAAI,YAAY,SAAS,CAAC;MAC9C;QACE,QAAQ;MACV;MACA;MACA;IACF;AACA,WAAO,MAAM,SAAS,KAAK;EAC7B;EAKA,MAAM,oBACJ,SACA,OACA,MACA,SACA,aACgF;AAChF,UAAM,eAAe,gBAAgB,WAAW,KAAK,oBAAqB,KAAa,mBAA4B,KAAK,IAAI;AAC5H,UAAM,EAAE,UAAU,YAAY,IAAI,MAAM,KAAK,sBAAsB,OAAO;AAE1E,UAAM,WAAW,MAAM;MACrB,GAAG,QAAQ,IAAI,KAAK,IAAI,YAAY,SAAS,CAAC;MAC9C;QACE,QAAQ;QACR,SAAS;UACP,gBAAgB;QAClB;QACA,MAAM,KAAK,UAAU,IAAI;MAC3B;MACA;MACA;IACF;AACA,WAAO,MAAM,SAAS,KAAK;EAC7B;EAKA,MAAM,mBAAmB,MAAuB,QAAgB,SAAiC,aAAmI;AAClO,UAAM,eAAe,gBAAgB,WAAW,KAAK,sCAAuC,KAAa,qCAA8C,KAAK,IAAI;AAChK,UAAM,SAAS,MAAM;MACnB,IAAI,IAAI;MACR;QACE,QAAQ;QACR,SAAS;UACP,gBAAgB;QAClB;QACA,MAAM,KAAK,UAAU,EAAE,SAAS,OAAO,CAAC;MAC1C;MACA;MACA,CAAC,YAAY,cAAc;IAC7B;AACA,QAAI,OAAO,WAAW,SAAS;AAC7B,aAAO;IACT;AACA,WAAO,MAAM,OAAO,KAAK,KAAK;EAChC;AACF;;;ACtiDO,IAAM,uBAAN,cAAmC,qBAAqB;EAC7D,YAA4B,SAAuC;AACjE,UAAM,OAAO;AADa,SAAA,UAAA;EAE5B;EAEA,MAAgB,kBAAkB,MAAc,SAAsB,SAAiC,cAAkC,UAAU;AACjJ,WAAO,MAAM,KAAK;MAChB;MACA;QACE,GAAG;QACH,SAAS;UACP,6BAA6B,qBAAqB,KAAK,UAAU,KAAK,QAAQ,kBAAkB;UAChG,GAAG,QAAQ;QACb;MACF;MACA;MACA;IACF;EACF;EAEA,MAAgB,oCACd,MACA,gBACA,kBACA,eASC;AACD,QAAI;AACF,aAAO,OAAO,GAAG,MAAM,KAAK,kBAAkB,MAAM,gBAAgB,gBAAgB,CAAC;IACvF,SAAS,GAAG;AACV,iBAAW,aAAa,eAAe;AACrC,YAAI,UAAU,WAAW,CAAC,GAAG;AAC3B,iBAAO,OAAO,MAAM,CAAoB;QAC1C;MACF;AACA,YAAM;IACR;EACF;EAEA,MAAM,iBAAiB,MAA2E;AAChG,UAAM,WAAW,MAAM,KAAK;MAC1B;MACA;QACE,QAAQ;QACR,SAAS;UACP,gBAAgB;QAClB;QACA,MAAM,KAAK,UAAU,IAAI;MAC3B;MACA;IACF;AACA,WAAO,MAAM,SAAS,KAAK;EAC7B;EAEA,MAAM,qBAAqB,SAA6E;AACtG,UAAM,kBAAkB,MAAM,KAAK;MACjC;MACA,CAAC;MACD;MACA,CAAC,YAAY,2BAA2B;IAC1C;AACA,QAAI,gBAAgB,WAAW,SAAS;AACtC,UAAI,YAAY,4BAA4B,WAAW,gBAAgB,KAAK,GAAG;AAC7E,eAAO;MACT,OAAO;AACL,cAAM,IAAI,oBAAoB,6BAA6B,EAAE,OAAO,gBAAgB,MAAM,CAAC;MAC7F;IACF;AACA,UAAM,WAAW,gBAAgB;AACjC,UAAM,OAA0C,MAAM,SAAS,KAAK;AACpE,QAAI,CAAE,KAAc,OAAM,IAAI,oBAAoB,uDAAuD;AACzG,WAAO;EACT;EAEA,MAAM,kBAAkB,QAA8D;AACpF,UAAM,kBAAkB,MAAM,KAAK;MACjC,mBAAmB,MAAM;MACzB,CAAC;MACD;MACA,CAAC,YAAY,YAAY;IAC3B;AACA,QAAI,gBAAgB,WAAW,SAAS;AACtC,aAAO,OAAO,MAAM,gBAAgB,KAAK;IAC3C;AACA,UAAM,OAAoC,MAAM,gBAAgB,KAAK,KAAK;AAC1E,WAAO,OAAO,GAAG,IAAI;EACvB;EAEA,MAAM,0BAA0B,SAEoB;AAClD,UAAM,WAAW,MAAM,KAAK;MAC1B,sCAAsC,QAAQ,MAAM;MACpD,CAAC;MACD;IACF;AACA,UAAM,SAAS,MAAM,SAAS,KAAK;AACnC,WAAO,OAAO;EAChB;EAEA,MAAM,2BAA2B,cAAsB,QAAgB;AACrE,UAAM,KAAK;MACT,8BAA8B,YAAY,YAAY,MAAM;MAC5D,EAAE,QAAQ,SAAS;MACnB;IACF;EACF;EAEA,MAAM,6BACJ,SAGqD;AACrD,UAAM,WAAW,MAAM,KAAK;MAC1B,0CAA0C,QAAQ,MAAM;MACxD,CAAC;MACD;IACF;AACA,UAAM,SAAS,MAAM,SAAS,KAAK;AACnC,WAAO,OAAO;EAChB;EAEA,MAAM,2BACJ,SAImD;AACnD,UAAM,WAAW,MAAM,KAAK;MAC1B,kCAAkC,QAAQ,MAAM,IAAI,QAAQ,MAAM;MAClE,CAAC;MACD;IACF;AACA,WAAO,MAAM,SAAS,KAAK;EAC7B;EAEA,MAAM,0BACJ,SAKA,SACkD;AAClD,UAAM,WAAW,MAAM,KAAK;MAC1B,qBAAqB,IAAI,gBAAgB,gBAAgB;QACvD,SAAS,QAAQ;QACjB,SAAS,QAAQ;QACjB,WAAW,QAAQ,UAAU,SAAS;MACxC,CAAC,CAAC,CAAC;MACH,CAAC;MACD;IACF;AACA,UAAM,SAAS,MAAM,SAAS,KAAK;AACnC,WAAO,OAAO;EAChB;EAEA,MAAM,6BACJ,SAIA,SACqD;AACrD,UAAM,WAAW,MAAM,KAAK;MAC1B,wBAAwB,IAAI,gBAAgB,gBAAgB;QAC1D,SAAS,QAAQ;QACjB,WAAW,QAAQ,UAAU,SAAS;MACxC,CAAC,CAAC,CAAC;MACH,CAAC;MACD;IACF;AACA,UAAM,SAAS,MAAM,SAAS,KAAK;AACnC,WAAO,OAAO;EAChB;EAEA,MAAM,gBAAgB,SAMmB;;AACvC,UAAM,eAAe,IAAI,gBAAgB,gBAAgB;MACvD,QAAQ,QAAQ;MAChB,QAAOO,MAAA,QAAQ,UAAR,gBAAAA,IAAe;MACtB,OAAMC,MAAA,QAAQ,SAAR,gBAAAA,IAAc;MACpB,GAAG,QAAQ,UAAU;QACnB,UAAU;UACR,YAAY;QACd,EAAE,QAAQ,OAAO;MACnB,IAAI,CAAC;MACL,GAAG,QAAQ,QAAQ;QACjB,OAAO,QAAQ;MACjB,IAAI,CAAC;IACP,CAAC,CAAC;AACF,UAAM,WAAW,MAAM,KAAK,kBAAkB,YAAY,aAAa,SAAS,GAAG,CAAC,GAAG,IAAI;AAC3F,WAAO,MAAM,SAAS,KAAK;EAC7B;EAEA,MAAM,gBAAgB,SAEqB;AACzC,UAAM,WAAW,MAAM,KAAK;MAC1B,UAAU,IAAI,gBAAgB,gBAAgB;QAC5C,SAAS,mCAAS;MACpB,CAAC,CAAC,CAAC;MACH,CAAC;MACD;IACF;AACA,UAAM,SAAS,MAAM,SAAS,KAAK;AACnC,WAAO,OAAO;EAChB;EAEA,MAAM,cAAc,QAAsD;AACxE,UAAM,WAAW,MAAM,KAAK;MAC1B,UAAU,MAAM;MAChB,CAAC;MACD;IACF;AACA,WAAO,MAAM,SAAS,KAAK;EAC7B;EAEA,MAAM,oBAAoB,QAAwD;AAChF,UAAM,WAAW,MAAM,KAAK,kBAAkB,kBAAkB,MAAM,IAAI,CAAC,GAAG,IAAI;AAClF,UAAM,SAAS,MAAM,SAAS,KAAK;AACnC,WAAO,OAAO;EAChB;;EAGA,MAAM,iBAAiB,MAA2E;AAChG,UAAM,WAAW,MAAM,KAAK;MAC1B;MACA;QACE,QAAQ;QACR,SAAS;UACP,gBAAgB;QAClB;QACA,MAAM,KAAK,UAAU,IAAI;MAC3B;MACA;IACF;AACA,WAAO,MAAM,SAAS,KAAK;EAC7B;EAEA,MAAM,iBAAiB,QAAgB,MAA2E;AAChH,UAAM,WAAW,MAAM,KAAK;MAC1B,mBAAmB,MAAM;MACzB;QACE,QAAQ;QACR,SAAS;UACP,gBAAgB;QAClB;QACA,MAAM,KAAK,UAAU,IAAI;MAC3B;MACA;IACF;AACA,WAAO,MAAM,SAAS,KAAK;EAC7B;EAEA,MAAM,iBAAiB,QAA+B;AACpD,UAAM,KAAK;MACT,mBAAmB,MAAM;MACzB,EAAE,QAAQ,SAAS;MACnB;IACF;EACF;EAEA,MAAM,oBAAoB,SAGyB;AACjD,UAAM,WAAW,MAAM,KAAK;MAC1B,8BAA8B,QAAQ,MAAM,IAAI,QAAQ,MAAM;MAC9D;QACE,QAAQ;QACR,SAAS;UACP,gBAAgB;QAClB;QACA,MAAM,KAAK,UAAU,CAAC,CAAC;MACzB;MACA;IACF;AACA,WAAO,MAAM,SAAS,KAAK;EAC7B;EAEA,MAAM,yBAAyB,SAG5B;AACD,UAAM,KAAK;MACT,8BAA8B,QAAQ,MAAM,IAAI,QAAQ,MAAM;MAC9D;QACE,QAAQ;QACR,SAAS;UACP,gBAAgB;QAClB;QACA,MAAM,KAAK,UAAU,CAAC,CAAC;MACzB;MACA;IACF;EACF;EAEA,MAAM,iBAAiB,QAAgB,QAA6E;AAClH,UAAM,WAAW,MAAM,KAAK;MAC1B,mBAAmB,MAAM;MACzB;QACE,QAAQ;QACR,SAAS;UACP,gBAAgB;QAClB;QACA,MAAM,KAAK,UAAU,MAAM;MAC7B;MACA;IACF;AACA,WAAO,MAAM,SAAS,KAAK;EAC7B;EAEA,MAAM,gCACJ,QACA,UACA,OAC4D;AAC5D,UAAM,WAAW,MAAM,KAAK;MAC1B,gCAAgC,MAAM,IAAI,QAAQ;MAClD;QACE,QAAQ;QACR,SAAS;UACP,gBAAgB;QAClB;QACA,MAAM,KAAK,UAAU,EAAE,MAAM,CAAC;MAChC;MACA;IACF;AACA,WAAO,MAAM,SAAS,KAAK;EAC7B;EAEA,MAAM,wBAAwB,QAAgB,iBAAyB,iBAAkF;AACvJ,UAAM,WAAW,MAAM,KAAK;MAC1B;MACA;QACE,QAAQ;QACR,SAAS;UACP,gBAAgB;QAClB;QACA,MAAM,KAAK,UAAU;UACnB,SAAS;UACT,mBAAmB;UACnB,kBAAkB;QACpB,CAAC;MACH;MACA;IACF;AACA,UAAM,SAAS,MAAM,SAAS,KAAK;AACnC,WAAO;MACL,aAAa,OAAO;MACpB,cAAc,OAAO;IACvB;EACF;EAEA,MAAM,gBACJ,SAIA;AACA,UAAM,KAAK;MACT,8BAA8B,QAAQ,MAAM,IAAI,QAAQ,MAAM;MAC9D;QACE,QAAQ;QACR,SAAS;UACP,gBAAgB;QAClB;QACA,MAAM,KAAK,UAAU,CAAC,CAAC;MACzB;MACA;IACF;EACF;EAEA,MAAM,8BAA8B,SAIjC;AACD,UAAM,KAAK;MACT,kCAAkC,QAAQ,MAAM,IAAI,QAAQ,MAAM;MAClE;QACE,QAAQ;QACR,SAAS;UACP,gBAAgB;QAClB;QACA,MAAM,KAAK,UAAU,QAAQ,OAAO;MACtC;MACA;IACF;EACF;EAEA,MAAM,8BAA8B,QAAgB,QAAgB,cAAsB;AACxF,UAAM,KAAK;MACT,8BAA8B,MAAM,IAAI,MAAM,IAAI,YAAY;MAC9D;QACE,QAAQ;QACR,SAAS;UACP,gBAAgB;QAClB;QACA,MAAM,KAAK,UAAU,CAAC,CAAC;MACzB;MACA;IACF;EACF;EAEA,MAAM,6BAA6B,QAAgB,cAAsB;AACvE,UAAM,KAAK;MACT,iCAAiC,MAAM,IAAI,YAAY;MACvD;QACE,QAAQ;QACR,SAAS;UACP,gBAAgB;QAClB;QACA,MAAM,KAAK,UAAU,CAAC,CAAC;MACzB;MACA;IACF;EACF;EAEA,MAAM,+BAA+B,QAAgB,QAAgB,cAAsB;AACzF,UAAM,KAAK;MACT,8BAA8B,MAAM,IAAI,MAAM,IAAI,YAAY;MAC9D;QACE,QAAQ;QACR,SAAS;UACP,gBAAgB;QAClB;QACA,MAAM,KAAK,UAAU,CAAC,CAAC;MACzB;MACA;IACF;EACF;EAEA,MAAM,8BAA8B,QAAgB,cAAsB;AACxE,UAAM,KAAK;MACT,iCAAiC,MAAM,IAAI,YAAY;MACvD;QACE,QAAQ;QACR,SAAS;UACP,gBAAgB;QAClB;QACA,MAAM,KAAK,UAAU,CAAC,CAAC;MACzB;MACA;IACF;EACF;EAEA,MAAM,iBAAiB,QAAgB;AACrC,UAAM,KAAK;MACT,mBAAmB,MAAM;MACzB;QACE,QAAQ;QACR,SAAS;UACP,gBAAgB;QAClB;QACA,MAAM,KAAK,UAAU,CAAC,CAAC;MACzB;MACA;IACF;EACF;EAEA,MAAM,2BACJ,MACgD;AAChD,UAAM,WAAW,MAAM,KAAK;MAC1B;MACA;QACE,QAAQ;QACR,SAAS;UACP,gBAAgB;QAClB;QACA,MAAM,KAAK,UAAU,IAAI;MAC3B;MACA;IACF;AACA,WAAO,MAAM,SAAS,KAAK;EAC7B;EAEA,MAAM,2BACJ,QACA,kBACA,MACgD;AAChD,UAAM,WAAW,MAAM,KAAK;MAC1B,8BAA8B,MAAM,IAAI,gBAAgB;MACxD;QACE,QAAQ;QACR,SAAS;UACP,gBAAgB;QAClB;QACA,MAAM,KAAK,UAAU,IAAI;MAC3B;MACA;IACF;AACA,WAAO,MAAM,SAAS,KAAK;EAC7B;EAEA,MAAM,2BACJ,QACA,kBACe;AACf,UAAM,KAAK;MACT,8BAA8B,MAAM,IAAI,gBAAgB;MACxD;QACE,QAAQ;MACV;MACA;IACF;EACF;EAEA,MAAM,0BACJ,QACkD;AAClD,UAAM,WAAW,MAAM,KAAK;MAC1B,sCAAsC,MAAM;MAC5C;QACE,QAAQ;MACV;MACA;IACF;AACA,UAAM,OAAO,MAAM,SAAS,KAAK;AACjC,WAAO,KAAK;EACd;EAEA,MAAM,0CACJ,QACA,kBACA,aACe;AACf,UAAM,KAAK;MACT,8BAA8B,MAAM,IAAI,gBAAgB;MACxD;QACE,QAAQ;QACR,SAAS;UACP,gBAAgB;QAClB;QACA,MAAM,KAAK,UAAU,EAAE,cAAc,YAAY,CAAC;MACpD;MACA;IACF;EACF;EAGA,MAAM,mBAAmB,QAA2D;AAClF,UAAM,WAAW,MAAM,KAAK;MAC1B,mCAAmC,MAAM;MACzC;QACE,QAAQ;MACV;MACA;IACF;AACA,WAAO,MAAM,SAAS,KAAK;EAC7B;EAEA,MAAM,oBAAoB,WAAmB;AAC3C,UAAM,KAAK;MACT,2BAA2B,SAAS;MACpC;QACE,QAAQ;MACV;MACA;IACF;EACF;EAGA,MAAM,yBAAyB,SAIb;AAChB,UAAM,KAAK;MACT;MACA;QACE,QAAQ;QACR,SAAS;UACP,gBAAgB;QAClB;QACA,MAAM,KAAK,UAAU;UACnB,OAAO,QAAQ;UACf,SAAS,QAAQ;UACjB,cAAc,QAAQ;QACxB,CAAC;MACH;MACA;IACF;EACF;EAEA,MAAM,eACJ,SAC8G;AAC9G,UAAM,MAAM,MAAM,KAAK;MACrB;MACA;QACE,QAAQ;QACR,SAAS;UACP,gBAAgB;QAClB;QACA,MAAM,KAAK,UAAU;UACnB,cAAc,QAAQ;UACtB,cAAc,QAAQ;QACxB,CAAC;MACH;MACA;MACA,CAAC,YAAY,8BAA8B,YAAY,0BAA0B;IACnF;AAEA,QAAI,IAAI,WAAW,SAAS;AAC1B,aAAO,IAAI;IACb;EACF;AACF;;;AChnBO,IAAM,sBAAN,cAAkC,qBAAqB;EAC5D,YAA4B,SAAsC;AAChE,UAAM,OAAO;AADa,SAAA,UAAA;EAE5B;EAEA,MAAa,iBAAiB,MAAc,SAAsB,SAAiC,cAAuB,SAAS;AACjI,WAAO,MAAM,KAAK;MAChB;MACA;QACE,GAAG;QACH,SAAS;UACP,kCAAkC,yBAAyB,KAAK,UAAU,KAAK,QAAQ,sBAAsB;UAC7G,GAAG,QAAQ;QACb;MACF;MACA;MACA;IACF;EACF;EAEA,MAAM,aAAqD;AACzD,UAAM,WAAW,MAAM,KAAK;MAC1B;MACA;QACE,QAAQ;MACV;MACA;IACF;AACA,WAAO,MAAM,SAAS,KAAK;EAC7B;EAEA,MAAM,cAAc,QAAiF;AACnG,UAAM,WAAW,MAAM,KAAK;MAC1B;MACA;QACE,QAAQ;QACR,SAAS;UACP,gBAAgB;QAClB;QACA,MAAM,KAAK,UAAU,MAAM;MAC7B;MACA;IACF;AACA,WAAO,MAAM,SAAS,KAAK;EAC7B;EAEA,MAAM,qBACJ,SAC2C;AAC3C,UAAM,WAAW,MAAM,KAAK;MAC1B;MACA;QACE,QAAQ;QACR,SAAS;UACP,gBAAgB;QAClB;QACA,MAAM,KAAK,UAAU,OAAO;MAC9B;MACA;IACF;AACA,WAAO,MAAM,SAAS,KAAK;EAC7B;EAEA,MAAM,sBAAuE;AAC3E,UAAM,WAAW,MAAM,KAAK,iBAAiB,sBAAsB,CAAC,GAAG,IAAI;AAC3E,UAAM,SAAS,MAAM,SAAS,KAAK;AACnC,WAAO,OAAO;EAChB;EAEA,MAAM,yBAAyB,IAAY;AACzC,UAAM,KAAK;MACT,sBAAsB,EAAE;MAAI;QAC1B,QAAQ;QACR,SAAS;UACP,gBAAgB;QAClB;QACA,MAAM,KAAK,UAAU;UACnB,SAAS;QACX,CAAC;MACH;MACA;IACF;EACF;EAEA,MAAM,kBAAkB,IAAY,SAAyE;AAC3G,UAAM,WAAW,MAAM,KAAK,iBAAiB,sBAAsB,EAAE,IAAI,CAAC,GAAG,OAAO;AACpF,WAAO,MAAM,SAAS,KAAK;EAC7B;EAEA,MAAM,qBAAoE;AACxE,UAAM,WAAW,MAAM,KAAK,iBAAiB,oBAAoB,CAAC,GAAG,IAAI;AACzE,UAAM,SAAS,MAAM,SAAS,KAAK;AACnC,WAAO,OAAO;EAChB;EAEA,MAAM,oBAAoB,MAAyB,MAAyF;AAC1I,UAAM,SAAS,MAAM,KAAK;MACxB,oBAAoB,IAAI;MACxB;QACE,QAAQ;QACR,SAAS;UACP,gBAAgB;QAClB;QACA,MAAM,KAAK,UAAU,IAAI;MAC3B;MACA;IACF;AACA,WAAO,MAAM,OAAO,KAAK;EAC3B;EAEA,MAAM,mBAAmB,MAAwC;AAC/D,UAAM,KAAK;MACT,oBAAoB,IAAI;MACxB,EAAE,QAAQ,SAAS;MACnB;IACF;EACF;;EAGA,MAAM,gCAA2F;AAC/F,UAAM,WAAW,MAAM,KAAK,iBAAiB,gCAAgC,CAAC,GAAG,IAAI;AACrF,UAAM,SAAS,MAAM,SAAS,KAAK;AACnC,WAAO,OAAO;EAChB;EAEA,MAAM,+BAA+B,MAAiH;AACpJ,UAAM,WAAW,MAAM,KAAK;MAC1B;MACA;QACE,QAAQ;QACR,SAAS;UACP,gBAAgB;QAClB;QACA,MAAM,KAAK,UAAU,IAAI;MAC3B;MACA;IACF;AACA,WAAO,MAAM,SAAS,KAAK;EAC7B;EAEA,MAAM,+BAA+B,cAAsB,MAAiH;AAC1K,UAAM,WAAW,MAAM,KAAK;MAC1B,gCAAgC,YAAY;MAC5C;QACE,QAAQ;QACR,SAAS;UACP,gBAAgB;QAClB;QACA,MAAM,KAAK,UAAU,IAAI;MAC3B;MACA;IACF;AACA,WAAO,MAAM,SAAS,KAAK;EAC7B;EAEA,MAAM,+BAA+B,cAAqC;AACxE,UAAM,KAAK;MACT,gCAAgC,YAAY;MAC5C,EAAE,QAAQ,SAAS;MACnB;IACF;EACF;EAEA,MAAM,mCAAiG;AACrG,UAAM,WAAW,MAAM,KAAK,iBAAiB,mCAAmC,CAAC,GAAG,IAAI;AACxF,UAAM,SAAS,MAAM,SAAS,KAAK;AACnC,WAAO,OAAO;EAChB;EAEA,MAAM,kCAAkC,MAAuH;AAC7J,UAAM,WAAW,MAAM,KAAK;MAC1B;MACA;QACE,QAAQ;QACR,SAAS;UACP,gBAAgB;QAClB;QACA,MAAM,KAAK,UAAU,IAAI;MAC3B;MACA;IACF;AACA,WAAO,MAAM,SAAS,KAAK;EAC7B;EAEA,MAAM,kCAAkC,cAAsB,MAAuH;AACnL,UAAM,WAAW,MAAM,KAAK;MAC1B,mCAAmC,YAAY;MAC/C;QACE,QAAQ;QACR,SAAS;UACP,gBAAgB;QAClB;QACA,MAAM,KAAK,UAAU,IAAI;MAC3B;MACA;IACF;AACA,WAAO,MAAM,SAAS,KAAK;EAC7B;EAEA,MAAM,kCAAkC,cAAqC;AAC3E,UAAM,KAAK;MACT,mCAAmC,YAAY;MAC/C,EAAE,QAAQ,SAAS;MACnB;IACF;EACF;EAEA,MAAM,eAAwD;AAC5D,UAAM,WAAW,MAAM,KAAK;MAC1B;MACA;QACE,QAAQ;QACR,SAAS;UACP,gBAAgB;QAClB;QACA,MAAM,KAAK,UAAU,CAAC,CAAC;MACzB;MACA;IACF;AACA,WAAO,MAAM,SAAS,KAAK;EAC7B;EAEA,MAAM,gBAA+B;AACnC,UAAM,KAAK;MACT;MACA;QACE,QAAQ;MACV;MACA;IACF;EACF;EAEA,MAAM,aAA2B;AAC/B,UAAM,WAAW,MAAM,KAAK;MAC1B;MACA;QACE,QAAQ;MACV;MACA;IACF;AACA,WAAO,MAAM,SAAS,KAAK;EAC7B;EAEA,MAAM,cAAc,MAUsC;AACxD,UAAM,WAAW,MAAM,KAAK,iBAAiB,6BAA6B;MACxE,QAAQ;MACR,SAAS;QACP,gBAAgB;MAClB;MACA,MAAM,KAAK,UAAU,IAAI;IAC3B,GAAG,IAAI;AACP,WAAO,MAAM,SAAS,KAAK;EAC7B;EAEA,MAAM,iBAA+D;AACnE,UAAM,WAAW,MAAM,KAAK,iBAAiB,oBAAoB;MAC/D,QAAQ;IACV,GAAG,IAAI;AACP,WAAO,MAAM,SAAS,KAAK;EAC7B;AACF;;;ACjSO,SAAS,cAAiB,UAA0E;AACzG,MAAI,SAAS;AACb,MAAI,gBAAyC;AAC7C,MAAI,UAA6B;AACjC,MAAI,SAAwB;AAC5B,QAAM,UAAU,IAAI,QAAW,CAAC,KAAK,QAAQ;AAC3C,cAAU,CAAC,UAAU;AACnB,UAAI,WAAW,UAAW;AAC1B,eAAS;AACT,sBAAgB;AAChB,UAAI,KAAK;IACX;AACA,aAAS,CAAC,WAAW;AACnB,UAAI,WAAW,UAAW;AAC1B,eAAS;AACT,sBAAgB;AAChB,UAAI,MAAM;IACZ;EACF,CAAC;AAED,WAAS,SAAU,MAAO;AAC1B,SAAO,OAAO,OAAO,SAAS;IAC5B;IACA,GAAG,WAAW,cAAc,EAAE,OAAO,cAAmB,IAAI,CAAC;IAC7D,GAAG,WAAW,aAAa,EAAE,QAAQ,cAAc,IAAI,CAAC;EAC1D,CAAQ;AACV;AAyCA,IAAI,gBAA0E;AAKvE,SAAS,SAAY,OAA2B;AACrD,oCAAkB,IAAI,gBAAkD;AACxE,MAAI,cAAc,IAAI,CAAC,KAAK,CAAC,GAAG;AAC9B,WAAO,cAAc,IAAI,CAAC,KAAK,CAAC;EAClC;AAEA,QAAM,MAAM,OAAO,OAAO,QAAQ,QAAQ,KAAK,GAAG;IAChD,QAAQ;IACR;EACF,CAAU;AACV,gBAAc,IAAI,CAAC,KAAK,GAAG,GAAG;AAC9B,SAAO;AACT;AAyBA,IAAI,gBAA0E;AAKvE,SAAS,SAAY,QAAkC;AAC5D,oCAAkB,IAAI,gBAAkD;AACxE,MAAI,cAAc,IAAI,CAAC,MAAM,CAAC,GAAG;AAC/B,WAAO,cAAc,IAAI,CAAC,MAAM,CAAC;EACnC;AAEA,QAAM,UAAU,QAAQ,OAAO,MAAM;AACrC,2BAAyB,OAAO;AAChC,QAAM,MAAM,OAAO,OAAO,SAAS;IACjC,QAAQ;IACR;EACF,CAAU;AACV,gBAAc,IAAI,CAAC,MAAM,GAAG,GAAG;AAC/B,SAAO;AACT;AA8BA,IAAM,sBAAsB,QAAQ,IAAI,QAAe,MAAM;AAAC,CAAC,CAAC;AACzD,SAAS,eAAoC;AAClD,SAAO;AACT;AAYO,SAAS,QAAW,SAAqB,UAA8C,CAAC,GAAoB;AACjH,QAAM,MAAM,QAAQ;IAClB,CAAA,UAAS;AACP,UAAI,SAAS;AACZ,UAAY,QAAQ;AACrB,aAAO;IACT;IACA,CAAA,iBAAgB;AACd,UAAI,SAAS;AACZ,UAAY,SAAS;AACtB,YAAM;IACR;EACF;AACA,MAAI,SAAS;AACb,SAAO;AACT;AAyBO,SAAS,yBAAiD,SAAkB;AACjF,UAAQ,MAAM,MAAM;EAAC,CAAC;AACxB;AAgBA,eAAsB,KAAK,IAAY;AACrC,MAAI,CAAC,OAAO,SAAS,EAAE,KAAK,KAAK,GAAG;AAClC,UAAM,IAAI,oBAAoB,kFAAkF,EAAE,KAAK;EACzH;AACA,MAAI,MAAM,KAAG,IAAI;AACf,UAAM,IAAI,oBAAoB,8EAA8E;EAC9G;AACA,SAAO,MAAM,IAAI,QAAc,CAAA,YAAW,WAAW,SAAS,EAAE,CAAC;AACnE;AAsBA,eAAsB,UAAU,MAAY;AAC1C,SAAO,MAAM,KAAK,KAAK,QAAQ,IAAI,KAAK,IAAI,CAAC;AAC/C;AAoBO,SAAS,8BAA8B,MAA4C;AACxF,SAAO;IACL,KAAK,CAAC;IACN;MACE,GAAG,KAAK,CAAC;MACT,SAAS,CAAA,UAAS;;AAChB,YAAI,WAAW,aAAa,KAAK,KAAK,OAAO,YAAY,gBAAgB,+CAA8B,SAAS,gBAAe;AAC7H,gBAAM,MAAM,OAAO;QACrB,OAAO;AACL,gBAAM,uCAAuC,OAAyC,kDAAkD,+BAA+B;;EAAO,KAAK,EAAE;QACvL;AACA,SAAAC,OAAAC,MAAA,KAAK,CAAC,MAAN,gBAAAA,IAAS,YAAT,gBAAAD,IAAA,KAAAC,KAAmB;MACrB;IACF;IACA,GAAG,KAAK,MAAM,CAAC;EACjB;AACF;AAeO,SAAS,kBACd,eACA,UAGI,CAAC,GACC;AACN,MAAI,OAAO,kBAAkB,YAAY;AACvC,oBAAgB,cAAc;EAChC;AACA,QAAM,cAAc,IAAI,MAAM;AAC9B,iDAAe,MAAM,CAAA,UAAS;;AAC5B,KAAAA,MAAA,QAAQ,YAAR,gBAAAA,IAAA,cAAkB;AAClB,UAAM,WAAW,IAAI;MACnB,8CAA8C,MAAM,SAAS;MAC7D,EAAE,OAAO,MAAM;IACjB;AACA,sBAAkB,UAAU,WAAW;AACvC,QAAI,CAAC,QAAQ,gBAAgB;AAC3B,mBAAa,qBAAqB,QAAQ;IAC5C;EACF;AACF;AAiBA,IAAM,eAAN,cAA2B,MAAM;EAC/B,YAA4B,IAAY;AACtC,UAAM,iBAAiB,EAAE,IAAI;AADH,SAAA,KAAA;AAE1B,SAAK,OAAO;EACd;AACF;AAEA,eAAsB,QAAW,SAAqB,IAA8C;AAClG,SAAO,MAAM,QAAQ,KAAK;IACxB,QAAQ,KAAK,CAAA,UAAS,OAAO,GAAG,KAAK,CAAC;IACtC,KAAK,EAAE,EAAE,KAAK,MAAM,OAAO,MAAM,IAAI,aAAa,EAAE,CAAC,CAAC;EACxD,CAAC;AACH;AAoBA,eAAsB,aAAgB,SAAqB,IAAwB;AACjF,SAAO,OAAO,QAAQ,MAAM,QAAQ,SAAS,EAAE,CAAC;AAClD;AAyCO,SAAS,YACd,MACA,SACkB;AAClB,MAAIC,aAAY,YAAY,IAAI;AAChC,MAAI,QAAkD,CAAC;AACvD,MAAI,wBAAwB,oBAAI,IAAA;AAEhC,QAAM,OAAO,YAAY;AACvB,WAAO,MAAM;AACX,UAAIA,aAAY,YAAY,IAAI,GAAG;AACjC,cAAM,KAAK,KAAK,IAAI,GAAGA,aAAY,YAAY,IAAI,IAAI,CAAC,CAAC;MAC3D,WAAW,MAAM,WAAW,GAAG;AAC7B,cAAM,OAAO,aAAa;AAC1B,cAAM,IAAI,QAAc,CAAA,YAAW;AACjC,gCAAsB,IAAI,MAAM,OAAO;QACzC,CAAC;AACD,8BAAsB,OAAO,IAAI;MACnC,OAAO;AACL;MACF;IACF;AACA,UAAM,YAAY,QAAQ,aAAa,MAAM,OAAO,GAAG,MAAM,MAAM,IAAI,CAAC,MAAM,MAAM,CAAE;AAEtF,UAAM,QAAQ,YAAY,IAAI;AAC9B,UAAM,QAAQ,MAAM,OAAO,YAAY,KAAK,CAAC;AAC7C,UAAM,MAAM,YAAY,IAAI;AAE5BA,iBAAY,KAAK;MACfA;MACA,SAAS,QAAQ,cAAc;MAC/B,OAAO,QAAQ,SAAS;IAC1B;AAEA,eAAW,YAAY,WAAW;AAChC,UAAI,MAAM,WAAW,MAAM;AACzB,iBAAS,CAAC,EAAE,MAAM,IAAI;MACxB,OAAO;AACL,iBAAS,CAAC,EAAE,MAAM,KAAK;MACzB;IACF;EACF;AAEA,oBAAkB,YAAY;AAC5B,WAAO,MAAM;AACX,YAAM,KAAK;IACb;EACF,CAAC;AAED,SAAO,MAAM;AACX,WAAO,IAAI,QAAW,CAAC,SAAS,WAAW;AACzCA,mBAAY,KAAK;QACfA;QACA,YAAY,IAAI,KAAK,QAAQ,cAAc;MAC7C;AACA,YAAM,KAAK,CAAC,SAAS,MAAM,CAAC;AAC5B,4BAAsB,QAAQ,CAAA,OAAM,GAAG,CAAC;IAC1C,CAAC;EACH;AACF;AAEO,SAAS,UAA8B,MAAkC,SAA6C;AAC3H,MAAIC,WAAgD;AACpD,MAAI,gBAAmC;AACvC,SAAO,UAAU,SAAS;AACxB,WAAO,kBAAkB,MAAM;AAC7B,YAAM;IACR;AACA,oBAAgB,IAAI,QAAW,CAAA,YAAW;AACxCA,iBAAU,WAAW,MAAM;AACzB,wBAAgB;AAChB,gBAAQ,KAAK,GAAG,IAAI,CAAC;MACvB,GAAG,OAAO;IACZ,CAAC;AACD,WAAO,MAAM;EACf;AACF;;;AC7eO,IAAM,SAAS;EACpB;EACA;EACA,aAAa;EACb,GAAM,MAA8C;AAClD,WAAO;MACL,QAAQ;MACR;IACF;EACF;EACA,MAAS,OAAkD;AACzD,WAAO;MACL,QAAQ;MACR;IACF;EACF;EACA,KAAK;EACL,IAAI,CAAU,QAAsB,aAAuB;AACzD,WAAO,OAAO,WAAW,OAAO,OAAO,OAAO;EAChD;EACA,SAAS,CAAO,WAA4B;AAC1C,QAAI,OAAO,WAAW,SAAS;AAC7B,YAAM,OAAO;IACf;AACA,WAAO,OAAO;EAChB;EACA,cAAc,OAAa,WAA8C;AACvE,WAAO,OAAO,QAAQ,MAAM,MAAM;EACpC;EACA;AACF;AA8CO,IAAM,cAAc;EACzB;EACA,aAAa;EACb,IAAI,OAAO;EACX,OAAO,OAAO;EACd,SAAAC;EACA,KAAK;EACL,IAAI,CAAa,QAA8B,aAAuB;AACpE,QAAI,OAAO,WAAW,WAAW;AAC/B,aAAO;IACT;AACA,WAAO,OAAO,GAAG,QAAQ,QAAQ;EACnC;EACA,SAAS,CAAU,WAAoC;AACrD,QAAI,OAAO,WAAW,WAAW;AAC/B,YAAM,IAAI,MAAM,sBAAsB;IACxC;AACA,WAAO,OAAO,QAAQ,MAAM;EAC9B;EACA;AACF;AAgCA,SAASA,SAAW,UAAoE;AACtF,SAAO;IACL,QAAQ;IACR;EACF;AACF;AAcA,eAAe,gBAAmB,SAAyC;AACzE,MAAI;AACF,UAAM,QAAQ,MAAM;AACpB,WAAO,OAAO,GAAG,KAAK;EACxB,SAAS,OAAO;AACd,WAAO,OAAO,MAAM,KAAK;EAC3B;AACF;AAoBA,SAAS,aAAgB,IAAiC;AACxD,MAAI;AACF,WAAO,OAAO,GAAG,GAAG,CAAC;EACvB,SAAS,OAAO;AACd,WAAO,OAAO,MAAM,KAAK;EAC3B;AACF;AAsBA,eAAe,kBAAqB,IAAmD;AACrF,MAAI;AACF,WAAO,OAAO,GAAG,MAAM,GAAG,CAAC;EAC7B,SAAS,OAAO;AACd,WAAO,OAAO,MAAM,KAAK;EAC3B;AACF;AAwBA,SAAS,UAA0C,QAA8B,IAA0C;AACzH,MAAI,OAAO,WAAW,QAAS,QAAO;IACpC,QAAQ;IACR,OAAO,OAAO;EAChB;AACA,MAAI,OAAO,WAAW,UAAW,QAAO;IACtC,QAAQ;IACR,GAAG,cAAc,SAAS,EAAE,UAAU,OAAO,SAAS,IAAI,CAAC;EAC7D;AAEA,SAAO,OAAO,GAAG,GAAG,OAAO,IAAI,CAAC;AAClC;AAqCA,IAAM,aAAN,cAAyB,eAAe;EACtC,YAA4B,QAAmB;AAC7C,UAAM,UAAU,OAAO,IAAI,CAAA,MAAK,OAAO,CAAC,CAAC;AACzC,UAAM,YAAY,QAAQ,SAAS,KAAK,QAAQ,MAAM,CAAA,MAAK,MAAM,QAAQ,CAAC,CAAC;AAC3E;MACE;MACA;oBACc,OAAO,MAAM;;QAEzB,YAAY;qBACC,OAAO,MAAM;YACtB,QAAQ,CAAC,CAAC;UACZ,QAAQ,IAAI,CAAC,GAAG,MAAM;oBACZ,IAAI,CAAC;cACX,CAAC;SACN,EAAE,KAAK,MAAM,CAAC;;MAEjB,EAAE,OAAO,OAAO,OAAO,SAAS,CAAC,EAAE;IACrC;AAjB0B,SAAA,SAAA;AAkB1B,SAAK,OAAO;EACd;EAEA,IAAI,WAAW;AACb,WAAO,KAAK,OAAO;EACrB;AACF;AACA,WAAW,UAAU,OAAO;AAmC5B,eAAe,MACb,IACA,eACA,EAAE,uBAAuB,IAAK,IAAI,CAAC,GACoB;AACvD,QAAM,SAAoB,CAAC;AAC3B,WAAS,IAAI,GAAG,IAAI,eAAe,KAAK;AACtC,UAAM,MAAM,MAAM,GAAG,CAAC;AACtB,QAAI,IAAI,WAAW,MAAM;AACvB,aAAO,OAAO,OAAO,OAAO,GAAG,IAAI,IAAI,GAAG,EAAE,UAAU,IAAI,EAAE,CAAC;IAC/D,OAAO;AACL,aAAO,KAAK,IAAI,KAAK;AACrB,UAAI,IAAI,gBAAgB,GAAG;AACzB,cAAM,MAAM,KAAK,OAAO,IAAI,OAAO,uBAAwB,KAAK,CAAE;MACpE;IACF;EACF;AACA,SAAO,OAAO,OAAO,OAAO,MAAM,IAAI,WAAW,MAAM,CAAC,GAAG,EAAE,UAAU,cAAc,CAAC;AACxF;;;AC9XO,IAAM,qBAAN,MAA2C;EAGhD,YAAY,OAAU;AACpB,QAAI,OAAO,YAAY,aAAa;AAClC,WAAK,OAAO,EAAE,OAAO,MAAM,MAAM;IACnC,OAAO;AACL,WAAK,OAAO,IAAI,QAAW,KAAK;IAClC;EACF;EAEA,QAAuB;AACrB,WAAO,KAAK,KAAK,MAAM;EACzB;AACF;AAhBA,IAAAC;AAAA,IAAAC;AAyCO,IAAM,kBAAN,MAA2C;EAIhD,YAAY,SAA+C;AA6C3D,SAACD,GAAA,IAAsB;AA5CrB,UAAM,gBAAgB,mCAAS,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,EAAE,OAAO,EAAE,CAAC,GAAG,QAAQ,IAAI,mBAAmB,EAAE,CAAC,CAAC,EAAE,CAAC;AACtG,SAAK,WAAW,IAAI,QAAQ,iBAAiB,CAAC,CAAC;AAC/C,SAAK,WAAW,IAAI,KAAI,+CAAe,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,YAAW,CAAC,CAAC;EACtE;EAEA,IAAI,KAAuB;;AACzB,YAAOA,MAAA,KAAK,SAAS,IAAI,GAAG,MAArB,gBAAAA,IAAwB;EACjC;EAEA,IAAI,KAAQ,OAAgB;AAC1B,UAAM,WAAW,KAAK,SAAS,IAAI,GAAG;AACtC,UAAM,UAAU,EAAE,OAAO,SAAQ,qCAAU,WAAU,IAAI,mBAAmB,GAAG,EAAE;AACjF,SAAK,SAAS,IAAI,KAAK,OAAO;AAC9B,SAAK,SAAS,IAAI,QAAQ,MAAM;AAChC,WAAO;EACT;EAEA,OAAO,KAAiB;AACtB,UAAM,MAAM,KAAK,SAAS,IAAI,GAAG;AACjC,QAAI,KAAK;AACP,WAAK,SAAS,OAAO,GAAG;AACxB,WAAK,SAAS,OAAO,IAAI,MAAM;AAC/B,aAAO;IACT;AACA,WAAO;EACT;EAEA,IAAI,KAAiB;AACnB,WAAO,KAAK,SAAS,IAAI,GAAG,KAAK,KAAK,SAAS,IAAI,KAAK,SAAS,IAAI,GAAG,EAAG,MAAM;EACnF;EAEA,GAAEC,MAAA,OAAO,UAaRD,MAAA,OAAO,aAbNC,IAAe,IAA8B;AAC7C,eAAW,UAAU,KAAK,UAAU;AAClC,YAAM,MAAM,OAAO,MAAM;AACzB,YAAM,WAAW,MAAM,KAAK,SAAS,IAAI,GAAG,IAAI;AAChD,UAAI,CAAC,KAAK;AAER,aAAK,SAAS,OAAO,MAAM;MAC7B,WAAW,UAAU;AACnB,cAAM,CAAC,KAAK,SAAS,KAAK;MAC5B;IACF;EACF;AAGF;AA3FA,IAAAD;AAAA,IAAAC;AAiKO,IAAM,eAAN,MAAyB;EAI9B,YAAY,SAA+C;AAgD3D,SAACD,IAAAA,IAAsB;AA/CrB,UAAM,eAAe,CAAC,GAAG,WAAW,CAAC,CAAC;AACtC,SAAK,gBAAgB,IAAI,IAAI,aAAa,OAAO,CAAC,MAAM,CAAC,KAAK,oBAAoB,EAAE,CAAC,CAAC,CAAC,CAAC;AACxF,SAAK,WAAW,IAAI,gBAAgB,aAAa,OAAO,CAAC,MAA6B,KAAK,oBAAoB,EAAE,CAAC,CAAC,CAAC,CAAC;EACvH;EAEQ,oBAAoB,KAA4B;AACtD,WAAQ,OAAO,QAAQ,YAAY,QAAQ,QAAU,OAAO,QAAQ,YAAY,OAAO,OAAO,GAAG,MAAM;EACzG;EAEA,IAAI,KAAuB;AACzB,QAAI,KAAK,oBAAoB,GAAG,GAAG;AACjC,aAAO,KAAK,SAAS,IAAI,GAAG;IAC9B,OAAO;AACL,aAAO,KAAK,cAAc,IAAI,GAAG;IACnC;EACF;EAEA,IAAI,KAAQ,OAAgB;AAC1B,QAAI,KAAK,oBAAoB,GAAG,GAAG;AACjC,WAAK,SAAS,IAAI,KAAK,KAAK;IAC9B,OAAO;AACL,WAAK,cAAc,IAAI,KAAK,KAAK;IACnC;AACA,WAAO;EACT;EAEA,OAAO,KAAiB;AACtB,QAAI,KAAK,oBAAoB,GAAG,GAAG;AACjC,aAAO,KAAK,SAAS,OAAO,GAAG;IACjC,OAAO;AACL,aAAO,KAAK,cAAc,OAAO,GAAG;IACtC;EACF;EAEA,IAAI,KAAiB;AACnB,QAAI,KAAK,oBAAoB,GAAG,GAAG;AACjC,aAAO,KAAK,SAAS,IAAI,GAAG;IAC9B,OAAO;AACL,aAAO,KAAK,cAAc,IAAI,GAAG;IACnC;EACF;EAEA,GAAEC,OAAA,OAAO,UAKRD,OAAA,OAAO,aALNC,KAAe,IAA8B;AAC7C,WAAO,KAAK;AACZ,WAAO,KAAK;EACd;AAGF;AAtNA,IAAAD;AAAA,IAAAC;AAkRO,IAAM,kBAAN,MAA0C;EAA1C,cAAA;AACL,SAAQ,SAAkC,EAAE,KAAK,IAAI,aAAa,GAAG,UAAU,OAAO,OAAO,OAAU;AA+EvG,SAACD,IAAAA,IAAsB;EAAA;EA7Ef,eAAe,OAAiD;AACtE,QAAI,MAAM,UAAU;AAClB,aAAO,OAAO,GAAG,MAAM,KAAK;IAC9B,OAAO;AACL,aAAO,OAAO,MAAM,MAAS;IAC/B;EACF;EAGQ,iBAAiB,cAAqB,OAAiD;AAC7F,QAAK,aAAa,WAAW,GAAI;AAC/B,aAAO,KAAK,eAAe,KAAK;IAClC,OAAO;AACL,YAAM,CAAC,KAAK,GAAG,IAAI,IAAI;AACvB,YAAM,WAAW,MAAM,IAAI,IAAI,GAAG;AAClC,UAAI,CAAC,UAAU;AACb,eAAO,OAAO,MAAM,MAAS;MAC/B;AACA,aAAO,KAAK,iBAAiB,MAAM,QAAQ;IAC7C;EACF;EAEQ,YAAY,cAAqB,OAAwB,OAAiD;AAChH,QAAI,aAAa,WAAW,GAAG;AAC7B,YAAM,MAAM,KAAK,eAAe,KAAK;AACrC,UAAI,MAAM,WAAW,MAAM;AACzB,cAAM,WAAW;AACjB,cAAM,QAAQ,MAAM;MACtB,OAAO;AACL,cAAM,WAAW;AACjB,cAAM,QAAQ;MAChB;AACA,aAAO;IACT,OAAO;AACL,YAAM,CAAC,KAAK,GAAG,IAAI,IAAI;AACvB,UAAI,WAAW,MAAM,IAAI,IAAI,GAAG;AAChC,UAAI,CAAC,UAAU;AACb,cAAM,IAAI,IAAI,KAAK,WAAW,EAAE,KAAK,IAAI,aAAa,GAAG,UAAU,OAAO,OAAO,OAAU,CAAC;MAC9F;AACA,aAAO,KAAK,YAAY,MAAM,OAAO,QAAQ;IAC/C;EACF;EAEA,CAAS,cAAc,cAAqB,OAA0D;AACpG,QAAI,MAAM,UAAU;AAClB,YAAM,CAAC,cAAmB,MAAM,KAAK;IACvC;AACA,eAAW,CAAC,KAAK,KAAK,KAAK,MAAM,KAAK;AACpC,aAAO,KAAK,cAAc,CAAC,GAAG,cAAc,GAAG,GAAG,KAAK;IACzD;EACF;EAEA,IAAI,cAAgC;AAClC,WAAO,OAAO,GAAG,KAAK,iBAAiB,cAAc,KAAK,MAAM,GAAG,MAAS;EAC9E;EAEA,IAAI,cAAiB,OAAgB;AACnC,SAAK,YAAY,cAAc,OAAO,GAAG,KAAK,GAAG,KAAK,MAAM;AAC5D,WAAO;EACT;EAEA,OAAO,cAA0B;AAC/B,WAAO,KAAK,YAAY,cAAc,OAAO,MAAM,MAAS,GAAG,KAAK,MAAM,EAAE,WAAW;EACzF;EAEA,IAAI,cAA0B;AAC5B,WAAO,KAAK,iBAAiB,cAAc,KAAK,MAAM,EAAE,WAAW;EACrE;EAEA,QAAc;AACZ,SAAK,SAAS,EAAE,KAAK,IAAI,aAAa,GAAG,UAAU,OAAO,OAAO,OAAU;EAC7E;EAEA,GAAEC,OAAA,OAAO,UAIRD,OAAA,OAAO,aAJNC,KAAe,IAA8B;AAC7C,WAAO,KAAK,cAAc,CAAC,GAAG,KAAK,MAAM;EAC3C;AAGF;", "names": ["CHUNK_SIZE", "decodeBase64", "message", "_a", "message", "types", "_a", "types", "_a", "_b", "base64url_exports", "decode", "encode", "encode", "decode", "jwt", "decode", "rejected", "USER_AGENT", "_b", "_a", "encoder", "decoder", "message", "isCryptoKey", "clockSkew", "clockTolerance", "_a", "_b", "_b", "_a", "waitUntil", "timeout", "pending", "_a", "_b"]}