import "./chunk-GVADET3V.js";
import "./chunk-QNRW4OZD.js";
import {
  KnownErrors
} from "./chunk-REXVUZ33.js";
import "./chunk-UFCUR7KH.js";
import "./chunk-JBDVY4WB.js";
import "./chunk-LWWYEWPG.js";
import "./chunk-Z26222H5.js";
import "./chunk-4BLY47KI.js";
import "./chunk-GMJRCDEU.js";
import "./chunk-7UVSMXVG.js";

// package/@stackframe/react/node_modules/@stackframe/stack-shared/dist/esm/helpers/password.js
var minLength = 8;
var maxLength = 70;
function getPasswordError(password) {
  if (password.length < minLength) {
    return new KnownErrors.PasswordTooShort(minLength);
  }
  if (password.length > maxLength) {
    return new KnownErrors.PasswordTooLong(maxLength);
  }
  return void 0;
}
export {
  getPasswordError
};
//# sourceMappingURL=@stackframe_stack-shared_dist_helpers_password.js.map
