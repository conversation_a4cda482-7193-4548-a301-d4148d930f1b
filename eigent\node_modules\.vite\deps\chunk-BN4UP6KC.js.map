{"version": 3, "sources": ["../../../package/@stackframe/react/node_modules/@stackframe/stack-shared/src/utils/react.tsx"], "sourcesContent": ["import React from \"react\";\nimport { isB<PERSON>er<PERSON>ike } from \"./env\";\nimport { neverResolve } from \"./promises\";\nimport { deindent } from \"./strings\";\n\nexport function forwardRefIfNeeded<T, P = {}>(render: React.ForwardRefRenderFunction<T, P>): React.FC<P & { ref?: React.Ref<T> }> {\n  // TODO: when we drop support for react 18, remove this\n\n  const version = React.version;\n  const major = parseInt(version.split(\".\")[0]);\n  if (major < 19) {\n    return React.forwardRef<T, P>(render as any) as any;\n  } else {\n    return ((props: P) => render(props, (props as any).ref)) as any;\n  }\n}\nundefined?.test(\"forwardRefIfNeeded\", ({ expect }) => {\n  // Mock React.version and React.forwardRef\n  const originalVersion = React.version;\n  const originalForwardRef = React.forwardRef;\n\n  try {\n    // Test with React version < 19\n    Object.defineProperty(React, 'version', { value: '18.2.0', writable: true });\n\n    // Create a render function\n    const renderFn = (props: any, ref: any) => null;\n\n    // Call forwardRefIfNeeded\n    const result = forwardRefIfNeeded(renderFn);\n\n    // Verify the function returns something\n    expect(result).toBeDefined();\n\n    // Test with React version >= 19\n    Object.defineProperty(React, 'version', { value: '19.0.0', writable: true });\n\n    // Call forwardRefIfNeeded again with React 19\n    const result19 = forwardRefIfNeeded(renderFn);\n\n    // Verify the function returns something\n    expect(result19).toBeDefined();\n  } finally {\n    // Restore original values\n    Object.defineProperty(React, 'version', { value: originalVersion });\n    React.forwardRef = originalForwardRef;\n  }\n});\n\nexport function getNodeText(node: React.ReactNode): string {\n  if ([\"number\", \"string\"].includes(typeof node)) {\n    return `${node}`;\n  }\n  if (!node) {\n    return \"\";\n  }\n  if (Array.isArray(node)) {\n    return node.map(getNodeText).join(\"\");\n  }\n  if (typeof node === \"object\" && \"props\" in node) {\n    return getNodeText(node.props.children);\n  }\n  throw new Error(`Unknown node type: ${typeof node}`);\n}\nundefined?.test(\"getNodeText\", ({ expect }) => {\n  // Test with string\n  expect(getNodeText(\"hello\")).toBe(\"hello\");\n\n  // Test with number\n  expect(getNodeText(42)).toBe(\"42\");\n\n  // Test with null/undefined\n  expect(getNodeText(null)).toBe(\"\");\n  expect(getNodeText(undefined)).toBe(\"\");\n\n  // Test with array\n  expect(getNodeText([\"hello\", \" \", \"world\"])).toBe(\"hello world\");\n  expect(getNodeText([1, 2, 3])).toBe(\"123\");\n\n  // Test with mixed array\n  expect(getNodeText([\"hello\", 42, null])).toBe(\"hello42\");\n\n  // Test with React element (mocked)\n  const mockElement = {\n    props: {\n      children: \"child text\"\n    }\n  } as React.ReactElement;\n  expect(getNodeText(mockElement)).toBe(\"child text\");\n\n  // Test with nested React elements\n  const nestedElement = {\n    props: {\n      children: {\n        props: {\n          children: \"nested text\"\n        }\n      } as React.ReactElement\n    }\n  } as React.ReactElement;\n  expect(getNodeText(nestedElement)).toBe(\"nested text\");\n\n  // Test with array of React elements\n  const arrayOfElements = [\n    { props: { children: \"first\" } } as React.ReactElement,\n    { props: { children: \"second\" } } as React.ReactElement\n  ];\n  expect(getNodeText(arrayOfElements)).toBe(\"firstsecond\");\n});\n\n/**\n * Suspends the currently rendered component indefinitely. Will not unsuspend unless the component rerenders.\n *\n * You can use this to translate older query- or AsyncResult-based code to new the Suspense system, for example: `if (query.isLoading) suspend();`\n */\nexport function suspend(): never {\n  React.use(neverResolve());\n  throw new Error(\"Somehow a Promise that never resolves was resolved?\");\n}\n\nexport class NoSuspenseBoundaryError extends Error {\n  digest: string;\n  reason: string;\n\n  constructor(options: { caller?: string }) {\n    super(deindent`\n      ${options.caller ?? \"This code path\"} attempted to display a loading indicator, but didn't find a Suspense boundary above it. Please read the error message below carefully.\n      \n      The fix depends on which of the 3 scenarios caused it:\n      \n      1. You are missing a loading.tsx file in your app directory. Fix it by adding a loading.tsx file in your app directory.\n\n      2. The component is rendered in the root (outermost) layout.tsx or template.tsx file. Next.js does not wrap those files in a Suspense boundary, even if there is a loading.tsx file in the same folder. To fix it, wrap your layout inside a route group like this:\n\n        - app\n        - - layout.tsx  // contains <html> and <body>, alongside providers and other components that don't need ${options.caller ?? \"this code path\"}\n        - - loading.tsx  // required for suspense\n        - - (main)\n        - - - layout.tsx  // contains the main layout of your app, like a sidebar or a header, and can use ${options.caller ?? \"this code path\"}\n        - - - route.tsx  // your actual main page\n        - - - the rest of your app\n\n        For more information on this approach, see Next's documentation on route groups: https://nextjs.org/docs/app/building-your-application/routing/route-groups\n      \n      3. You caught this error with try-catch or a custom error boundary. Fix this by rethrowing the error or not catching it in the first place.\n\n      See: https://nextjs.org/docs/messages/missing-suspense-with-csr-bailout\n\n      More information on SSR and Suspense boundaries: https://react.dev/reference/react/Suspense#providing-a-fallback-for-server-errors-and-client-only-content\n    `);\n\n    this.name = \"NoSuspenseBoundaryError\";\n    this.reason = options.caller ?? \"suspendIfSsr()\";\n\n    // set the digest so nextjs doesn't log the error\n    // https://github.com/vercel/next.js/blob/d01d6d9c35a8c2725b3d74c1402ab76d4779a6cf/packages/next/src/shared/lib/lazy-dynamic/bailout-to-csr.ts#L14\n    this.digest = \"BAILOUT_TO_CLIENT_SIDE_RENDERING\";\n  }\n}\nundefined?.test(\"NoSuspenseBoundaryError\", ({ expect }) => {\n  // Test with default options\n  const defaultError = new NoSuspenseBoundaryError({});\n  expect(defaultError.name).toBe(\"NoSuspenseBoundaryError\");\n  expect(defaultError.reason).toBe(\"suspendIfSsr()\");\n  expect(defaultError.digest).toBe(\"BAILOUT_TO_CLIENT_SIDE_RENDERING\");\n  expect(defaultError.message).toContain(\"This code path attempted to display a loading indicator\");\n\n  // Test with custom caller\n  const customError = new NoSuspenseBoundaryError({ caller: \"CustomComponent\" });\n  expect(customError.name).toBe(\"NoSuspenseBoundaryError\");\n  expect(customError.reason).toBe(\"CustomComponent\");\n  expect(customError.digest).toBe(\"BAILOUT_TO_CLIENT_SIDE_RENDERING\");\n  expect(customError.message).toContain(\"CustomComponent attempted to display a loading indicator\");\n\n  // Verify error message contains all the necessary information\n  expect(customError.message).toContain(\"loading.tsx\");\n  expect(customError.message).toContain(\"route groups\");\n  expect(customError.message).toContain(\"https://nextjs.org/docs/messages/missing-suspense-with-csr-bailout\");\n});\n\n\n/**\n * Use this in a component or a hook to disable SSR. Should be wrapped in a Suspense boundary, or it will throw an error.\n */\nexport function suspendIfSsr(caller?: string) {\n  if (!isBrowserLike()) {\n    throw new NoSuspenseBoundaryError({ caller });\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;AAAA,mBAAkB;AAKX,SAAS,mBAA8B,QAAoF;AAGhI,QAAM,UAAU,aAAAA,QAAM;AACtB,QAAM,QAAQ,SAAS,QAAQ,MAAM,GAAG,EAAE,CAAC,CAAC;AAC5C,MAAI,QAAQ,IAAI;AACd,WAAO,aAAAA,QAAM,WAAiB,MAAa;EAC7C,OAAO;AACL,WAAQ,CAAC,UAAa,OAAO,OAAQ,MAAc,GAAG;EACxD;AACF;AAkCO,SAAS,YAAY,MAA+B;AACzD,MAAI,CAAC,UAAU,QAAQ,EAAE,SAAS,OAAO,IAAI,GAAG;AAC9C,WAAO,GAAG,IAAI;EAChB;AACA,MAAI,CAAC,MAAM;AACT,WAAO;EACT;AACA,MAAI,MAAM,QAAQ,IAAI,GAAG;AACvB,WAAO,KAAK,IAAI,WAAW,EAAE,KAAK,EAAE;EACtC;AACA,MAAI,OAAO,SAAS,YAAY,WAAW,MAAM;AAC/C,WAAO,YAAY,KAAK,MAAM,QAAQ;EACxC;AACA,QAAM,IAAI,MAAM,sBAAsB,OAAO,IAAI,EAAE;AACrD;AAoDO,SAAS,UAAiB;AAC/B,eAAAA,QAAM,IAAI,aAAa,CAAC;AACxB,QAAM,IAAI,MAAM,qDAAqD;AACvE;AAEO,IAAM,0BAAN,cAAsC,MAAM;EAIjD,YAAY,SAA8B;AACxC,UAAM;QACF,QAAQ,UAAU,gBAAgB;;;;;;;;;kHASwE,QAAQ,UAAU,gBAAgB;;;6GAGvC,QAAQ,UAAU,gBAAgB;;;;;;;;;;;KAW1I;AAED,SAAK,OAAO;AACZ,SAAK,SAAS,QAAQ,UAAU;AAIhC,SAAK,SAAS;EAChB;AACF;AA0BO,SAAS,aAAa,QAAiB;AAC5C,MAAI,CAAC,cAAc,GAAG;AACpB,UAAM,IAAI,wBAAwB,EAAE,OAAO,CAAC;EAC9C;AACF;", "names": ["React"]}