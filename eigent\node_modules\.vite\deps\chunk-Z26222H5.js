import {
  globalVar
} from "./chunk-4BLY47KI.js";

// package/@stackframe/react/node_modules/@stackframe/stack-shared/dist/esm/utils/arrays.js
function findLastIndex(arr, predicate) {
  for (let i = arr.length - 1; i >= 0; i--) {
    if (predicate(arr[i])) return i;
  }
  return -1;
}
function unique(arr) {
  return [...new Set(arr)];
}

// package/@stackframe/react/node_modules/@stackframe/stack-shared/dist/esm/utils/functions.js
function identity(t) {
  return t;
}
function identityArgs(...args) {
  return args;
}

// package/@stackframe/react/node_modules/@stackframe/stack-shared/dist/esm/utils/objects.js
function isNotNull(value) {
  return value !== null && value !== void 0;
}
function deepPlainEquals(obj1, obj2, options = {}) {
  if (typeof obj1 !== typeof obj2) return false;
  if (obj1 === obj2) return true;
  switch (typeof obj1) {
    case "object": {
      if (!obj1 || !obj2) return false;
      if (Array.isArray(obj1) || Array.isArray(obj2)) {
        if (!Array.isArray(obj1) || !Array.isArray(obj2)) return false;
        if (obj1.length !== obj2.length) return false;
        return obj1.every((v, i) => deepPlainEquals(v, obj2[i], options));
      }
      const entries1 = Object.entries(obj1).filter(([k, v]) => !options.ignoreUndefinedValues || v !== void 0);
      const entries2 = Object.entries(obj2).filter(([k, v]) => !options.ignoreUndefinedValues || v !== void 0);
      if (entries1.length !== entries2.length) return false;
      return entries1.every(([k, v1]) => {
        const e2 = entries2.find(([k2]) => k === k2);
        if (!e2) return false;
        return deepPlainEquals(v1, e2[1], options);
      });
    }
    case "undefined":
    case "string":
    case "number":
    case "boolean":
    case "bigint":
    case "symbol":
    case "function": {
      return false;
    }
    default: {
      throw new Error("Unexpected typeof " + typeof obj1);
    }
  }
}
function isCloneable(obj) {
  return typeof obj !== "symbol" && typeof obj !== "function";
}
function shallowClone(obj) {
  if (!isCloneable(obj)) throw new StackAssertionError("shallowClone does not support symbols or functions", { obj });
  if (Array.isArray(obj)) return obj.map(identity);
  return { ...obj };
}
function deepPlainClone(obj) {
  if (typeof obj === "function") throw new StackAssertionError("deepPlainClone does not support functions");
  if (typeof obj === "symbol") throw new StackAssertionError("deepPlainClone does not support symbols");
  if (typeof obj !== "object" || !obj) return obj;
  if (Array.isArray(obj)) return obj.map(deepPlainClone);
  return Object.fromEntries(Object.entries(obj).map(([k, v]) => [k, deepPlainClone(v)]));
}
function deepMerge(baseObj, mergeObj) {
  if ([baseObj, mergeObj, ...Object.values(baseObj), ...Object.values(mergeObj)].some((o) => !isCloneable(o))) throw new StackAssertionError("deepMerge does not support functions or symbols", { baseObj, mergeObj });
  const res = shallowClone(baseObj);
  for (const [key, mergeValue] of Object.entries(mergeObj)) {
    if (has(res, key)) {
      const baseValue = get(res, key);
      if (isObjectLike(baseValue) && isObjectLike(mergeValue)) {
        set(res, key, deepMerge(baseValue, mergeValue));
        continue;
      }
    }
    set(res, key, mergeValue);
  }
  return res;
}
function typedEntries(obj) {
  return Object.entries(obj);
}
function typedFromEntries(entries) {
  return Object.fromEntries(entries);
}
function typedKeys(obj) {
  return Object.keys(obj);
}
function typedValues(obj) {
  return Object.values(obj);
}
function typedAssign(target, source) {
  return Object.assign(target, source);
}
function filterUndefined(obj) {
  return Object.fromEntries(Object.entries(obj).filter(([, v]) => v !== void 0));
}
function filterUndefinedOrNull(obj) {
  return Object.fromEntries(Object.entries(obj).filter(([, v]) => v !== void 0 && v !== null));
}
function deepFilterUndefined(obj) {
  return Object.fromEntries(Object.entries(obj).filter(([, v]) => v !== void 0).map(([k, v]) => [k, isObjectLike(v) ? deepFilterUndefined(v) : v]));
}
function pick(obj, keys) {
  return Object.fromEntries(Object.entries(obj).filter(([k]) => keys.includes(k)));
}
function omit(obj, keys) {
  if (!Array.isArray(keys)) throw new StackAssertionError("omit: keys must be an array", { obj, keys });
  return Object.fromEntries(Object.entries(obj).filter(([k]) => !keys.includes(k)));
}
function split(obj, keys) {
  return [pick(obj, keys), omit(obj, keys)];
}
function mapValues(obj, fn) {
  if (Array.isArray(obj)) {
    return obj.map((v) => fn(v));
  }
  return Object.fromEntries(Object.entries(obj).map(([k, v]) => [k, fn(v)]));
}
function sortKeys(obj) {
  if (Array.isArray(obj)) {
    return [...obj];
  }
  return Object.fromEntries(Object.entries(obj).sort(([a], [b]) => stringCompare(a, b)));
}
function deepSortKeys(obj) {
  return sortKeys(mapValues(obj, (v) => isObjectLike(v) ? deepSortKeys(v) : v));
}
function set(obj, key, value) {
  Object.defineProperty(obj, key, { value, writable: true, configurable: true, enumerable: true });
}
function get(obj, key) {
  const descriptor = Object.getOwnPropertyDescriptor(obj, key);
  if (!descriptor) throw new StackAssertionError(`get: key ${String(key)} does not exist`, { obj, key });
  return descriptor.value;
}
function getOrUndefined(obj, key) {
  return has(obj, key) ? get(obj, key) : void 0;
}
function has(obj, key) {
  return Object.prototype.hasOwnProperty.call(obj, key);
}
function hasAndNotUndefined(obj, key) {
  return has(obj, key) && get(obj, key) !== void 0;
}
function deleteKey(obj, key) {
  if (has(obj, key)) {
    Reflect.deleteProperty(obj, key);
  } else {
    throw new StackAssertionError(`deleteKey: key ${String(key)} does not exist`, { obj, key });
  }
}
function isObjectLike(value) {
  return (typeof value === "object" || typeof value === "function") && value !== null;
}

// package/@stackframe/react/node_modules/@stackframe/stack-shared/dist/esm/utils/errors.js
function throwErr(...args) {
  if (typeof args[0] === "string") {
    throw new StackAssertionError(args[0], args[1]);
  } else if (args[0] instanceof Error) {
    throw args[0];
  } else {
    throw new StatusError(...args);
  }
}
function removeStacktraceNameLine(stack) {
  var _a;
  const addsNameLine = (_a = new Error().stack) == null ? void 0 : _a.startsWith("Error\n");
  return stack.split("\n").slice(addsNameLine ? 1 : 0).join("\n");
}
function concatStacktraces(first, ...errors) {
  var _a;
  const addsEmptyLineAtEnd = (_a = first.stack) == null ? void 0 : _a.endsWith("\n");
  const separator = removeStacktraceNameLine(new Error().stack ?? "").split("\n")[0];
  for (const error of errors) {
    const toAppend = removeStacktraceNameLine(error.stack ?? "");
    first.stack += (addsEmptyLineAtEnd ? "" : "\n") + separator + "\n" + toAppend;
  }
}
var StackAssertionError = class extends Error {
  constructor(message, extraData) {
    const disclaimer = `

This is likely an error in Stack. Please make sure you are running the newest version and report it.`;
    super(`${message}${message.endsWith(disclaimer) ? "" : disclaimer}`, pick(extraData ?? {}, ["cause"]));
    this.extraData = extraData;
    Object.defineProperty(this, "customCaptureExtraArgs", {
      get() {
        return [this.extraData];
      },
      enumerable: false
    });
  }
};
StackAssertionError.prototype.name = "StackAssertionError";
function errorToNiceString(error) {
  if (!(error instanceof Error)) return `${typeof error}<${nicify(error)}>`;
  return nicify(error, { maxDepth: 8 });
}
var errorSinks = /* @__PURE__ */ new Set();
function registerErrorSink(sink) {
  if (errorSinks.has(sink)) {
    return;
  }
  errorSinks.add(sink);
}
registerErrorSink((location, error, ...extraArgs) => {
  console.error(
    `\x1B[41mCaptured error in ${location}:`,
    // HACK: Log a nicified version of the error to get around buggy Next.js pretty-printing
    // https://www.reddit.com/r/nextjs/comments/1gkxdqe/comment/m19kxgn/?utm_source=share&utm_medium=web3x&utm_name=web3xcss&utm_term=1&utm_content=share_button
    errorToNiceString(error),
    ...extraArgs,
    "\x1B[0m"
  );
});
registerErrorSink((location, error, ...extraArgs) => {
  globalVar.stackCapturedErrors = globalVar.stackCapturedErrors ?? [];
  globalVar.stackCapturedErrors.push({ location, error, extraArgs });
});
function captureError(location, error) {
  for (const sink of errorSinks) {
    sink(
      location,
      error,
      ...error && (typeof error === "object" || typeof error === "function") && "customCaptureExtraArgs" in error && Array.isArray(error.customCaptureExtraArgs) ? error.customCaptureExtraArgs : []
    );
  }
}
var StatusError = class extends Error {
  constructor(status, message) {
    if (typeof status === "object") {
      message ?? (message = status.message);
      status = status.statusCode;
    }
    super(message);
    this.__stackStatusErrorBrand = "stack-status-error-brand-sentinel";
    this.name = "StatusError";
    this.statusCode = status;
    if (!message) {
      throw new StackAssertionError("StatusError always requires a message unless a Status object is passed", { cause: this });
    }
  }
  static isStatusError(error) {
    return typeof error === "object" && error !== null && "__stackStatusErrorBrand" in error && error.__stackStatusErrorBrand === "stack-status-error-brand-sentinel";
  }
  isClientError() {
    return this.statusCode >= 400 && this.statusCode < 500;
  }
  isServerError() {
    return !this.isClientError();
  }
  getStatusCode() {
    return this.statusCode;
  }
  getBody() {
    return new TextEncoder().encode(this.message);
  }
  getHeaders() {
    return {
      "Content-Type": ["text/plain; charset=utf-8"]
    };
  }
  toDescriptiveJson() {
    return {
      status_code: this.getStatusCode(),
      message: this.message,
      headers: this.getHeaders()
    };
  }
  /**
   * @deprecated this is not a good way to make status errors human-readable, use toDescriptiveJson instead
   */
  toHttpJson() {
    return {
      status_code: this.statusCode,
      body: this.message,
      headers: this.getHeaders()
    };
  }
};
StatusError.BadRequest = { statusCode: 400, message: "Bad Request" };
StatusError.Unauthorized = { statusCode: 401, message: "Unauthorized" };
StatusError.PaymentRequired = { statusCode: 402, message: "Payment Required" };
StatusError.Forbidden = { statusCode: 403, message: "Forbidden" };
StatusError.NotFound = { statusCode: 404, message: "Not Found" };
StatusError.MethodNotAllowed = { statusCode: 405, message: "Method Not Allowed" };
StatusError.NotAcceptable = { statusCode: 406, message: "Not Acceptable" };
StatusError.ProxyAuthenticationRequired = { statusCode: 407, message: "Proxy Authentication Required" };
StatusError.RequestTimeout = { statusCode: 408, message: "Request Timeout" };
StatusError.Conflict = { statusCode: 409, message: "Conflict" };
StatusError.Gone = { statusCode: 410, message: "Gone" };
StatusError.LengthRequired = { statusCode: 411, message: "Length Required" };
StatusError.PreconditionFailed = { statusCode: 412, message: "Precondition Failed" };
StatusError.PayloadTooLarge = { statusCode: 413, message: "Payload Too Large" };
StatusError.URITooLong = { statusCode: 414, message: "URI Too Long" };
StatusError.UnsupportedMediaType = { statusCode: 415, message: "Unsupported Media Type" };
StatusError.RangeNotSatisfiable = { statusCode: 416, message: "Range Not Satisfiable" };
StatusError.ExpectationFailed = { statusCode: 417, message: "Expectation Failed" };
StatusError.ImATeapot = { statusCode: 418, message: "I'm a teapot" };
StatusError.MisdirectedRequest = { statusCode: 421, message: "Misdirected Request" };
StatusError.UnprocessableEntity = { statusCode: 422, message: "Unprocessable Entity" };
StatusError.Locked = { statusCode: 423, message: "Locked" };
StatusError.FailedDependency = { statusCode: 424, message: "Failed Dependency" };
StatusError.TooEarly = { statusCode: 425, message: "Too Early" };
StatusError.UpgradeRequired = { statusCode: 426, message: "Upgrade Required" };
StatusError.PreconditionRequired = { statusCode: 428, message: "Precondition Required" };
StatusError.TooManyRequests = { statusCode: 429, message: "Too Many Requests" };
StatusError.RequestHeaderFieldsTooLarge = { statusCode: 431, message: "Request Header Fields Too Large" };
StatusError.UnavailableForLegalReasons = { statusCode: 451, message: "Unavailable For Legal Reasons" };
StatusError.InternalServerError = { statusCode: 500, message: "Internal Server Error" };
StatusError.NotImplemented = { statusCode: 501, message: "Not Implemented" };
StatusError.BadGateway = { statusCode: 502, message: "Bad Gateway" };
StatusError.ServiceUnavailable = { statusCode: 503, message: "Service Unavailable" };
StatusError.GatewayTimeout = { statusCode: 504, message: "Gateway Timeout" };
StatusError.HTTPVersionNotSupported = { statusCode: 505, message: "HTTP Version Not Supported" };
StatusError.VariantAlsoNegotiates = { statusCode: 506, message: "Variant Also Negotiates" };
StatusError.InsufficientStorage = { statusCode: 507, message: "Insufficient Storage" };
StatusError.LoopDetected = { statusCode: 508, message: "Loop Detected" };
StatusError.NotExtended = { statusCode: 510, message: "Not Extended" };
StatusError.NetworkAuthenticationRequired = { statusCode: 511, message: "Network Authentication Required" };
StatusError.prototype.name = "StatusError";

// package/@stackframe/react/node_modules/@stackframe/stack-shared/dist/esm/utils/strings.js
function typedToLowercase(s) {
  if (typeof s !== "string") throw new StackAssertionError("Expected a string for typedToLowercase", { s });
  return s.toLowerCase();
}
function typedToUppercase(s) {
  if (typeof s !== "string") throw new StackAssertionError("Expected a string for typedToUppercase", { s });
  return s.toUpperCase();
}
function typedCapitalize(s) {
  return s.charAt(0).toUpperCase() + s.slice(1);
}
function stringCompare(a, b) {
  if (typeof a !== "string" || typeof b !== "string") throw new StackAssertionError(`Expected two strings for stringCompare, found ${typeof a} and ${typeof b}`, { a, b });
  const cmp = (a2, b2) => a2 < b2 ? -1 : a2 > b2 ? 1 : 0;
  return cmp(a.toUpperCase(), b.toUpperCase()) || cmp(b, a);
}
function getWhitespacePrefix(s) {
  return s.substring(0, s.length - s.trimStart().length);
}
function getWhitespaceSuffix(s) {
  return s.substring(s.trimEnd().length);
}
function trimEmptyLinesStart(s) {
  const lines = s.split("\n");
  const firstNonEmptyLineIndex = lines.findIndex((line) => line.trim() !== "");
  if (firstNonEmptyLineIndex === -1) return "";
  return lines.slice(firstNonEmptyLineIndex).join("\n");
}
function trimEmptyLinesEnd(s) {
  const lines = s.split("\n");
  const lastNonEmptyLineIndex = findLastIndex(lines, (line) => line.trim() !== "");
  return lines.slice(0, lastNonEmptyLineIndex + 1).join("\n");
}
function trimLines(s) {
  return trimEmptyLinesEnd(trimEmptyLinesStart(s));
}
function templateIdentity(strings, ...values) {
  if (values.length !== strings.length - 1) throw new StackAssertionError("Invalid number of values; must be one less than strings", { strings, values });
  return strings.reduce((result, str, i) => result + str + (values[i] ?? ""), "");
}
function deindent(strings, ...values) {
  if (typeof strings === "string") return deindent([strings]);
  return templateIdentity(...deindentTemplate(strings, ...values));
}
function deindentTemplate(strings, ...values) {
  if (values.length !== strings.length - 1) throw new StackAssertionError("Invalid number of values; must be one less than strings", { strings, values });
  const trimmedStrings = [...strings];
  trimmedStrings[0] = trimEmptyLinesStart(trimmedStrings[0] + "+").slice(0, -1);
  trimmedStrings[trimmedStrings.length - 1] = trimEmptyLinesEnd("+" + trimmedStrings[trimmedStrings.length - 1]).slice(1);
  const indentation = trimmedStrings.join("${SOME_VALUE}").split("\n").filter((line) => line.trim() !== "").map((line) => getWhitespacePrefix(line).length).reduce((min, current) => Math.min(min, current), Infinity);
  const deindentedStrings = trimmedStrings.map((string, stringIndex) => {
    return string.split("\n").map((line, lineIndex) => stringIndex !== 0 && lineIndex === 0 ? line : line.substring(indentation)).join("\n");
  });
  const indentedValues = values.map((value, i) => {
    const firstLineIndentation = getWhitespacePrefix(deindentedStrings[i].split("\n").at(-1));
    return `${value}`.replaceAll("\n", `
${firstLineIndentation}`);
  });
  return [deindentedStrings, ...indentedValues];
}
function extractScopes(scope, removeDuplicates = true) {
  const trimmedString = scope.trim();
  const scopesArray = trimmedString.split(/\s+/);
  const filtered = scopesArray.filter((scope2) => scope2.length > 0);
  return removeDuplicates ? [...new Set(filtered)] : filtered;
}
function mergeScopeStrings(...scopes) {
  const allScope = scopes.map((s) => extractScopes(s)).flat().join(" ");
  return extractScopes(allScope).join(" ");
}
function escapeTemplateLiteral(s) {
  return s.replaceAll("`", "\\`").replaceAll("\\", "\\\\").replaceAll("$", "\\$");
}
var nicifiableClassNameOverrides = new Map(Object.entries({
  Headers
}).map(([k, v]) => [v, k]));
function nicify(value, options = {}) {
  const fullOptions = {
    maxDepth: 5,
    currentIndent: "",
    lineIndent: "  ",
    multiline: true,
    refs: /* @__PURE__ */ new Map(),
    path: "value",
    parent: null,
    overrides: () => null,
    keyInParent: null,
    hideFields: [],
    ...filterUndefined(options)
  };
  const {
    maxDepth,
    currentIndent,
    lineIndent,
    multiline,
    refs,
    path,
    overrides,
    hideFields
  } = fullOptions;
  const nl = `
${currentIndent}`;
  const overrideResult = overrides(value, options);
  if (overrideResult !== null) return overrideResult;
  if (["function", "object", "symbol"].includes(typeof value) && value !== null) {
    if (refs.has(value)) {
      return `Ref<${refs.get(value)}>`;
    }
    refs.set(value, path);
  }
  const newOptions = {
    maxDepth: maxDepth - 1,
    currentIndent,
    lineIndent,
    multiline,
    refs,
    path: path + "->[unknown property]",
    overrides,
    parent: { value, options: fullOptions },
    keyInParent: null,
    hideFields: []
  };
  const nestedNicify = (newValue, newPath, keyInParent, options2 = {}) => {
    return nicify(newValue, {
      ...newOptions,
      path: newPath,
      currentIndent: currentIndent + lineIndent,
      keyInParent,
      ...options2
    });
  };
  switch (typeof value) {
    case "boolean":
    case "number": {
      return JSON.stringify(value);
    }
    case "string": {
      const isDeindentable = (v) => deindent(v) === v && v.includes("\n");
      const wrapInDeindent = (v) => deindent`
        deindent\`
        ${currentIndent + lineIndent}${escapeTemplateLiteral(v).replaceAll("\n", nl + lineIndent)}
        ${currentIndent}\`
      `;
      if (isDeindentable(value)) {
        return wrapInDeindent(value);
      } else if (value.endsWith("\n") && isDeindentable(value.slice(0, -1))) {
        return wrapInDeindent(value.slice(0, -1)) + ' + "\\n"';
      } else {
        return JSON.stringify(value);
      }
    }
    case "undefined": {
      return "undefined";
    }
    case "symbol": {
      return value.toString();
    }
    case "bigint": {
      return `${value}n`;
    }
    case "function": {
      if (value.name) return `function ${value.name}(...) { ... }`;
      return `(...) => { ... }`;
    }
    case "object": {
      if (value === null) return "null";
      if (Array.isArray(value)) {
        const extraLines2 = getNicifiedObjectExtraLines(value);
        const resValueLength2 = value.length + extraLines2.length;
        if (maxDepth <= 0 && resValueLength2 === 0) return "[...]";
        const resValues2 = value.map((v, i) => nestedNicify(v, `${path}[${i}]`, i));
        resValues2.push(...extraLines2);
        if (resValues2.length !== resValueLength2) throw new StackAssertionError("nicify of object: resValues.length !== resValueLength", { value, resValues: resValues2, resValueLength: resValueLength2 });
        const shouldIndent2 = resValues2.length > 4 || resValues2.some((x) => resValues2.length > 1 && x.length > 4 || x.includes("\n"));
        if (shouldIndent2) {
          return `[${nl}${resValues2.map((x) => `${lineIndent}${x},${nl}`).join("")}]`;
        } else {
          return `[${resValues2.join(", ")}]`;
        }
      }
      if (value instanceof URL) {
        return `URL(${nestedNicify(value.toString(), `${path}.toString()`, null)})`;
      }
      if (ArrayBuffer.isView(value)) {
        return `${value.constructor.name}([${value.toString()}])`;
      }
      if (value instanceof Error) {
        let stack = value.stack ?? "";
        const toString = value.toString();
        if (!stack.startsWith(toString)) stack = `${toString}
${stack}`;
        stack = stack.trimEnd();
        stack = stack.replace(/\n\s+/g, `
${lineIndent}${lineIndent}`);
        stack = stack.replace("\n", `
${lineIndent}Stack:
`);
        if (Object.keys(value).length > 0) {
          stack += `
${lineIndent}Extra properties: ${nestedNicify(Object.fromEntries(Object.entries(value)), path, null)}`;
        }
        if (value.cause) {
          stack += `
${lineIndent}Cause:
${lineIndent}${lineIndent}${nestedNicify(value.cause, path, null, { currentIndent: currentIndent + lineIndent + lineIndent })}`;
        }
        stack = stack.replaceAll("\n", `
${currentIndent}`);
        return stack;
      }
      const constructorName = [null, Object.prototype].includes(Object.getPrototypeOf(value)) ? null : nicifiableClassNameOverrides.get(value.constructor) ?? value.constructor.name;
      const constructorString = constructorName ? `${constructorName} ` : "";
      const entries = getNicifiableEntries(value).filter(([k]) => !hideFields.includes(k));
      const extraLines = [
        ...getNicifiedObjectExtraLines(value),
        ...hideFields.length > 0 ? [`<some fields may have been hidden>`] : []
      ];
      const resValueLength = entries.length + extraLines.length;
      if (resValueLength === 0) return `${constructorString}{}`;
      if (maxDepth <= 0) return `${constructorString}{ ... }`;
      const resValues = entries.map(([k, v], keyIndex) => {
        const keyNicified = nestedNicify(k, `Object.keys(${path})[${keyIndex}]`, null);
        const keyInObjectLiteral = typeof k === "string" ? nicifyPropertyString(k) : `[${keyNicified}]`;
        if (typeof v === "function" && v.name === k) {
          return `${keyInObjectLiteral}(...): { ... }`;
        } else {
          return `${keyInObjectLiteral}: ${nestedNicify(v, `${path}[${keyNicified}]`, k)}`;
        }
      });
      resValues.push(...extraLines);
      if (resValues.length !== resValueLength) throw new StackAssertionError("nicify of object: resValues.length !== resValueLength", { value, resValues, resValueLength });
      const shouldIndent = resValues.length > 1 || resValues.some((x) => x.includes("\n"));
      if (resValues.length === 0) return `${constructorString}{}`;
      if (shouldIndent) {
        return `${constructorString}{${nl}${resValues.map((x) => `${lineIndent}${x},${nl}`).join("")}}`;
      } else {
        return `${constructorString}{ ${resValues.join(", ")} }`;
      }
    }
    default: {
      return `${typeof value}<${value}>`;
    }
  }
}
function replaceAll(input, searchValue, replaceValue) {
  if (searchValue === "") throw new StackAssertionError("replaceAll: searchValue is empty");
  return input.split(searchValue).join(replaceValue);
}
function nicifyPropertyString(str) {
  return JSON.stringify(str);
}
function getNicifiableKeys(value) {
  var _a, _b;
  const overridden = (_b = "getNicifiableKeys" in value ? (_a = value.getNicifiableKeys) == null ? void 0 : _a.bind(value) : null) == null ? void 0 : _b();
  if (overridden != null) return overridden;
  const keys = Object.keys(value).sort();
  return unique(keys);
}
function getNicifiableEntries(value) {
  const recordLikes = [Headers];
  function isRecordLike(value2) {
    return recordLikes.some((x) => value2 instanceof x);
  }
  if (isRecordLike(value)) {
    return [...value.entries()].sort(([a], [b]) => stringCompare(`${a}`, `${b}`));
  }
  const keys = getNicifiableKeys(value);
  return keys.map((k) => [k, value[k]]);
}
function getNicifiedObjectExtraLines(value) {
  var _a;
  return ((_a = "getNicifiedObjectExtraLines" in value ? value.getNicifiedObjectExtraLines : null) == null ? void 0 : _a()) ?? [];
}

export {
  identityArgs,
  typedToLowercase,
  typedToUppercase,
  typedCapitalize,
  stringCompare,
  getWhitespacePrefix,
  getWhitespaceSuffix,
  trimEmptyLinesStart,
  trimEmptyLinesEnd,
  trimLines,
  templateIdentity,
  deindent,
  deindentTemplate,
  extractScopes,
  mergeScopeStrings,
  escapeTemplateLiteral,
  nicify,
  replaceAll,
  isNotNull,
  deepPlainEquals,
  isCloneable,
  shallowClone,
  deepPlainClone,
  deepMerge,
  typedEntries,
  typedFromEntries,
  typedKeys,
  typedValues,
  typedAssign,
  filterUndefined,
  filterUndefinedOrNull,
  deepFilterUndefined,
  pick,
  omit,
  split,
  mapValues,
  sortKeys,
  deepSortKeys,
  set,
  get,
  getOrUndefined,
  has,
  hasAndNotUndefined,
  deleteKey,
  isObjectLike,
  throwErr,
  concatStacktraces,
  StackAssertionError,
  errorToNiceString,
  registerErrorSink,
  captureError,
  StatusError
};
//# sourceMappingURL=chunk-Z26222H5.js.map
