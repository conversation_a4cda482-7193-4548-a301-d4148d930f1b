{"version": 3, "sources": ["../../@simplewebauthn/browser/dist/bundle/index.js"], "sourcesContent": ["/* [@simplewebauthn/browser@11.0.0] */\nfunction bufferToBase64URLString(buffer) {\n    const bytes = new Uint8Array(buffer);\n    let str = '';\n    for (const charCode of bytes) {\n        str += String.fromCharCode(charCode);\n    }\n    const base64String = btoa(str);\n    return base64String.replace(/\\+/g, '-').replace(/\\//g, '_').replace(/=/g, '');\n}\n\nfunction base64URLStringToBuffer(base64URLString) {\n    const base64 = base64URLString.replace(/-/g, '+').replace(/_/g, '/');\n    const padLength = (4 - (base64.length % 4)) % 4;\n    const padded = base64.padEnd(base64.length + padLength, '=');\n    const binary = atob(padded);\n    const buffer = new ArrayBuffer(binary.length);\n    const bytes = new Uint8Array(buffer);\n    for (let i = 0; i < binary.length; i++) {\n        bytes[i] = binary.charCodeAt(i);\n    }\n    return buffer;\n}\n\nfunction browserSupportsWebAuthn() {\n    return (window?.PublicKeyCredential !== undefined &&\n        typeof window.PublicKeyCredential === 'function');\n}\n\nfunction toPublicKeyCredentialDescriptor(descriptor) {\n    const { id } = descriptor;\n    return {\n        ...descriptor,\n        id: base64URLStringToBuffer(id),\n        transports: descriptor.transports,\n    };\n}\n\nfunction isValidDomain(hostname) {\n    return (hostname === 'localhost' ||\n        /^([a-z0-9]+(-[a-z0-9]+)*\\.)+[a-z]{2,}$/i.test(hostname));\n}\n\nclass WebAuthnError extends Error {\n    constructor({ message, code, cause, name, }) {\n        super(message, { cause });\n        this.name = name ?? cause.name;\n        this.code = code;\n    }\n}\n\nfunction identifyRegistrationError({ error, options, }) {\n    const { publicKey } = options;\n    if (!publicKey) {\n        throw Error('options was missing required publicKey property');\n    }\n    if (error.name === 'AbortError') {\n        if (options.signal instanceof AbortSignal) {\n            return new WebAuthnError({\n                message: 'Registration ceremony was sent an abort signal',\n                code: 'ERROR_CEREMONY_ABORTED',\n                cause: error,\n            });\n        }\n    }\n    else if (error.name === 'ConstraintError') {\n        if (publicKey.authenticatorSelection?.requireResidentKey === true) {\n            return new WebAuthnError({\n                message: 'Discoverable credentials were required but no available authenticator supported it',\n                code: 'ERROR_AUTHENTICATOR_MISSING_DISCOVERABLE_CREDENTIAL_SUPPORT',\n                cause: error,\n            });\n        }\n        else if (options.mediation === 'conditional' &&\n            publicKey.authenticatorSelection?.userVerification === 'required') {\n            return new WebAuthnError({\n                message: 'User verification was required during automatic registration but it could not be performed',\n                code: 'ERROR_AUTO_REGISTER_USER_VERIFICATION_FAILURE',\n                cause: error,\n            });\n        }\n        else if (publicKey.authenticatorSelection?.userVerification === 'required') {\n            return new WebAuthnError({\n                message: 'User verification was required but no available authenticator supported it',\n                code: 'ERROR_AUTHENTICATOR_MISSING_USER_VERIFICATION_SUPPORT',\n                cause: error,\n            });\n        }\n    }\n    else if (error.name === 'InvalidStateError') {\n        return new WebAuthnError({\n            message: 'The authenticator was previously registered',\n            code: 'ERROR_AUTHENTICATOR_PREVIOUSLY_REGISTERED',\n            cause: error,\n        });\n    }\n    else if (error.name === 'NotAllowedError') {\n        return new WebAuthnError({\n            message: error.message,\n            code: 'ERROR_PASSTHROUGH_SEE_CAUSE_PROPERTY',\n            cause: error,\n        });\n    }\n    else if (error.name === 'NotSupportedError') {\n        const validPubKeyCredParams = publicKey.pubKeyCredParams.filter((param) => param.type === 'public-key');\n        if (validPubKeyCredParams.length === 0) {\n            return new WebAuthnError({\n                message: 'No entry in pubKeyCredParams was of type \"public-key\"',\n                code: 'ERROR_MALFORMED_PUBKEYCREDPARAMS',\n                cause: error,\n            });\n        }\n        return new WebAuthnError({\n            message: 'No available authenticator supported any of the specified pubKeyCredParams algorithms',\n            code: 'ERROR_AUTHENTICATOR_NO_SUPPORTED_PUBKEYCREDPARAMS_ALG',\n            cause: error,\n        });\n    }\n    else if (error.name === 'SecurityError') {\n        const effectiveDomain = window.location.hostname;\n        if (!isValidDomain(effectiveDomain)) {\n            return new WebAuthnError({\n                message: `${window.location.hostname} is an invalid domain`,\n                code: 'ERROR_INVALID_DOMAIN',\n                cause: error,\n            });\n        }\n        else if (publicKey.rp.id !== effectiveDomain) {\n            return new WebAuthnError({\n                message: `The RP ID \"${publicKey.rp.id}\" is invalid for this domain`,\n                code: 'ERROR_INVALID_RP_ID',\n                cause: error,\n            });\n        }\n    }\n    else if (error.name === 'TypeError') {\n        if (publicKey.user.id.byteLength < 1 || publicKey.user.id.byteLength > 64) {\n            return new WebAuthnError({\n                message: 'User ID was not between 1 and 64 characters',\n                code: 'ERROR_INVALID_USER_ID_LENGTH',\n                cause: error,\n            });\n        }\n    }\n    else if (error.name === 'UnknownError') {\n        return new WebAuthnError({\n            message: 'The authenticator was unable to process the specified options, or could not create a new credential',\n            code: 'ERROR_AUTHENTICATOR_GENERAL_ERROR',\n            cause: error,\n        });\n    }\n    return error;\n}\n\nclass BaseWebAuthnAbortService {\n    createNewAbortSignal() {\n        if (this.controller) {\n            const abortError = new Error('Cancelling existing WebAuthn API call for new one');\n            abortError.name = 'AbortError';\n            this.controller.abort(abortError);\n        }\n        const newController = new AbortController();\n        this.controller = newController;\n        return newController.signal;\n    }\n    cancelCeremony() {\n        if (this.controller) {\n            const abortError = new Error('Manually cancelling existing WebAuthn API call');\n            abortError.name = 'AbortError';\n            this.controller.abort(abortError);\n            this.controller = undefined;\n        }\n    }\n}\nconst WebAuthnAbortService = new BaseWebAuthnAbortService();\n\nconst attachments = ['cross-platform', 'platform'];\nfunction toAuthenticatorAttachment(attachment) {\n    if (!attachment) {\n        return;\n    }\n    if (attachments.indexOf(attachment) < 0) {\n        return;\n    }\n    return attachment;\n}\n\nasync function startRegistration(options) {\n    const { optionsJSON, useAutoRegister = false } = options;\n    if (!browserSupportsWebAuthn()) {\n        throw new Error('WebAuthn is not supported in this browser');\n    }\n    const publicKey = {\n        ...optionsJSON,\n        challenge: base64URLStringToBuffer(optionsJSON.challenge),\n        user: {\n            ...optionsJSON.user,\n            id: base64URLStringToBuffer(optionsJSON.user.id),\n        },\n        excludeCredentials: optionsJSON.excludeCredentials?.map(toPublicKeyCredentialDescriptor),\n    };\n    const createOptions = {};\n    if (useAutoRegister) {\n        createOptions.mediation = 'conditional';\n    }\n    createOptions.publicKey = publicKey;\n    createOptions.signal = WebAuthnAbortService.createNewAbortSignal();\n    let credential;\n    try {\n        credential = (await navigator.credentials.create(createOptions));\n    }\n    catch (err) {\n        throw identifyRegistrationError({ error: err, options: createOptions });\n    }\n    if (!credential) {\n        throw new Error('Registration was not completed');\n    }\n    const { id, rawId, response, type } = credential;\n    let transports = undefined;\n    if (typeof response.getTransports === 'function') {\n        transports = response.getTransports();\n    }\n    let responsePublicKeyAlgorithm = undefined;\n    if (typeof response.getPublicKeyAlgorithm === 'function') {\n        try {\n            responsePublicKeyAlgorithm = response.getPublicKeyAlgorithm();\n        }\n        catch (error) {\n            warnOnBrokenImplementation('getPublicKeyAlgorithm()', error);\n        }\n    }\n    let responsePublicKey = undefined;\n    if (typeof response.getPublicKey === 'function') {\n        try {\n            const _publicKey = response.getPublicKey();\n            if (_publicKey !== null) {\n                responsePublicKey = bufferToBase64URLString(_publicKey);\n            }\n        }\n        catch (error) {\n            warnOnBrokenImplementation('getPublicKey()', error);\n        }\n    }\n    let responseAuthenticatorData;\n    if (typeof response.getAuthenticatorData === 'function') {\n        try {\n            responseAuthenticatorData = bufferToBase64URLString(response.getAuthenticatorData());\n        }\n        catch (error) {\n            warnOnBrokenImplementation('getAuthenticatorData()', error);\n        }\n    }\n    return {\n        id,\n        rawId: bufferToBase64URLString(rawId),\n        response: {\n            attestationObject: bufferToBase64URLString(response.attestationObject),\n            clientDataJSON: bufferToBase64URLString(response.clientDataJSON),\n            transports,\n            publicKeyAlgorithm: responsePublicKeyAlgorithm,\n            publicKey: responsePublicKey,\n            authenticatorData: responseAuthenticatorData,\n        },\n        type,\n        clientExtensionResults: credential.getClientExtensionResults(),\n        authenticatorAttachment: toAuthenticatorAttachment(credential.authenticatorAttachment),\n    };\n}\nfunction warnOnBrokenImplementation(methodName, cause) {\n    console.warn(`The browser extension that intercepted this WebAuthn API call incorrectly implemented ${methodName}. You should report this error to them.\\n`, cause);\n}\n\nfunction browserSupportsWebAuthnAutofill() {\n    if (!browserSupportsWebAuthn()) {\n        return new Promise((resolve) => resolve(false));\n    }\n    const globalPublicKeyCredential = window\n        .PublicKeyCredential;\n    if (globalPublicKeyCredential.isConditionalMediationAvailable === undefined) {\n        return new Promise((resolve) => resolve(false));\n    }\n    return globalPublicKeyCredential.isConditionalMediationAvailable();\n}\n\nfunction identifyAuthenticationError({ error, options, }) {\n    const { publicKey } = options;\n    if (!publicKey) {\n        throw Error('options was missing required publicKey property');\n    }\n    if (error.name === 'AbortError') {\n        if (options.signal instanceof AbortSignal) {\n            return new WebAuthnError({\n                message: 'Authentication ceremony was sent an abort signal',\n                code: 'ERROR_CEREMONY_ABORTED',\n                cause: error,\n            });\n        }\n    }\n    else if (error.name === 'NotAllowedError') {\n        return new WebAuthnError({\n            message: error.message,\n            code: 'ERROR_PASSTHROUGH_SEE_CAUSE_PROPERTY',\n            cause: error,\n        });\n    }\n    else if (error.name === 'SecurityError') {\n        const effectiveDomain = window.location.hostname;\n        if (!isValidDomain(effectiveDomain)) {\n            return new WebAuthnError({\n                message: `${window.location.hostname} is an invalid domain`,\n                code: 'ERROR_INVALID_DOMAIN',\n                cause: error,\n            });\n        }\n        else if (publicKey.rpId !== effectiveDomain) {\n            return new WebAuthnError({\n                message: `The RP ID \"${publicKey.rpId}\" is invalid for this domain`,\n                code: 'ERROR_INVALID_RP_ID',\n                cause: error,\n            });\n        }\n    }\n    else if (error.name === 'UnknownError') {\n        return new WebAuthnError({\n            message: 'The authenticator was unable to process the specified options, or could not create a new assertion signature',\n            code: 'ERROR_AUTHENTICATOR_GENERAL_ERROR',\n            cause: error,\n        });\n    }\n    return error;\n}\n\nasync function startAuthentication(options) {\n    const { optionsJSON, useBrowserAutofill = false, verifyBrowserAutofillInput = true, } = options;\n    if (!browserSupportsWebAuthn()) {\n        throw new Error('WebAuthn is not supported in this browser');\n    }\n    let allowCredentials;\n    if (optionsJSON.allowCredentials?.length !== 0) {\n        allowCredentials = optionsJSON.allowCredentials?.map(toPublicKeyCredentialDescriptor);\n    }\n    const publicKey = {\n        ...optionsJSON,\n        challenge: base64URLStringToBuffer(optionsJSON.challenge),\n        allowCredentials,\n    };\n    const getOptions = {};\n    if (useBrowserAutofill) {\n        if (!(await browserSupportsWebAuthnAutofill())) {\n            throw Error('Browser does not support WebAuthn autofill');\n        }\n        const eligibleInputs = document.querySelectorAll(\"input[autocomplete$='webauthn']\");\n        if (eligibleInputs.length < 1 && verifyBrowserAutofillInput) {\n            throw Error('No <input> with \"webauthn\" as the only or last value in its `autocomplete` attribute was detected');\n        }\n        getOptions.mediation = 'conditional';\n        publicKey.allowCredentials = [];\n    }\n    getOptions.publicKey = publicKey;\n    getOptions.signal = WebAuthnAbortService.createNewAbortSignal();\n    let credential;\n    try {\n        credential = (await navigator.credentials.get(getOptions));\n    }\n    catch (err) {\n        throw identifyAuthenticationError({ error: err, options: getOptions });\n    }\n    if (!credential) {\n        throw new Error('Authentication was not completed');\n    }\n    const { id, rawId, response, type } = credential;\n    let userHandle = undefined;\n    if (response.userHandle) {\n        userHandle = bufferToBase64URLString(response.userHandle);\n    }\n    return {\n        id,\n        rawId: bufferToBase64URLString(rawId),\n        response: {\n            authenticatorData: bufferToBase64URLString(response.authenticatorData),\n            clientDataJSON: bufferToBase64URLString(response.clientDataJSON),\n            signature: bufferToBase64URLString(response.signature),\n            userHandle,\n        },\n        type,\n        clientExtensionResults: credential.getClientExtensionResults(),\n        authenticatorAttachment: toAuthenticatorAttachment(credential.authenticatorAttachment),\n    };\n}\n\nfunction platformAuthenticatorIsAvailable() {\n    if (!browserSupportsWebAuthn()) {\n        return new Promise((resolve) => resolve(false));\n    }\n    return PublicKeyCredential.isUserVerifyingPlatformAuthenticatorAvailable();\n}\n\nexport { WebAuthnAbortService, WebAuthnError, base64URLStringToBuffer, browserSupportsWebAuthn, browserSupportsWebAuthnAutofill, bufferToBase64URLString, platformAuthenticatorIsAvailable, startAuthentication, startRegistration };\n"], "mappings": ";;;AACA,SAAS,wBAAwB,QAAQ;AACrC,QAAM,QAAQ,IAAI,WAAW,MAAM;AACnC,MAAI,MAAM;AACV,aAAW,YAAY,OAAO;AAC1B,WAAO,OAAO,aAAa,QAAQ;AAAA,EACvC;AACA,QAAM,eAAe,KAAK,GAAG;AAC7B,SAAO,aAAa,QAAQ,OAAO,GAAG,EAAE,QAAQ,OAAO,GAAG,EAAE,QAAQ,MAAM,EAAE;AAChF;AAEA,SAAS,wBAAwB,iBAAiB;AAC9C,QAAM,SAAS,gBAAgB,QAAQ,MAAM,GAAG,EAAE,QAAQ,MAAM,GAAG;AACnE,QAAM,aAAa,IAAK,OAAO,SAAS,KAAM;AAC9C,QAAM,SAAS,OAAO,OAAO,OAAO,SAAS,WAAW,GAAG;AAC3D,QAAM,SAAS,KAAK,MAAM;AAC1B,QAAM,SAAS,IAAI,YAAY,OAAO,MAAM;AAC5C,QAAM,QAAQ,IAAI,WAAW,MAAM;AACnC,WAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACpC,UAAM,CAAC,IAAI,OAAO,WAAW,CAAC;AAAA,EAClC;AACA,SAAO;AACX;AAEA,SAAS,0BAA0B;AAC/B,UAAQ,iCAAQ,yBAAwB,UACpC,OAAO,OAAO,wBAAwB;AAC9C;AAEA,SAAS,gCAAgC,YAAY;AACjD,QAAM,EAAE,GAAG,IAAI;AACf,SAAO;AAAA,IACH,GAAG;AAAA,IACH,IAAI,wBAAwB,EAAE;AAAA,IAC9B,YAAY,WAAW;AAAA,EAC3B;AACJ;AAEA,SAAS,cAAc,UAAU;AAC7B,SAAQ,aAAa,eACjB,0CAA0C,KAAK,QAAQ;AAC/D;AAEA,IAAM,gBAAN,cAA4B,MAAM;AAAA,EAC9B,YAAY,EAAE,SAAS,MAAM,OAAO,KAAM,GAAG;AACzC,UAAM,SAAS,EAAE,MAAM,CAAC;AACxB,SAAK,OAAO,QAAQ,MAAM;AAC1B,SAAK,OAAO;AAAA,EAChB;AACJ;AAEA,SAAS,0BAA0B,EAAE,OAAO,QAAS,GAAG;AAnDxD;AAoDI,QAAM,EAAE,UAAU,IAAI;AACtB,MAAI,CAAC,WAAW;AACZ,UAAM,MAAM,iDAAiD;AAAA,EACjE;AACA,MAAI,MAAM,SAAS,cAAc;AAC7B,QAAI,QAAQ,kBAAkB,aAAa;AACvC,aAAO,IAAI,cAAc;AAAA,QACrB,SAAS;AAAA,QACT,MAAM;AAAA,QACN,OAAO;AAAA,MACX,CAAC;AAAA,IACL;AAAA,EACJ,WACS,MAAM,SAAS,mBAAmB;AACvC,UAAI,eAAU,2BAAV,mBAAkC,wBAAuB,MAAM;AAC/D,aAAO,IAAI,cAAc;AAAA,QACrB,SAAS;AAAA,QACT,MAAM;AAAA,QACN,OAAO;AAAA,MACX,CAAC;AAAA,IACL,WACS,QAAQ,cAAc,mBAC3B,eAAU,2BAAV,mBAAkC,sBAAqB,YAAY;AACnE,aAAO,IAAI,cAAc;AAAA,QACrB,SAAS;AAAA,QACT,MAAM;AAAA,QACN,OAAO;AAAA,MACX,CAAC;AAAA,IACL,aACS,eAAU,2BAAV,mBAAkC,sBAAqB,YAAY;AACxE,aAAO,IAAI,cAAc;AAAA,QACrB,SAAS;AAAA,QACT,MAAM;AAAA,QACN,OAAO;AAAA,MACX,CAAC;AAAA,IACL;AAAA,EACJ,WACS,MAAM,SAAS,qBAAqB;AACzC,WAAO,IAAI,cAAc;AAAA,MACrB,SAAS;AAAA,MACT,MAAM;AAAA,MACN,OAAO;AAAA,IACX,CAAC;AAAA,EACL,WACS,MAAM,SAAS,mBAAmB;AACvC,WAAO,IAAI,cAAc;AAAA,MACrB,SAAS,MAAM;AAAA,MACf,MAAM;AAAA,MACN,OAAO;AAAA,IACX,CAAC;AAAA,EACL,WACS,MAAM,SAAS,qBAAqB;AACzC,UAAM,wBAAwB,UAAU,iBAAiB,OAAO,CAAC,UAAU,MAAM,SAAS,YAAY;AACtG,QAAI,sBAAsB,WAAW,GAAG;AACpC,aAAO,IAAI,cAAc;AAAA,QACrB,SAAS;AAAA,QACT,MAAM;AAAA,QACN,OAAO;AAAA,MACX,CAAC;AAAA,IACL;AACA,WAAO,IAAI,cAAc;AAAA,MACrB,SAAS;AAAA,MACT,MAAM;AAAA,MACN,OAAO;AAAA,IACX,CAAC;AAAA,EACL,WACS,MAAM,SAAS,iBAAiB;AACrC,UAAM,kBAAkB,OAAO,SAAS;AACxC,QAAI,CAAC,cAAc,eAAe,GAAG;AACjC,aAAO,IAAI,cAAc;AAAA,QACrB,SAAS,GAAG,OAAO,SAAS,QAAQ;AAAA,QACpC,MAAM;AAAA,QACN,OAAO;AAAA,MACX,CAAC;AAAA,IACL,WACS,UAAU,GAAG,OAAO,iBAAiB;AAC1C,aAAO,IAAI,cAAc;AAAA,QACrB,SAAS,cAAc,UAAU,GAAG,EAAE;AAAA,QACtC,MAAM;AAAA,QACN,OAAO;AAAA,MACX,CAAC;AAAA,IACL;AAAA,EACJ,WACS,MAAM,SAAS,aAAa;AACjC,QAAI,UAAU,KAAK,GAAG,aAAa,KAAK,UAAU,KAAK,GAAG,aAAa,IAAI;AACvE,aAAO,IAAI,cAAc;AAAA,QACrB,SAAS;AAAA,QACT,MAAM;AAAA,QACN,OAAO;AAAA,MACX,CAAC;AAAA,IACL;AAAA,EACJ,WACS,MAAM,SAAS,gBAAgB;AACpC,WAAO,IAAI,cAAc;AAAA,MACrB,SAAS;AAAA,MACT,MAAM;AAAA,MACN,OAAO;AAAA,IACX,CAAC;AAAA,EACL;AACA,SAAO;AACX;AAEA,IAAM,2BAAN,MAA+B;AAAA,EAC3B,uBAAuB;AACnB,QAAI,KAAK,YAAY;AACjB,YAAM,aAAa,IAAI,MAAM,mDAAmD;AAChF,iBAAW,OAAO;AAClB,WAAK,WAAW,MAAM,UAAU;AAAA,IACpC;AACA,UAAM,gBAAgB,IAAI,gBAAgB;AAC1C,SAAK,aAAa;AAClB,WAAO,cAAc;AAAA,EACzB;AAAA,EACA,iBAAiB;AACb,QAAI,KAAK,YAAY;AACjB,YAAM,aAAa,IAAI,MAAM,gDAAgD;AAC7E,iBAAW,OAAO;AAClB,WAAK,WAAW,MAAM,UAAU;AAChC,WAAK,aAAa;AAAA,IACtB;AAAA,EACJ;AACJ;AACA,IAAM,uBAAuB,IAAI,yBAAyB;AAE1D,IAAM,cAAc,CAAC,kBAAkB,UAAU;AACjD,SAAS,0BAA0B,YAAY;AAC3C,MAAI,CAAC,YAAY;AACb;AAAA,EACJ;AACA,MAAI,YAAY,QAAQ,UAAU,IAAI,GAAG;AACrC;AAAA,EACJ;AACA,SAAO;AACX;AAEA,eAAe,kBAAkB,SAAS;AA3L1C;AA4LI,QAAM,EAAE,aAAa,kBAAkB,MAAM,IAAI;AACjD,MAAI,CAAC,wBAAwB,GAAG;AAC5B,UAAM,IAAI,MAAM,2CAA2C;AAAA,EAC/D;AACA,QAAM,YAAY;AAAA,IACd,GAAG;AAAA,IACH,WAAW,wBAAwB,YAAY,SAAS;AAAA,IACxD,MAAM;AAAA,MACF,GAAG,YAAY;AAAA,MACf,IAAI,wBAAwB,YAAY,KAAK,EAAE;AAAA,IACnD;AAAA,IACA,qBAAoB,iBAAY,uBAAZ,mBAAgC,IAAI;AAAA,EAC5D;AACA,QAAM,gBAAgB,CAAC;AACvB,MAAI,iBAAiB;AACjB,kBAAc,YAAY;AAAA,EAC9B;AACA,gBAAc,YAAY;AAC1B,gBAAc,SAAS,qBAAqB,qBAAqB;AACjE,MAAI;AACJ,MAAI;AACA,iBAAc,MAAM,UAAU,YAAY,OAAO,aAAa;AAAA,EAClE,SACO,KAAK;AACR,UAAM,0BAA0B,EAAE,OAAO,KAAK,SAAS,cAAc,CAAC;AAAA,EAC1E;AACA,MAAI,CAAC,YAAY;AACb,UAAM,IAAI,MAAM,gCAAgC;AAAA,EACpD;AACA,QAAM,EAAE,IAAI,OAAO,UAAU,KAAK,IAAI;AACtC,MAAI,aAAa;AACjB,MAAI,OAAO,SAAS,kBAAkB,YAAY;AAC9C,iBAAa,SAAS,cAAc;AAAA,EACxC;AACA,MAAI,6BAA6B;AACjC,MAAI,OAAO,SAAS,0BAA0B,YAAY;AACtD,QAAI;AACA,mCAA6B,SAAS,sBAAsB;AAAA,IAChE,SACO,OAAO;AACV,iCAA2B,2BAA2B,KAAK;AAAA,IAC/D;AAAA,EACJ;AACA,MAAI,oBAAoB;AACxB,MAAI,OAAO,SAAS,iBAAiB,YAAY;AAC7C,QAAI;AACA,YAAM,aAAa,SAAS,aAAa;AACzC,UAAI,eAAe,MAAM;AACrB,4BAAoB,wBAAwB,UAAU;AAAA,MAC1D;AAAA,IACJ,SACO,OAAO;AACV,iCAA2B,kBAAkB,KAAK;AAAA,IACtD;AAAA,EACJ;AACA,MAAI;AACJ,MAAI,OAAO,SAAS,yBAAyB,YAAY;AACrD,QAAI;AACA,kCAA4B,wBAAwB,SAAS,qBAAqB,CAAC;AAAA,IACvF,SACO,OAAO;AACV,iCAA2B,0BAA0B,KAAK;AAAA,IAC9D;AAAA,EACJ;AACA,SAAO;AAAA,IACH;AAAA,IACA,OAAO,wBAAwB,KAAK;AAAA,IACpC,UAAU;AAAA,MACN,mBAAmB,wBAAwB,SAAS,iBAAiB;AAAA,MACrE,gBAAgB,wBAAwB,SAAS,cAAc;AAAA,MAC/D;AAAA,MACA,oBAAoB;AAAA,MACpB,WAAW;AAAA,MACX,mBAAmB;AAAA,IACvB;AAAA,IACA;AAAA,IACA,wBAAwB,WAAW,0BAA0B;AAAA,IAC7D,yBAAyB,0BAA0B,WAAW,uBAAuB;AAAA,EACzF;AACJ;AACA,SAAS,2BAA2B,YAAY,OAAO;AACnD,UAAQ,KAAK,yFAAyF,UAAU;AAAA,GAA6C,KAAK;AACtK;AAEA,SAAS,kCAAkC;AACvC,MAAI,CAAC,wBAAwB,GAAG;AAC5B,WAAO,IAAI,QAAQ,CAAC,YAAY,QAAQ,KAAK,CAAC;AAAA,EAClD;AACA,QAAM,4BAA4B,OAC7B;AACL,MAAI,0BAA0B,oCAAoC,QAAW;AACzE,WAAO,IAAI,QAAQ,CAAC,YAAY,QAAQ,KAAK,CAAC;AAAA,EAClD;AACA,SAAO,0BAA0B,gCAAgC;AACrE;AAEA,SAAS,4BAA4B,EAAE,OAAO,QAAS,GAAG;AACtD,QAAM,EAAE,UAAU,IAAI;AACtB,MAAI,CAAC,WAAW;AACZ,UAAM,MAAM,iDAAiD;AAAA,EACjE;AACA,MAAI,MAAM,SAAS,cAAc;AAC7B,QAAI,QAAQ,kBAAkB,aAAa;AACvC,aAAO,IAAI,cAAc;AAAA,QACrB,SAAS;AAAA,QACT,MAAM;AAAA,QACN,OAAO;AAAA,MACX,CAAC;AAAA,IACL;AAAA,EACJ,WACS,MAAM,SAAS,mBAAmB;AACvC,WAAO,IAAI,cAAc;AAAA,MACrB,SAAS,MAAM;AAAA,MACf,MAAM;AAAA,MACN,OAAO;AAAA,IACX,CAAC;AAAA,EACL,WACS,MAAM,SAAS,iBAAiB;AACrC,UAAM,kBAAkB,OAAO,SAAS;AACxC,QAAI,CAAC,cAAc,eAAe,GAAG;AACjC,aAAO,IAAI,cAAc;AAAA,QACrB,SAAS,GAAG,OAAO,SAAS,QAAQ;AAAA,QACpC,MAAM;AAAA,QACN,OAAO;AAAA,MACX,CAAC;AAAA,IACL,WACS,UAAU,SAAS,iBAAiB;AACzC,aAAO,IAAI,cAAc;AAAA,QACrB,SAAS,cAAc,UAAU,IAAI;AAAA,QACrC,MAAM;AAAA,QACN,OAAO;AAAA,MACX,CAAC;AAAA,IACL;AAAA,EACJ,WACS,MAAM,SAAS,gBAAgB;AACpC,WAAO,IAAI,cAAc;AAAA,MACrB,SAAS;AAAA,MACT,MAAM;AAAA,MACN,OAAO;AAAA,IACX,CAAC;AAAA,EACL;AACA,SAAO;AACX;AAEA,eAAe,oBAAoB,SAAS;AA5U5C;AA6UI,QAAM,EAAE,aAAa,qBAAqB,OAAO,6BAA6B,KAAM,IAAI;AACxF,MAAI,CAAC,wBAAwB,GAAG;AAC5B,UAAM,IAAI,MAAM,2CAA2C;AAAA,EAC/D;AACA,MAAI;AACJ,QAAI,iBAAY,qBAAZ,mBAA8B,YAAW,GAAG;AAC5C,wBAAmB,iBAAY,qBAAZ,mBAA8B,IAAI;AAAA,EACzD;AACA,QAAM,YAAY;AAAA,IACd,GAAG;AAAA,IACH,WAAW,wBAAwB,YAAY,SAAS;AAAA,IACxD;AAAA,EACJ;AACA,QAAM,aAAa,CAAC;AACpB,MAAI,oBAAoB;AACpB,QAAI,CAAE,MAAM,gCAAgC,GAAI;AAC5C,YAAM,MAAM,4CAA4C;AAAA,IAC5D;AACA,UAAM,iBAAiB,SAAS,iBAAiB,iCAAiC;AAClF,QAAI,eAAe,SAAS,KAAK,4BAA4B;AACzD,YAAM,MAAM,mGAAmG;AAAA,IACnH;AACA,eAAW,YAAY;AACvB,cAAU,mBAAmB,CAAC;AAAA,EAClC;AACA,aAAW,YAAY;AACvB,aAAW,SAAS,qBAAqB,qBAAqB;AAC9D,MAAI;AACJ,MAAI;AACA,iBAAc,MAAM,UAAU,YAAY,IAAI,UAAU;AAAA,EAC5D,SACO,KAAK;AACR,UAAM,4BAA4B,EAAE,OAAO,KAAK,SAAS,WAAW,CAAC;AAAA,EACzE;AACA,MAAI,CAAC,YAAY;AACb,UAAM,IAAI,MAAM,kCAAkC;AAAA,EACtD;AACA,QAAM,EAAE,IAAI,OAAO,UAAU,KAAK,IAAI;AACtC,MAAI,aAAa;AACjB,MAAI,SAAS,YAAY;AACrB,iBAAa,wBAAwB,SAAS,UAAU;AAAA,EAC5D;AACA,SAAO;AAAA,IACH;AAAA,IACA,OAAO,wBAAwB,KAAK;AAAA,IACpC,UAAU;AAAA,MACN,mBAAmB,wBAAwB,SAAS,iBAAiB;AAAA,MACrE,gBAAgB,wBAAwB,SAAS,cAAc;AAAA,MAC/D,WAAW,wBAAwB,SAAS,SAAS;AAAA,MACrD;AAAA,IACJ;AAAA,IACA;AAAA,IACA,wBAAwB,WAAW,0BAA0B;AAAA,IAC7D,yBAAyB,0BAA0B,WAAW,uBAAuB;AAAA,EACzF;AACJ;AAEA,SAAS,mCAAmC;AACxC,MAAI,CAAC,wBAAwB,GAAG;AAC5B,WAAO,IAAI,QAAQ,CAAC,YAAY,QAAQ,KAAK,CAAC;AAAA,EAClD;AACA,SAAO,oBAAoB,8CAA8C;AAC7E;", "names": []}