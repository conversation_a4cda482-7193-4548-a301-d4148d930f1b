var __defProp = Object.defineProperty;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __publicField = (obj, key, value) => __defNormalProp(obj, typeof key !== "symbol" ? key + "" : key, value);
import { ipcMain, app, BrowserWindow, WebContentsView, nativeTheme, session, shell, protocol, dialog, Menu } from "electron";
import { fileURLToPath } from "node:url";
import path$e from "node:path";
import os$1, { homedir } from "node:os";
import log from "electron-log";
import { createRequire } from "node:module";
import { spawn } from "child_process";
import fs$j from "fs";
import os from "os";
import path$d from "path";
import * as net from "net";
import mammoth from "mammoth";
import Papa from "papaparse";
import * as unzipper from "unzipper";
import { parseStringPromise } from "xml2js";
import fs$k, { existsSync } from "node:fs";
import fsp from "fs/promises";
import require$$0 from "constants";
import require$$0$1 from "stream";
import require$$4 from "util";
import require$$5 from "assert";
import kill from "tree-kill";
const { autoUpdater } = createRequire(import.meta.url)("electron-updater");
function update(win2) {
  var _a;
  autoUpdater.autoDownload = false;
  autoUpdater.disableWebInstaller = false;
  autoUpdater.allowDowngrade = false;
  autoUpdater.forceDevUpdateConfig = true;
  autoUpdater.on("checking-for-update", function() {
  });
  autoUpdater.on("update-available", (arg) => {
    if (win2 && !win2.isDestroyed()) {
      win2.webContents.send("update-can-available", { update: true, version: app.getVersion(), newVersion: arg == null ? void 0 : arg.version });
    }
  });
  autoUpdater.on("update-not-available", (arg) => {
    if (win2 && !win2.isDestroyed()) {
      win2.webContents.send("update-can-available", { update: false, version: app.getVersion(), newVersion: arg == null ? void 0 : arg.version });
    }
  });
  console.log("Current version:", autoUpdater.currentVersion.version);
  console.log("Update config path:", (_a = autoUpdater.getUpdateConfigPath) == null ? void 0 : _a.call(autoUpdater));
  console.log("User data path (where config lives):", app.getPath("userData"));
  if (app.isPackaged) {
    autoUpdater.checkForUpdatesAndNotify();
  }
  if (!app.isPackaged) {
    const updateUrl = "http://dev.eigent.ai/public/win";
    const feed = {
      provider: "generic",
      url: updateUrl
    };
    console.log("[DEV] setFeedURL:", updateUrl);
    autoUpdater.setFeedURL(feed);
    autoUpdater.checkForUpdates();
  }
}
function registerUpdateIpcHandlers() {
  ipcMain.handle("check-update", async () => {
    try {
      return await autoUpdater.checkForUpdatesAndNotify();
    } catch (error) {
      return { message: "Network error", error };
    }
  });
  ipcMain.handle("start-download", (event) => {
    startDownload(
      (error, progressInfo) => {
        if (error) {
          if (!event.sender.isDestroyed()) {
            event.sender.send("update-error", { message: error.message, error });
          }
        } else {
          if (!event.sender.isDestroyed()) {
            event.sender.send("download-progress", progressInfo);
          }
        }
      },
      () => {
        if (!event.sender.isDestroyed()) {
          event.sender.send("update-downloaded");
        }
      }
    );
  });
  ipcMain.handle("quit-and-install", () => {
    autoUpdater.quitAndInstall(false, true);
  });
}
function startDownload(callback, complete) {
  autoUpdater.on("download-progress", (info) => callback(null, info));
  autoUpdater.on("error", (error) => callback(error, null));
  autoUpdater.on("update-downloaded", complete);
  autoUpdater.downloadUpdate();
}
function getResourcePath() {
  return path$d.join(app.getAppPath(), "resources");
}
function getBackendPath() {
  if (app.isPackaged) {
    return path$d.join(process.resourcesPath, "backend");
  } else {
    return path$d.join(app.getAppPath(), "backend");
  }
}
function runInstallScript(scriptPath) {
  return new Promise((resolve, reject) => {
    const installScriptPath = path$d.join(getResourcePath(), "scripts", scriptPath);
    log.info(`Running script at: ${installScriptPath}`);
    const nodeProcess = spawn(process.execPath, [installScriptPath], {
      env: { ...process.env, ELECTRON_RUN_AS_NODE: "1" }
    });
    nodeProcess.stdout.on("data", (data) => {
      log.info(`Script output: ${data}`);
    });
    nodeProcess.stderr.on("data", (data) => {
      log.error(`Script error: ${data}`);
    });
    nodeProcess.on("close", (code) => {
      if (code === 0) {
        log.info("Script completed successfully");
        resolve(true);
      } else {
        log.error(`Script exited with code ${code}`);
        reject(false);
      }
    });
  });
}
async function getBinaryName(name) {
  if (process.platform === "win32") {
    return `${name}.exe`;
  }
  return name;
}
async function getBinaryPath(name) {
  if (!name) {
    return path$d.join(os.homedir(), ".eigent", "bin");
  }
  const binaryName = await getBinaryName(name);
  const binariesDir = path$d.join(os.homedir(), ".eigent", "bin");
  const binariesDirExists = await fs$j.existsSync(binariesDir);
  return binariesDirExists ? path$d.join(binariesDir, binaryName) : binaryName;
}
async function isBinaryExists(name) {
  const cmd = await getBinaryPath(name);
  return await fs$j.existsSync(cmd);
}
function getMainWindow() {
  const windows = BrowserWindow.getAllWindows();
  return windows.length > 0 ? windows[0] : null;
}
async function checkToolInstalled() {
  return new Promise(async (resolve, reject) => {
    if (!await isBinaryExists("uv")) {
      resolve(false);
    }
    if (!await isBinaryExists("bun")) {
      resolve(false);
    }
    resolve(true);
  });
}
async function installCommandTool() {
  return new Promise(async (resolve, reject) => {
    let isAllInstalled = true;
    console.log("Checking if command line tools are installed, installing if not");
    if (!await isBinaryExists("uv")) {
      console.log("start install uv");
      await runInstallScript("install-uv.js");
      const uv_installed = await isBinaryExists("uv");
      const mainWindow = getMainWindow();
      if (mainWindow && !mainWindow.isDestroyed()) {
        if (uv_installed) {
          mainWindow.webContents.send("install-dependencies-log", { type: "stdout", data: "" });
        } else {
          isAllInstalled = false;
          mainWindow.webContents.send("install-dependencies-complete", { success: false, code: 2, error: `Script exited with code ${2}` });
        }
      }
    }
    if (!await isBinaryExists("bun")) {
      console.log("start install bun");
      await runInstallScript("install-bun.js");
      const bun_installed = await isBinaryExists("bun");
      const mainWindow = getMainWindow();
      if (mainWindow && !mainWindow.isDestroyed()) {
        if (bun_installed) {
          mainWindow.webContents.send("install-dependencies-log", { type: "stdout", data: "" });
        } else {
          isAllInstalled = false;
          mainWindow.webContents.send("install-dependencies-complete", { success: false, code: 2, error: `Script exited with code ${2}` });
        }
      }
    }
    resolve(isAllInstalled);
  });
}
async function installDependencies() {
  return new Promise(async (resolve, reject) => {
    console.log("start install dependencies");
    const mainWindow = getMainWindow();
    if (mainWindow && !mainWindow.isDestroyed()) {
      mainWindow.webContents.send("install-dependencies-start");
    }
    const isInstalCommandTool = await installCommandTool();
    if (!isInstalCommandTool) {
      resolve(false);
      return;
    }
    const uv_path = await getBinaryPath("uv");
    const backendPath = getBackendPath();
    if (!fs$j.existsSync(backendPath)) {
      fs$j.mkdirSync(backendPath, { recursive: true });
    }
    const installingLockPath = path$d.join(backendPath, "uv_installing.lock");
    fs$j.writeFileSync(installingLockPath, "");
    const proxy = ["--default-index", "https://pypi.tuna.tsinghua.edu.cn/simple"];
    function isInChinaTimezone() {
      const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
      return timezone === "Asia/Shanghai";
    }
    console.log("isInChinaTimezone", isInChinaTimezone());
    const node_process = spawn(uv_path, ["sync", "--no-dev", ...isInChinaTimezone() ? proxy : []], { cwd: backendPath });
    node_process.stdout.on("data", (data) => {
      log.info(`Script output: ${data}`);
      const mainWindow2 = getMainWindow();
      if (mainWindow2 && !mainWindow2.isDestroyed()) {
        mainWindow2.webContents.send("install-dependencies-log", { type: "stdout", data: data.toString() });
      }
    });
    node_process.stderr.on("data", (data) => {
      log.error(`Script error: uv ${data}`);
      const mainWindow2 = getMainWindow();
      if (mainWindow2 && !mainWindow2.isDestroyed()) {
        mainWindow2.webContents.send("install-dependencies-log", { type: "stderr", data: data.toString() });
      }
    });
    node_process.on("close", async (code) => {
      if (fs$j.existsSync(installingLockPath)) {
        fs$j.unlinkSync(installingLockPath);
      }
      if (code === 0) {
        log.info("Script completed successfully");
        const installedLockPath = path$d.join(backendPath, "uv_installed.lock");
        fs$j.writeFileSync(installedLockPath, "");
        console.log("end install dependencies");
        spawn(uv_path, ["run", "task", "babel"], { cwd: backendPath });
        resolve(true);
      } else {
        log.error(`Script exited with code ${code}`);
        const mainWindow2 = getMainWindow();
        if (mainWindow2 && !mainWindow2.isDestroyed()) {
          mainWindow2.webContents.send("install-dependencies-complete", { success: false, code, error: `Script exited with code ${code}` });
          resolve(false);
        }
      }
    });
  });
}
async function startBackend(setPort) {
  console.log("start fastapi");
  const uv_path = await getBinaryPath("uv");
  const backendPath = getBackendPath();
  const port = await findAvailablePort(5001);
  if (setPort) {
    setPort(port);
  }
  const env = {
    ...process.env,
    SERVER_URL: "https://dev.eigent.ai/api",
    PYTHONIOENCODING: "utf-8"
  };
  return new Promise((resolve, reject) => {
    const node_process = spawn(
      uv_path,
      ["run", "uvicorn", "main:api", "--port", port.toString(), "--loop", "asyncio"],
      { cwd: backendPath, env, detached: false }
    );
    let started = false;
    node_process.stdout.on("data", (data) => {
      log.info(`fastapi output: ${data}`);
      if (!started && data.toString().includes("Uvicorn running on")) {
        started = true;
        resolve(node_process);
      }
    });
    node_process.stderr.on("data", (data) => {
      log.error(`fastapi stderr output: ${data}`);
      if (!started && data.toString().includes("Uvicorn running on")) {
        started = true;
        resolve(node_process);
      }
    });
    node_process.on("close", (code) => {
      if (!started) {
        reject(new Error(`fastapi exited with code ${code}`));
      }
    });
  });
}
function checkPortAvailable(port) {
  return new Promise((resolve) => {
    const server = net.createServer();
    server.once("error", (err) => {
      if (err.code === "EADDRINUSE") {
        resolve(false);
      } else {
        resolve(false);
      }
    });
    server.once("listening", () => {
      server.close(() => {
        console.log("try port", port);
        resolve(true);
      });
    });
    server.listen({ port, host: "127.0.0.1", exclusive: true });
  });
}
async function findAvailablePort(startPort, maxAttempts = 50) {
  let port = startPort;
  for (let i = 0; i < maxAttempts; i++) {
    const available = await checkPortAvailable(port);
    if (available) {
      return port;
    }
    port++;
  }
  throw new Error("No available port found");
}
class WebViewManager {
  constructor(window2) {
    __publicField(this, "webViews", /* @__PURE__ */ new Map());
    __publicField(this, "win", null);
    __publicField(this, "size", { x: 0, y: 0, width: 0, height: 0 });
    this.win = window2;
  }
  // Remove automatic IPC handler registration from constructor
  // IPC handlers should be registered once in the main process
  async captureWebview(webviewId) {
    const webContents = this.webViews.get(webviewId);
    if (!webContents) return null;
    const image = await webContents.view.webContents.capturePage();
    const jpegBuffer = image.toJPEG(10);
    return "data:image/jpeg;base64," + jpegBuffer.toString("base64");
  }
  setSize(size) {
    this.size = size;
    this.webViews.forEach((webview) => {
      if (webview.isActive && webview.isShow) {
        this.changeViewSize(webview.id, size);
      }
    });
  }
  getActiveWebview() {
    const activeWebviews = Array.from(this.webViews.values()).filter((webview) => webview.isActive);
    return activeWebviews.map((webview) => webview.id);
  }
  async createWebview(id = "1", url = "about:blank?use=0") {
    var _a;
    try {
      if (this.webViews.has(id)) {
        return { success: false, error: `Webview with id ${id} already exists` };
      }
      const view = new WebContentsView({
        webPreferences: {
          nodeIntegration: false,
          contextIsolation: true
        }
      });
      view.webContents.on("did-finish-load", () => {
        view.webContents.executeJavaScript(`
          window.addEventListener('mousedown', (e) => {
            if (!(e.target instanceof HTMLButtonElement || e.target instanceof HTMLInputElement)) {
              e.preventDefault();
            }
          }, true);
        `);
      });
      view.webContents.audioMuted = true;
      let newId = Number(id);
      view.setBounds({ x: -9999 + newId * 100, y: -9999 + newId * 100, width: 100, height: 100 });
      view.setBorderRadius(16);
      await view.webContents.loadURL(url);
      const webViewInfo = {
        id,
        view,
        initialUrl: url,
        currentUrl: url,
        isActive: false,
        isShow: false
      };
      view.webContents.on("did-navigate-in-page", (event, url2) => {
        var _a2;
        if (webViewInfo.isActive && webViewInfo.isShow && url2 !== "about:blank?use=0" && url2 !== "about:blank") {
          console.log("did-navigate-in-page", id, url2);
          (_a2 = this.win) == null ? void 0 : _a2.webContents.send("url-updated", url2);
          return;
        }
      });
      view.webContents.on("did-navigate", (event, navigationUrl) => {
        var _a2;
        webViewInfo.currentUrl = navigationUrl;
        if (navigationUrl !== webViewInfo.initialUrl) {
          webViewInfo.isActive = true;
        }
        console.log(`Webview ${id} navigated to: ${navigationUrl}`);
        if (webViewInfo.isActive && webViewInfo.isShow && navigationUrl !== "about:blank?use=0" && navigationUrl !== "about:blank") {
          console.log("did-navigate", id, url);
          (_a2 = this.win) == null ? void 0 : _a2.webContents.send("url-updated", url);
          return;
        }
        webViewInfo.view.setBounds({ x: -1919, y: -1079, width: 1920, height: 1080 });
        const activeSize = this.getActiveWebview().length;
        const allSize = Array.from(this.webViews.values()).length;
        if (allSize - activeSize <= 3) {
          const newId2 = Array.from(this.webViews.keys()).length + 2;
          this.createWebview(newId2.toString(), "about:blank?use=0");
          this.createWebview((newId2 + 1).toString(), "about:blank?use=0");
          this.createWebview((newId2 + 2).toString(), "about:blank?use=0");
        }
        if (this.win && !this.win.isDestroyed()) {
          this.win.webContents.send("webview-navigated", id, navigationUrl);
        }
      });
      view.webContents.setWindowOpenHandler(({ url: url2 }) => {
        view.webContents.loadURL(url2);
        return { action: "deny" };
      });
      this.webViews.set(id, webViewInfo);
      (_a = this.win) == null ? void 0 : _a.contentView.addChildView(view);
      return { success: true, id, hidden: true };
    } catch (error) {
      console.error(`Failed to create hidden webview ${id}:`, error);
      return { success: false, error: error.message };
    }
  }
  changeViewSize(id, size) {
    try {
      const webViewInfo = this.webViews.get(id);
      if (!webViewInfo) {
        return { success: false, error: `Webview with id ${id} not found` };
      }
      const { x, y, width, height } = size;
      if (webViewInfo.isActive && webViewInfo.isShow) {
        webViewInfo.view.setBounds({ x, y, width: Math.max(width, 100), height: Math.max(height, 100) });
      } else {
        let newId = Number(id);
        webViewInfo.view.setBounds({ x: -9999 + newId * 100, y: -9999 + newId * 100, width: Math.max(width, 100), height: Math.max(height, 100) });
      }
      return { success: true };
    } catch (error) {
      console.error(`Failed to resize all webviews:`, error);
      return { success: false, error: error.message };
    }
  }
  hideWebview(id) {
    const webViewInfo = this.webViews.get(id);
    if (!webViewInfo) {
      return { success: false, error: `Webview with id ${id} not found` };
    }
    let newId = Number(id);
    webViewInfo.view.setBounds({ x: -9999 + newId * 100, y: -9999 + newId * 100, width: 100, height: 100 });
    webViewInfo.isShow = false;
    return { success: true };
  }
  hideAllWebview() {
    this.webViews.forEach((webview) => {
      let newId = Number(webview.id);
      webview.view.setBounds({ x: -9999 + newId * 100, y: -9999 + newId * 100, width: 100, height: 100 });
      webview.isShow = false;
    });
  }
  showWebview(id) {
    var _a;
    const webViewInfo = this.webViews.get(id);
    if (!webViewInfo) {
      return { success: false, error: `Webview with id ${id} not found` };
    }
    const currentUrl = webViewInfo.view.webContents.getURL();
    (_a = this.win) == null ? void 0 : _a.webContents.send("url-updated", currentUrl);
    webViewInfo.isShow = true;
    this.changeViewSize(id, this.size);
    console.log("showWebview", id, this.size);
    if (this.win && !this.win.isDestroyed()) {
      this.win.webContents.send("webview-show", id);
    }
    return { success: true };
  }
  getShowWebview() {
    return JSON.parse(JSON.stringify(Array.from(this.webViews.values()).filter((webview) => webview.isShow).map((webview) => webview.id)));
  }
  destroyWebview(id) {
    var _a;
    try {
      const webViewInfo = this.webViews.get(id);
      if (!webViewInfo) {
        return { success: false, error: `Webview with id ${id} not found` };
      }
      if ((_a = this.win) == null ? void 0 : _a.contentView) {
        this.win.contentView.removeChildView(webViewInfo.view);
      }
      webViewInfo.view.webContents.close();
      this.webViews.delete(id);
      console.log(`Webview ${id} destroyed successfully`);
      return { success: true };
    } catch (error) {
      console.error(`Failed to destroy webview ${id}:`, error);
      return { success: false, error: error.message };
    }
  }
  distroy() {
  }
}
class FileReader {
  constructor(window2) {
    __publicField(this, "win", null);
    this.win = window2;
  }
  // Remove automatic IPC handler registration from constructor
  // IPC handlers should be registered once in the main process
  async parseDocx(filePath) {
    try {
      const result = await mammoth.convertToHtml({ path: filePath });
      return result.value;
    } catch (error) {
      console.error("DOCX parsing error:", error);
      throw error;
    }
  }
  async parseDoc(filePath) {
    try {
      const result = await mammoth.convertToHtml({ path: filePath });
      return result.value;
    } catch (error) {
      console.error("DOC parsing error:", error);
      throw error;
    }
  }
  async parsePptx(filePath) {
    var _a, _b, _c;
    try {
      const directory = await unzipper.Open.file(filePath);
      const slideFiles = directory.files.filter((f) => f.path.match(/^ppt\/slides\/slide\d+\.xml$/));
      let html = '<div style="font-family: sans-serif;">';
      for (let i = 0; i < slideFiles.length; i++) {
        const file2 = slideFiles[i];
        const contentBuffer = await file2.buffer();
        const content = contentBuffer.toString("utf-8");
        const parsed = await parseStringPromise(content);
        html += `<h3>Slide ${i + 1}</h3><ul>`;
        const texts = parsed["p:sld"]["p:cSld"][0]["p:spTree"][0]["p:sp"] || [];
        for (const textNode of texts) {
          const paras = ((_b = (_a = textNode == null ? void 0 : textNode["p:txBody"]) == null ? void 0 : _a[0]) == null ? void 0 : _b["a:p"]) || [];
          for (const para of paras) {
            const runs = (para == null ? void 0 : para["a:r"]) || [];
            for (const run of runs) {
              const text = (_c = run == null ? void 0 : run["a:t"]) == null ? void 0 : _c[0];
              if (text) {
                html += `<li>${text}</li>`;
              }
            }
          }
        }
        html += "</ul><hr/>";
      }
      html += "</div>";
      return html;
    } catch (error) {
      console.error("PPTX unzip parse error:", error);
      throw error;
    }
  }
  async parseCsv(filePath) {
    try {
      const fileContent = fs$j.readFileSync(filePath, "utf-8");
      const result = Papa.parse(fileContent, {
        header: true,
        skipEmptyLines: true,
        delimiter: ","
      });
      if (result.data && result.data.length > 0) {
        const headers = Object.keys(result.data[0]);
        let html = '<table style="border-collapse: collapse; width: 100%; font-family: monospace;">';
        html += '<thead><tr style="background-color: #f5f5f5;">';
        headers.forEach((header) => {
          html += `<th style="border: 1px solid #ddd; padding: 8px; text-align: left;">${header}</th>`;
        });
        html += "</tr></thead>";
        html += "<tbody>";
        result.data.forEach((row) => {
          html += "<tr>";
          headers.forEach((header) => {
            html += `<td style="border: 1px solid #ddd; padding: 8px;">${row[header] || ""}</td>`;
          });
          html += "</tr>";
        });
        html += "</tbody></table>";
        return html;
      }
      return "<p>Empty CSV file</p>";
    } catch (error) {
      console.error("CSV parsing error:", error);
      throw error;
    }
  }
  openFile(type, filePath, isShowSourceCode) {
    return new Promise(async (resolve, reject) => {
      try {
        if (type === "md") {
          const content = fs$j.readFileSync(filePath, "utf-8");
          resolve(content);
        } else if (isShowSourceCode && type === "html") {
          const content = fs$j.readFileSync(filePath, "utf-8");
          resolve(content);
        } else if (["pdf", "html"].includes(type)) {
          resolve(filePath);
        } else if (type === "csv") {
          try {
            const htmlContent = await this.parseCsv(filePath);
            resolve(htmlContent);
          } catch (error) {
            console.warn("CSV parsing failed, reading as text:", error);
            const content = fs$j.readFileSync(filePath, "utf-8");
            resolve(content);
          }
        } else if (type === "docx") {
          try {
            const htmlContent = await this.parseDocx(filePath);
            resolve(htmlContent);
          } catch (error) {
            console.warn("DOCX parsing failed, reading as text:", error);
            const content = fs$j.readFileSync(filePath, "utf-8");
            resolve(content);
          }
        } else if (type === "doc") {
          try {
            const htmlContent = await this.parseDoc(filePath);
            resolve(htmlContent);
          } catch (error) {
            console.warn("DOC parsing failed, reading as text:", error);
            const content = fs$j.readFileSync(filePath, "utf-8");
            resolve(content);
          }
        } else if (type === "pptx") {
          try {
            const htmlContent = await this.parsePptx(filePath);
            resolve(htmlContent);
          } catch (error) {
            console.warn("PPTX parsing failed, reading as binary string:", error);
            const content = fs$j.readFileSync(filePath, "base64");
            resolve(`<pre>${content}</pre>`);
          }
        } else {
          const content = fs$j.readFileSync(filePath, "utf-8");
          resolve(content);
        }
      } catch (error) {
        reject(error);
      }
    });
  }
  getFileList(email, taskId) {
    const safeEmail = email.split("@")[0].replace(/[\\/*?:"<>|\s]/g, "_").replace(/^\.+|\.+$/g, "");
    const userHome = app.getPath("home");
    const dirPath = path$d.join(userHome, "eigent", safeEmail, `task_${taskId}`);
    try {
      if (!fs$j.existsSync(dirPath)) {
        return [];
      }
      const files = fs$j.readdirSync(dirPath);
      return files.filter((file2) => !file2.startsWith(".")).map((file2) => {
        var _a;
        return {
          path: path$d.join(dirPath, file2),
          name: file2,
          type: ((_a = file2.split(".").pop()) == null ? void 0 : _a.toLowerCase()) || ""
        };
      });
    } catch (err) {
      console.error("Load file failed:", err);
      return [];
    }
  }
}
const MCP_CONFIG_DIR = path$d.join(os.homedir(), ".eigent");
const MCP_CONFIG_PATH = path$d.join(MCP_CONFIG_DIR, "mcp.json");
function getDefaultConfig() {
  return { mcpServers: {} };
}
function readMcpConfig() {
  try {
    if (!fs$j.existsSync(MCP_CONFIG_PATH)) {
      writeMcpConfig(getDefaultConfig());
      return getDefaultConfig();
    }
    const data = fs$j.readFileSync(MCP_CONFIG_PATH, "utf-8");
    const parsed = JSON.parse(data);
    if (!parsed.mcpServers || typeof parsed.mcpServers !== "object") {
      return getDefaultConfig();
    }
    return parsed;
  } catch (e) {
    return getDefaultConfig();
  }
}
function writeMcpConfig(config) {
  if (!fs$j.existsSync(MCP_CONFIG_DIR)) {
    fs$j.mkdirSync(MCP_CONFIG_DIR, { recursive: true });
  }
  fs$j.writeFileSync(MCP_CONFIG_PATH, JSON.stringify(config, null, 2), "utf-8");
}
function addMcp(name, mcp) {
  const config = readMcpConfig();
  if (!config.mcpServers[name]) {
    config.mcpServers[name] = mcp;
    writeMcpConfig(config);
  }
}
function removeMcp(name) {
  const config = readMcpConfig();
  console.log("removeMcp", name);
  if (config.mcpServers[name]) {
    delete config.mcpServers[name];
    writeMcpConfig(config);
  }
}
function updateMcp(name, mcp) {
  const config = readMcpConfig();
  config.mcpServers[name] = mcp;
  writeMcpConfig(config);
}
const ENV_START = "# === MCP INTEGRATION ENV START ===";
const ENV_END = "# === MCP INTEGRATION ENV END ===";
function getEnvPath(email) {
  const tempEmail = email.split("@")[0].replace(/[\\/*?:"<>|\s]/g, "_").replace(".", "_");
  const envPath = path$d.join(os.homedir(), ".eigent", ".env." + tempEmail);
  const defaultEnv = path$d.join(process.resourcesPath, "backend", ".env");
  if (!fs$j.existsSync(envPath) && fs$j.existsSync(defaultEnv)) {
    fs$j.copyFileSync(defaultEnv, envPath);
    fs$j.chmodSync(envPath, 384);
  }
  return envPath;
}
function updateEnvBlock(lines, kv) {
  let start = lines.findIndex((l) => l.trim() === ENV_START);
  let end = lines.findIndex((l) => l.trim() === ENV_END);
  if (start === -1 || end === -1 || end < start) {
    lines.push(ENV_START);
    Object.entries(kv).forEach(([k, v]) => {
      lines.push(`${k}=${v}`);
    });
    lines.push(ENV_END);
    return lines;
  }
  const block = lines.slice(start + 1, end);
  const map = {};
  block.forEach((line) => {
    const m = line.match(/^([A-Z0-9_]+)=(.*)$/);
    if (m) map[m[1]] = m[2];
  });
  Object.entries(kv).forEach(([k, v]) => {
    map[k] = v;
  });
  const newBlock = Object.entries(map).map(([k, v]) => `${k}=${v}`);
  return [
    ...lines.slice(0, start + 1),
    ...newBlock,
    ...lines.slice(end)
  ];
}
function removeEnvKey(lines, key) {
  let start = lines.findIndex((l) => l.trim() === ENV_START);
  let end = lines.findIndex((l) => l.trim() === ENV_END);
  if (start === -1 || end === -1 || end < start) return lines;
  const block = lines.slice(start + 1, end);
  const newBlock = block.filter((line) => !line.startsWith(key + "="));
  return [
    ...lines.slice(0, start + 1),
    ...newBlock,
    ...lines.slice(end)
  ];
}
var commonjsGlobal = typeof globalThis !== "undefined" ? globalThis : typeof window !== "undefined" ? window : typeof global !== "undefined" ? global : typeof self !== "undefined" ? self : {};
function getDefaultExportFromCjs(x) {
  return x && x.__esModule && Object.prototype.hasOwnProperty.call(x, "default") ? x["default"] : x;
}
var lib = { exports: {} };
var fs$i = {};
var universalify$1 = {};
universalify$1.fromCallback = function(fn) {
  return Object.defineProperty(function(...args) {
    if (typeof args[args.length - 1] === "function") fn.apply(this, args);
    else {
      return new Promise((resolve, reject) => {
        args.push((err, res) => err != null ? reject(err) : resolve(res));
        fn.apply(this, args);
      });
    }
  }, "name", { value: fn.name });
};
universalify$1.fromPromise = function(fn) {
  return Object.defineProperty(function(...args) {
    const cb = args[args.length - 1];
    if (typeof cb !== "function") return fn.apply(this, args);
    else {
      args.pop();
      fn.apply(this, args).then((r) => cb(null, r), cb);
    }
  }, "name", { value: fn.name });
};
var constants = require$$0;
var origCwd = process.cwd;
var cwd = null;
var platform = process.env.GRACEFUL_FS_PLATFORM || process.platform;
process.cwd = function() {
  if (!cwd)
    cwd = origCwd.call(process);
  return cwd;
};
try {
  process.cwd();
} catch (er) {
}
if (typeof process.chdir === "function") {
  var chdir = process.chdir;
  process.chdir = function(d) {
    cwd = null;
    chdir.call(process, d);
  };
  if (Object.setPrototypeOf) Object.setPrototypeOf(process.chdir, chdir);
}
var polyfills$1 = patch$1;
function patch$1(fs2) {
  if (constants.hasOwnProperty("O_SYMLINK") && process.version.match(/^v0\.6\.[0-2]|^v0\.5\./)) {
    patchLchmod(fs2);
  }
  if (!fs2.lutimes) {
    patchLutimes(fs2);
  }
  fs2.chown = chownFix(fs2.chown);
  fs2.fchown = chownFix(fs2.fchown);
  fs2.lchown = chownFix(fs2.lchown);
  fs2.chmod = chmodFix(fs2.chmod);
  fs2.fchmod = chmodFix(fs2.fchmod);
  fs2.lchmod = chmodFix(fs2.lchmod);
  fs2.chownSync = chownFixSync(fs2.chownSync);
  fs2.fchownSync = chownFixSync(fs2.fchownSync);
  fs2.lchownSync = chownFixSync(fs2.lchownSync);
  fs2.chmodSync = chmodFixSync(fs2.chmodSync);
  fs2.fchmodSync = chmodFixSync(fs2.fchmodSync);
  fs2.lchmodSync = chmodFixSync(fs2.lchmodSync);
  fs2.stat = statFix(fs2.stat);
  fs2.fstat = statFix(fs2.fstat);
  fs2.lstat = statFix(fs2.lstat);
  fs2.statSync = statFixSync(fs2.statSync);
  fs2.fstatSync = statFixSync(fs2.fstatSync);
  fs2.lstatSync = statFixSync(fs2.lstatSync);
  if (fs2.chmod && !fs2.lchmod) {
    fs2.lchmod = function(path2, mode, cb) {
      if (cb) process.nextTick(cb);
    };
    fs2.lchmodSync = function() {
    };
  }
  if (fs2.chown && !fs2.lchown) {
    fs2.lchown = function(path2, uid, gid, cb) {
      if (cb) process.nextTick(cb);
    };
    fs2.lchownSync = function() {
    };
  }
  if (platform === "win32") {
    fs2.rename = typeof fs2.rename !== "function" ? fs2.rename : function(fs$rename) {
      function rename2(from, to, cb) {
        var start = Date.now();
        var backoff = 0;
        fs$rename(from, to, function CB(er) {
          if (er && (er.code === "EACCES" || er.code === "EPERM" || er.code === "EBUSY") && Date.now() - start < 6e4) {
            setTimeout(function() {
              fs2.stat(to, function(stater, st) {
                if (stater && stater.code === "ENOENT")
                  fs$rename(from, to, CB);
                else
                  cb(er);
              });
            }, backoff);
            if (backoff < 100)
              backoff += 10;
            return;
          }
          if (cb) cb(er);
        });
      }
      if (Object.setPrototypeOf) Object.setPrototypeOf(rename2, fs$rename);
      return rename2;
    }(fs2.rename);
  }
  fs2.read = typeof fs2.read !== "function" ? fs2.read : function(fs$read) {
    function read(fd, buffer, offset, length, position, callback_) {
      var callback;
      if (callback_ && typeof callback_ === "function") {
        var eagCounter = 0;
        callback = function(er, _, __) {
          if (er && er.code === "EAGAIN" && eagCounter < 10) {
            eagCounter++;
            return fs$read.call(fs2, fd, buffer, offset, length, position, callback);
          }
          callback_.apply(this, arguments);
        };
      }
      return fs$read.call(fs2, fd, buffer, offset, length, position, callback);
    }
    if (Object.setPrototypeOf) Object.setPrototypeOf(read, fs$read);
    return read;
  }(fs2.read);
  fs2.readSync = typeof fs2.readSync !== "function" ? fs2.readSync : /* @__PURE__ */ function(fs$readSync) {
    return function(fd, buffer, offset, length, position) {
      var eagCounter = 0;
      while (true) {
        try {
          return fs$readSync.call(fs2, fd, buffer, offset, length, position);
        } catch (er) {
          if (er.code === "EAGAIN" && eagCounter < 10) {
            eagCounter++;
            continue;
          }
          throw er;
        }
      }
    };
  }(fs2.readSync);
  function patchLchmod(fs22) {
    fs22.lchmod = function(path2, mode, callback) {
      fs22.open(
        path2,
        constants.O_WRONLY | constants.O_SYMLINK,
        mode,
        function(err, fd) {
          if (err) {
            if (callback) callback(err);
            return;
          }
          fs22.fchmod(fd, mode, function(err2) {
            fs22.close(fd, function(err22) {
              if (callback) callback(err2 || err22);
            });
          });
        }
      );
    };
    fs22.lchmodSync = function(path2, mode) {
      var fd = fs22.openSync(path2, constants.O_WRONLY | constants.O_SYMLINK, mode);
      var threw = true;
      var ret;
      try {
        ret = fs22.fchmodSync(fd, mode);
        threw = false;
      } finally {
        if (threw) {
          try {
            fs22.closeSync(fd);
          } catch (er) {
          }
        } else {
          fs22.closeSync(fd);
        }
      }
      return ret;
    };
  }
  function patchLutimes(fs22) {
    if (constants.hasOwnProperty("O_SYMLINK") && fs22.futimes) {
      fs22.lutimes = function(path2, at, mt, cb) {
        fs22.open(path2, constants.O_SYMLINK, function(er, fd) {
          if (er) {
            if (cb) cb(er);
            return;
          }
          fs22.futimes(fd, at, mt, function(er2) {
            fs22.close(fd, function(er22) {
              if (cb) cb(er2 || er22);
            });
          });
        });
      };
      fs22.lutimesSync = function(path2, at, mt) {
        var fd = fs22.openSync(path2, constants.O_SYMLINK);
        var ret;
        var threw = true;
        try {
          ret = fs22.futimesSync(fd, at, mt);
          threw = false;
        } finally {
          if (threw) {
            try {
              fs22.closeSync(fd);
            } catch (er) {
            }
          } else {
            fs22.closeSync(fd);
          }
        }
        return ret;
      };
    } else if (fs22.futimes) {
      fs22.lutimes = function(_a, _b, _c, cb) {
        if (cb) process.nextTick(cb);
      };
      fs22.lutimesSync = function() {
      };
    }
  }
  function chmodFix(orig) {
    if (!orig) return orig;
    return function(target, mode, cb) {
      return orig.call(fs2, target, mode, function(er) {
        if (chownErOk(er)) er = null;
        if (cb) cb.apply(this, arguments);
      });
    };
  }
  function chmodFixSync(orig) {
    if (!orig) return orig;
    return function(target, mode) {
      try {
        return orig.call(fs2, target, mode);
      } catch (er) {
        if (!chownErOk(er)) throw er;
      }
    };
  }
  function chownFix(orig) {
    if (!orig) return orig;
    return function(target, uid, gid, cb) {
      return orig.call(fs2, target, uid, gid, function(er) {
        if (chownErOk(er)) er = null;
        if (cb) cb.apply(this, arguments);
      });
    };
  }
  function chownFixSync(orig) {
    if (!orig) return orig;
    return function(target, uid, gid) {
      try {
        return orig.call(fs2, target, uid, gid);
      } catch (er) {
        if (!chownErOk(er)) throw er;
      }
    };
  }
  function statFix(orig) {
    if (!orig) return orig;
    return function(target, options, cb) {
      if (typeof options === "function") {
        cb = options;
        options = null;
      }
      function callback(er, stats) {
        if (stats) {
          if (stats.uid < 0) stats.uid += 4294967296;
          if (stats.gid < 0) stats.gid += 4294967296;
        }
        if (cb) cb.apply(this, arguments);
      }
      return options ? orig.call(fs2, target, options, callback) : orig.call(fs2, target, callback);
    };
  }
  function statFixSync(orig) {
    if (!orig) return orig;
    return function(target, options) {
      var stats = options ? orig.call(fs2, target, options) : orig.call(fs2, target);
      if (stats) {
        if (stats.uid < 0) stats.uid += 4294967296;
        if (stats.gid < 0) stats.gid += 4294967296;
      }
      return stats;
    };
  }
  function chownErOk(er) {
    if (!er)
      return true;
    if (er.code === "ENOSYS")
      return true;
    var nonroot = !process.getuid || process.getuid() !== 0;
    if (nonroot) {
      if (er.code === "EINVAL" || er.code === "EPERM")
        return true;
    }
    return false;
  }
}
var Stream = require$$0$1.Stream;
var legacyStreams = legacy$1;
function legacy$1(fs2) {
  return {
    ReadStream,
    WriteStream
  };
  function ReadStream(path2, options) {
    if (!(this instanceof ReadStream)) return new ReadStream(path2, options);
    Stream.call(this);
    var self2 = this;
    this.path = path2;
    this.fd = null;
    this.readable = true;
    this.paused = false;
    this.flags = "r";
    this.mode = 438;
    this.bufferSize = 64 * 1024;
    options = options || {};
    var keys = Object.keys(options);
    for (var index = 0, length = keys.length; index < length; index++) {
      var key = keys[index];
      this[key] = options[key];
    }
    if (this.encoding) this.setEncoding(this.encoding);
    if (this.start !== void 0) {
      if ("number" !== typeof this.start) {
        throw TypeError("start must be a Number");
      }
      if (this.end === void 0) {
        this.end = Infinity;
      } else if ("number" !== typeof this.end) {
        throw TypeError("end must be a Number");
      }
      if (this.start > this.end) {
        throw new Error("start must be <= end");
      }
      this.pos = this.start;
    }
    if (this.fd !== null) {
      process.nextTick(function() {
        self2._read();
      });
      return;
    }
    fs2.open(this.path, this.flags, this.mode, function(err, fd) {
      if (err) {
        self2.emit("error", err);
        self2.readable = false;
        return;
      }
      self2.fd = fd;
      self2.emit("open", fd);
      self2._read();
    });
  }
  function WriteStream(path2, options) {
    if (!(this instanceof WriteStream)) return new WriteStream(path2, options);
    Stream.call(this);
    this.path = path2;
    this.fd = null;
    this.writable = true;
    this.flags = "w";
    this.encoding = "binary";
    this.mode = 438;
    this.bytesWritten = 0;
    options = options || {};
    var keys = Object.keys(options);
    for (var index = 0, length = keys.length; index < length; index++) {
      var key = keys[index];
      this[key] = options[key];
    }
    if (this.start !== void 0) {
      if ("number" !== typeof this.start) {
        throw TypeError("start must be a Number");
      }
      if (this.start < 0) {
        throw new Error("start must be >= zero");
      }
      this.pos = this.start;
    }
    this.busy = false;
    this._queue = [];
    if (this.fd === null) {
      this._open = fs2.open;
      this._queue.push([this._open, this.path, this.flags, this.mode, void 0]);
      this.flush();
    }
  }
}
var clone_1 = clone$1;
var getPrototypeOf = Object.getPrototypeOf || function(obj) {
  return obj.__proto__;
};
function clone$1(obj) {
  if (obj === null || typeof obj !== "object")
    return obj;
  if (obj instanceof Object)
    var copy2 = { __proto__: getPrototypeOf(obj) };
  else
    var copy2 = /* @__PURE__ */ Object.create(null);
  Object.getOwnPropertyNames(obj).forEach(function(key) {
    Object.defineProperty(copy2, key, Object.getOwnPropertyDescriptor(obj, key));
  });
  return copy2;
}
var fs$h = fs$j;
var polyfills = polyfills$1;
var legacy = legacyStreams;
var clone = clone_1;
var util$1 = require$$4;
var gracefulQueue;
var previousSymbol;
if (typeof Symbol === "function" && typeof Symbol.for === "function") {
  gracefulQueue = Symbol.for("graceful-fs.queue");
  previousSymbol = Symbol.for("graceful-fs.previous");
} else {
  gracefulQueue = "___graceful-fs.queue";
  previousSymbol = "___graceful-fs.previous";
}
function noop() {
}
function publishQueue(context, queue2) {
  Object.defineProperty(context, gracefulQueue, {
    get: function() {
      return queue2;
    }
  });
}
var debug = noop;
if (util$1.debuglog)
  debug = util$1.debuglog("gfs4");
else if (/\bgfs4\b/i.test(process.env.NODE_DEBUG || ""))
  debug = function() {
    var m = util$1.format.apply(util$1, arguments);
    m = "GFS4: " + m.split(/\n/).join("\nGFS4: ");
    console.error(m);
  };
if (!fs$h[gracefulQueue]) {
  var queue = commonjsGlobal[gracefulQueue] || [];
  publishQueue(fs$h, queue);
  fs$h.close = function(fs$close) {
    function close(fd, cb) {
      return fs$close.call(fs$h, fd, function(err) {
        if (!err) {
          resetQueue();
        }
        if (typeof cb === "function")
          cb.apply(this, arguments);
      });
    }
    Object.defineProperty(close, previousSymbol, {
      value: fs$close
    });
    return close;
  }(fs$h.close);
  fs$h.closeSync = function(fs$closeSync) {
    function closeSync(fd) {
      fs$closeSync.apply(fs$h, arguments);
      resetQueue();
    }
    Object.defineProperty(closeSync, previousSymbol, {
      value: fs$closeSync
    });
    return closeSync;
  }(fs$h.closeSync);
  if (/\bgfs4\b/i.test(process.env.NODE_DEBUG || "")) {
    process.on("exit", function() {
      debug(fs$h[gracefulQueue]);
      require$$5.equal(fs$h[gracefulQueue].length, 0);
    });
  }
}
if (!commonjsGlobal[gracefulQueue]) {
  publishQueue(commonjsGlobal, fs$h[gracefulQueue]);
}
var gracefulFs = patch(clone(fs$h));
if (process.env.TEST_GRACEFUL_FS_GLOBAL_PATCH && !fs$h.__patched) {
  gracefulFs = patch(fs$h);
  fs$h.__patched = true;
}
function patch(fs2) {
  polyfills(fs2);
  fs2.gracefulify = patch;
  fs2.createReadStream = createReadStream;
  fs2.createWriteStream = createWriteStream;
  var fs$readFile = fs2.readFile;
  fs2.readFile = readFile2;
  function readFile2(path2, options, cb) {
    if (typeof options === "function")
      cb = options, options = null;
    return go$readFile(path2, options, cb);
    function go$readFile(path22, options2, cb2, startTime) {
      return fs$readFile(path22, options2, function(err) {
        if (err && (err.code === "EMFILE" || err.code === "ENFILE"))
          enqueue([go$readFile, [path22, options2, cb2], err, startTime || Date.now(), Date.now()]);
        else {
          if (typeof cb2 === "function")
            cb2.apply(this, arguments);
        }
      });
    }
  }
  var fs$writeFile = fs2.writeFile;
  fs2.writeFile = writeFile2;
  function writeFile2(path2, data, options, cb) {
    if (typeof options === "function")
      cb = options, options = null;
    return go$writeFile(path2, data, options, cb);
    function go$writeFile(path22, data2, options2, cb2, startTime) {
      return fs$writeFile(path22, data2, options2, function(err) {
        if (err && (err.code === "EMFILE" || err.code === "ENFILE"))
          enqueue([go$writeFile, [path22, data2, options2, cb2], err, startTime || Date.now(), Date.now()]);
        else {
          if (typeof cb2 === "function")
            cb2.apply(this, arguments);
        }
      });
    }
  }
  var fs$appendFile = fs2.appendFile;
  if (fs$appendFile)
    fs2.appendFile = appendFile;
  function appendFile(path2, data, options, cb) {
    if (typeof options === "function")
      cb = options, options = null;
    return go$appendFile(path2, data, options, cb);
    function go$appendFile(path22, data2, options2, cb2, startTime) {
      return fs$appendFile(path22, data2, options2, function(err) {
        if (err && (err.code === "EMFILE" || err.code === "ENFILE"))
          enqueue([go$appendFile, [path22, data2, options2, cb2], err, startTime || Date.now(), Date.now()]);
        else {
          if (typeof cb2 === "function")
            cb2.apply(this, arguments);
        }
      });
    }
  }
  var fs$copyFile = fs2.copyFile;
  if (fs$copyFile)
    fs2.copyFile = copyFile2;
  function copyFile2(src, dest, flags, cb) {
    if (typeof flags === "function") {
      cb = flags;
      flags = 0;
    }
    return go$copyFile(src, dest, flags, cb);
    function go$copyFile(src2, dest2, flags2, cb2, startTime) {
      return fs$copyFile(src2, dest2, flags2, function(err) {
        if (err && (err.code === "EMFILE" || err.code === "ENFILE"))
          enqueue([go$copyFile, [src2, dest2, flags2, cb2], err, startTime || Date.now(), Date.now()]);
        else {
          if (typeof cb2 === "function")
            cb2.apply(this, arguments);
        }
      });
    }
  }
  var fs$readdir = fs2.readdir;
  fs2.readdir = readdir;
  var noReaddirOptionVersions = /^v[0-5]\./;
  function readdir(path2, options, cb) {
    if (typeof options === "function")
      cb = options, options = null;
    var go$readdir = noReaddirOptionVersions.test(process.version) ? function go$readdir2(path22, options2, cb2, startTime) {
      return fs$readdir(path22, fs$readdirCallback(
        path22,
        options2,
        cb2,
        startTime
      ));
    } : function go$readdir2(path22, options2, cb2, startTime) {
      return fs$readdir(path22, options2, fs$readdirCallback(
        path22,
        options2,
        cb2,
        startTime
      ));
    };
    return go$readdir(path2, options, cb);
    function fs$readdirCallback(path22, options2, cb2, startTime) {
      return function(err, files) {
        if (err && (err.code === "EMFILE" || err.code === "ENFILE"))
          enqueue([
            go$readdir,
            [path22, options2, cb2],
            err,
            startTime || Date.now(),
            Date.now()
          ]);
        else {
          if (files && files.sort)
            files.sort();
          if (typeof cb2 === "function")
            cb2.call(this, err, files);
        }
      };
    }
  }
  if (process.version.substr(0, 4) === "v0.8") {
    var legStreams = legacy(fs2);
    ReadStream = legStreams.ReadStream;
    WriteStream = legStreams.WriteStream;
  }
  var fs$ReadStream = fs2.ReadStream;
  if (fs$ReadStream) {
    ReadStream.prototype = Object.create(fs$ReadStream.prototype);
    ReadStream.prototype.open = ReadStream$open;
  }
  var fs$WriteStream = fs2.WriteStream;
  if (fs$WriteStream) {
    WriteStream.prototype = Object.create(fs$WriteStream.prototype);
    WriteStream.prototype.open = WriteStream$open;
  }
  Object.defineProperty(fs2, "ReadStream", {
    get: function() {
      return ReadStream;
    },
    set: function(val) {
      ReadStream = val;
    },
    enumerable: true,
    configurable: true
  });
  Object.defineProperty(fs2, "WriteStream", {
    get: function() {
      return WriteStream;
    },
    set: function(val) {
      WriteStream = val;
    },
    enumerable: true,
    configurable: true
  });
  var FileReadStream = ReadStream;
  Object.defineProperty(fs2, "FileReadStream", {
    get: function() {
      return FileReadStream;
    },
    set: function(val) {
      FileReadStream = val;
    },
    enumerable: true,
    configurable: true
  });
  var FileWriteStream = WriteStream;
  Object.defineProperty(fs2, "FileWriteStream", {
    get: function() {
      return FileWriteStream;
    },
    set: function(val) {
      FileWriteStream = val;
    },
    enumerable: true,
    configurable: true
  });
  function ReadStream(path2, options) {
    if (this instanceof ReadStream)
      return fs$ReadStream.apply(this, arguments), this;
    else
      return ReadStream.apply(Object.create(ReadStream.prototype), arguments);
  }
  function ReadStream$open() {
    var that = this;
    open(that.path, that.flags, that.mode, function(err, fd) {
      if (err) {
        if (that.autoClose)
          that.destroy();
        that.emit("error", err);
      } else {
        that.fd = fd;
        that.emit("open", fd);
        that.read();
      }
    });
  }
  function WriteStream(path2, options) {
    if (this instanceof WriteStream)
      return fs$WriteStream.apply(this, arguments), this;
    else
      return WriteStream.apply(Object.create(WriteStream.prototype), arguments);
  }
  function WriteStream$open() {
    var that = this;
    open(that.path, that.flags, that.mode, function(err, fd) {
      if (err) {
        that.destroy();
        that.emit("error", err);
      } else {
        that.fd = fd;
        that.emit("open", fd);
      }
    });
  }
  function createReadStream(path2, options) {
    return new fs2.ReadStream(path2, options);
  }
  function createWriteStream(path2, options) {
    return new fs2.WriteStream(path2, options);
  }
  var fs$open = fs2.open;
  fs2.open = open;
  function open(path2, flags, mode, cb) {
    if (typeof mode === "function")
      cb = mode, mode = null;
    return go$open(path2, flags, mode, cb);
    function go$open(path22, flags2, mode2, cb2, startTime) {
      return fs$open(path22, flags2, mode2, function(err, fd) {
        if (err && (err.code === "EMFILE" || err.code === "ENFILE"))
          enqueue([go$open, [path22, flags2, mode2, cb2], err, startTime || Date.now(), Date.now()]);
        else {
          if (typeof cb2 === "function")
            cb2.apply(this, arguments);
        }
      });
    }
  }
  return fs2;
}
function enqueue(elem) {
  debug("ENQUEUE", elem[0].name, elem[1]);
  fs$h[gracefulQueue].push(elem);
  retry();
}
var retryTimer;
function resetQueue() {
  var now = Date.now();
  for (var i = 0; i < fs$h[gracefulQueue].length; ++i) {
    if (fs$h[gracefulQueue][i].length > 2) {
      fs$h[gracefulQueue][i][3] = now;
      fs$h[gracefulQueue][i][4] = now;
    }
  }
  retry();
}
function retry() {
  clearTimeout(retryTimer);
  retryTimer = void 0;
  if (fs$h[gracefulQueue].length === 0)
    return;
  var elem = fs$h[gracefulQueue].shift();
  var fn = elem[0];
  var args = elem[1];
  var err = elem[2];
  var startTime = elem[3];
  var lastTime = elem[4];
  if (startTime === void 0) {
    debug("RETRY", fn.name, args);
    fn.apply(null, args);
  } else if (Date.now() - startTime >= 6e4) {
    debug("TIMEOUT", fn.name, args);
    var cb = args.pop();
    if (typeof cb === "function")
      cb.call(null, err);
  } else {
    var sinceAttempt = Date.now() - lastTime;
    var sinceStart = Math.max(lastTime - startTime, 1);
    var desiredDelay = Math.min(sinceStart * 1.2, 100);
    if (sinceAttempt >= desiredDelay) {
      debug("RETRY", fn.name, args);
      fn.apply(null, args.concat([startTime]));
    } else {
      fs$h[gracefulQueue].push(elem);
    }
  }
  if (retryTimer === void 0) {
    retryTimer = setTimeout(retry, 0);
  }
}
(function(exports) {
  const u2 = universalify$1.fromCallback;
  const fs2 = gracefulFs;
  const api = [
    "access",
    "appendFile",
    "chmod",
    "chown",
    "close",
    "copyFile",
    "fchmod",
    "fchown",
    "fdatasync",
    "fstat",
    "fsync",
    "ftruncate",
    "futimes",
    "lchmod",
    "lchown",
    "link",
    "lstat",
    "mkdir",
    "mkdtemp",
    "open",
    "opendir",
    "readdir",
    "readFile",
    "readlink",
    "realpath",
    "rename",
    "rm",
    "rmdir",
    "stat",
    "symlink",
    "truncate",
    "unlink",
    "utimes",
    "writeFile"
  ].filter((key) => {
    return typeof fs2[key] === "function";
  });
  Object.keys(fs2).forEach((key) => {
    if (key === "promises") {
      return;
    }
    exports[key] = fs2[key];
  });
  api.forEach((method) => {
    exports[method] = u2(fs2[method]);
  });
  exports.exists = function(filename, callback) {
    if (typeof callback === "function") {
      return fs2.exists(filename, callback);
    }
    return new Promise((resolve) => {
      return fs2.exists(filename, resolve);
    });
  };
  exports.read = function(fd, buffer, offset, length, position, callback) {
    if (typeof callback === "function") {
      return fs2.read(fd, buffer, offset, length, position, callback);
    }
    return new Promise((resolve, reject) => {
      fs2.read(fd, buffer, offset, length, position, (err, bytesRead, buffer2) => {
        if (err) return reject(err);
        resolve({ bytesRead, buffer: buffer2 });
      });
    });
  };
  exports.write = function(fd, buffer, ...args) {
    if (typeof args[args.length - 1] === "function") {
      return fs2.write(fd, buffer, ...args);
    }
    return new Promise((resolve, reject) => {
      fs2.write(fd, buffer, ...args, (err, bytesWritten, buffer2) => {
        if (err) return reject(err);
        resolve({ bytesWritten, buffer: buffer2 });
      });
    });
  };
  if (typeof fs2.writev === "function") {
    exports.writev = function(fd, buffers, ...args) {
      if (typeof args[args.length - 1] === "function") {
        return fs2.writev(fd, buffers, ...args);
      }
      return new Promise((resolve, reject) => {
        fs2.writev(fd, buffers, ...args, (err, bytesWritten, buffers2) => {
          if (err) return reject(err);
          resolve({ bytesWritten, buffers: buffers2 });
        });
      });
    };
  }
  if (typeof fs2.realpath.native === "function") {
    exports.realpath.native = u2(fs2.realpath.native);
  }
})(fs$i);
var makeDir$1 = {};
var atLeastNode$2 = (r) => {
  const n = process.versions.node.split(".").map((x) => parseInt(x, 10));
  r = r.split(".").map((x) => parseInt(x, 10));
  return n[0] > r[0] || n[0] === r[0] && (n[1] > r[1] || n[1] === r[1] && n[2] >= r[2]);
};
const fs$g = fs$i;
const path$c = path$d;
const atLeastNode$1 = atLeastNode$2;
const useNativeRecursiveOption = atLeastNode$1("10.12.0");
const checkPath = (pth) => {
  if (process.platform === "win32") {
    const pathHasInvalidWinCharacters = /[<>:"|?*]/.test(pth.replace(path$c.parse(pth).root, ""));
    if (pathHasInvalidWinCharacters) {
      const error = new Error(`Path contains invalid characters: ${pth}`);
      error.code = "EINVAL";
      throw error;
    }
  }
};
const processOptions = (options) => {
  const defaults2 = { mode: 511 };
  if (typeof options === "number") options = { mode: options };
  return { ...defaults2, ...options };
};
const permissionError = (pth) => {
  const error = new Error(`operation not permitted, mkdir '${pth}'`);
  error.code = "EPERM";
  error.errno = -4048;
  error.path = pth;
  error.syscall = "mkdir";
  return error;
};
makeDir$1.makeDir = async (input, options) => {
  checkPath(input);
  options = processOptions(options);
  if (useNativeRecursiveOption) {
    const pth = path$c.resolve(input);
    return fs$g.mkdir(pth, {
      mode: options.mode,
      recursive: true
    });
  }
  const make = async (pth) => {
    try {
      await fs$g.mkdir(pth, options.mode);
    } catch (error) {
      if (error.code === "EPERM") {
        throw error;
      }
      if (error.code === "ENOENT") {
        if (path$c.dirname(pth) === pth) {
          throw permissionError(pth);
        }
        if (error.message.includes("null bytes")) {
          throw error;
        }
        await make(path$c.dirname(pth));
        return make(pth);
      }
      try {
        const stats = await fs$g.stat(pth);
        if (!stats.isDirectory()) {
          throw new Error("The path is not a directory");
        }
      } catch {
        throw error;
      }
    }
  };
  return make(path$c.resolve(input));
};
makeDir$1.makeDirSync = (input, options) => {
  checkPath(input);
  options = processOptions(options);
  if (useNativeRecursiveOption) {
    const pth = path$c.resolve(input);
    return fs$g.mkdirSync(pth, {
      mode: options.mode,
      recursive: true
    });
  }
  const make = (pth) => {
    try {
      fs$g.mkdirSync(pth, options.mode);
    } catch (error) {
      if (error.code === "EPERM") {
        throw error;
      }
      if (error.code === "ENOENT") {
        if (path$c.dirname(pth) === pth) {
          throw permissionError(pth);
        }
        if (error.message.includes("null bytes")) {
          throw error;
        }
        make(path$c.dirname(pth));
        return make(pth);
      }
      try {
        if (!fs$g.statSync(pth).isDirectory()) {
          throw new Error("The path is not a directory");
        }
      } catch {
        throw error;
      }
    }
  };
  return make(path$c.resolve(input));
};
const u$a = universalify$1.fromPromise;
const { makeDir: _makeDir, makeDirSync } = makeDir$1;
const makeDir = u$a(_makeDir);
var mkdirs$2 = {
  mkdirs: makeDir,
  mkdirsSync: makeDirSync,
  // alias
  mkdirp: makeDir,
  mkdirpSync: makeDirSync,
  ensureDir: makeDir,
  ensureDirSync: makeDirSync
};
const fs$f = gracefulFs;
function utimesMillis$1(path2, atime, mtime, callback) {
  fs$f.open(path2, "r+", (err, fd) => {
    if (err) return callback(err);
    fs$f.futimes(fd, atime, mtime, (futimesErr) => {
      fs$f.close(fd, (closeErr) => {
        if (callback) callback(futimesErr || closeErr);
      });
    });
  });
}
function utimesMillisSync$1(path2, atime, mtime) {
  const fd = fs$f.openSync(path2, "r+");
  fs$f.futimesSync(fd, atime, mtime);
  return fs$f.closeSync(fd);
}
var utimes = {
  utimesMillis: utimesMillis$1,
  utimesMillisSync: utimesMillisSync$1
};
const fs$e = fs$i;
const path$b = path$d;
const util = require$$4;
const atLeastNode = atLeastNode$2;
const nodeSupportsBigInt = atLeastNode("10.5.0");
const stat$4 = (file2) => nodeSupportsBigInt ? fs$e.stat(file2, { bigint: true }) : fs$e.stat(file2);
const statSync = (file2) => nodeSupportsBigInt ? fs$e.statSync(file2, { bigint: true }) : fs$e.statSync(file2);
function getStats$2(src, dest) {
  return Promise.all([
    stat$4(src),
    stat$4(dest).catch((err) => {
      if (err.code === "ENOENT") return null;
      throw err;
    })
  ]).then(([srcStat, destStat]) => ({ srcStat, destStat }));
}
function getStatsSync(src, dest) {
  let destStat;
  const srcStat = statSync(src);
  try {
    destStat = statSync(dest);
  } catch (err) {
    if (err.code === "ENOENT") return { srcStat, destStat: null };
    throw err;
  }
  return { srcStat, destStat };
}
function checkPaths(src, dest, funcName, cb) {
  util.callbackify(getStats$2)(src, dest, (err, stats) => {
    if (err) return cb(err);
    const { srcStat, destStat } = stats;
    if (destStat && areIdentical(srcStat, destStat)) {
      return cb(new Error("Source and destination must not be the same."));
    }
    if (srcStat.isDirectory() && isSrcSubdir(src, dest)) {
      return cb(new Error(errMsg(src, dest, funcName)));
    }
    return cb(null, { srcStat, destStat });
  });
}
function checkPathsSync(src, dest, funcName) {
  const { srcStat, destStat } = getStatsSync(src, dest);
  if (destStat && areIdentical(srcStat, destStat)) {
    throw new Error("Source and destination must not be the same.");
  }
  if (srcStat.isDirectory() && isSrcSubdir(src, dest)) {
    throw new Error(errMsg(src, dest, funcName));
  }
  return { srcStat, destStat };
}
function checkParentPaths(src, srcStat, dest, funcName, cb) {
  const srcParent = path$b.resolve(path$b.dirname(src));
  const destParent = path$b.resolve(path$b.dirname(dest));
  if (destParent === srcParent || destParent === path$b.parse(destParent).root) return cb();
  const callback = (err, destStat) => {
    if (err) {
      if (err.code === "ENOENT") return cb();
      return cb(err);
    }
    if (areIdentical(srcStat, destStat)) {
      return cb(new Error(errMsg(src, dest, funcName)));
    }
    return checkParentPaths(src, srcStat, destParent, funcName, cb);
  };
  if (nodeSupportsBigInt) fs$e.stat(destParent, { bigint: true }, callback);
  else fs$e.stat(destParent, callback);
}
function checkParentPathsSync(src, srcStat, dest, funcName) {
  const srcParent = path$b.resolve(path$b.dirname(src));
  const destParent = path$b.resolve(path$b.dirname(dest));
  if (destParent === srcParent || destParent === path$b.parse(destParent).root) return;
  let destStat;
  try {
    destStat = statSync(destParent);
  } catch (err) {
    if (err.code === "ENOENT") return;
    throw err;
  }
  if (areIdentical(srcStat, destStat)) {
    throw new Error(errMsg(src, dest, funcName));
  }
  return checkParentPathsSync(src, srcStat, destParent, funcName);
}
function areIdentical(srcStat, destStat) {
  if (destStat.ino && destStat.dev && destStat.ino === srcStat.ino && destStat.dev === srcStat.dev) {
    if (nodeSupportsBigInt || destStat.ino < Number.MAX_SAFE_INTEGER) {
      return true;
    }
    if (destStat.size === srcStat.size && destStat.mode === srcStat.mode && destStat.nlink === srcStat.nlink && destStat.atimeMs === srcStat.atimeMs && destStat.mtimeMs === srcStat.mtimeMs && destStat.ctimeMs === srcStat.ctimeMs && destStat.birthtimeMs === srcStat.birthtimeMs) {
      return true;
    }
  }
  return false;
}
function isSrcSubdir(src, dest) {
  const srcArr = path$b.resolve(src).split(path$b.sep).filter((i) => i);
  const destArr = path$b.resolve(dest).split(path$b.sep).filter((i) => i);
  return srcArr.reduce((acc, cur, i) => acc && destArr[i] === cur, true);
}
function errMsg(src, dest, funcName) {
  return `Cannot ${funcName} '${src}' to a subdirectory of itself, '${dest}'.`;
}
var stat_1 = {
  checkPaths,
  checkPathsSync,
  checkParentPaths,
  checkParentPathsSync,
  isSrcSubdir
};
const fs$d = gracefulFs;
const path$a = path$d;
const mkdirsSync$1 = mkdirs$2.mkdirsSync;
const utimesMillisSync = utimes.utimesMillisSync;
const stat$3 = stat_1;
function copySync$2(src, dest, opts) {
  if (typeof opts === "function") {
    opts = { filter: opts };
  }
  opts = opts || {};
  opts.clobber = "clobber" in opts ? !!opts.clobber : true;
  opts.overwrite = "overwrite" in opts ? !!opts.overwrite : opts.clobber;
  if (opts.preserveTimestamps && process.arch === "ia32") {
    console.warn(`fs-extra: Using the preserveTimestamps option in 32-bit node is not recommended;

    see https://github.com/jprichardson/node-fs-extra/issues/269`);
  }
  const { srcStat, destStat } = stat$3.checkPathsSync(src, dest, "copy");
  stat$3.checkParentPathsSync(src, srcStat, dest, "copy");
  return handleFilterAndCopy(destStat, src, dest, opts);
}
function handleFilterAndCopy(destStat, src, dest, opts) {
  if (opts.filter && !opts.filter(src, dest)) return;
  const destParent = path$a.dirname(dest);
  if (!fs$d.existsSync(destParent)) mkdirsSync$1(destParent);
  return startCopy$1(destStat, src, dest, opts);
}
function startCopy$1(destStat, src, dest, opts) {
  if (opts.filter && !opts.filter(src, dest)) return;
  return getStats$1(destStat, src, dest, opts);
}
function getStats$1(destStat, src, dest, opts) {
  const statSync2 = opts.dereference ? fs$d.statSync : fs$d.lstatSync;
  const srcStat = statSync2(src);
  if (srcStat.isDirectory()) return onDir$1(srcStat, destStat, src, dest, opts);
  else if (srcStat.isFile() || srcStat.isCharacterDevice() || srcStat.isBlockDevice()) return onFile$1(srcStat, destStat, src, dest, opts);
  else if (srcStat.isSymbolicLink()) return onLink$1(destStat, src, dest, opts);
}
function onFile$1(srcStat, destStat, src, dest, opts) {
  if (!destStat) return copyFile$1(srcStat, src, dest, opts);
  return mayCopyFile$1(srcStat, src, dest, opts);
}
function mayCopyFile$1(srcStat, src, dest, opts) {
  if (opts.overwrite) {
    fs$d.unlinkSync(dest);
    return copyFile$1(srcStat, src, dest, opts);
  } else if (opts.errorOnExist) {
    throw new Error(`'${dest}' already exists`);
  }
}
function copyFile$1(srcStat, src, dest, opts) {
  fs$d.copyFileSync(src, dest);
  if (opts.preserveTimestamps) handleTimestamps(srcStat.mode, src, dest);
  return setDestMode$1(dest, srcStat.mode);
}
function handleTimestamps(srcMode, src, dest) {
  if (fileIsNotWritable$1(srcMode)) makeFileWritable$1(dest, srcMode);
  return setDestTimestamps$1(src, dest);
}
function fileIsNotWritable$1(srcMode) {
  return (srcMode & 128) === 0;
}
function makeFileWritable$1(dest, srcMode) {
  return setDestMode$1(dest, srcMode | 128);
}
function setDestMode$1(dest, srcMode) {
  return fs$d.chmodSync(dest, srcMode);
}
function setDestTimestamps$1(src, dest) {
  const updatedSrcStat = fs$d.statSync(src);
  return utimesMillisSync(dest, updatedSrcStat.atime, updatedSrcStat.mtime);
}
function onDir$1(srcStat, destStat, src, dest, opts) {
  if (!destStat) return mkDirAndCopy$1(srcStat.mode, src, dest, opts);
  if (destStat && !destStat.isDirectory()) {
    throw new Error(`Cannot overwrite non-directory '${dest}' with directory '${src}'.`);
  }
  return copyDir$1(src, dest, opts);
}
function mkDirAndCopy$1(srcMode, src, dest, opts) {
  fs$d.mkdirSync(dest);
  copyDir$1(src, dest, opts);
  return setDestMode$1(dest, srcMode);
}
function copyDir$1(src, dest, opts) {
  fs$d.readdirSync(src).forEach((item) => copyDirItem$1(item, src, dest, opts));
}
function copyDirItem$1(item, src, dest, opts) {
  const srcItem = path$a.join(src, item);
  const destItem = path$a.join(dest, item);
  const { destStat } = stat$3.checkPathsSync(srcItem, destItem, "copy");
  return startCopy$1(destStat, srcItem, destItem, opts);
}
function onLink$1(destStat, src, dest, opts) {
  let resolvedSrc = fs$d.readlinkSync(src);
  if (opts.dereference) {
    resolvedSrc = path$a.resolve(process.cwd(), resolvedSrc);
  }
  if (!destStat) {
    return fs$d.symlinkSync(resolvedSrc, dest);
  } else {
    let resolvedDest;
    try {
      resolvedDest = fs$d.readlinkSync(dest);
    } catch (err) {
      if (err.code === "EINVAL" || err.code === "UNKNOWN") return fs$d.symlinkSync(resolvedSrc, dest);
      throw err;
    }
    if (opts.dereference) {
      resolvedDest = path$a.resolve(process.cwd(), resolvedDest);
    }
    if (stat$3.isSrcSubdir(resolvedSrc, resolvedDest)) {
      throw new Error(`Cannot copy '${resolvedSrc}' to a subdirectory of itself, '${resolvedDest}'.`);
    }
    if (fs$d.statSync(dest).isDirectory() && stat$3.isSrcSubdir(resolvedDest, resolvedSrc)) {
      throw new Error(`Cannot overwrite '${resolvedDest}' with '${resolvedSrc}'.`);
    }
    return copyLink$1(resolvedSrc, dest);
  }
}
function copyLink$1(resolvedSrc, dest) {
  fs$d.unlinkSync(dest);
  return fs$d.symlinkSync(resolvedSrc, dest);
}
var copySync_1 = copySync$2;
var copySync$1 = {
  copySync: copySync_1
};
const u$9 = universalify$1.fromPromise;
const fs$c = fs$i;
function pathExists$6(path2) {
  return fs$c.access(path2).then(() => true).catch(() => false);
}
var pathExists_1 = {
  pathExists: u$9(pathExists$6),
  pathExistsSync: fs$c.existsSync
};
const fs$b = gracefulFs;
const path$9 = path$d;
const mkdirs$1 = mkdirs$2.mkdirs;
const pathExists$5 = pathExists_1.pathExists;
const utimesMillis = utimes.utimesMillis;
const stat$2 = stat_1;
function copy$2(src, dest, opts, cb) {
  if (typeof opts === "function" && !cb) {
    cb = opts;
    opts = {};
  } else if (typeof opts === "function") {
    opts = { filter: opts };
  }
  cb = cb || function() {
  };
  opts = opts || {};
  opts.clobber = "clobber" in opts ? !!opts.clobber : true;
  opts.overwrite = "overwrite" in opts ? !!opts.overwrite : opts.clobber;
  if (opts.preserveTimestamps && process.arch === "ia32") {
    console.warn(`fs-extra: Using the preserveTimestamps option in 32-bit node is not recommended;

    see https://github.com/jprichardson/node-fs-extra/issues/269`);
  }
  stat$2.checkPaths(src, dest, "copy", (err, stats) => {
    if (err) return cb(err);
    const { srcStat, destStat } = stats;
    stat$2.checkParentPaths(src, srcStat, dest, "copy", (err2) => {
      if (err2) return cb(err2);
      if (opts.filter) return handleFilter(checkParentDir, destStat, src, dest, opts, cb);
      return checkParentDir(destStat, src, dest, opts, cb);
    });
  });
}
function checkParentDir(destStat, src, dest, opts, cb) {
  const destParent = path$9.dirname(dest);
  pathExists$5(destParent, (err, dirExists) => {
    if (err) return cb(err);
    if (dirExists) return startCopy(destStat, src, dest, opts, cb);
    mkdirs$1(destParent, (err2) => {
      if (err2) return cb(err2);
      return startCopy(destStat, src, dest, opts, cb);
    });
  });
}
function handleFilter(onInclude, destStat, src, dest, opts, cb) {
  Promise.resolve(opts.filter(src, dest)).then((include) => {
    if (include) return onInclude(destStat, src, dest, opts, cb);
    return cb();
  }, (error) => cb(error));
}
function startCopy(destStat, src, dest, opts, cb) {
  if (opts.filter) return handleFilter(getStats, destStat, src, dest, opts, cb);
  return getStats(destStat, src, dest, opts, cb);
}
function getStats(destStat, src, dest, opts, cb) {
  const stat2 = opts.dereference ? fs$b.stat : fs$b.lstat;
  stat2(src, (err, srcStat) => {
    if (err) return cb(err);
    if (srcStat.isDirectory()) return onDir(srcStat, destStat, src, dest, opts, cb);
    else if (srcStat.isFile() || srcStat.isCharacterDevice() || srcStat.isBlockDevice()) return onFile(srcStat, destStat, src, dest, opts, cb);
    else if (srcStat.isSymbolicLink()) return onLink(destStat, src, dest, opts, cb);
  });
}
function onFile(srcStat, destStat, src, dest, opts, cb) {
  if (!destStat) return copyFile(srcStat, src, dest, opts, cb);
  return mayCopyFile(srcStat, src, dest, opts, cb);
}
function mayCopyFile(srcStat, src, dest, opts, cb) {
  if (opts.overwrite) {
    fs$b.unlink(dest, (err) => {
      if (err) return cb(err);
      return copyFile(srcStat, src, dest, opts, cb);
    });
  } else if (opts.errorOnExist) {
    return cb(new Error(`'${dest}' already exists`));
  } else return cb();
}
function copyFile(srcStat, src, dest, opts, cb) {
  fs$b.copyFile(src, dest, (err) => {
    if (err) return cb(err);
    if (opts.preserveTimestamps) return handleTimestampsAndMode(srcStat.mode, src, dest, cb);
    return setDestMode(dest, srcStat.mode, cb);
  });
}
function handleTimestampsAndMode(srcMode, src, dest, cb) {
  if (fileIsNotWritable(srcMode)) {
    return makeFileWritable(dest, srcMode, (err) => {
      if (err) return cb(err);
      return setDestTimestampsAndMode(srcMode, src, dest, cb);
    });
  }
  return setDestTimestampsAndMode(srcMode, src, dest, cb);
}
function fileIsNotWritable(srcMode) {
  return (srcMode & 128) === 0;
}
function makeFileWritable(dest, srcMode, cb) {
  return setDestMode(dest, srcMode | 128, cb);
}
function setDestTimestampsAndMode(srcMode, src, dest, cb) {
  setDestTimestamps(src, dest, (err) => {
    if (err) return cb(err);
    return setDestMode(dest, srcMode, cb);
  });
}
function setDestMode(dest, srcMode, cb) {
  return fs$b.chmod(dest, srcMode, cb);
}
function setDestTimestamps(src, dest, cb) {
  fs$b.stat(src, (err, updatedSrcStat) => {
    if (err) return cb(err);
    return utimesMillis(dest, updatedSrcStat.atime, updatedSrcStat.mtime, cb);
  });
}
function onDir(srcStat, destStat, src, dest, opts, cb) {
  if (!destStat) return mkDirAndCopy(srcStat.mode, src, dest, opts, cb);
  if (destStat && !destStat.isDirectory()) {
    return cb(new Error(`Cannot overwrite non-directory '${dest}' with directory '${src}'.`));
  }
  return copyDir(src, dest, opts, cb);
}
function mkDirAndCopy(srcMode, src, dest, opts, cb) {
  fs$b.mkdir(dest, (err) => {
    if (err) return cb(err);
    copyDir(src, dest, opts, (err2) => {
      if (err2) return cb(err2);
      return setDestMode(dest, srcMode, cb);
    });
  });
}
function copyDir(src, dest, opts, cb) {
  fs$b.readdir(src, (err, items) => {
    if (err) return cb(err);
    return copyDirItems(items, src, dest, opts, cb);
  });
}
function copyDirItems(items, src, dest, opts, cb) {
  const item = items.pop();
  if (!item) return cb();
  return copyDirItem(items, item, src, dest, opts, cb);
}
function copyDirItem(items, item, src, dest, opts, cb) {
  const srcItem = path$9.join(src, item);
  const destItem = path$9.join(dest, item);
  stat$2.checkPaths(srcItem, destItem, "copy", (err, stats) => {
    if (err) return cb(err);
    const { destStat } = stats;
    startCopy(destStat, srcItem, destItem, opts, (err2) => {
      if (err2) return cb(err2);
      return copyDirItems(items, src, dest, opts, cb);
    });
  });
}
function onLink(destStat, src, dest, opts, cb) {
  fs$b.readlink(src, (err, resolvedSrc) => {
    if (err) return cb(err);
    if (opts.dereference) {
      resolvedSrc = path$9.resolve(process.cwd(), resolvedSrc);
    }
    if (!destStat) {
      return fs$b.symlink(resolvedSrc, dest, cb);
    } else {
      fs$b.readlink(dest, (err2, resolvedDest) => {
        if (err2) {
          if (err2.code === "EINVAL" || err2.code === "UNKNOWN") return fs$b.symlink(resolvedSrc, dest, cb);
          return cb(err2);
        }
        if (opts.dereference) {
          resolvedDest = path$9.resolve(process.cwd(), resolvedDest);
        }
        if (stat$2.isSrcSubdir(resolvedSrc, resolvedDest)) {
          return cb(new Error(`Cannot copy '${resolvedSrc}' to a subdirectory of itself, '${resolvedDest}'.`));
        }
        if (destStat.isDirectory() && stat$2.isSrcSubdir(resolvedDest, resolvedSrc)) {
          return cb(new Error(`Cannot overwrite '${resolvedDest}' with '${resolvedSrc}'.`));
        }
        return copyLink(resolvedSrc, dest, cb);
      });
    }
  });
}
function copyLink(resolvedSrc, dest, cb) {
  fs$b.unlink(dest, (err) => {
    if (err) return cb(err);
    return fs$b.symlink(resolvedSrc, dest, cb);
  });
}
var copy_1 = copy$2;
const u$8 = universalify$1.fromCallback;
var copy$1 = {
  copy: u$8(copy_1)
};
const fs$a = gracefulFs;
const path$8 = path$d;
const assert = require$$5;
const isWindows = process.platform === "win32";
function defaults(options) {
  const methods = [
    "unlink",
    "chmod",
    "stat",
    "lstat",
    "rmdir",
    "readdir"
  ];
  methods.forEach((m) => {
    options[m] = options[m] || fs$a[m];
    m = m + "Sync";
    options[m] = options[m] || fs$a[m];
  });
  options.maxBusyTries = options.maxBusyTries || 3;
}
function rimraf$1(p, options, cb) {
  let busyTries = 0;
  if (typeof options === "function") {
    cb = options;
    options = {};
  }
  assert(p, "rimraf: missing path");
  assert.strictEqual(typeof p, "string", "rimraf: path should be a string");
  assert.strictEqual(typeof cb, "function", "rimraf: callback function required");
  assert(options, "rimraf: invalid options argument provided");
  assert.strictEqual(typeof options, "object", "rimraf: options should be object");
  defaults(options);
  rimraf_(p, options, function CB(er) {
    if (er) {
      if ((er.code === "EBUSY" || er.code === "ENOTEMPTY" || er.code === "EPERM") && busyTries < options.maxBusyTries) {
        busyTries++;
        const time = busyTries * 100;
        return setTimeout(() => rimraf_(p, options, CB), time);
      }
      if (er.code === "ENOENT") er = null;
    }
    cb(er);
  });
}
function rimraf_(p, options, cb) {
  assert(p);
  assert(options);
  assert(typeof cb === "function");
  options.lstat(p, (er, st) => {
    if (er && er.code === "ENOENT") {
      return cb(null);
    }
    if (er && er.code === "EPERM" && isWindows) {
      return fixWinEPERM(p, options, er, cb);
    }
    if (st && st.isDirectory()) {
      return rmdir(p, options, er, cb);
    }
    options.unlink(p, (er2) => {
      if (er2) {
        if (er2.code === "ENOENT") {
          return cb(null);
        }
        if (er2.code === "EPERM") {
          return isWindows ? fixWinEPERM(p, options, er2, cb) : rmdir(p, options, er2, cb);
        }
        if (er2.code === "EISDIR") {
          return rmdir(p, options, er2, cb);
        }
      }
      return cb(er2);
    });
  });
}
function fixWinEPERM(p, options, er, cb) {
  assert(p);
  assert(options);
  assert(typeof cb === "function");
  options.chmod(p, 438, (er2) => {
    if (er2) {
      cb(er2.code === "ENOENT" ? null : er);
    } else {
      options.stat(p, (er3, stats) => {
        if (er3) {
          cb(er3.code === "ENOENT" ? null : er);
        } else if (stats.isDirectory()) {
          rmdir(p, options, er, cb);
        } else {
          options.unlink(p, cb);
        }
      });
    }
  });
}
function fixWinEPERMSync(p, options, er) {
  let stats;
  assert(p);
  assert(options);
  try {
    options.chmodSync(p, 438);
  } catch (er2) {
    if (er2.code === "ENOENT") {
      return;
    } else {
      throw er;
    }
  }
  try {
    stats = options.statSync(p);
  } catch (er3) {
    if (er3.code === "ENOENT") {
      return;
    } else {
      throw er;
    }
  }
  if (stats.isDirectory()) {
    rmdirSync(p, options, er);
  } else {
    options.unlinkSync(p);
  }
}
function rmdir(p, options, originalEr, cb) {
  assert(p);
  assert(options);
  assert(typeof cb === "function");
  options.rmdir(p, (er) => {
    if (er && (er.code === "ENOTEMPTY" || er.code === "EEXIST" || er.code === "EPERM")) {
      rmkids(p, options, cb);
    } else if (er && er.code === "ENOTDIR") {
      cb(originalEr);
    } else {
      cb(er);
    }
  });
}
function rmkids(p, options, cb) {
  assert(p);
  assert(options);
  assert(typeof cb === "function");
  options.readdir(p, (er, files) => {
    if (er) return cb(er);
    let n = files.length;
    let errState;
    if (n === 0) return options.rmdir(p, cb);
    files.forEach((f) => {
      rimraf$1(path$8.join(p, f), options, (er2) => {
        if (errState) {
          return;
        }
        if (er2) return cb(errState = er2);
        if (--n === 0) {
          options.rmdir(p, cb);
        }
      });
    });
  });
}
function rimrafSync(p, options) {
  let st;
  options = options || {};
  defaults(options);
  assert(p, "rimraf: missing path");
  assert.strictEqual(typeof p, "string", "rimraf: path should be a string");
  assert(options, "rimraf: missing options");
  assert.strictEqual(typeof options, "object", "rimraf: options should be object");
  try {
    st = options.lstatSync(p);
  } catch (er) {
    if (er.code === "ENOENT") {
      return;
    }
    if (er.code === "EPERM" && isWindows) {
      fixWinEPERMSync(p, options, er);
    }
  }
  try {
    if (st && st.isDirectory()) {
      rmdirSync(p, options, null);
    } else {
      options.unlinkSync(p);
    }
  } catch (er) {
    if (er.code === "ENOENT") {
      return;
    } else if (er.code === "EPERM") {
      return isWindows ? fixWinEPERMSync(p, options, er) : rmdirSync(p, options, er);
    } else if (er.code !== "EISDIR") {
      throw er;
    }
    rmdirSync(p, options, er);
  }
}
function rmdirSync(p, options, originalEr) {
  assert(p);
  assert(options);
  try {
    options.rmdirSync(p);
  } catch (er) {
    if (er.code === "ENOTDIR") {
      throw originalEr;
    } else if (er.code === "ENOTEMPTY" || er.code === "EEXIST" || er.code === "EPERM") {
      rmkidsSync(p, options);
    } else if (er.code !== "ENOENT") {
      throw er;
    }
  }
}
function rmkidsSync(p, options) {
  assert(p);
  assert(options);
  options.readdirSync(p).forEach((f) => rimrafSync(path$8.join(p, f), options));
  if (isWindows) {
    const startTime = Date.now();
    do {
      try {
        const ret = options.rmdirSync(p, options);
        return ret;
      } catch {
      }
    } while (Date.now() - startTime < 500);
  } else {
    const ret = options.rmdirSync(p, options);
    return ret;
  }
}
var rimraf_1 = rimraf$1;
rimraf$1.sync = rimrafSync;
const u$7 = universalify$1.fromCallback;
const rimraf = rimraf_1;
var remove$2 = {
  remove: u$7(rimraf),
  removeSync: rimraf.sync
};
const u$6 = universalify$1.fromCallback;
const fs$9 = gracefulFs;
const path$7 = path$d;
const mkdir$3 = mkdirs$2;
const remove$1 = remove$2;
const emptyDir = u$6(function emptyDir2(dir, callback) {
  callback = callback || function() {
  };
  fs$9.readdir(dir, (err, items) => {
    if (err) return mkdir$3.mkdirs(dir, callback);
    items = items.map((item) => path$7.join(dir, item));
    deleteItem();
    function deleteItem() {
      const item = items.pop();
      if (!item) return callback();
      remove$1.remove(item, (err2) => {
        if (err2) return callback(err2);
        deleteItem();
      });
    }
  });
});
function emptyDirSync(dir) {
  let items;
  try {
    items = fs$9.readdirSync(dir);
  } catch {
    return mkdir$3.mkdirsSync(dir);
  }
  items.forEach((item) => {
    item = path$7.join(dir, item);
    remove$1.removeSync(item);
  });
}
var empty = {
  emptyDirSync,
  emptydirSync: emptyDirSync,
  emptyDir,
  emptydir: emptyDir
};
const u$5 = universalify$1.fromCallback;
const path$6 = path$d;
const fs$8 = gracefulFs;
const mkdir$2 = mkdirs$2;
function createFile(file2, callback) {
  function makeFile() {
    fs$8.writeFile(file2, "", (err) => {
      if (err) return callback(err);
      callback();
    });
  }
  fs$8.stat(file2, (err, stats) => {
    if (!err && stats.isFile()) return callback();
    const dir = path$6.dirname(file2);
    fs$8.stat(dir, (err2, stats2) => {
      if (err2) {
        if (err2.code === "ENOENT") {
          return mkdir$2.mkdirs(dir, (err3) => {
            if (err3) return callback(err3);
            makeFile();
          });
        }
        return callback(err2);
      }
      if (stats2.isDirectory()) makeFile();
      else {
        fs$8.readdir(dir, (err3) => {
          if (err3) return callback(err3);
        });
      }
    });
  });
}
function createFileSync(file2) {
  let stats;
  try {
    stats = fs$8.statSync(file2);
  } catch {
  }
  if (stats && stats.isFile()) return;
  const dir = path$6.dirname(file2);
  try {
    if (!fs$8.statSync(dir).isDirectory()) {
      fs$8.readdirSync(dir);
    }
  } catch (err) {
    if (err && err.code === "ENOENT") mkdir$2.mkdirsSync(dir);
    else throw err;
  }
  fs$8.writeFileSync(file2, "");
}
var file$1 = {
  createFile: u$5(createFile),
  createFileSync
};
const u$4 = universalify$1.fromCallback;
const path$5 = path$d;
const fs$7 = gracefulFs;
const mkdir$1 = mkdirs$2;
const pathExists$4 = pathExists_1.pathExists;
function createLink(srcpath, dstpath, callback) {
  function makeLink(srcpath2, dstpath2) {
    fs$7.link(srcpath2, dstpath2, (err) => {
      if (err) return callback(err);
      callback(null);
    });
  }
  pathExists$4(dstpath, (err, destinationExists) => {
    if (err) return callback(err);
    if (destinationExists) return callback(null);
    fs$7.lstat(srcpath, (err2) => {
      if (err2) {
        err2.message = err2.message.replace("lstat", "ensureLink");
        return callback(err2);
      }
      const dir = path$5.dirname(dstpath);
      pathExists$4(dir, (err3, dirExists) => {
        if (err3) return callback(err3);
        if (dirExists) return makeLink(srcpath, dstpath);
        mkdir$1.mkdirs(dir, (err4) => {
          if (err4) return callback(err4);
          makeLink(srcpath, dstpath);
        });
      });
    });
  });
}
function createLinkSync(srcpath, dstpath) {
  const destinationExists = fs$7.existsSync(dstpath);
  if (destinationExists) return void 0;
  try {
    fs$7.lstatSync(srcpath);
  } catch (err) {
    err.message = err.message.replace("lstat", "ensureLink");
    throw err;
  }
  const dir = path$5.dirname(dstpath);
  const dirExists = fs$7.existsSync(dir);
  if (dirExists) return fs$7.linkSync(srcpath, dstpath);
  mkdir$1.mkdirsSync(dir);
  return fs$7.linkSync(srcpath, dstpath);
}
var link$1 = {
  createLink: u$4(createLink),
  createLinkSync
};
const path$4 = path$d;
const fs$6 = gracefulFs;
const pathExists$3 = pathExists_1.pathExists;
function symlinkPaths$1(srcpath, dstpath, callback) {
  if (path$4.isAbsolute(srcpath)) {
    return fs$6.lstat(srcpath, (err) => {
      if (err) {
        err.message = err.message.replace("lstat", "ensureSymlink");
        return callback(err);
      }
      return callback(null, {
        toCwd: srcpath,
        toDst: srcpath
      });
    });
  } else {
    const dstdir = path$4.dirname(dstpath);
    const relativeToDst = path$4.join(dstdir, srcpath);
    return pathExists$3(relativeToDst, (err, exists) => {
      if (err) return callback(err);
      if (exists) {
        return callback(null, {
          toCwd: relativeToDst,
          toDst: srcpath
        });
      } else {
        return fs$6.lstat(srcpath, (err2) => {
          if (err2) {
            err2.message = err2.message.replace("lstat", "ensureSymlink");
            return callback(err2);
          }
          return callback(null, {
            toCwd: srcpath,
            toDst: path$4.relative(dstdir, srcpath)
          });
        });
      }
    });
  }
}
function symlinkPathsSync$1(srcpath, dstpath) {
  let exists;
  if (path$4.isAbsolute(srcpath)) {
    exists = fs$6.existsSync(srcpath);
    if (!exists) throw new Error("absolute srcpath does not exist");
    return {
      toCwd: srcpath,
      toDst: srcpath
    };
  } else {
    const dstdir = path$4.dirname(dstpath);
    const relativeToDst = path$4.join(dstdir, srcpath);
    exists = fs$6.existsSync(relativeToDst);
    if (exists) {
      return {
        toCwd: relativeToDst,
        toDst: srcpath
      };
    } else {
      exists = fs$6.existsSync(srcpath);
      if (!exists) throw new Error("relative srcpath does not exist");
      return {
        toCwd: srcpath,
        toDst: path$4.relative(dstdir, srcpath)
      };
    }
  }
}
var symlinkPaths_1 = {
  symlinkPaths: symlinkPaths$1,
  symlinkPathsSync: symlinkPathsSync$1
};
const fs$5 = gracefulFs;
function symlinkType$1(srcpath, type, callback) {
  callback = typeof type === "function" ? type : callback;
  type = typeof type === "function" ? false : type;
  if (type) return callback(null, type);
  fs$5.lstat(srcpath, (err, stats) => {
    if (err) return callback(null, "file");
    type = stats && stats.isDirectory() ? "dir" : "file";
    callback(null, type);
  });
}
function symlinkTypeSync$1(srcpath, type) {
  let stats;
  if (type) return type;
  try {
    stats = fs$5.lstatSync(srcpath);
  } catch {
    return "file";
  }
  return stats && stats.isDirectory() ? "dir" : "file";
}
var symlinkType_1 = {
  symlinkType: symlinkType$1,
  symlinkTypeSync: symlinkTypeSync$1
};
const u$3 = universalify$1.fromCallback;
const path$3 = path$d;
const fs$4 = gracefulFs;
const _mkdirs = mkdirs$2;
const mkdirs = _mkdirs.mkdirs;
const mkdirsSync = _mkdirs.mkdirsSync;
const _symlinkPaths = symlinkPaths_1;
const symlinkPaths = _symlinkPaths.symlinkPaths;
const symlinkPathsSync = _symlinkPaths.symlinkPathsSync;
const _symlinkType = symlinkType_1;
const symlinkType = _symlinkType.symlinkType;
const symlinkTypeSync = _symlinkType.symlinkTypeSync;
const pathExists$2 = pathExists_1.pathExists;
function createSymlink(srcpath, dstpath, type, callback) {
  callback = typeof type === "function" ? type : callback;
  type = typeof type === "function" ? false : type;
  pathExists$2(dstpath, (err, destinationExists) => {
    if (err) return callback(err);
    if (destinationExists) return callback(null);
    symlinkPaths(srcpath, dstpath, (err2, relative) => {
      if (err2) return callback(err2);
      srcpath = relative.toDst;
      symlinkType(relative.toCwd, type, (err3, type2) => {
        if (err3) return callback(err3);
        const dir = path$3.dirname(dstpath);
        pathExists$2(dir, (err4, dirExists) => {
          if (err4) return callback(err4);
          if (dirExists) return fs$4.symlink(srcpath, dstpath, type2, callback);
          mkdirs(dir, (err5) => {
            if (err5) return callback(err5);
            fs$4.symlink(srcpath, dstpath, type2, callback);
          });
        });
      });
    });
  });
}
function createSymlinkSync(srcpath, dstpath, type) {
  const destinationExists = fs$4.existsSync(dstpath);
  if (destinationExists) return void 0;
  const relative = symlinkPathsSync(srcpath, dstpath);
  srcpath = relative.toDst;
  type = symlinkTypeSync(relative.toCwd, type);
  const dir = path$3.dirname(dstpath);
  const exists = fs$4.existsSync(dir);
  if (exists) return fs$4.symlinkSync(srcpath, dstpath, type);
  mkdirsSync(dir);
  return fs$4.symlinkSync(srcpath, dstpath, type);
}
var symlink$1 = {
  createSymlink: u$3(createSymlink),
  createSymlinkSync
};
const file = file$1;
const link = link$1;
const symlink = symlink$1;
var ensure = {
  // file
  createFile: file.createFile,
  createFileSync: file.createFileSync,
  ensureFile: file.createFile,
  ensureFileSync: file.createFileSync,
  // link
  createLink: link.createLink,
  createLinkSync: link.createLinkSync,
  ensureLink: link.createLink,
  ensureLinkSync: link.createLinkSync,
  // symlink
  createSymlink: symlink.createSymlink,
  createSymlinkSync: symlink.createSymlinkSync,
  ensureSymlink: symlink.createSymlink,
  ensureSymlinkSync: symlink.createSymlinkSync
};
function stringify$3(obj, { EOL = "\n", finalEOL = true, replacer = null, spaces } = {}) {
  const EOF = finalEOL ? EOL : "";
  const str = JSON.stringify(obj, replacer, spaces);
  return str.replace(/\n/g, EOL) + EOF;
}
function stripBom$1(content) {
  if (Buffer.isBuffer(content)) content = content.toString("utf8");
  return content.replace(/^\uFEFF/, "");
}
var utils = { stringify: stringify$3, stripBom: stripBom$1 };
let _fs;
try {
  _fs = gracefulFs;
} catch (_) {
  _fs = fs$j;
}
const universalify = universalify$1;
const { stringify: stringify$2, stripBom } = utils;
async function _readFile(file2, options = {}) {
  if (typeof options === "string") {
    options = { encoding: options };
  }
  const fs2 = options.fs || _fs;
  const shouldThrow = "throws" in options ? options.throws : true;
  let data = await universalify.fromCallback(fs2.readFile)(file2, options);
  data = stripBom(data);
  let obj;
  try {
    obj = JSON.parse(data, options ? options.reviver : null);
  } catch (err) {
    if (shouldThrow) {
      err.message = `${file2}: ${err.message}`;
      throw err;
    } else {
      return null;
    }
  }
  return obj;
}
const readFile = universalify.fromPromise(_readFile);
function readFileSync(file2, options = {}) {
  if (typeof options === "string") {
    options = { encoding: options };
  }
  const fs2 = options.fs || _fs;
  const shouldThrow = "throws" in options ? options.throws : true;
  try {
    let content = fs2.readFileSync(file2, options);
    content = stripBom(content);
    return JSON.parse(content, options.reviver);
  } catch (err) {
    if (shouldThrow) {
      err.message = `${file2}: ${err.message}`;
      throw err;
    } else {
      return null;
    }
  }
}
async function _writeFile(file2, obj, options = {}) {
  const fs2 = options.fs || _fs;
  const str = stringify$2(obj, options);
  await universalify.fromCallback(fs2.writeFile)(file2, str, options);
}
const writeFile = universalify.fromPromise(_writeFile);
function writeFileSync(file2, obj, options = {}) {
  const fs2 = options.fs || _fs;
  const str = stringify$2(obj, options);
  return fs2.writeFileSync(file2, str, options);
}
const jsonfile$1 = {
  readFile,
  readFileSync,
  writeFile,
  writeFileSync
};
var jsonfile_1 = jsonfile$1;
const jsonFile$1 = jsonfile_1;
var jsonfile = {
  // jsonfile exports
  readJson: jsonFile$1.readFile,
  readJsonSync: jsonFile$1.readFileSync,
  writeJson: jsonFile$1.writeFile,
  writeJsonSync: jsonFile$1.writeFileSync
};
const u$2 = universalify$1.fromCallback;
const fs$3 = gracefulFs;
const path$2 = path$d;
const mkdir = mkdirs$2;
const pathExists$1 = pathExists_1.pathExists;
function outputFile$1(file2, data, encoding, callback) {
  if (typeof encoding === "function") {
    callback = encoding;
    encoding = "utf8";
  }
  const dir = path$2.dirname(file2);
  pathExists$1(dir, (err, itDoes) => {
    if (err) return callback(err);
    if (itDoes) return fs$3.writeFile(file2, data, encoding, callback);
    mkdir.mkdirs(dir, (err2) => {
      if (err2) return callback(err2);
      fs$3.writeFile(file2, data, encoding, callback);
    });
  });
}
function outputFileSync$1(file2, ...args) {
  const dir = path$2.dirname(file2);
  if (fs$3.existsSync(dir)) {
    return fs$3.writeFileSync(file2, ...args);
  }
  mkdir.mkdirsSync(dir);
  fs$3.writeFileSync(file2, ...args);
}
var output = {
  outputFile: u$2(outputFile$1),
  outputFileSync: outputFileSync$1
};
const { stringify: stringify$1 } = utils;
const { outputFile } = output;
async function outputJson(file2, data, options = {}) {
  const str = stringify$1(data, options);
  await outputFile(file2, str, options);
}
var outputJson_1 = outputJson;
const { stringify } = utils;
const { outputFileSync } = output;
function outputJsonSync(file2, data, options) {
  const str = stringify(data, options);
  outputFileSync(file2, str, options);
}
var outputJsonSync_1 = outputJsonSync;
const u$1 = universalify$1.fromPromise;
const jsonFile = jsonfile;
jsonFile.outputJson = u$1(outputJson_1);
jsonFile.outputJsonSync = outputJsonSync_1;
jsonFile.outputJSON = jsonFile.outputJson;
jsonFile.outputJSONSync = jsonFile.outputJsonSync;
jsonFile.writeJSON = jsonFile.writeJson;
jsonFile.writeJSONSync = jsonFile.writeJsonSync;
jsonFile.readJSON = jsonFile.readJson;
jsonFile.readJSONSync = jsonFile.readJsonSync;
var json = jsonFile;
const fs$2 = gracefulFs;
const path$1 = path$d;
const copySync = copySync$1.copySync;
const removeSync = remove$2.removeSync;
const mkdirpSync = mkdirs$2.mkdirpSync;
const stat$1 = stat_1;
function moveSync$1(src, dest, opts) {
  opts = opts || {};
  const overwrite = opts.overwrite || opts.clobber || false;
  const { srcStat } = stat$1.checkPathsSync(src, dest, "move");
  stat$1.checkParentPathsSync(src, srcStat, dest, "move");
  mkdirpSync(path$1.dirname(dest));
  return doRename$1(src, dest, overwrite);
}
function doRename$1(src, dest, overwrite) {
  if (overwrite) {
    removeSync(dest);
    return rename$1(src, dest, overwrite);
  }
  if (fs$2.existsSync(dest)) throw new Error("dest already exists.");
  return rename$1(src, dest, overwrite);
}
function rename$1(src, dest, overwrite) {
  try {
    fs$2.renameSync(src, dest);
  } catch (err) {
    if (err.code !== "EXDEV") throw err;
    return moveAcrossDevice$1(src, dest, overwrite);
  }
}
function moveAcrossDevice$1(src, dest, overwrite) {
  const opts = {
    overwrite,
    errorOnExist: true
  };
  copySync(src, dest, opts);
  return removeSync(src);
}
var moveSync_1 = moveSync$1;
var moveSync = {
  moveSync: moveSync_1
};
const fs$1 = gracefulFs;
const path = path$d;
const copy = copy$1.copy;
const remove = remove$2.remove;
const mkdirp = mkdirs$2.mkdirp;
const pathExists = pathExists_1.pathExists;
const stat = stat_1;
function move$1(src, dest, opts, cb) {
  if (typeof opts === "function") {
    cb = opts;
    opts = {};
  }
  const overwrite = opts.overwrite || opts.clobber || false;
  stat.checkPaths(src, dest, "move", (err, stats) => {
    if (err) return cb(err);
    const { srcStat } = stats;
    stat.checkParentPaths(src, srcStat, dest, "move", (err2) => {
      if (err2) return cb(err2);
      mkdirp(path.dirname(dest), (err3) => {
        if (err3) return cb(err3);
        return doRename(src, dest, overwrite, cb);
      });
    });
  });
}
function doRename(src, dest, overwrite, cb) {
  if (overwrite) {
    return remove(dest, (err) => {
      if (err) return cb(err);
      return rename(src, dest, overwrite, cb);
    });
  }
  pathExists(dest, (err, destExists) => {
    if (err) return cb(err);
    if (destExists) return cb(new Error("dest already exists."));
    return rename(src, dest, overwrite, cb);
  });
}
function rename(src, dest, overwrite, cb) {
  fs$1.rename(src, dest, (err) => {
    if (!err) return cb();
    if (err.code !== "EXDEV") return cb(err);
    return moveAcrossDevice(src, dest, overwrite, cb);
  });
}
function moveAcrossDevice(src, dest, overwrite, cb) {
  const opts = {
    overwrite,
    errorOnExist: true
  };
  copy(src, dest, opts, (err) => {
    if (err) return cb(err);
    return remove(src, cb);
  });
}
var move_1 = move$1;
const u = universalify$1.fromCallback;
var move = {
  move: u(move_1)
};
(function(module) {
  module.exports = {
    // Export promiseified graceful-fs:
    ...fs$i,
    // Export extra methods:
    ...copySync$1,
    ...copy$1,
    ...empty,
    ...ensure,
    ...json,
    ...mkdirs$2,
    ...moveSync,
    ...move,
    ...output,
    ...pathExists_1,
    ...remove$2
  };
  const fs2 = fs$j;
  if (Object.getOwnPropertyDescriptor(fs2, "promises")) {
    Object.defineProperty(module.exports, "promises", {
      get() {
        return fs2.promises;
      }
    });
  }
})(lib);
var libExports = lib.exports;
const fs = /* @__PURE__ */ getDefaultExportFromCjs(libExports);
async function copyBrowserData(browserName, browserPath, electronUserDataPath) {
  const subdirs = ["Local Storage", "IndexedDB"];
  const cookieFile = "Cookies";
  for (const dir of subdirs) {
    const src = path$d.join(browserPath, dir);
    const dest = path$d.join(electronUserDataPath, browserName, dir);
    if (fs.existsSync(src)) {
      await fs.copy(src, dest, { overwrite: true });
      console.log(`[${browserName}] copy ${dir} success`);
    }
  }
  const cookieSrc = path$d.join(browserPath, cookieFile);
  const cookieDest = path$d.join(electronUserDataPath, browserName, cookieFile);
  if (fs.existsSync(cookieSrc)) {
    await fs.copy(cookieSrc, cookieDest, { overwrite: true });
    console.log(`[${browserName}] copy Cookies success`);
  }
}
const userData = app.getPath("userData");
const versionFile = path$e.join(userData, "version.txt");
const __dirname = path$e.dirname(fileURLToPath(import.meta.url));
const MAIN_DIST = path$e.join(__dirname, "../..");
const RENDERER_DIST = path$e.join(MAIN_DIST, "dist");
const VITE_DEV_SERVER_URL = process.env.VITE_DEV_SERVER_URL;
const VITE_PUBLIC = VITE_DEV_SERVER_URL ? path$e.join(MAIN_DIST, "public") : RENDERER_DIST;
let win = null;
let webViewManager = null;
let fileReader = null;
let python_process = null;
let backendPort = 5001;
let browser_port = 9222;
const preload = path$e.join(__dirname, "../preload/index.mjs");
const indexHtml = path$e.join(RENDERER_DIST, "index.html");
const logPath = log.transports.file.getFile().path;
findAvailablePort(browser_port).then((port) => {
  browser_port = port;
  app.commandLine.appendSwitch("remote-debugging-port", port + "");
});
async function checkAndInstallDepsOnUpdate() {
  const currentVersion = app.getVersion();
  return new Promise(async (resolve, reject) => {
    try {
      log.info(" start check version", { currentVersion });
      const versionExists = fs$k.existsSync(versionFile);
      let savedVersion = "";
      if (versionExists) {
        savedVersion = fs$k.readFileSync(versionFile, "utf-8").trim();
        log.info(" read saved version", { savedVersion });
      } else {
        log.info(" version file not exist, will create new file");
      }
      if (!versionExists || savedVersion !== currentVersion) {
        log.info(" version changed, prepare to reinstall uv dependencies...", {
          currentVersion,
          savedVersion: versionExists ? savedVersion : "none",
          reason: !versionExists ? "version file not exist" : "version not match"
        });
        if (win && !win.isDestroyed()) {
          win.webContents.send("update-notification", {
            type: "version-update",
            currentVersion,
            previousVersion: versionExists ? savedVersion : "none",
            reason: !versionExists ? "version file not exist" : "version not match"
          });
        }
        fs$k.writeFileSync(versionFile, currentVersion);
        log.info(" version file updated", { currentVersion });
        const result = await installDependencies();
        if (!result) {
          log.error(" install dependencies failed");
          resolve(false);
          return;
        }
        resolve(true);
        log.info(" install dependencies complete");
        return;
      } else {
        log.info(" version not changed, skip install dependencies", { currentVersion });
        resolve(true);
        return;
      }
    } catch (error) {
      log.error(" check version and install dependencies error:", error);
      resolve(false);
      return;
    }
  });
}
process.env.APP_ROOT = MAIN_DIST;
process.env.VITE_PUBLIC = VITE_PUBLIC;
nativeTheme.themeSource = "light";
log.transports.console.level = "info";
log.transports.file.level = "info";
if (os$1.release().startsWith("6.1")) app.disableHardwareAcceleration();
if (process.platform === "win32") app.setAppUserModelId(app.getName());
if (!app.requestSingleInstanceLock()) {
  app.quit();
  process.exit(0);
}
const setupProtocolHandlers = () => {
  if (process.env.NODE_ENV === "development") {
    const isDefault = app.isDefaultProtocolClient("eigent", process.execPath, [path$e.resolve(process.argv[1])]);
    if (!isDefault) {
      app.setAsDefaultProtocolClient("eigent", process.execPath, [path$e.resolve(process.argv[1])]);
    }
  } else {
    app.setAsDefaultProtocolClient("eigent");
  }
};
function handleProtocolUrl(url) {
  log.info("enter handleProtocolUrl", url);
  const urlObj = new URL(url);
  const code = urlObj.searchParams.get("code");
  const share_token = urlObj.searchParams.get("share_token");
  log.info("urlObj", urlObj);
  log.info("code", code);
  log.info("share_token", share_token);
  if (win && !win.isDestroyed()) {
    log.info("urlObj.pathname", urlObj.pathname);
    if (urlObj.pathname === "/oauth") {
      log.info("oauth");
      const provider = urlObj.searchParams.get("provider");
      const code2 = urlObj.searchParams.get("code");
      log.info("protocol oauth", provider, code2);
      win.webContents.send("oauth-authorized", { provider, code: code2 });
      return;
    }
    if (code) {
      log.error("protocol code:", code);
      win.webContents.send("auth-code-received", code);
    }
    if (share_token) {
      win.webContents.send("auth-share-token-received", share_token);
    }
  } else {
    log.error("window not available");
  }
}
const setupSingleInstanceLock = () => {
  const gotLock = app.requestSingleInstanceLock();
  if (!gotLock) {
    log.info("no-lock");
    app.quit();
  } else {
    app.on("second-instance", (event, argv) => {
      log.info("second-instance", argv);
      const url = argv.find((arg) => arg.startsWith("eigent://"));
      if (url) handleProtocolUrl(url);
      if (win) win.show();
    });
    app.on("open-url", (event, url) => {
      log.info("open-url");
      event.preventDefault();
      handleProtocolUrl(url);
    });
  }
};
const initializeApp = () => {
  setupProtocolHandlers();
  setupSingleInstanceLock();
};
const getBackupLogPath = () => {
  const userDataPath = app.getPath("userData");
  return path$e.join(userDataPath, "logs", "main.log");
};
const BROWSER_PATHS = {
  win32: {
    chrome: "C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe",
    edge: "C:\\Program Files (x86)\\Microsoft\\Edge\\Application\\msedge.exe",
    firefox: "C:\\Program Files\\Mozilla Firefox\\firefox.exe",
    qq: "C:\\Program Files\\Tencent\\QQBrowser\\QQBrowser.exe",
    "360": path$e.join(homedir(), "AppData\\Local\\360Chrome\\Chrome\\Application\\360chrome.exe"),
    arc: path$e.join(homedir(), "AppData\\Local\\Arc\\User Data\\Arc.exe"),
    dia: path$e.join(homedir(), "AppData\\Local\\Dia\\Application\\dia.exe"),
    fellou: path$e.join(homedir(), "AppData\\Local\\Fellou\\Application\\fellou.exe")
  },
  darwin: {
    chrome: "/Applications/Google Chrome.app",
    edge: "/Applications/Microsoft Edge.app",
    firefox: "/Applications/Firefox.app",
    safari: "/Applications/Safari.app",
    arc: "/Applications/Arc.app",
    dia: "/Applications/Dia.app",
    fellou: "/Applications/Fellou.app"
  }
};
const getSystemLanguage = async () => {
  const locale = app.getLocale();
  return locale === "zh-CN" ? "zh-cn" : "en";
};
const checkManagerInstance = (manager, name) => {
  if (!manager) {
    throw new Error(`${name} not initialized`);
  }
  return manager;
};
const handleDependencyInstallation = async () => {
  try {
    log.info(" start install dependencies...");
    const isSuccess = await installDependencies();
    if (!isSuccess) {
      log.error(" install dependencies failed");
      return { success: false, error: "install dependencies failed" };
    }
    log.info(" install dependencies success, check tool installed status...");
    const isToolInstalled = await checkToolInstalled();
    if (isToolInstalled && !python_process) {
      log.info(" tool installed, start backend service...");
      python_process = await startBackend((port) => {
        backendPort = port;
        log.info(" backend service start success", { port });
      });
      if (win && !win.isDestroyed()) {
        win.webContents.send("install-dependencies-complete", { success: true, code: 0 });
      }
      python_process == null ? void 0 : python_process.on("exit", (code, signal) => {
        log.info(" python process exit", { code, signal });
      });
    } else if (!isToolInstalled) {
      log.warn(" tool not installed, skip backend start");
    } else {
      log.info(" backend process already exist, skip start");
    }
    log.info(" install dependencies complete");
    return { success: true };
  } catch (error) {
    log.error(" install dependencies error:", error);
    if (win && !win.isDestroyed()) {
      win.webContents.send("install-dependencies-complete", { success: false, code: 2 });
    }
    return { success: false, error: error.message };
  }
};
function registerIpcHandlers() {
  ipcMain.handle("get-browser-port", () => browser_port);
  ipcMain.handle("get-app-version", () => app.getVersion());
  ipcMain.handle("get-backend-port", () => backendPort);
  ipcMain.handle("get-system-language", getSystemLanguage);
  ipcMain.handle("is-fullscreen", () => (win == null ? void 0 : win.isFullScreen()) || false);
  ipcMain.handle("get-home-dir", () => {
    const platform2 = process.platform;
    return platform2 === "win32" ? process.env.USERPROFILE : process.env.HOME;
  });
  ipcMain.handle("execute-command", async (event, command, email) => {
    log.info("execute-command", command);
    const tempEmail = email.split("@")[0].replace(/[\\/*?:"<>|\s]/g, "_").replace(".", "_");
    const MCP_CONFIG_DIR2 = path$e.join(os$1.homedir(), ".eigent");
    const MCP_REMOTE_CONFIG_DIR = path$e.join(MCP_CONFIG_DIR2, tempEmail);
    if (!fs$k.existsSync(MCP_REMOTE_CONFIG_DIR)) {
      fs$k.mkdirSync(MCP_REMOTE_CONFIG_DIR, { recursive: true });
    }
    try {
      const { spawn: spawn2 } = await import("child_process");
      const commandWithHost = `${command} --debug --host "dev.eigent.ai/api/oauth/notion/callback?code=1"`;
      log.info(" start execute command:", commandWithHost);
      const [cmd, ...args] = commandWithHost.split(" ");
      return new Promise((resolve) => {
        const child = spawn2(cmd, args, {
          cwd: process.cwd(),
          env: { ...process.env, MCP_REMOTE_CONFIG_DIR },
          stdio: ["pipe", "pipe", "pipe"]
        });
        let stdout = "";
        let stderr = "";
        child.stdout.on("data", (data) => {
          const output2 = data.toString();
          stdout += output2;
          log.info("Real-time output:", output2.trim());
        });
        child.stderr.on("data", (data) => {
          const output2 = data.toString();
          stderr += output2;
          if (output2.includes("OAuth callback server running at")) {
            const url = output2.split("OAuth callback server running at")[1].trim();
            log.info(" detect OAuth callback URL:", url);
            if (win && !win.isDestroyed()) {
              const match = url.match(/^https?:\/\/[^:\n]+:\d+/);
              const cleanedUrl = match ? match[0] : null;
              log.info("cleanedUrl", cleanedUrl);
              win.webContents.send("oauth-callback-url", {
                url: cleanedUrl,
                provider: "notion"
                // TODO: can be set dynamically according to actual situation
              });
            }
          }
          if (output2.includes("Press Ctrl+C to exit")) {
            child.kill();
          }
          log.info(" realtime error output:", output2.trim());
        });
        child.on("close", (code) => {
          log.info(` command execute complete, exit code: ${code}`);
          resolve({ success: code === 0, stdout, stderr });
        });
        child.on("error", (error) => {
          log.error(" command execute error:", error);
          resolve({ success: false, error: error.message });
        });
      });
    } catch (error) {
      log.error(" command execute failed:", error);
      return { success: false, error: error.message };
    }
  });
  ipcMain.handle("export-log", async () => {
    try {
      let targetLogPath = logPath;
      if (!fs$k.existsSync(targetLogPath)) {
        const backupPath = getBackupLogPath();
        if (fs$k.existsSync(backupPath)) {
          targetLogPath = backupPath;
        } else {
          return { success: false, error: "no log file" };
        }
      }
      await fsp.access(targetLogPath, fs$k.constants.R_OK);
      const stats = await fsp.stat(targetLogPath);
      if (stats.size === 0) {
        return { success: true, data: "log file is empty" };
      }
      const logContent = await fsp.readFile(targetLogPath, "utf-8");
      const appVersion = app.getVersion();
      const platform2 = process.platform;
      const arch = process.arch;
      const systemVersion = `${platform2}-${arch}`;
      const defaultFileName = `eigent-${appVersion}-${systemVersion}.log`;
      const { canceled, filePath } = await dialog.showSaveDialog({
        title: "save log file",
        defaultPath: defaultFileName,
        filters: [{ name: "log file", extensions: ["log", "txt"] }]
      });
      if (canceled || !filePath) {
        return { success: false, error: "" };
      }
      await fsp.writeFile(filePath, logContent, "utf-8");
      return { success: true, savedPath: filePath };
    } catch (error) {
      return { success: false, error: error.message };
    }
  });
  ipcMain.handle("mcp-install", async (event, name, mcp) => {
    addMcp(name, mcp);
    return { success: true };
  });
  ipcMain.handle("mcp-remove", async (event, name) => {
    removeMcp(name);
    return { success: true };
  });
  ipcMain.handle("mcp-update", async (event, name, mcp) => {
    updateMcp(name, mcp);
    return { success: true };
  });
  ipcMain.handle("mcp-list", async () => {
    return readMcpConfig();
  });
  ipcMain.handle("check-install-browser", async () => {
    try {
      const platform2 = process.platform;
      const results = {};
      const paths = BROWSER_PATHS[platform2];
      if (!paths) {
        log.warn(`not support current platform: ${platform2}`);
        return {};
      }
      for (const [browser, execPath] of Object.entries(paths)) {
        results[browser] = existsSync(execPath);
      }
      return results;
    } catch (error) {
      log.error("Failed to check browser installation:", error);
      return {};
    }
  });
  ipcMain.handle("start-browser-import", async (event, args) => {
    const isWin = process.platform === "win32";
    const localAppData = process.env.LOCALAPPDATA || "";
    const appData = process.env.APPDATA || "";
    const home = os$1.homedir();
    const candidates = {
      chrome: isWin ? `${localAppData}\\Google\\Chrome\\User Data\\Default` : `${home}/Library/Application Support/Google/Chrome/Default`,
      edge: isWin ? `${localAppData}\\Microsoft\\Edge\\User Data\\Default` : `${home}/Library/Application Support/Microsoft Edge/Default`,
      firefox: isWin ? `${appData}\\Mozilla\\Firefox\\Profiles` : `${home}/Library/Application Support/Firefox/Profiles`,
      qq: `${localAppData}\\Tencent\\QQBrowser\\User Data\\Default`,
      "360": `${localAppData}\\360Chrome\\Chrome\\User Data\\Default`,
      arc: isWin ? `${localAppData}\\Arc\\User Data\\Default` : `${home}/Library/Application Support/Arc/Default`,
      dia: `${localAppData}\\Dia\\User Data\\Default`,
      fellou: `${localAppData}\\Fellou\\User Data\\Default`,
      safari: `${home}/Library/Safari`
    };
    Object.keys(candidates).forEach((key) => {
      const browser = args.find((item) => item.browserId === key);
      if (!browser || !browser.checked) {
        delete candidates[key];
      }
    });
    const result = {};
    for (const [name, p] of Object.entries(candidates)) {
      result[name] = fs$k.existsSync(p) ? p : null;
    }
    const electronUserDataPath = app.getPath("userData");
    for (const [browserName, browserPath] of Object.entries(result)) {
      if (!browserPath) continue;
      await copyBrowserData(browserName, browserPath, electronUserDataPath);
    }
    return { success: true };
  });
  ipcMain.on("window-close", () => win == null ? void 0 : win.close());
  ipcMain.on("window-minimize", () => win == null ? void 0 : win.minimize());
  ipcMain.on("window-toggle-maximize", () => {
    if (win == null ? void 0 : win.isMaximized()) {
      win == null ? void 0 : win.unmaximize();
    } else {
      win == null ? void 0 : win.maximize();
    }
  });
  ipcMain.handle("select-file", async (event, options = {}) => {
    const result = await dialog.showOpenDialog(win, {
      properties: ["openFile", "multiSelections"],
      ...options
    });
    if (!result.canceled && result.filePaths.length > 0) {
      const files = result.filePaths.map((filePath) => ({
        filePath,
        fileName: filePath.split(/[/\\]/).pop() || ""
      }));
      return {
        success: true,
        files,
        fileCount: files.length
      };
    }
    return {
      success: false,
      canceled: result.canceled
    };
  });
  ipcMain.handle("reveal-in-folder", async (event, filePath) => {
    try {
      shell.showItemInFolder(filePath);
    } catch (e) {
      log.error("reveal in folder failed", e);
    }
  });
  ipcMain.handle("read-file", async (event, filePath) => {
    try {
      log.info("Reading file:", filePath);
      if (!fs$k.existsSync(filePath)) {
        log.error("File does not exist:", filePath);
        return { success: false, error: "File does not exist" };
      }
      const fileContent = await fsp.readFile(filePath);
      log.info("File read successfully:", filePath);
      return {
        success: true,
        data: fileContent,
        size: fileContent.length
      };
    } catch (error) {
      log.error("Failed to read file:", filePath, error);
      return {
        success: false,
        error: error.message || "Failed to read file"
      };
    }
  });
  ipcMain.handle("delete-folder", async (event, email) => {
    const tempEmail = email.split("@")[0].replace(/[\\/*?:"<>|\s]/g, "_").replace(".", "_");
    const MCP_CONFIG_DIR2 = path$e.join(os$1.homedir(), ".eigent");
    const MCP_REMOTE_CONFIG_DIR = path$e.join(MCP_CONFIG_DIR2, tempEmail);
    try {
      log.info("Deleting folder:", MCP_REMOTE_CONFIG_DIR);
      if (!fs$k.existsSync(MCP_REMOTE_CONFIG_DIR)) {
        log.error("Folder does not exist:", MCP_REMOTE_CONFIG_DIR);
        return { success: false, error: "Folder does not exist" };
      }
      const stats = await fsp.stat(MCP_REMOTE_CONFIG_DIR);
      if (!stats.isDirectory()) {
        log.error("Path is not a directory:", MCP_REMOTE_CONFIG_DIR);
        return { success: false, error: "Path is not a directory" };
      }
      await fsp.rm(MCP_REMOTE_CONFIG_DIR, { recursive: true, force: true });
      log.info("Folder deleted successfully:", MCP_REMOTE_CONFIG_DIR);
      return {
        success: true,
        message: "Folder deleted successfully"
      };
    } catch (error) {
      log.error("Failed to delete folder:", MCP_REMOTE_CONFIG_DIR, error);
      return {
        success: false,
        error: error.message || "Failed to delete folder"
      };
    }
  });
  ipcMain.handle("get-mcp-config-path", async (event, email) => {
    try {
      const tempEmail = email.split("@")[0].replace(/[\\/*?:"<>|\s]/g, "_").replace(".", "_");
      const MCP_CONFIG_DIR2 = path$e.join(os$1.homedir(), ".eigent");
      const MCP_REMOTE_CONFIG_DIR = path$e.join(MCP_CONFIG_DIR2, tempEmail);
      log.info("Getting MCP config path for email:", email);
      log.info("MCP config path:", MCP_REMOTE_CONFIG_DIR);
      return {
        success: true,
        path: MCP_REMOTE_CONFIG_DIR,
        tempEmail,
        baseDir: MCP_CONFIG_DIR2
      };
    } catch (error) {
      log.error("Failed to get MCP config path:", error);
      return {
        success: false,
        error: error.message || "Failed to get MCP config path"
      };
    }
  });
  ipcMain.handle("get-env-path", async (_event, email) => {
    return getEnvPath(email);
  });
  ipcMain.handle("env-write", async (_event, email, { key, value }) => {
    const ENV_PATH = getEnvPath(email);
    let content = "";
    try {
      content = fs$k.existsSync(ENV_PATH) ? fs$k.readFileSync(ENV_PATH, "utf-8") : "";
    } catch (error) {
      log.error("env-write error:", error);
    }
    let lines = content.split(/\r?\n/);
    lines = updateEnvBlock(lines, { [key]: value });
    fs$k.writeFileSync(ENV_PATH, lines.join("\n"), "utf-8");
    return { success: true };
  });
  ipcMain.handle("env-remove", async (_event, email, key) => {
    log.info("env-remove", key);
    const ENV_PATH = getEnvPath(email);
    let content = "";
    try {
      content = fs$k.existsSync(ENV_PATH) ? fs$k.readFileSync(ENV_PATH, "utf-8") : "";
    } catch (error) {
      log.error("env-remove error:", error);
    }
    let lines = content.split(/\r?\n/);
    lines = removeEnvKey(lines, key);
    fs$k.writeFileSync(ENV_PATH, lines.join("\n"), "utf-8");
    log.info("env-remove success", ENV_PATH);
    return { success: true };
  });
  ipcMain.handle("open-win", (_, arg) => {
    const childWindow = new BrowserWindow({
      webPreferences: {
        preload,
        nodeIntegration: true,
        contextIsolation: false
      }
    });
    if (VITE_DEV_SERVER_URL) {
      childWindow.loadURL(`${VITE_DEV_SERVER_URL}#${arg}`);
    } else {
      childWindow.loadFile(indexHtml, { hash: arg });
    }
  });
  ipcMain.handle("open-file", async (_, type, filePath, isShowSourceCode) => {
    const manager = checkManagerInstance(fileReader, "FileReader");
    return manager.openFile(type, filePath, isShowSourceCode);
  });
  ipcMain.handle("get-file-list", async (_, email, taskId) => {
    const manager = checkManagerInstance(fileReader, "FileReader");
    return manager.getFileList(email, taskId);
  });
  const webviewHandlers = [
    { name: "capture-webview", method: "captureWebview" },
    { name: "create-webview", method: "createWebview" },
    { name: "hide-webview", method: "hideWebview" },
    { name: "show-webview", method: "showWebview" },
    { name: "change-view-size", method: "changeViewSize" },
    { name: "hide-all-webview", method: "hideAllWebview" },
    { name: "get-active-webview", method: "getActiveWebview" },
    { name: "set-size", method: "setSize" },
    { name: "get-show-webview", method: "getShowWebview" },
    { name: "webview-destroy", method: "destroyWebview" }
  ];
  webviewHandlers.forEach(({ name, method }) => {
    ipcMain.handle(name, async (_, ...args) => {
      const manager = checkManagerInstance(webViewManager, "WebViewManager");
      return manager[method](...args);
    });
  });
  ipcMain.handle("install-dependencies", handleDependencyInstallation);
  ipcMain.handle("frontend-ready", handleDependencyInstallation);
  ipcMain.handle("check-tool-installed", async () => {
    try {
      const isInstalled = await checkToolInstalled();
      return { success: true, isInstalled };
    } catch (error) {
      return { success: false, error: error.message };
    }
  });
  registerUpdateIpcHandlers();
}
async function createWindow() {
  const isMac = process.platform === "darwin";
  win = new BrowserWindow({
    title: "Eigent",
    width: 1200,
    height: 800,
    frame: false,
    transparent: true,
    vibrancy: "sidebar",
    visualEffectState: "active",
    backgroundColor: "#00000000",
    titleBarStyle: isMac ? "hidden" : void 0,
    trafficLightPosition: isMac ? { x: 10, y: 10 } : void 0,
    icon: path$e.join(VITE_PUBLIC, "favicon.ico"),
    roundedCorners: true,
    webPreferences: {
      webSecurity: false,
      preload,
      nodeIntegration: true,
      contextIsolation: true,
      webviewTag: true,
      spellcheck: false
    }
  });
  fileReader = new FileReader(win);
  webViewManager = new WebViewManager(win);
  for (let i = 1; i <= 8; i++) {
    webViewManager.createWebview(i === 1 ? void 0 : i.toString());
  }
  if (VITE_DEV_SERVER_URL) {
    win.loadURL(VITE_DEV_SERVER_URL);
    win.webContents.openDevTools();
  } else {
    win.loadFile(indexHtml);
  }
  setupWindowEventListeners();
  setupDevToolsShortcuts();
  setupExternalLinkHandling();
  update(win);
  let res = await checkAndInstallDepsOnUpdate();
  if (!res) {
    log.info("checkAndInstallDepsOnUpdate,install dependencies failed");
    win.webContents.send("install-dependencies-complete", { success: false, code: 2 });
    return;
  }
  await checkAndStartBackend();
}
const setupWindowEventListeners = () => {
  if (!win) return;
  Menu.setApplicationMenu(null);
};
const setupDevToolsShortcuts = () => {
  if (!win) return;
  const toggleDevTools = () => win == null ? void 0 : win.webContents.toggleDevTools();
  win.webContents.on("before-input-event", (event, input) => {
    if (input.key === "F12" && input.type === "keyDown") {
      toggleDevTools();
    }
    if (input.control && input.shift && input.key.toLowerCase() === "i" && input.type === "keyDown") {
      toggleDevTools();
    }
    if (input.meta && input.shift && input.key.toLowerCase() === "i" && input.type === "keyDown") {
      toggleDevTools();
    }
  });
};
const setupExternalLinkHandling = () => {
  if (!win) return;
  win.webContents.setWindowOpenHandler(({ url }) => {
    if (url.startsWith("https:") || url.startsWith("http:")) {
      shell.openExternal(url);
    }
    return { action: "deny" };
  });
  win.webContents.on("will-navigate", (event, url) => {
    event.preventDefault();
    shell.openExternal(url);
  });
};
const checkAndStartBackend = async () => {
  log.info("Checking and starting backend service...");
  const isToolInstalled = await checkToolInstalled();
  if (isToolInstalled) {
    log.info("Tools installed, starting backend service...");
    if (win && !win.isDestroyed()) {
      win.webContents.send("install-dependencies-complete", { success: true, code: 0 });
    }
    python_process = await startBackend((port) => {
      backendPort = port;
      log.info("Backend service started successfully", { port });
    });
    python_process == null ? void 0 : python_process.on("exit", (code, signal) => {
      log.info("Python process exited", { code, signal });
    });
  } else {
    log.warn("Tools not installed, unable to start backend service");
  }
};
const cleanupPythonProcess = () => {
  try {
    if (python_process == null ? void 0 : python_process.pid) {
      log.info("Cleaning up Python process", { pid: python_process.pid });
      kill(python_process.pid, "SIGINT", (err) => {
        if (err) {
          log.error("Failed to clean up process tree:", err);
        } else {
          log.info("Successfully cleaned up Python process tree");
        }
      });
    } else {
      log.info("No Python process to clean up");
    }
  } catch (error) {
    log.error("Error occurred while cleaning up process:", error);
  }
};
app.whenReady().then(() => {
  session.defaultSession.on("will-download", (event, item, webContents) => {
    item.once("done", (event2, state) => {
      shell.showItemInFolder(item.getURL().replace("localfile://", ""));
    });
  });
  protocol.handle("localfile", async (request) => {
    const url = decodeURIComponent(request.url.replace("localfile://", ""));
    const filePath = path$e.normalize(url);
    try {
      const data = await fsp.readFile(filePath);
      const ext = path$e.extname(filePath).toLowerCase();
      let contentType = "application/octet-stream";
      switch (ext) {
        case ".pdf":
          contentType = "application/pdf";
          break;
        case ".html":
        case ".htm":
          contentType = "text/html";
          break;
      }
      return new Response(data, {
        headers: {
          "Content-Type": contentType
        }
      });
    } catch (err) {
      return new Response("Not Found", { status: 404 });
    }
  });
  initializeApp();
  registerIpcHandlers();
  createWindow();
});
app.on("window-all-closed", () => {
  log.info("window-all-closed");
  webViewManager = null;
  win = null;
  if (process.platform !== "darwin") {
    app.quit();
  }
});
app.on("activate", () => {
  const allWindows = BrowserWindow.getAllWindows();
  log.info("activate", allWindows.length);
  if (allWindows.length) {
    allWindows[0].focus();
  } else {
    cleanupPythonProcess();
    createWindow();
  }
});
app.on("before-quit", () => {
  log.info("before-quit");
  log.info("quit python_process.pid: " + (python_process == null ? void 0 : python_process.pid));
  if (win) {
    win.destroy();
  }
  cleanupPythonProcess();
});
//# sourceMappingURL=index.js.map
