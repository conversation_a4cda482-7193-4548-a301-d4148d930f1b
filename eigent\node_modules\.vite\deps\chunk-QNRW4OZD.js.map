{"version": 3, "sources": ["../../../package/@stackframe/react/node_modules/@stackframe/stack-shared/src/utils/uuids.tsx"], "sourcesContent": ["import { generateRandomValues } from \"./crypto\";\n\nexport function generateUuid() {\n  // crypto.randomUuid is not supported in all browsers, so this is a polyfill\n  return \"10000000-1000-4000-8000-100000000000\".replace(/[018]/g, c =>\n    (+c ^ generateRandomValues(new Uint8Array(1))[0] & 15 >> +c / 4).toString(16)\n  );\n}\nundefined?.test(\"generateUuid\", ({ expect }) => {\n  // Test that the function returns a valid UUID\n  const uuid = generateUuid();\n  expect(uuid).toMatch(/^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/);\n\n  // Test that multiple calls generate different UUIDs\n  const uuid2 = generateUuid();\n  expect(uuid).not.toBe(uuid2);\n\n  // Test that the UUID is version 4 (random)\n  expect(uuid.charAt(14)).toBe('4');\n\n  // Test that the UUID has the correct variant (8, 9, a, or b in position 19)\n  expect('89ab').toContain(uuid.charAt(19));\n});\n\nexport function isUuid(str: string) {\n  return /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/.test(str);\n}\nundefined?.test(\"isUuid\", ({ expect }) => {\n  // Test with valid UUIDs\n  expect(isUuid(\"123e4567-e89b-42d3-a456-************\")).toBe(true);\n  expect(isUuid(\"123e4567-e89b-42d3-8456-************\")).toBe(true);\n  expect(isUuid(\"123e4567-e89b-42d3-9456-************\")).toBe(true);\n  expect(isUuid(\"123e4567-e89b-42d3-a456-************\")).toBe(true);\n  expect(isUuid(\"123e4567-e89b-42d3-b456-************\")).toBe(true);\n\n  // Test with invalid UUIDs\n  expect(isUuid(\"\")).toBe(false);\n  expect(isUuid(\"not-a-uuid\")).toBe(false);\n  expect(isUuid(\"123e4567-e89b-12d3-a456-************\")).toBe(false); // Wrong version (not 4)\n  expect(isUuid(\"123e4567-e89b-42d3-c456-************\")).toBe(false); // Wrong variant (not 8, 9, a, or b)\n  expect(isUuid(\"123e4567-e89b-42d3-a456-42661417400\")).toBe(false); // Too short\n  expect(isUuid(\"123e4567-e89b-42d3-a456-************0\")).toBe(false); // Too long\n  expect(isUuid(\"123e4567-e89b-42d3-a456_************\")).toBe(false); // Wrong format (underscore instead of dash)\n\n  // Test with uppercase letters (should fail as UUID should be lowercase)\n  expect(isUuid(\"123E4567-E89B-42D3-A456-************\")).toBe(false);\n});\n"], "mappings": ";;;;;AAEO,SAAS,eAAe;AAE7B,SAAO,uCAAuC;IAAQ;IAAU,CAAA,OAC7D,CAAC,IAAI,qBAAqB,IAAI,WAAW,CAAC,CAAC,EAAE,CAAC,IAAI,MAAM,CAAC,IAAI,GAAG,SAAS,EAAE;EAC9E;AACF;AAiBO,SAAS,OAAO,KAAa;AAClC,SAAO,wEAAwE,KAAK,GAAG;AACzF;", "names": []}