import {
  encodeBase32
} from "./chunk-LWWYEWPG.js";
import {
  StackAssertionError
} from "./chunk-Z26222H5.js";
import {
  globalVar
} from "./chunk-4BLY47KI.js";

// package/@stackframe/react/node_modules/@stackframe/stack-shared/dist/esm/utils/crypto.js
function generateRandomValues(array) {
  if (!globalVar.crypto) {
    throw new StackAssertionError("Crypto API is not available in this environment. Are you using an old browser?");
  }
  if (!globalVar.crypto.getRandomValues) {
    throw new StackAssertionError("crypto.getRandomValues is not available in this environment. Are you using an old browser?");
  }
  return globalVar.crypto.getRandomValues(array);
}
function generateSecureRandomString(minBitsOfEntropy = 224) {
  const base32CharactersCount = Math.ceil(minBitsOfEntropy / 5);
  const bytesCount = Math.ceil(base32CharactersCount * 5 / 8);
  const randomBytes = generateRandomValues(new Uint8Array(bytesCount));
  const str = encodeBase32(randomBytes);
  return str.slice(str.length - base32CharactersCount).toLowerCase();
}

export {
  generateRandomValues,
  generateSecureRandomString
};
//# sourceMappingURL=chunk-JBDVY4WB.js.map
